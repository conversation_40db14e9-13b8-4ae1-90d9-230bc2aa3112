<?php
require_once 'config/database.php';

$database = new Database();
$db = $database->getConnection();

echo "<h2>Database Update - Adding Receipt Numbers Table</h2>";

try {
    // Read and execute the SQL file
    $sql = file_get_contents('database/add_receipt_numbers_table.sql');
    
    // Split by semicolon and execute each statement
    $statements = array_filter(array_map('trim', explode(';', $sql)));
    
    foreach ($statements as $statement) {
        if (!empty($statement)) {
            $db->exec($statement);
            echo "<p>✅ Executed: " . substr($statement, 0, 50) . "...</p>";
        }
    }
    
    echo "<p style='color: green;'><strong>✅ Database updated successfully!</strong></p>";
    echo "<p>New features added:</p>";
    echo "<ul>";
    echo "<li>receipt_numbers table for storing individual receipt numbers</li>";
    echo "<li>total_amount field in expenses table</li>";
    echo "<li>Better tracking of receipt amounts and numbers</li>";
    echo "</ul>";
    
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ Error: " . $e->getMessage() . "</p>";
}

echo "<hr>";
echo "<p><a href='expenses/create.php'>Go to Create Expense</a></p>";
echo "<p><strong>Note:</strong> Delete this file after use.</p>";
?>
