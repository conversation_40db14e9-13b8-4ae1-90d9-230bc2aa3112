<?php
/**
 * Simple Server Check for MariaDB 5.5.68
 * ตรวจสอบระบบแบบง่าย ๆ
 */
?>
<!DOCTYPE html>
<html lang="th">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Simple Check - Expenses System</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
</head>
<body class="bg-light">
    <div class="container mt-4">
        <div class="card shadow">
            <div class="card-header bg-success text-white">
                <h3><i class="fas fa-check-circle me-2"></i>Simple System Check</h3>
            </div>
            <div class="card-body">

<?php
echo "<h4>🔍 Basic Checks</h4>";

// 1. PHP Version
echo "<p><strong>PHP Version:</strong> " . PHP_VERSION;
if (version_compare(PHP_VERSION, '8.0.0', '>=')) {
    echo " ✅</p>";
} else {
    echo " ⚠️</p>";
}

// 2. Database Connection
echo "<p><strong>Database Connection:</strong> ";
try {
    require_once 'config/database.php';
    $database = new Database();
    $db = $database->getConnection();
    echo "✅ Connected</p>";
    
    // Database version
    $stmt = $db->query("SELECT VERSION() as version");
    $version = $stmt->fetch()['version'];
    echo "<p><strong>Database Version:</strong> {$version}</p>";
    
} catch (Exception $e) {
    echo "❌ Failed: " . $e->getMessage() . "</p>";
    $db = null;
}

// 3. Tables Check (Simple)
if ($db) {
    echo "<h4>📋 Tables</h4>";
    try {
        $stmt = $db->query("SHOW TABLES");
        $tables = $stmt->fetchAll(PDO::FETCH_COLUMN);
        
        echo "<p><strong>Total Tables:</strong> " . count($tables) . "</p>";
        
        $required = ['users', 'items', 'customers', 'drivers', 'expenses', 'activity_logs'];
        echo "<p><strong>Key Tables:</strong></p><ul>";
        
        foreach ($required as $table) {
            if (in_array($table, $tables)) {
                echo "<li>✅ {$table}</li>";
            } else {
                echo "<li>❌ {$table} - Missing</li>";
            }
        }
        echo "</ul>";
        
    } catch (Exception $e) {
        echo "<p>❌ Table check failed: " . $e->getMessage() . "</p>";
    }
    
    // 4. Sample Data
    echo "<h4>📊 Sample Data</h4>";
    try {
        $check_tables = ['users', 'customers', 'items'];
        foreach ($check_tables as $table) {
            if (in_array($table, $tables)) {
                $stmt = $db->query("SELECT COUNT(*) as count FROM {$table}");
                $count = $stmt->fetch()['count'];
                echo "<p><strong>{$table}:</strong> {$count} records";
                if ($count > 0) {
                    echo " ✅</p>";
                } else {
                    echo " ⚠️ (empty)</p>";
                }
            }
        }
    } catch (Exception $e) {
        echo "<p>⚠️ Data check failed: " . $e->getMessage() . "</p>";
    }
    
    // 5. Procedures
    echo "<h4>⚙️ Stored Procedures</h4>";
    try {
        $stmt = $db->query("SHOW PROCEDURE STATUS WHERE Db = 'expenses_system'");
        $procedures = $stmt->fetchAll();
        echo "<p><strong>Procedures Found:</strong> " . count($procedures);
        if (count($procedures) >= 2) {
            echo " ✅</p>";
        } else {
            echo " ⚠️</p>";
        }
        
        if (count($procedures) > 0) {
            echo "<ul>";
            foreach ($procedures as $proc) {
                echo "<li>✅ {$proc['Name']}</li>";
            }
            echo "</ul>";
        }
        
    } catch (Exception $e) {
        echo "<p>⚠️ Procedure check failed: " . $e->getMessage() . "</p>";
    }
}

// 6. Directories
echo "<h4>📁 Upload Directories</h4>";
$dirs = ['uploads/receipts', 'uploads/transfer_slips', 'uploads/verification_slips', 'uploads/review_slips', 'backups'];

foreach ($dirs as $dir) {
    echo "<p><strong>{$dir}:</strong> ";
    if (is_dir($dir)) {
        if (is_writable($dir)) {
            echo "✅ OK</p>";
        } else {
            echo "⚠️ Not writable</p>";
        }
    } else {
        echo "❌ Missing</p>";
    }
}

// 7. Config File
echo "<h4>🔧 Configuration</h4>";
echo "<p><strong>config/database.php:</strong> ";
if (file_exists('config/database.php')) {
    echo "✅ Exists</p>";
} else {
    echo "❌ Missing</p>";
}

// Quick Test
if ($db) {
    echo "<h4>🧪 Quick Test</h4>";
    try {
        // Test a simple query
        $stmt = $db->query("SELECT 1 as test");
        $result = $stmt->fetch();
        if ($result['test'] == 1) {
            echo "<p>✅ Basic query test passed</p>";
        } else {
            echo "<p>❌ Basic query test failed</p>";
        }
        
        // Test user table
        if (in_array('users', $tables ?? [])) {
            $stmt = $db->query("SELECT COUNT(*) as count FROM users WHERE role = 'administrator'");
            $admin_count = $stmt->fetch()['count'];
            echo "<p><strong>Admin users:</strong> {$admin_count}";
            if ($admin_count > 0) {
                echo " ✅</p>";
            } else {
                echo " ⚠️ (no admin users)</p>";
            }
        }
        
    } catch (Exception $e) {
        echo "<p>❌ Quick test failed: " . $e->getMessage() . "</p>";
    }
}

echo "<hr>";
echo "<div class='alert alert-info'>";
echo "<h5>📝 Summary</h5>";
echo "<p>หากเห็น ✅ ในส่วนสำคัญ แสดงว่าระบบพร้อมใช้งาน</p>";
echo "<p>หากเห็น ❌ ต้องแก้ไขก่อนใช้งาน</p>";
echo "<p>หากเห็น ⚠️ เป็นคำเตือน อาจใช้งานได้แต่ควรตรวจสอบ</p>";
echo "</div>";

echo "<div class='alert alert-warning'>";
echo "<h6>🔒 Security</h6>";
echo "<p><strong>ลบไฟล์นี้หลังจากตรวจสอบเสร็จ:</strong></p>";
echo "<ul><li>simple_check.php</li><li>server_check.php</li></ul>";
echo "</div>";

echo "<div class='text-center mt-4'>";
echo "<a href='index.php' class='btn btn-primary btn-lg'>";
echo "<i class='fas fa-home me-2'></i>เข้าสู่ระบบ";
echo "</a>";
echo "</div>";
?>

            </div>
        </div>
    </div>
</body>
</html>
