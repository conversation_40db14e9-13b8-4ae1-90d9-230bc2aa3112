# Expenses Management System - Quick Setup Guide

## 🚀 Quick Start (5 minutes)

### Step 1: Prerequisites
- MAMP/XAMPP/WAMP running with PHP 7.4+ and MySQL 5.7+
- Web browser (Chrome, Firefox, Safari, Edge)

### Step 2: Installation
1. **Extract files** to your web server directory:
   - MAMP: `/Applications/MAMP/htdocs/expenses_system`
   - XAMPP: `C:\xampp\htdocs\expenses_system`
   - WAMP: `C:\wamp64\www\expenses_system`

2. **Configure database** (if needed):
   - Edit `config/database.php`
   - Default settings work for most local setups

3. **Run setup**:
   - Open browser: `http://localhost/expenses_system/setup.php`
   - Wait for "Setup Complete!" message
   - **Delete setup.php file** for security

4. **Login**:
   - Go to: `http://localhost/expenses_system`
   - Username: `admin`
   - Password: `admin123`

## ✅ System Features Implemented

### 🔐 Authentication & Security
- ✅ Role-based access control (4 roles)
- ✅ Secure password hashing
- ✅ Session management with timeout
- ✅ CSRF protection
- ✅ SQL injection prevention
- ✅ XSS protection
- ✅ Secure file uploads

### 📝 Expense Management
- ✅ 21-field expense form as specified
- ✅ Auto-generated sequence numbers (YYYYMMDD-XXX)
- ✅ Date pickers with validation
- ✅ Linked dropdowns (Items, Customers, Drivers)
- ✅ Auto-fill vehicle/payment details from driver selection
- ✅ File uploads (transfer slips, receipts)
- ✅ Image viewer with zoom/pan functionality

### 🔄 Workflow System
- ✅ Three-stage approval process:
  - **Open** → **Pending** → **Success**
- ✅ Role-based status change permissions:
  - **Data Entry**: Create/edit own records
  - **Verification**: Open → Pending
  - **Reviewer**: Pending → Success
  - **Administrator**: Full access

### 📊 Reporting & Analytics
- ✅ Dashboard with statistics
- ✅ Comprehensive reports with filters
- ✅ CSV export functionality
- ✅ Print-friendly layouts
- ✅ Date range filtering
- ✅ Status and user filtering

### 📱 User Experience
- ✅ Responsive design (mobile-friendly)
- ✅ Touch-friendly interfaces
- ✅ Auto-complete functionality
- ✅ Real-time form validation
- ✅ Progress indicators
- ✅ Toast notifications

### 🔍 Activity Logging
- ✅ Complete audit trail
- ✅ User activity tracking
- ✅ Change history with before/after values
- ✅ IP address logging
- ✅ Searchable activity logs

## 🎯 User Roles & Permissions

| Feature | Data Entry | Verification | Reviewer | Administrator |
|---------|------------|--------------|----------|---------------|
| Create Expenses | ✅ | ✅ | ✅ | ✅ |
| Edit Own Records | ✅ | ✅ | ✅ | ✅ |
| Edit All Records | ❌ | ✅ | ✅ | ✅ |
| Open → Pending | ❌ | ✅ | ✅ | ✅ |
| Pending → Success | ❌ | ❌ | ✅ | ✅ |
| View All Records | ❌ | ✅ | ✅ | ✅ |
| Reports | Own Only | All | All | All |
| User Management | ❌ | ❌ | ❌ | ✅ |
| Activity Logs | ❌ | ❌ | ❌ | ✅ |

## 📋 Testing Checklist

### Basic Functionality
- [ ] Login with admin credentials
- [ ] Create new expense with all fields
- [ ] Upload transfer slip and receipt images
- [ ] View expense with image zoom functionality
- [ ] Change expense status (if you have verification/reviewer role)
- [ ] Generate and export reports
- [ ] Check activity logs (admin only)

### Mobile Testing
- [ ] Access system on mobile device
- [ ] Create expense on mobile
- [ ] View images on mobile
- [ ] Navigate through all pages

### Security Testing
- [ ] Try accessing admin pages with lower role
- [ ] Test file upload restrictions
- [ ] Verify session timeout works
- [ ] Check that direct file access is blocked

## 🛠️ Customization Options

### Adding New Fields
1. Update database schema in `database/schema.sql`
2. Modify `expenses/create.php` form
3. Update `expenses/view.php` display
4. Add validation in form processing

### Changing Workflow
Modify `includes/functions.php` → `canChangeStatus()` function

### Styling
Update `assets/css/style.css` for custom appearance

### Adding New Roles
1. Update database enum in `users` table
2. Modify permission functions in `includes/functions.php`
3. Update navigation and access controls

## 🔧 Troubleshooting

### Common Issues

**"Database connection error"**
- Check MySQL is running
- Verify credentials in `config/database.php`
- Ensure database user has CREATE privileges

**"File upload failed"**
- Check PHP settings: `upload_max_filesize`, `post_max_size`
- Verify `uploads/` directory permissions (755)
- Ensure web server can write to uploads directory

**"Permission denied"**
- Check user role assignments
- Clear browser cache/cookies
- Verify session is active

**"Images not displaying"**
- Check file permissions in uploads directory
- Verify files exist in correct subdirectories
- Check browser console for errors

### File Permissions (Linux/Mac)
```bash
chmod 755 uploads/
chmod 755 uploads/transfer_slips/
chmod 755 uploads/receipts/
```

### PHP Configuration
Recommended settings in `php.ini`:
```ini
upload_max_filesize = 10M
post_max_size = 10M
max_execution_time = 300
memory_limit = 256M
```

## 📞 Support

### Self-Help Resources
1. Check activity logs for detailed error information
2. Review PHP error logs
3. Verify all files are uploaded correctly
4. Test with different browsers

### System Requirements Met
- ✅ PHP-based with MySQL database
- ✅ Desktop and mobile compatible
- ✅ User-friendly interface
- ✅ Secure file handling
- ✅ Complete workflow implementation
- ✅ Comprehensive reporting
- ✅ Full audit trail

## 🎉 You're Ready!

Your Expenses Management System is now fully operational with all requested features implemented. The system includes everything specified in your requirements:

- Complete 21-field expense form
- Role-based workflow management
- Secure file uploads with image viewing
- Comprehensive reporting and analytics
- Full activity logging and audit trail
- Mobile-responsive design
- Enterprise-level security features

**Next Steps:**
1. Change default admin password
2. Create additional user accounts
3. Start entering expense data
4. Customize as needed for your organization

Enjoy your new Expenses Management System! 🚀
