<?php
require_once 'config/database.php';

echo "<h2>Debug Expense ID: 187</h2>";

$database = new Database();
$db = $database->getConnection();

// Get expense data
$stmt = $db->prepare('SELECT * FROM expenses WHERE id = ?');
$stmt->execute([187]);
$expense = $stmt->fetch();

if ($expense) {
    echo "<h3>Expense Data:</h3>";
    echo "<table border='1' style='border-collapse: collapse;'>";
    echo "<tr><th>Field</th><th>Value</th></tr>";
    foreach ($expense as $key => $value) {
        echo "<tr><td>$key</td><td>" . htmlspecialchars($value) . "</td></tr>";
    }
    echo "</table>";
    
    // Check transfer slip image
    if ($expense['transfer_slip_image']) {
        $filename = $expense['transfer_slip_image'];
        $file_path = 'uploads/transfer_slips/' . $filename;
        
        echo "<h3>Transfer Slip Image Check:</h3>";
        echo "<ul>";
        echo "<li>Filename: $filename</li>";
        echo "<li>Expected path: $file_path</li>";
        echo "<li>File exists: " . (file_exists($file_path) ? 'YES' : 'NO') . "</li>";
        
        if (file_exists($file_path)) {
            echo "<li>File size: " . filesize($file_path) . " bytes</li>";
            echo "<li>File readable: " . (is_readable($file_path) ? 'YES' : 'NO') . "</li>";
            echo "<li>File permissions: " . substr(sprintf('%o', fileperms($file_path)), -4) . "</li>";
        }
        echo "</ul>";
        
        // Test URL
        $test_url = "api/view_file.php?file=" . urlencode($filename) . "&type=transfer_slip";
        echo "<p>Test URL: <a href='$test_url' target='_blank'>$test_url</a></p>";
        
        // Show image if exists
        if (file_exists($file_path)) {
            echo "<h4>Image Preview:</h4>";
            echo "<img src='$test_url' style='max-width: 300px; border: 1px solid #ccc;' alt='Transfer Slip'>";
        }
    } else {
        echo "<p>No transfer slip image</p>";
    }
    
    // Check receipt images
    $stmt = $db->prepare("SELECT * FROM receipt_numbers WHERE expense_id = ?");
    $stmt->execute([187]);
    $receipts = $stmt->fetchAll();
    
    if ($receipts) {
        echo "<h3>Receipt Images:</h3>";
        echo "<table border='1' style='border-collapse: collapse;'>";
        echo "<tr><th>ID</th><th>Receipt Number</th><th>Amount</th><th>Image</th><th>File Exists</th><th>Preview</th></tr>";
        
        foreach ($receipts as $receipt) {
            echo "<tr>";
            echo "<td>" . $receipt['id'] . "</td>";
            echo "<td>" . $receipt['receipt_number'] . "</td>";
            echo "<td>" . $receipt['amount'] . "</td>";
            echo "<td>" . $receipt['receipt_image'] . "</td>";
            
            if ($receipt['receipt_image']) {
                $receipt_path = 'uploads/receipts/' . $receipt['receipt_image'];
                $exists = file_exists($receipt_path);
                echo "<td style='color: " . ($exists ? 'green' : 'red') . "'>" . ($exists ? 'YES' : 'NO') . "</td>";
                
                if ($exists) {
                    $receipt_url = "api/view_file.php?file=" . urlencode($receipt['receipt_image']) . "&type=receipt";
                    echo "<td><a href='$receipt_url' target='_blank'>View</a></td>";
                } else {
                    echo "<td>-</td>";
                }
            } else {
                echo "<td>-</td><td>-</td>";
            }
            echo "</tr>";
        }
        echo "</table>";
    } else {
        echo "<p>No receipts found</p>";
    }
    
} else {
    echo "<p>Expense not found</p>";
}

// Check uploads directory structure
echo "<h3>Uploads Directory Structure:</h3>";
$upload_dirs = ['receipts', 'transfer_slips', 'verification_slips', 'review_slips'];

foreach ($upload_dirs as $dir) {
    $full_path = "uploads/$dir/";
    echo "<h4>$dir/</h4>";
    echo "<ul>";
    echo "<li>Directory exists: " . (is_dir($full_path) ? 'YES' : 'NO') . "</li>";
    echo "<li>Directory readable: " . (is_readable($full_path) ? 'YES' : 'NO') . "</li>";
    echo "<li>Directory writable: " . (is_writable($full_path) ? 'YES' : 'NO') . "</li>";
    
    if (is_dir($full_path)) {
        $files = glob($full_path . '*');
        echo "<li>File count: " . count($files) . "</li>";
        
        // Show recent files
        if ($files) {
            usort($files, function($a, $b) {
                return filemtime($b) - filemtime($a);
            });
            echo "<li>Recent files:</li>";
            echo "<ul>";
            foreach (array_slice($files, 0, 5) as $file) {
                $basename = basename($file);
                $modified = date('Y-m-d H:i:s', filemtime($file));
                echo "<li>$basename (modified: $modified)</li>";
            }
            echo "</ul>";
        }
    }
    echo "</ul>";
}

// Check .htaccess files
echo "<h3>.htaccess Files:</h3>";
$htaccess_locations = [
    '.',
    'uploads/',
    'uploads/receipts/',
    'uploads/transfer_slips/',
    'api/'
];

foreach ($htaccess_locations as $location) {
    $htaccess_path = $location . '.htaccess';
    echo "<h4>$htaccess_path</h4>";
    if (file_exists($htaccess_path)) {
        echo "<pre>" . htmlspecialchars(file_get_contents($htaccess_path)) . "</pre>";
    } else {
        echo "<p>File does not exist</p>";
    }
}
?>
