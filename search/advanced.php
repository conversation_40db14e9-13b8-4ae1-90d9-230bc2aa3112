<?php
session_start();
require_once '../config/database.php';
require_once '../includes/functions.php';

// Check if user is logged in
if (!isset($_SESSION['user_id'])) {
    header('Location: ../login.php');
    exit();
}

// Initialize database connection
$database = new Database();
$db = $database->getConnection();

$user_id = $_SESSION['user_id'];
$user_role = $_SESSION['role'];

// Initialize search parameters
$search_params = [
    'exno' => $_GET['exno'] ?? '',
    'status' => $_GET['status'] ?? '',
    'created_by' => $_GET['created_by'] ?? '',
    'item_id' => $_GET['item_id'] ?? '',
    'customer_id' => $_GET['customer_id'] ?? '',
    'driver_id' => $_GET['driver_id'] ?? '',
    'date_from' => $_GET['date_from'] ?? '',
    'date_to' => $_GET['date_to'] ?? '',
    'amount_from' => $_GET['amount_from'] ?? '',
    'amount_to' => $_GET['amount_to'] ?? '',
    'transfer_no' => $_GET['transfer_no'] ?? '',
    'verification_by' => $_GET['verification_by'] ?? '',
    'reviewer_by' => $_GET['reviewer_by'] ?? '',
    'has_receipts' => $_GET['has_receipts'] ?? '',
    'sort_by' => $_GET['sort_by'] ?? 'created_at',
    'sort_order' => $_GET['sort_order'] ?? 'DESC'
];

// Pagination
$page = max(1, intval($_GET['page'] ?? 1));
$per_page = 20;
$offset = ($page - 1) * $per_page;

// Build search query
$where_conditions = [];
$params = [];

// Role-based access control
if ($user_role === 'data_entry') {
    $where_conditions[] = "e.created_by = ?";
    $params[] = $user_id;
}

// Search filters
if (!empty($search_params['exno'])) {
    $where_conditions[] = "e.exno LIKE ?";
    $params[] = '%' . $search_params['exno'] . '%';
}

if (!empty($search_params['status'])) {
    $where_conditions[] = "e.status = ?";
    $params[] = $search_params['status'];
}

if (!empty($search_params['created_by']) && $user_role !== 'data_entry') {
    $where_conditions[] = "e.created_by = ?";
    $params[] = $search_params['created_by'];
}

if (!empty($search_params['item_id'])) {
    $where_conditions[] = "e.item_id = ?";
    $params[] = $search_params['item_id'];
}

if (!empty($search_params['customer_id'])) {
    $where_conditions[] = "e.customer_id = ?";
    $params[] = $search_params['customer_id'];
}

if (!empty($search_params['driver_id'])) {
    $where_conditions[] = "e.driver_id = ?";
    $params[] = $search_params['driver_id'];
}

if (!empty($search_params['date_from'])) {
    $where_conditions[] = "DATE(e.created_at) >= ?";
    $params[] = $search_params['date_from'];
}

if (!empty($search_params['date_to'])) {
    $where_conditions[] = "DATE(e.created_at) <= ?";
    $params[] = $search_params['date_to'];
}

if (!empty($search_params['amount_from'])) {
    $where_conditions[] = "e.total_amount >= ?";
    $params[] = floatval($search_params['amount_from']);
}

if (!empty($search_params['amount_to'])) {
    $where_conditions[] = "e.total_amount <= ?";
    $params[] = floatval($search_params['amount_to']);
}

if (!empty($search_params['transfer_no'])) {
    $where_conditions[] = "(e.transfer_no LIKE ? OR e.verification_transfer_no LIKE ? OR e.reviewer_transfer_no LIKE ?)";
    $transfer_search = '%' . $search_params['transfer_no'] . '%';
    $params[] = $transfer_search;
    $params[] = $transfer_search;
    $params[] = $transfer_search;
}

if (!empty($search_params['verification_by']) && in_array($user_role, ['verification', 'reviewer', 'administrator'])) {
    $where_conditions[] = "e.verification_by = ?";
    $params[] = $search_params['verification_by'];
}

if (!empty($search_params['reviewer_by']) && in_array($user_role, ['reviewer', 'administrator'])) {
    $where_conditions[] = "e.reviewer_by = ?";
    $params[] = $search_params['reviewer_by'];
}

if ($search_params['has_receipts'] !== '') {
    if ($search_params['has_receipts'] === '1') {
        $where_conditions[] = "(e.receipt_images IS NOT NULL AND e.receipt_images != '' AND e.receipt_images != '[]')";
    } else {
        $where_conditions[] = "(e.receipt_images IS NULL OR e.receipt_images = '' OR e.receipt_images = '[]')";
    }
}

// Build WHERE clause
$where_clause = '';
if (!empty($where_conditions)) {
    $where_clause = 'WHERE ' . implode(' AND ', $where_conditions);
}

// Valid sort columns
$valid_sort_columns = [
    'created_at' => 'e.created_at',
    'exno' => 'e.exno',
    'status' => 'e.status',
    'total_amount' => 'e.total_amount',
    'created_by_name' => 'u.full_name'
];

$sort_column = $valid_sort_columns[$search_params['sort_by']] ?? 'e.created_at';
$sort_order = in_array(strtoupper($search_params['sort_order']), ['ASC', 'DESC']) ? strtoupper($search_params['sort_order']) : 'DESC';

// Get total count
$count_sql = "
    SELECT COUNT(*) as total
    FROM expenses e
    LEFT JOIN users u ON e.created_by = u.id
    {$where_clause}
";

$stmt = $db->prepare($count_sql);
$stmt->execute($params);
$total_records = $stmt->fetch()['total'];
$total_pages = ceil($total_records / $per_page);

// Get search results
$search_sql = "
    SELECT e.*, 
           u.full_name as created_by_name,
           i.name as item_name,
           c.name as customer_name,
           d.name as driver_name,
           vb.full_name as verification_by_name,
           rb.full_name as reviewer_by_name,
           CASE
               WHEN e.receipt_images IS NULL OR e.receipt_images = '' OR e.receipt_images = '[]' THEN 0
               ELSE (LENGTH(e.receipt_images) - LENGTH(REPLACE(e.receipt_images, ',', '')) + 1)
           END as receipt_count
    FROM expenses e
    LEFT JOIN users u ON e.created_by = u.id
    LEFT JOIN items i ON e.item_id = i.id
    LEFT JOIN customers c ON e.customer_id = c.id
    LEFT JOIN drivers d ON e.driver_id = d.id
    LEFT JOIN users vb ON e.verification_by = vb.id
    LEFT JOIN users rb ON e.reviewer_by = rb.id
    {$where_clause}
    ORDER BY {$sort_column} {$sort_order}
    LIMIT {$per_page} OFFSET {$offset}
";

$stmt = $db->prepare($search_sql);
$stmt->execute($params);
$search_results = $stmt->fetchAll();

// Get dropdown data for filters
$users = [];
$items = [];
$customers = [];
$drivers = [];

if ($user_role !== 'data_entry') {
    $stmt = $db->query("SELECT id, full_name FROM users WHERE is_active = 1 ORDER BY full_name");
    $users = $stmt->fetchAll();
}

$stmt = $db->query("SELECT id, name FROM items WHERE is_active = 1 ORDER BY name");
$items = $stmt->fetchAll();

$stmt = $db->query("SELECT id, name FROM customers WHERE is_active = 1 ORDER BY name");
$customers = $stmt->fetchAll();

$stmt = $db->query("SELECT id, name FROM drivers WHERE is_active = 1 ORDER BY name");
$drivers = $stmt->fetchAll();

// Get verification and reviewer users
$verification_users = [];
$reviewer_users = [];

if (in_array($user_role, ['verification', 'reviewer', 'administrator'])) {
    $stmt = $db->query("SELECT id, full_name FROM users WHERE role = 'verification' AND is_active = 1 ORDER BY full_name");
    $verification_users = $stmt->fetchAll();
}

if (in_array($user_role, ['reviewer', 'administrator'])) {
    $stmt = $db->query("SELECT id, full_name FROM users WHERE role = 'reviewer' AND is_active = 1 ORDER BY full_name");
    $reviewer_users = $stmt->fetchAll();
}
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Advanced Search - Expense Management System</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <link href="../assets/css/style.css" rel="stylesheet">
    <style>
        :root {
            --primary-gradient: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            --success-gradient: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            --warning-gradient: linear-gradient(135deg, #fa709a 0%, #fee140 100%);
            --info-gradient: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%);
        }

        body {
            background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
            min-height: 100vh;
        }

        .page-header {
            background: var(--primary-gradient);
            color: white;
            padding: 2rem 0;
            margin-bottom: 2rem;
            border-radius: 0 0 20px 20px;
            box-shadow: 0 4px 20px rgba(0,0,0,0.1);
        }

        .search-container {
            background: white;
            border-radius: 20px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            overflow: hidden;
            margin-bottom: 2rem;
        }

        .search-header {
            background: var(--success-gradient);
            color: white;
            padding: 1.5rem;
            border: none;
        }

        .filter-tabs {
            background: #f8f9fa;
            border-bottom: 1px solid #dee2e6;
        }

        .filter-tabs .nav-link {
            border: none;
            color: #6c757d;
            font-weight: 500;
            padding: 1rem 1.5rem;
            transition: all 0.3s ease;
        }

        .filter-tabs .nav-link.active {
            background: white;
            color: #495057;
            border-bottom: 3px solid #667eea;
        }

        .filter-tabs .nav-link:hover {
            background: #e9ecef;
            color: #495057;
        }

        .filter-content {
            padding: 2rem;
        }

        .form-floating > label {
            color: #6c757d;
        }

        .form-floating > .form-control:focus ~ label,
        .form-floating > .form-control:not(:placeholder-shown) ~ label,
        .form-floating > .form-select ~ label {
            color: #667eea;
        }

        .btn-search {
            background: var(--primary-gradient);
            border: none;
            padding: 0.75rem 2rem;
            border-radius: 50px;
            font-weight: 600;
            transition: all 0.3s ease;
        }

        .btn-search:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
        }

        .btn-clear {
            background: var(--warning-gradient);
            border: none;
            padding: 0.75rem 2rem;
            border-radius: 50px;
            font-weight: 600;
            color: white;
            transition: all 0.3s ease;
        }

        .btn-clear:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(250, 112, 154, 0.3);
            color: white;
        }

        .results-container {
            background: white;
            border-radius: 20px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            overflow: hidden;
        }

        .results-header {
            background: var(--info-gradient);
            padding: 1.5rem;
            border: none;
        }

        .expense-card {
            border: 2px solid #e9ecef;
            border-radius: 15px;
            margin-bottom: 1rem;
            transition: all 0.3s ease;
            box-shadow: 0 2px 10px rgba(0,0,0,0.05);
            background: white;
        }

        .expense-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 30px rgba(0,0,0,0.15);
            border-color: #667eea;
        }

        .expense-card .card-body {
            border: 1px solid #dee2e6;
            border-radius: 12px;
            margin: 8px;
            padding: 1.25rem;
            background: #fafbfc;
            transition: all 0.3s ease;
        }

        .expense-card:hover .card-body {
            border-color: #667eea;
            background: #f8f9ff;
            box-shadow: 0 2px 8px rgba(102, 126, 234, 0.1);
        }

        .status-badge {
            padding: 0.5rem 1rem;
            border-radius: 50px;
            font-weight: 600;
            font-size: 0.85rem;
        }

        .pagination .page-link {
            border: none;
            color: #667eea;
            font-weight: 500;
            margin: 0 0.25rem;
            border-radius: 10px;
        }

        .pagination .page-link:hover {
            background: #667eea;
            color: white;
        }

        .pagination .page-item.active .page-link {
            background: var(--primary-gradient);
            border: none;
        }

        .quick-filters {
            display: flex;
            gap: 0.5rem;
            flex-wrap: wrap;
            margin-bottom: 1rem;
        }

        .quick-filter-btn {
            padding: 0.5rem 1rem;
            border: 2px solid #e9ecef;
            background: white;
            color: #6c757d;
            border-radius: 50px;
            font-size: 0.875rem;
            font-weight: 500;
            transition: all 0.3s ease;
            text-decoration: none;
        }

        .quick-filter-btn:hover,
        .quick-filter-btn.active {
            background: var(--primary-gradient);
            border-color: transparent;
            color: white;
            transform: translateY(-2px);
        }

        .stats-card {
            background: white;
            border-radius: 15px;
            padding: 1.5rem;
            text-align: center;
            box-shadow: 0 5px 15px rgba(0,0,0,0.08);
            transition: all 0.3s ease;
        }

        .stats-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 30px rgba(0,0,0,0.15);
        }

        .stats-icon {
            width: 60px;
            height: 60px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 1rem;
            font-size: 1.5rem;
            color: white;
        }

        .animate-fade-in {
            animation: fadeIn 0.5s ease-in;
        }

        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(20px); }
            to { opacity: 1; transform: translateY(0); }
        }

        .floating-action {
            position: fixed;
            bottom: 2rem;
            right: 2rem;
            z-index: 1000;
        }

        .floating-btn {
            width: 60px;
            height: 60px;
            border-radius: 50%;
            background: var(--primary-gradient);
            border: none;
            color: white;
            font-size: 1.5rem;
            box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
            transition: all 0.3s ease;
        }

        .floating-btn:hover {
            transform: scale(1.1);
            box-shadow: 0 12px 35px rgba(102, 126, 234, 0.4);
            color: white;
        }
    </style>
</head>
<body>
    <?php include '../includes/navbar.php'; ?>

    <!-- Page Header -->
    <div class="page-header">
        <div class="container">
            <div class="row align-items-center">
                <div class="col-md-8">
                    <h1 class="mb-2">
                        <i class="fas fa-search-plus me-3"></i>Advanced Search
                    </h1>
                    <p class="mb-0 opacity-75">Powerful search and filtering for expense management</p>
                </div>
                <div class="col-md-4 text-end">
                    <span class="badge bg-light text-dark px-3 py-2 rounded-pill">
                        <i class="fas fa-user me-2"></i><?php echo ucfirst($user_role); ?>
                    </span>
                </div>
            </div>
        </div>
    </div>

    <div class="container-fluid px-4">

        <!-- Quick Filters -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="quick-filters">
                    <a href="?status=" class="quick-filter-btn <?php echo empty($search_params['status']) ? 'active' : ''; ?>">
                        <i class="fas fa-list me-2"></i>All
                    </a>
                    <a href="?status=open" class="quick-filter-btn <?php echo $search_params['status'] === 'open' ? 'active' : ''; ?>">
                        <i class="fas fa-folder-open me-2"></i>Open
                    </a>
                    <a href="?status=pending" class="quick-filter-btn <?php echo $search_params['status'] === 'pending' ? 'active' : ''; ?>">
                        <i class="fas fa-clock me-2"></i>Pending
                    </a>
                    <a href="?status=success" class="quick-filter-btn <?php echo $search_params['status'] === 'success' ? 'active' : ''; ?>">
                        <i class="fas fa-check-circle me-2"></i>Approved
                    </a>
                    <a href="?status=rejected" class="quick-filter-btn <?php echo $search_params['status'] === 'rejected' ? 'active' : ''; ?>">
                        <i class="fas fa-times-circle me-2"></i>Rejected
                    </a>
                    <a href="?status=returned" class="quick-filter-btn <?php echo $search_params['status'] === 'returned' ? 'active' : ''; ?>">
                        <i class="fas fa-undo me-2"></i>Returned
                    </a>
                </div>
            </div>
        </div>

        <!-- Advanced Search Form -->
        <div class="search-container animate-fade-in">
            <div class="search-header">
                <h4 class="mb-0">
                    <i class="fas fa-filter me-3"></i>Advanced Search & Filters
                </h4>
            </div>

            <!-- Filter Tabs -->
            <ul class="nav nav-tabs filter-tabs" id="filterTabs" role="tablist">
                <li class="nav-item" role="presentation">
                    <button class="nav-link active" id="basic-tab" data-bs-toggle="tab" data-bs-target="#basic" type="button" role="tab">
                        <i class="fas fa-search me-2"></i>Basic Search
                    </button>
                </li>
                <li class="nav-item" role="presentation">
                    <button class="nav-link" id="advanced-tab" data-bs-toggle="tab" data-bs-target="#advanced" type="button" role="tab">
                        <i class="fas fa-cogs me-2"></i>Advanced Filters
                    </button>
                </li>
                <?php if (in_array($user_role, ['verification', 'reviewer', 'administrator'])): ?>
                <li class="nav-item" role="presentation">
                    <button class="nav-link" id="workflow-tab" data-bs-toggle="tab" data-bs-target="#workflow" type="button" role="tab">
                        <i class="fas fa-users-cog me-2"></i>Workflow
                    </button>
                </li>
                <?php endif; ?>
                <li class="nav-item" role="presentation">
                    <button class="nav-link" id="sort-tab" data-bs-toggle="tab" data-bs-target="#sort" type="button" role="tab">
                        <i class="fas fa-sort me-2"></i>Sort & Display
                    </button>
                </li>
            </ul>

            <div class="filter-content">
                <form method="GET" id="searchForm">
                    <div class="tab-content" id="filterTabContent">
                        <!-- Basic Search Tab -->
                        <div class="tab-pane fade show active" id="basic" role="tabpanel">
                            <div class="row g-4">
                                <div class="col-md-6">
                                    <div class="form-floating">
                                        <input type="text" class="form-control" id="exno" name="exno"
                                               value="<?php echo htmlspecialchars($search_params['exno']); ?>"
                                               placeholder="Search by expense number">
                                        <label for="exno">
                                            <i class="fas fa-hashtag me-2"></i>Expense Number
                                        </label>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="form-floating">
                                        <select class="form-select" id="status" name="status">
                                            <option value="">All Statuses</option>
                                            <option value="open" <?php echo $search_params['status'] === 'open' ? 'selected' : ''; ?>>Open</option>
                                            <option value="pending" <?php echo $search_params['status'] === 'pending' ? 'selected' : ''; ?>>Pending</option>
                                            <option value="success" <?php echo $search_params['status'] === 'success' ? 'selected' : ''; ?>>Approved</option>
                                            <option value="rejected" <?php echo $search_params['status'] === 'rejected' ? 'selected' : ''; ?>>Rejected</option>
                                            <option value="returned" <?php echo $search_params['status'] === 'returned' ? 'selected' : ''; ?>>Returned</option>
                                        </select>
                                        <label for="status">
                                            <i class="fas fa-flag me-2"></i>Status
                                        </label>
                                    </div>
                                </div>
                                <?php if ($user_role !== 'data_entry'): ?>
                                <div class="col-md-6">
                                    <div class="form-floating">
                                        <select class="form-select" id="created_by" name="created_by">
                                            <option value="">All Users</option>
                                            <?php foreach ($users as $user): ?>
                                            <option value="<?php echo $user['id']; ?>"
                                                    <?php echo $search_params['created_by'] == $user['id'] ? 'selected' : ''; ?>>
                                                <?php echo htmlspecialchars($user['full_name']); ?>
                                            </option>
                                            <?php endforeach; ?>
                                        </select>
                                        <label for="created_by">
                                            <i class="fas fa-user me-2"></i>Created By
                                        </label>
                                    </div>
                                </div>
                                <?php endif; ?>
                                <div class="col-md-6">
                                    <div class="form-floating">
                                        <input type="text" class="form-control" id="transfer_no" name="transfer_no"
                                               value="<?php echo htmlspecialchars($search_params['transfer_no']); ?>"
                                               placeholder="Search in all transfer number fields">
                                        <label for="transfer_no">
                                            <i class="fas fa-receipt me-2"></i>Transfer Number
                                        </label>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Advanced Filters Tab -->
                        <div class="tab-pane fade" id="advanced" role="tabpanel">
                            <div class="row g-4">
                                <!-- Master Data Filters -->
                                <div class="col-md-4">
                                    <div class="form-floating">
                                        <select class="form-select" id="item_id" name="item_id">
                                            <option value="">All Items</option>
                                            <?php foreach ($items as $item): ?>
                                            <option value="<?php echo $item['id']; ?>"
                                                    <?php echo $search_params['item_id'] == $item['id'] ? 'selected' : ''; ?>>
                                                <?php echo htmlspecialchars($item['name']); ?>
                                            </option>
                                            <?php endforeach; ?>
                                        </select>
                                        <label for="item_id">
                                            <i class="fas fa-box me-2"></i>Item
                                        </label>
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="form-floating">
                                        <select class="form-select" id="customer_id" name="customer_id">
                                            <option value="">All Customers</option>
                                            <?php foreach ($customers as $customer): ?>
                                            <option value="<?php echo $customer['id']; ?>"
                                                    <?php echo $search_params['customer_id'] == $customer['id'] ? 'selected' : ''; ?>>
                                                <?php echo htmlspecialchars($customer['name']); ?>
                                            </option>
                                            <?php endforeach; ?>
                                        </select>
                                        <label for="customer_id">
                                            <i class="fas fa-building me-2"></i>Customer
                                        </label>
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="form-floating">
                                        <select class="form-select" id="driver_id" name="driver_id">
                                            <option value="">All Drivers</option>
                                            <?php foreach ($drivers as $driver): ?>
                                            <option value="<?php echo $driver['id']; ?>"
                                                    <?php echo $search_params['driver_id'] == $driver['id'] ? 'selected' : ''; ?>>
                                                <?php echo htmlspecialchars($driver['name']); ?>
                                            </option>
                                            <?php endforeach; ?>
                                        </select>
                                        <label for="driver_id">
                                            <i class="fas fa-car me-2"></i>Driver
                                        </label>
                                    </div>
                                </div>

                                <!-- Date Range -->
                                <div class="col-md-6">
                                    <div class="form-floating">
                                        <input type="date" class="form-control" id="date_from" name="date_from"
                                               value="<?php echo htmlspecialchars($search_params['date_from']); ?>">
                                        <label for="date_from">
                                            <i class="fas fa-calendar-alt me-2"></i>Date From
                                        </label>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="form-floating">
                                        <input type="date" class="form-control" id="date_to" name="date_to"
                                               value="<?php echo htmlspecialchars($search_params['date_to']); ?>">
                                        <label for="date_to">
                                            <i class="fas fa-calendar-alt me-2"></i>Date To
                                        </label>
                                    </div>
                                </div>

                                <!-- Amount Range -->
                                <div class="col-md-6">
                                    <div class="form-floating">
                                        <input type="number" class="form-control" id="amount_from" name="amount_from"
                                               value="<?php echo htmlspecialchars($search_params['amount_from']); ?>"
                                               step="0.01" min="0" placeholder="0.00">
                                        <label for="amount_from">
                                            <i class="fas fa-money-bill me-2"></i>Amount From (฿)
                                        </label>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="form-floating">
                                        <input type="number" class="form-control" id="amount_to" name="amount_to"
                                               value="<?php echo htmlspecialchars($search_params['amount_to']); ?>"
                                               step="0.01" min="0" placeholder="0.00">
                                        <label for="amount_to">
                                            <i class="fas fa-money-bill me-2"></i>Amount To (฿)
                                        </label>
                                    </div>
                                </div>

                                <!-- Receipt Filter -->
                                <div class="col-md-12">
                                    <div class="form-floating">
                                        <select class="form-select" id="has_receipts" name="has_receipts">
                                            <option value="">All Expenses</option>
                                            <option value="1" <?php echo $search_params['has_receipts'] === '1' ? 'selected' : ''; ?>>With Receipts</option>
                                            <option value="0" <?php echo $search_params['has_receipts'] === '0' ? 'selected' : ''; ?>>Without Receipts</option>
                                        </select>
                                        <label for="has_receipts">
                                            <i class="fas fa-file-image me-2"></i>Receipt Status
                                        </label>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Workflow Filters Tab -->
                        <?php if (in_array($user_role, ['verification', 'reviewer', 'administrator'])): ?>
                        <div class="tab-pane fade" id="workflow" role="tabpanel">
                            <div class="row g-4">
                                <?php if (!empty($verification_users)): ?>
                                <div class="col-md-6">
                                    <div class="form-floating">
                                        <select class="form-select" id="verification_by" name="verification_by">
                                            <option value="">All Verification Officers</option>
                                            <?php foreach ($verification_users as $user): ?>
                                            <option value="<?php echo $user['id']; ?>"
                                                    <?php echo $search_params['verification_by'] == $user['id'] ? 'selected' : ''; ?>>
                                                <?php echo htmlspecialchars($user['full_name']); ?>
                                            </option>
                                            <?php endforeach; ?>
                                        </select>
                                        <label for="verification_by">
                                            <i class="fas fa-user-check me-2"></i>Verified By
                                        </label>
                                    </div>
                                </div>
                                <?php endif; ?>

                                <?php if (!empty($reviewer_users)): ?>
                                <div class="col-md-6">
                                    <div class="form-floating">
                                        <select class="form-select" id="reviewer_by" name="reviewer_by">
                                            <option value="">All Reviewers</option>
                                            <?php foreach ($reviewer_users as $user): ?>
                                            <option value="<?php echo $user['id']; ?>"
                                                    <?php echo $search_params['reviewer_by'] == $user['id'] ? 'selected' : ''; ?>>
                                                <?php echo htmlspecialchars($user['full_name']); ?>
                                            </option>
                                            <?php endforeach; ?>
                                        </select>
                                        <label for="reviewer_by">
                                            <i class="fas fa-user-tie me-2"></i>Reviewed By
                                        </label>
                                    </div>
                                </div>
                                <?php endif; ?>
                            </div>
                        </div>
                        <?php endif; ?>

                        <!-- Sort & Display Tab -->
                        <div class="tab-pane fade" id="sort" role="tabpanel">
                            <div class="row g-4">
                                <div class="col-md-6">
                                    <div class="form-floating">
                                        <select class="form-select" id="sort_by" name="sort_by">
                                            <option value="created_at" <?php echo $search_params['sort_by'] === 'created_at' ? 'selected' : ''; ?>>Date Created</option>
                                            <option value="exno" <?php echo $search_params['sort_by'] === 'exno' ? 'selected' : ''; ?>>Expense Number</option>
                                            <option value="status" <?php echo $search_params['sort_by'] === 'status' ? 'selected' : ''; ?>>Status</option>
                                            <option value="total_amount" <?php echo $search_params['sort_by'] === 'total_amount' ? 'selected' : ''; ?>>Amount</option>
                                            <option value="created_by_name" <?php echo $search_params['sort_by'] === 'created_by_name' ? 'selected' : ''; ?>>Created By</option>
                                        </select>
                                        <label for="sort_by">
                                            <i class="fas fa-sort me-2"></i>Sort By
                                        </label>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="form-floating">
                                        <select class="form-select" id="sort_order" name="sort_order">
                                            <option value="DESC" <?php echo $search_params['sort_order'] === 'DESC' ? 'selected' : ''; ?>>Descending (Newest First)</option>
                                            <option value="ASC" <?php echo $search_params['sort_order'] === 'ASC' ? 'selected' : ''; ?>>Ascending (Oldest First)</option>
                                        </select>
                                        <label for="sort_order">
                                            <i class="fas fa-sort-amount-down me-2"></i>Sort Order
                                        </label>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Action Buttons -->
                    <div class="row mt-4">
                        <div class="col-12 text-center">
                            <button type="submit" class="btn btn-search me-3">
                                <i class="fas fa-search me-2"></i>Search Expenses
                            </button>
                            <button type="button" class="btn btn-clear" onclick="clearForm()">
                                <i class="fas fa-eraser me-2"></i>Clear Filters
                            </button>
                        </div>
                    </div>
                </form>
            </div>
        </div>

        <!-- Search Results -->
        <?php if (!empty($_GET) && array_filter($search_params)): ?>
        <div class="results-container animate-fade-in">
            <div class="results-header">
                <div class="row align-items-center">
                    <div class="col-md-8">
                        <h4 class="mb-0">
                            <i class="fas fa-list-alt me-3"></i>Search Results
                        </h4>
                        <p class="mb-0 opacity-75">Found <?php echo $total_records; ?> expense(s) matching your criteria</p>
                    </div>
                    <div class="col-md-4 text-end">
                        <div class="btn-group" role="group">
                            <a href="../expenses/export_csv.php?<?php echo http_build_query($search_params); ?>"
                               class="btn btn-outline-primary btn-sm">
                                <i class="fas fa-download me-2"></i>Export CSV
                            </a>
                            <a href="?<?php echo http_build_query(array_merge($search_params, ['print' => '1'])); ?>"
                               class="btn btn-outline-secondary btn-sm" target="_blank">
                                <i class="fas fa-print me-2"></i>Print
                            </a>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Results Content -->
            <div class="p-4">
                <?php if (empty($search_results)): ?>
                <div class="text-center py-5">
                    <div class="mb-4">
                        <i class="fas fa-search fa-4x text-muted opacity-50"></i>
                    </div>
                    <h3 class="text-muted mb-3">No Results Found</h3>
                    <p class="text-muted mb-4">Try adjusting your search criteria or clearing some filters.</p>
                    <button type="button" class="btn btn-outline-primary" onclick="clearForm()">
                        <i class="fas fa-eraser me-2"></i>Clear All Filters
                    </button>
                </div>
                <?php else: ?>

                <!-- Statistics Cards -->
                <div class="row mb-4">
                    <div class="col-md-3">
                        <div class="stats-card">
                            <div class="stats-icon" style="background: var(--primary-gradient);">
                                <i class="fas fa-list"></i>
                            </div>
                            <h5 class="mb-1"><?php echo number_format($total_records); ?></h5>
                            <small class="text-muted">Total Results</small>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="stats-card">
                            <div class="stats-icon" style="background: var(--success-gradient);">
                                <i class="fas fa-money-bill-wave"></i>
                            </div>
                            <h5 class="mb-1">฿<?php echo number_format($total_amount ?? 0, 2); ?></h5>
                            <small class="text-muted">Total Amount</small>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="stats-card">
                            <div class="stats-icon" style="background: var(--warning-gradient);">
                                <i class="fas fa-clock"></i>
                            </div>
                            <h5 class="mb-1"><?php echo $pending_count ?? 0; ?></h5>
                            <small class="text-muted">Pending</small>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="stats-card">
                            <div class="stats-icon" style="background: var(--info-gradient);">
                                <i class="fas fa-check-circle"></i>
                            </div>
                            <h5 class="mb-1"><?php echo $approved_count ?? 0; ?></h5>
                            <small class="text-muted">Approved</small>
                        </div>
                    </div>
                </div>

                <!-- Expense Cards -->
                <div class="row">
                    <?php foreach ($search_results as $expense): ?>
                    <div class="col-md-6 col-lg-4 mb-4">
                        <div class="expense-card">
                            <div class="card-body">
                                <div class="d-flex justify-content-between align-items-start mb-3">
                                    <h6 class="mb-0">
                                        <strong><?php echo htmlspecialchars($expense['exno']); ?></strong>
                                    </h6>
                                    <?php echo getStatusBadge($expense['status']); ?>
                                </div>

                                <div class="mb-3">
                                    <div class="d-flex align-items-center mb-2">
                                        <i class="fas fa-money-bill-wave text-success me-2"></i>
                                        <strong class="text-success">฿<?php echo number_format($expense['total_amount'], 2); ?></strong>
                                    </div>
                                    <div class="d-flex align-items-center mb-2">
                                        <i class="fas fa-user text-primary me-2"></i>
                                        <small><?php echo htmlspecialchars($expense['created_by_name']); ?></small>
                                    </div>
                                    <div class="d-flex align-items-center mb-2">
                                        <i class="fas fa-calendar text-info me-2"></i>
                                        <small><?php echo date('M j, Y', strtotime($expense['created_at'])); ?></small>
                                    </div>
                                    <?php if (!empty($expense['receipt_count']) && $expense['receipt_count'] > 0): ?>
                                    <div class="d-flex align-items-center">
                                        <i class="fas fa-paperclip text-warning me-2"></i>
                                        <small><?php echo $expense['receipt_count']; ?> receipt(s)</small>
                                    </div>
                                    <?php endif; ?>
                                </div>

                                <div class="d-grid">
                                    <a href="../expenses/view.php?id=<?php echo $expense['id']; ?>"
                                       class="btn btn-outline-primary btn-sm">
                                        <i class="fas fa-eye me-2"></i>View Details
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                    <?php endforeach; ?>
                </div>

                <!-- Pagination -->
                <?php if ($total_pages > 1): ?>
                <div class="d-flex justify-content-center mt-5">
                    <nav aria-label="Search results pagination">
                        <ul class="pagination">
                            <?php if ($page > 1): ?>
                            <li class="page-item">
                                <a class="page-link" href="?<?php echo http_build_query(array_merge($search_params, ['page' => $page - 1])); ?>">
                                    <i class="fas fa-chevron-left me-1"></i>Previous
                                </a>
                            </li>
                            <?php endif; ?>

                            <?php
                            $start_page = max(1, $page - 2);
                            $end_page = min($total_pages, $page + 2);
                            for ($i = $start_page; $i <= $end_page; $i++):
                            ?>
                            <li class="page-item <?php echo $i === $page ? 'active' : ''; ?>">
                                <a class="page-link" href="?<?php echo http_build_query(array_merge($search_params, ['page' => $i])); ?>">
                                    <?php echo $i; ?>
                                </a>
                            </li>
                            <?php endfor; ?>

                            <?php if ($page < $total_pages): ?>
                            <li class="page-item">
                                <a class="page-link" href="?<?php echo http_build_query(array_merge($search_params, ['page' => $page + 1])); ?>">
                                    Next<i class="fas fa-chevron-right ms-1"></i>
                                </a>
                            </li>
                            <?php endif; ?>
                        </ul>
                    </nav>
                </div>
                <?php endif; ?>
                <?php endif; ?>
            </div>
        </div>
        <?php endif; ?>
    </div>

    <!-- Floating Action Button -->
    <div class="floating-action">
        <button class="floating-btn" onclick="window.scrollTo({top: 0, behavior: 'smooth'})" title="Back to Top">
            <i class="fas fa-arrow-up"></i>
        </button>
    </div>

    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        $(document).ready(function() {
            // Initialize tooltips
            var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
            var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
                return new bootstrap.Tooltip(tooltipTriggerEl);
            });

            // Auto-submit form on quick filter click
            $('.quick-filter-btn').click(function(e) {
                e.preventDefault();
                const status = $(this).attr('href').split('status=')[1] || '';
                $('#status').val(status);
                $('#searchForm').submit();
            });

            // Form validation
            $('#searchForm').submit(function(e) {
                const dateFrom = $('#date_from').val();
                const dateTo = $('#date_to').val();

                if (dateFrom && dateTo && dateFrom > dateTo) {
                    e.preventDefault();
                    alert('Date From cannot be later than Date To');
                    return false;
                }

                const amountFrom = parseFloat($('#amount_from').val()) || 0;
                const amountTo = parseFloat($('#amount_to').val()) || 0;

                if (amountFrom > 0 && amountTo > 0 && amountFrom > amountTo) {
                    e.preventDefault();
                    alert('Amount From cannot be greater than Amount To');
                    return false;
                }
            });
        });

        function clearForm() {
            // Reset form
            document.getElementById('searchForm').reset();

            // Reset all select elements
            const selects = document.querySelectorAll('#searchForm select');
            selects.forEach(select => {
                select.selectedIndex = 0;
            });

            // Clear all inputs
            const inputs = document.querySelectorAll('#searchForm input');
            inputs.forEach(input => {
                if (input.type !== 'hidden') {
                    input.value = '';
                }
            });

            // Redirect to clean URL
            window.location.href = window.location.pathname;
        }

        // Smooth scroll for floating button
        document.querySelector('.floating-btn').addEventListener('click', function() {
            window.scrollTo({
                top: 0,
                behavior: 'smooth'
            });
        });

        // Show/hide floating button based on scroll
        window.addEventListener('scroll', function() {
            const floatingBtn = document.querySelector('.floating-action');
            if (window.pageYOffset > 300) {
                floatingBtn.style.opacity = '1';
                floatingBtn.style.visibility = 'visible';
            } else {
                floatingBtn.style.opacity = '0';
                floatingBtn.style.visibility = 'hidden';
            }
        });
    </script>
</body>
</html>
