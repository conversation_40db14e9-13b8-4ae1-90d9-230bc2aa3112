<?php
/**
 * Check current database structure for issues
 */

session_start();
require_once 'config/database.php';

$_SESSION['user_id'] = 1;
$_SESSION['role'] = 'administrator';

echo "<h1>🔍 Database Structure Check</h1>";

try {
    $database = new Database();
    $db = $database->getConnection();
    
    // Check expenses table structure
    echo "<h2>1. 📋 Expenses Table Structure</h2>";
    
    $stmt = $db->query("DESCRIBE expenses");
    $columns = $stmt->fetchAll();
    
    echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
    echo "<tr><th>Field</th><th>Type</th><th>Null</th><th>Key</th><th>Default</th><th>Extra</th></tr>";
    
    $exno_length = null;
    $workflow_history_type = null;
    
    foreach ($columns as $column) {
        echo "<tr>";
        echo "<td>{$column['Field']}</td>";
        echo "<td>{$column['Type']}</td>";
        echo "<td>{$column['Null']}</td>";
        echo "<td>{$column['Key']}</td>";
        echo "<td>{$column['Default']}</td>";
        echo "<td>{$column['Extra']}</td>";
        echo "</tr>";
        
        if ($column['Field'] === 'exno') {
            $exno_length = $column['Type'];
        }
        if ($column['Field'] === 'workflow_history') {
            $workflow_history_type = $column['Type'];
        }
    }
    echo "</table>";
    
    // Check exno length issue
    echo "<h2>2. 🔍 EXNO Length Analysis</h2>";
    if ($exno_length) {
        echo "<p><strong>Current exno type:</strong> {$exno_length}</p>";
        
        if (strpos($exno_length, 'varchar(15)') !== false) {
            echo "<p>⚠️ exno is VARCHAR(15) - may be too short for some test data</p>";
            
            // Check current exno values
            $stmt = $db->query("SELECT exno, LENGTH(exno) as len FROM expenses ORDER BY LENGTH(exno) DESC LIMIT 10");
            $exnos = $stmt->fetchAll();
            
            echo "<h3>Current EXNO values (longest first):</h3>";
            echo "<ul>";
            foreach ($exnos as $row) {
                $color = $row['len'] > 15 ? 'red' : ($row['len'] > 12 ? 'orange' : 'green');
                echo "<li style='color: {$color};'>{$row['exno']} (length: {$row['len']})</li>";
            }
            echo "</ul>";
        }
    } else {
        echo "<p>❌ exno column not found!</p>";
    }
    
    // Check workflow_history
    echo "<h2>3. 🔍 Workflow History Analysis</h2>";
    if ($workflow_history_type) {
        echo "<p><strong>Current workflow_history type:</strong> {$workflow_history_type}</p>";
        
        if (strpos(strtolower($workflow_history_type), 'json') !== false) {
            echo "<p>⚠️ workflow_history uses JSON type - not compatible with MariaDB 5.5.68</p>";
        } elseif (strpos(strtolower($workflow_history_type), 'text') !== false) {
            echo "<p>✅ workflow_history uses TEXT type - compatible with MariaDB 5.5.68</p>";
        } else {
            echo "<p>❓ workflow_history type: {$workflow_history_type}</p>";
        }
    } else {
        echo "<p>ℹ️ workflow_history column not found (will be added by migration)</p>";
    }
    
    // Check for JSON functions usage
    echo "<h2>4. 🔍 JSON Functions Test</h2>";
    try {
        $stmt = $db->query("SELECT JSON_LENGTH('[1,2,3]') as test");
        $result = $stmt->fetchColumn();
        echo "<p>⚠️ JSON functions are available (result: {$result})</p>";
        echo "<p>📝 This means we're on MySQL/newer MariaDB, but production is MariaDB 5.5.68</p>";
    } catch (Exception $e) {
        echo "<p>✅ JSON functions not available - compatible with MariaDB 5.5.68</p>";
    }
    
    // Check upload directories
    echo "<h2>5. 📁 Upload Directories Check</h2>";
    $upload_dirs = [
        'uploads/receipts',
        'uploads/transfer_slips',
        'uploads/verification_slips',
        'uploads/review_slips',
        'uploads/reviewer_slips', // Old name
        'uploads/batch_documents',
        'uploads/bulk_operations'
    ];
    
    foreach ($upload_dirs as $dir) {
        if (is_dir($dir)) {
            $files = scandir($dir);
            $file_count = count($files) - 2;
            echo "<p>✅ {$dir} exists ({$file_count} files)</p>";
        } else {
            echo "<p>❌ {$dir} missing</p>";
        }
    }
    
    // Suggest fixes
    echo "<h2>6. 🛠️ Suggested Fixes</h2>";
    
    echo "<h3>Fix 1: Extend EXNO Length</h3>";
    echo "<pre>ALTER TABLE expenses MODIFY COLUMN exno VARCHAR(50) NOT NULL;</pre>";
    
    echo "<h3>Fix 2: Add workflow_history as TEXT (if missing)</h3>";
    echo "<pre>ALTER TABLE expenses ADD COLUMN workflow_history TEXT DEFAULT NULL COMMENT 'Workflow history as JSON string';</pre>";
    
    echo "<h3>Fix 3: Create missing upload directories</h3>";
    echo "<pre>";
    echo "mkdir -p uploads/review_slips\n";
    echo "chmod 755 uploads/review_slips\n";
    echo "</pre>";
    
    echo "<h3>Fix 4: Rename reviewer_slips to review_slips (if exists)</h3>";
    if (is_dir('uploads/reviewer_slips') && !is_dir('uploads/review_slips')) {
        echo "<pre>mv uploads/reviewer_slips uploads/review_slips</pre>";
    }
    
    // Auto-fix option
    echo "<h2>7. 🔧 Auto-Fix</h2>";
    echo "<p><a href='?autofix=1' style='background: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>🔧 Apply Auto-Fixes</a></p>";
    
    // Handle auto-fix
    if (isset($_GET['autofix']) && $_GET['autofix'] == '1') {
        echo "<h3>Applying Fixes...</h3>";
        
        try {
            // Fix 1: Extend exno length
            $db->exec("ALTER TABLE expenses MODIFY COLUMN exno VARCHAR(50) NOT NULL");
            echo "<p>✅ Extended exno to VARCHAR(50)</p>";
            
            // Fix 2: Add workflow_history if missing
            try {
                $db->exec("ALTER TABLE expenses ADD COLUMN workflow_history TEXT DEFAULT NULL COMMENT 'Workflow history as JSON string'");
                echo "<p>✅ Added workflow_history column</p>";
            } catch (Exception $e) {
                if (strpos($e->getMessage(), 'Duplicate column') !== false) {
                    echo "<p>ℹ️ workflow_history column already exists</p>";
                } else {
                    echo "<p>⚠️ workflow_history: " . $e->getMessage() . "</p>";
                }
            }
            
            // Fix 3: Create missing directories
            $dirs_to_create = ['uploads/review_slips'];
            foreach ($dirs_to_create as $dir) {
                if (!is_dir($dir)) {
                    mkdir($dir, 0755, true);
                    echo "<p>✅ Created directory: {$dir}</p>";
                } else {
                    echo "<p>ℹ️ Directory already exists: {$dir}</p>";
                }
            }
            
            // Fix 4: Rename reviewer_slips if needed
            if (is_dir('uploads/reviewer_slips') && !is_dir('uploads/review_slips')) {
                rename('uploads/reviewer_slips', 'uploads/review_slips');
                echo "<p>✅ Renamed reviewer_slips to review_slips</p>";
            }
            
            echo "<p><strong>🎉 Auto-fixes completed! <a href='production_readiness_test.php'>Run production test again</a></strong></p>";
            
        } catch (Exception $e) {
            echo "<p>❌ Auto-fix error: " . $e->getMessage() . "</p>";
        }
    }
    
} catch (Exception $e) {
    echo "<p>❌ <strong>Error:</strong> " . $e->getMessage() . "</p>";
}

echo "<p><a href='production_readiness_test.php'>🔙 Back to Production Test</a></p>";
?>
