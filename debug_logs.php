<?php
session_start();
require_once 'config/database.php';
require_once 'includes/functions.php';

// Create database connection
$database = new Database();
$db = $database->getConnection();

// Check if user is logged in
if (!isset($_SESSION['user_id'])) {
    header('Location: login.php');
    exit;
}

$user_id = $_SESSION['user_id'];
$user_role = $_SESSION['role'];

?>
<!DOCTYPE html>
<html lang="th">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Debug Activity Logs</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
</head>
<body class="bg-light">
    <div class="container py-4">
        <div class="row">
            <div class="col-12">
                <div class="card">
                    <div class="card-header bg-primary text-white">
                        <h4 class="mb-0">Debug Activity Logs</h4>
                        <small>User ID: <?php echo $user_id; ?> | Role: <?php echo $user_role; ?></small>
                    </div>
                    <div class="card-body">
                        
                        <!-- Total Logs Count -->
                        <div class="row mb-4">
                            <div class="col-md-4">
                                <div class="card border-info">
                                    <div class="card-body text-center">
                                        <h5 class="text-info">Total Activity Logs</h5>
                                        <?php
                                        try {
                                            $stmt = $db->query("SELECT COUNT(*) as total FROM activity_logs");
                                            $total_logs = $stmt->fetch()['total'];
                                            echo '<h3 class="text-info">' . number_format($total_logs) . '</h3>';
                                        } catch (Exception $e) {
                                            echo '<p class="text-danger">Error: ' . $e->getMessage() . '</p>';
                                        }
                                        ?>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="col-md-4">
                                <div class="card border-warning">
                                    <div class="card-body text-center">
                                        <h5 class="text-warning">My Activity Logs</h5>
                                        <?php
                                        try {
                                            $stmt = $db->prepare("SELECT COUNT(*) as total FROM activity_logs WHERE user_id = ?");
                                            $stmt->execute([$user_id]);
                                            $my_logs = $stmt->fetch()['total'];
                                            echo '<h3 class="text-warning">' . number_format($my_logs) . '</h3>';
                                        } catch (Exception $e) {
                                            echo '<p class="text-danger">Error: ' . $e->getMessage() . '</p>';
                                        }
                                        ?>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="col-md-4">
                                <div class="card border-success">
                                    <div class="card-body text-center">
                                        <h5 class="text-success">Batch Logs</h5>
                                        <?php
                                        try {
                                            $stmt = $db->query("SELECT COUNT(*) as total FROM activity_logs WHERE action_type IN ('batch_verification', 'batch_review')");
                                            $batch_logs = $stmt->fetch()['total'];
                                            echo '<h3 class="text-success">' . number_format($batch_logs) . '</h3>';
                                        } catch (Exception $e) {
                                            echo '<p class="text-danger">Error: ' . $e->getMessage() . '</p>';
                                        }
                                        ?>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Action Types Breakdown -->
                        <div class="card mb-4">
                            <div class="card-header">
                                <h6 class="mb-0">Action Types Breakdown</h6>
                            </div>
                            <div class="card-body">
                                <?php
                                try {
                                    $stmt = $db->query("
                                        SELECT action_type, COUNT(*) as count 
                                        FROM activity_logs 
                                        GROUP BY action_type 
                                        ORDER BY count DESC
                                    ");
                                    $action_types = $stmt->fetchAll();
                                    
                                    if (empty($action_types)) {
                                        echo '<p class="text-muted">No activity logs found</p>';
                                    } else {
                                        echo '<div class="table-responsive">';
                                        echo '<table class="table table-striped">';
                                        echo '<thead><tr><th>Action Type</th><th>Count</th></tr></thead>';
                                        echo '<tbody>';
                                        foreach ($action_types as $type) {
                                            echo '<tr>';
                                            echo '<td><span class="badge bg-secondary">' . htmlspecialchars($type['action_type']) . '</span></td>';
                                            echo '<td>' . number_format($type['count']) . '</td>';
                                            echo '</tr>';
                                        }
                                        echo '</tbody></table>';
                                        echo '</div>';
                                    }
                                } catch (Exception $e) {
                                    echo '<p class="text-danger">Error: ' . $e->getMessage() . '</p>';
                                }
                                ?>
                            </div>
                        </div>

                        <!-- Recent Logs (All) -->
                        <div class="card mb-4">
                            <div class="card-header">
                                <h6 class="mb-0">Recent Activity Logs (Last 10)</h6>
                            </div>
                            <div class="card-body">
                                <?php
                                try {
                                    $stmt = $db->prepare("
                                        SELECT al.*, u.full_name, u.username
                                        FROM activity_logs al
                                        LEFT JOIN users u ON al.user_id = u.id
                                        ORDER BY al.created_at DESC
                                        LIMIT 10
                                    ");
                                    $stmt->execute();
                                    $recent_logs = $stmt->fetchAll();
                                    
                                    if (empty($recent_logs)) {
                                        echo '<p class="text-muted">No recent activity logs found</p>';
                                    } else {
                                        echo '<div class="table-responsive">';
                                        echo '<table class="table table-striped table-sm">';
                                        echo '<thead>';
                                        echo '<tr>';
                                        echo '<th>Date/Time</th>';
                                        echo '<th>User</th>';
                                        echo '<th>Action</th>';
                                        echo '<th>Table</th>';
                                        echo '<th>Record ID</th>';
                                        echo '<th>Description</th>';
                                        echo '</tr>';
                                        echo '</thead>';
                                        echo '<tbody>';
                                        
                                        foreach ($recent_logs as $log) {
                                            echo '<tr>';
                                            echo '<td><small>' . date('d/m/Y H:i:s', strtotime($log['created_at'])) . '</small></td>';
                                            echo '<td>' . htmlspecialchars($log['full_name'] ?: $log['username']) . '</td>';
                                            echo '<td><span class="badge bg-info">' . htmlspecialchars($log['action_type']) . '</span></td>';
                                            echo '<td>' . htmlspecialchars($log['table_name'] ?: '-') . '</td>';
                                            echo '<td>' . ($log['record_id'] ?: '-') . '</td>';
                                            echo '<td><small>' . htmlspecialchars(substr($log['description'], 0, 100)) . '</small></td>';
                                            echo '</tr>';
                                        }
                                        
                                        echo '</tbody>';
                                        echo '</table>';
                                        echo '</div>';
                                    }
                                } catch (Exception $e) {
                                    echo '<p class="text-danger">Error: ' . $e->getMessage() . '</p>';
                                }
                                ?>
                            </div>
                        </div>

                        <!-- My Recent Logs -->
                        <div class="card mb-4">
                            <div class="card-header">
                                <h6 class="mb-0">My Recent Activity Logs (Last 10)</h6>
                            </div>
                            <div class="card-body">
                                <?php
                                try {
                                    $stmt = $db->prepare("
                                        SELECT al.*, e.exno 
                                        FROM activity_logs al 
                                        LEFT JOIN expenses e ON al.table_name = 'expenses' AND al.record_id = e.id
                                        WHERE al.user_id = ? 
                                        ORDER BY al.created_at DESC 
                                        LIMIT 10
                                    ");
                                    $stmt->execute([$user_id]);
                                    $my_recent_logs = $stmt->fetchAll();
                                    
                                    if (empty($my_recent_logs)) {
                                        echo '<div class="alert alert-warning">';
                                        echo '<i class="fas fa-exclamation-triangle me-2"></i>';
                                        echo '<strong>No activity logs found for your user ID: ' . $user_id . '</strong><br>';
                                        echo 'This could mean:<br>';
                                        echo '• You haven\'t performed any logged actions yet<br>';
                                        echo '• The logging system is not working<br>';
                                        echo '• Your user_id is not being recorded correctly';
                                        echo '</div>';
                                    } else {
                                        echo '<div class="table-responsive">';
                                        echo '<table class="table table-striped table-sm">';
                                        echo '<thead>';
                                        echo '<tr>';
                                        echo '<th>Date/Time</th>';
                                        echo '<th>Action</th>';
                                        echo '<th>Table</th>';
                                        echo '<th>Record ID</th>';
                                        echo '<th>Expense No</th>';
                                        echo '<th>Description</th>';
                                        echo '</tr>';
                                        echo '</thead>';
                                        echo '<tbody>';
                                        
                                        foreach ($my_recent_logs as $log) {
                                            echo '<tr>';
                                            echo '<td><small>' . date('d/m/Y H:i:s', strtotime($log['created_at'])) . '</small></td>';
                                            echo '<td><span class="badge bg-primary">' . htmlspecialchars($log['action_type']) . '</span></td>';
                                            echo '<td>' . htmlspecialchars($log['table_name'] ?: '-') . '</td>';
                                            echo '<td>' . ($log['record_id'] ?: '-') . '</td>';
                                            echo '<td>' . htmlspecialchars($log['exno'] ?: '-') . '</td>';
                                            echo '<td><small>' . htmlspecialchars(substr($log['description'], 0, 100)) . '</small></td>';
                                            echo '</tr>';
                                        }
                                        
                                        echo '</tbody>';
                                        echo '</table>';
                                        echo '</div>';
                                    }
                                } catch (Exception $e) {
                                    echo '<p class="text-danger">Error: ' . $e->getMessage() . '</p>';
                                }
                                ?>
                            </div>
                        </div>

                        <!-- Test Logging Function -->
                        <div class="card">
                            <div class="card-header">
                                <h6 class="mb-0">Test Logging Function</h6>
                            </div>
                            <div class="card-body">
                                <?php
                                if (isset($_POST['test_log'])) {
                                    try {
                                        require_once 'includes/functions.php';
                                        
                                        logActivity(
                                            $db,
                                            $user_id,
                                            'test',
                                            'debug',
                                            null,
                                            'Test log entry from debug page',
                                            $_SERVER['REMOTE_ADDR'],
                                            $_SERVER['HTTP_USER_AGENT']
                                        );
                                        
                                        echo '<div class="alert alert-success">';
                                        echo '<i class="fas fa-check me-2"></i>';
                                        echo 'Test log entry created successfully! Refresh page to see it.';
                                        echo '</div>';
                                    } catch (Exception $e) {
                                        echo '<div class="alert alert-danger">';
                                        echo '<i class="fas fa-times me-2"></i>';
                                        echo 'Error creating test log: ' . $e->getMessage();
                                        echo '</div>';
                                    }
                                }
                                ?>
                                
                                <form method="POST">
                                    <button type="submit" name="test_log" class="btn btn-primary">
                                        <i class="fas fa-plus me-1"></i>Create Test Log Entry
                                    </button>
                                </form>
                            </div>
                        </div>

                        <!-- Navigation -->
                        <div class="mt-4 text-center">
                            <a href="dashboard.php" class="btn btn-secondary me-2">
                                <i class="fas fa-arrow-left me-1"></i>Back to Dashboard
                            </a>
                            <a href="test_batch_logging.php" class="btn btn-info me-2">
                                <i class="fas fa-clipboard-list me-1"></i>Test Batch Logging
                            </a>
                            <a href="reports/activity_logs.php" class="btn btn-success">
                                <i class="fas fa-list me-1"></i>View Activity Reports
                            </a>
                        </div>

                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
