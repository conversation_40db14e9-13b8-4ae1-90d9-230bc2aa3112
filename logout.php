<?php
session_start();
require_once 'config/database.php';
require_once 'includes/functions.php';

// Log the logout activity if user is logged in
if (isset($_SESSION['user_id'])) {
    try {
        $database = new Database();
        $db = $database->getConnection();
        
        logActivity(
            $db, 
            $_SESSION['user_id'], 
            'logout', 
            null, 
            null, 
            'User logged out', 
            $_SERVER['REMOTE_ADDR'], 
            $_SERVER['HTTP_USER_AGENT']
        );
    } catch (Exception $e) {
        error_log('Logout logging error: ' . $e->getMessage());
    }
}

// Destroy session
session_destroy();

// Redirect to login page
header('Location: login.php');
exit();
?>
