<?php
session_start();

// Simulate logged in user for testing
if (!isset($_SESSION['user_id'])) {
    $_SESSION['user_id'] = 1;
    $_SESSION['role'] = 'administrator';
    $_SESSION['username'] = 'test_user';
}

echo "<h2>Test Fixed Path</h2>";

$test_file = $_GET['file'] ?? 'transfer_68fcafb1efdee_1761390513.jpg';

echo "<h3>Testing: $test_file</h3>";

// Test old path (current production)
echo "<h3>Old Path Test:</h3>";
$old_upload_dir = '../uploads/';
$old_file_path = $old_upload_dir . 'transfer_slips/' . $test_file;
echo "<ul>";
echo "<li><strong>Old path:</strong> $old_file_path</li>";
echo "<li><strong>File exists:</strong> " . (file_exists($old_file_path) ? '✅ YES' : '❌ NO') . "</li>";
echo "<li><strong>Absolute path:</strong> " . realpath($old_file_path) . "</li>";
echo "</ul>";

// Test new path (fixed)
echo "<h3>New Path Test:</h3>";
$new_upload_dir = dirname(__DIR__) . '/uploads/';
$new_file_path = $new_upload_dir . 'transfer_slips/' . $test_file;
echo "<ul>";
echo "<li><strong>New path:</strong> $new_file_path</li>";
echo "<li><strong>File exists:</strong> " . (file_exists($new_file_path) ? '✅ YES' : '❌ NO') . "</li>";
echo "<li><strong>Absolute path:</strong> " . realpath($new_file_path) . "</li>";
echo "</ul>";

// Show directory structure
echo "<h3>Directory Structure:</h3>";
echo "<ul>";
echo "<li><strong>Current file location:</strong> " . __FILE__ . "</li>";
echo "<li><strong>__DIR__:</strong> " . __DIR__ . "</li>";
echo "<li><strong>dirname(__DIR__):</strong> " . dirname(__DIR__) . "</li>";
echo "<li><strong>getcwd():</strong> " . getcwd() . "</li>";
echo "</ul>";

// Test both paths with different files
echo "<h3>Test Multiple Files:</h3>";
$test_files = [
    'transfer_68fcafb1efdee_1761390513.jpg',
    'transfer_68fb856aef8b7_1761314154.jpg',
    'transfer_68fb847fcc6db_1761313919.jpg'
];

echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
echo "<tr><th>Filename</th><th>Old Path</th><th>New Path</th><th>Recommendation</th></tr>";

foreach ($test_files as $filename) {
    $old_path = $old_upload_dir . 'transfer_slips/' . $filename;
    $new_path = $new_upload_dir . 'transfer_slips/' . $filename;
    
    $old_exists = file_exists($old_path);
    $new_exists = file_exists($new_path);
    
    echo "<tr>";
    echo "<td>$filename</td>";
    echo "<td style='color: " . ($old_exists ? 'green' : 'red') . "'>" . ($old_exists ? '✅ EXISTS' : '❌ NOT FOUND') . "</td>";
    echo "<td style='color: " . ($new_exists ? 'green' : 'red') . "'>" . ($new_exists ? '✅ EXISTS' : '❌ NOT FOUND') . "</td>";
    
    if ($new_exists && !$old_exists) {
        echo "<td style='color: green;'>✅ Use NEW path</td>";
    } elseif ($old_exists && !$new_exists) {
        echo "<td style='color: orange;'>⚠️ Use OLD path</td>";
    } elseif ($old_exists && $new_exists) {
        echo "<td style='color: blue;'>ℹ️ Both work</td>";
    } else {
        echo "<td style='color: red;'>❌ File missing</td>";
    }
    echo "</tr>";
}
echo "</table>";

// Create a working view_file.php test
echo "<h3>Test Working View File:</h3>";

if (isset($_GET['test_view']) && $_GET['test_view'] === 'yes') {
    $file = basename($test_file);
    
    // Use the path that works
    $upload_dir = dirname(__DIR__) . '/uploads/';
    $file_path = $upload_dir . 'transfer_slips/' . $file;
    
    if (file_exists($file_path)) {
        // Get file info
        $file_info = pathinfo($file_path);
        $file_extension = strtolower($file_info['extension']);
        
        // Set appropriate content type
        $content_types = [
            'jpg' => 'image/jpeg',
            'jpeg' => 'image/jpeg',
            'png' => 'image/png',
            'gif' => 'image/gif',
            'pdf' => 'application/pdf'
        ];
        
        $content_type = $content_types[$file_extension] ?? 'application/octet-stream';
        
        // Set headers and output file
        header('Content-Type: ' . $content_type);
        header('Content-Length: ' . filesize($file_path));
        header('Cache-Control: private, max-age=3600');
        
        readfile($file_path);
        exit;
    } else {
        header('HTTP/1.0 404 Not Found');
        exit('File not found: ' . basename($file_path));
    }
}

// Test links
echo "<p><strong>Test the working view file:</strong></p>";
echo "<p><a href='?file=$test_file&test_view=yes' target='_blank'>Test View File (New Path)</a></p>";

// Show the exact fix needed
echo "<h3>Fix for Production Server:</h3>";
echo "<div style='background: #f8f9fa; border: 1px solid #dee2e6; padding: 15px; border-radius: 5px;'>";
echo "<p><strong>In api/view_file.php, change line 40:</strong></p>";
echo "<p style='background: #f8d7da; padding: 10px; border-radius: 3px;'>";
echo "<code>❌ \$upload_dir = '../uploads/';</code>";
echo "</p>";
echo "<p style='background: #d4edda; padding: 10px; border-radius: 3px;'>";
echo "<code>✅ \$upload_dir = dirname(__DIR__) . '/uploads/';</code>";
echo "</p>";
echo "</div>";

// Test form
echo "<h3>Test Different File:</h3>";
echo "<form method='GET' style='background: #f9f9f9; padding: 15px; border: 1px solid #ddd;'>";
echo "<label>Filename: <input type='text' name='file' value='$test_file' style='width: 400px;'></label><br><br>";
echo "<input type='submit' value='Test Path' style='padding: 10px 20px;'>";
echo "</form>";
?>
