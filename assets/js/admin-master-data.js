// Admin Master Data Management JavaScript

// Generic functions for all master data types (items, customers, drivers)
function editRecord(record, type) {
    const modalId = `edit${type.charAt(0).toUpperCase() + type.slice(1)}Modal`;

    document.getElementById(`edit_id`).value = record.id;
    document.getElementById(`edit_name`).value = record.name;

    // Handle different field types based on record type
    if (type === 'customer') {
        document.getElementById(`edit_contact_person`).value = record.contact_person || '';
        document.getElementById(`edit_phone`).value = record.phone || '';
        document.getElementById(`edit_email`).value = record.email || '';
        document.getElementById(`edit_address`).value = record.address || '';
    } else if (type === 'driver') {
        document.getElementById(`edit_license_number`).value = record.license_number || '';
        document.getElementById(`edit_phone`).value = record.phone || '';
        document.getElementById(`edit_vehicle_plate`).value = record.vehicle_plate || '';
        document.getElementById(`edit_payment_account_no`).value = record.payment_account_no || '';
    } else {
        // For items
        document.getElementById(`edit_description`).value = record.description || '';
    }

    document.getElementById(`edit_is_active`).checked = record.is_active == 1;

    new bootstrap.Modal(document.getElementById(modalId)).show();
}

function deleteRecord(id, name, type) {
    document.getElementById('delete_id').value = id;
    document.getElementById('delete_name').textContent = name;
    
    new bootstrap.Modal(document.getElementById('deleteModal')).show();
}

function exportCSV(type) {
    const table = document.querySelector('.table');
    const rows = table.querySelectorAll('tr');
    let csv = [];
    
    // Headers
    const headers = ['ID', 'Name', 'Description', 'Status', 'Usage Count', 'Created By', 'Created Date'];
    csv.push(headers.join(','));
    
    // Data rows
    for (let i = 1; i < rows.length; i++) {
        const row = rows[i];
        const cols = row.querySelectorAll('td');
        if (cols.length > 0) {
            const rowData = [
                cols[0].textContent.trim(), // ID
                '"' + cols[1].textContent.trim().replace(/"/g, '""') + '"', // Name
                '"' + cols[2].textContent.trim().replace(/"/g, '""') + '"', // Description
                cols[3].textContent.trim(), // Status
                cols[4].textContent.trim(), // Usage
                '"' + cols[5].textContent.trim().replace(/"/g, '""') + '"', // Created By
                cols[6].textContent.trim() // Created Date
            ];
            csv.push(rowData.join(','));
        }
    }
    
    // Download
    const csvContent = csv.join('\n');
    const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
    const link = document.createElement('a');
    const url = URL.createObjectURL(blob);
    link.setAttribute('href', url);
    link.setAttribute('download', `${type}_` + new Date().toISOString().split('T')[0] + '.csv');
    link.style.visibility = 'hidden';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
}

function downloadSampleCSV(type) {
    let sampleData = [];
    
    switch(type) {
        case 'items':
            sampleData = [
                ['Name', 'Description'],
                ['Container Transport', 'Transportation of containers'],
                ['Fuel Cost', 'Fuel expenses for vehicles'],
                ['Maintenance', 'Vehicle maintenance costs']
            ];
            break;
        case 'customers':
            sampleData = [
                ['Name', 'Contact Person', 'Phone', 'Email', 'Address'],
                ['ABC Company Ltd.', 'John Smith', '+66-2-123-4567', '<EMAIL>', '123 Business District, Bangkok'],
                ['XYZ Corporation', 'Jane Doe', '+66-2-234-5678', '<EMAIL>', '456 Industrial Zone, Bangkok'],
                ['DEF Trading', 'Mike Johnson', '+66-2-345-6789', '<EMAIL>', '789 Trade Center, Bangkok']
            ];
            break;
        case 'drivers':
            sampleData = [
                ['Name', 'License Number', 'Phone', 'Vehicle Plate', 'Payment Account No.'],
                ['John Smith', 'DL123456789', '+66-81-234-5678', 'ABC-1234', '**********'],
                ['Mike Johnson', 'DL987654321', '+66-82-345-6789', 'XYZ-5678', '**********'],
                ['David Brown', 'DL456789123', '+66-83-456-7890', 'DEF-9012', '**********']
            ];
            break;
    }
    
    const csv = sampleData.map(row => 
        row.map(cell => '"' + cell.replace(/"/g, '""') + '"').join(',')
    ).join('\n');
    
    const blob = new Blob([csv], { type: 'text/csv;charset=utf-8;' });
    const link = document.createElement('a');
    const url = URL.createObjectURL(blob);
    link.setAttribute('href', url);
    link.setAttribute('download', `${type}_sample.csv`);
    link.style.visibility = 'hidden';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
}

// Specific functions for each type
function editItem(item) {
    editRecord(item, 'item');
}

function deleteItem(id, name) {
    deleteRecord(id, name, 'item');
}

function editCustomer(customer) {
    editRecord(customer, 'customer');
}

function deleteCustomer(id, name) {
    deleteRecord(id, name, 'customer');
}

function editDriver(driver) {
    editRecord(driver, 'driver');
}

function deleteDriver(id, name) {
    deleteRecord(id, name, 'driver');
}

// Initialize when document is ready
document.addEventListener('DOMContentLoaded', function() {
    // Clear forms when modals are hidden
    const addModals = ['addItemModal', 'addCustomerModal', 'addDriverModal'];
    addModals.forEach(modalId => {
        const modal = document.getElementById(modalId);
        if (modal) {
            modal.addEventListener('hidden.bs.modal', function () {
                this.querySelector('form').reset();
            });
        }
    });
    
    // Auto-focus on name field when add modals are shown
    addModals.forEach(modalId => {
        const modal = document.getElementById(modalId);
        if (modal) {
            modal.addEventListener('shown.bs.modal', function () {
                const nameField = this.querySelector('input[name="name"]');
                if (nameField) {
                    nameField.focus();
                }
            });
        }
    });
});

// Search functionality
function performSearch() {
    const form = document.querySelector('form[method="GET"]');
    if (form) {
        form.submit();
    }
}

// Reset search
function resetSearch() {
    const searchInput = document.querySelector('input[name="search"]');
    const statusSelect = document.querySelector('select[name="status"]');
    
    if (searchInput) searchInput.value = '';
    if (statusSelect) statusSelect.value = '';
    
    performSearch();
}

// Keyboard shortcuts
document.addEventListener('keydown', function(e) {
    // Ctrl/Cmd + K for search
    if ((e.ctrlKey || e.metaKey) && e.key === 'k') {
        e.preventDefault();
        const searchInput = document.querySelector('input[name="search"]');
        if (searchInput) {
            searchInput.focus();
            searchInput.select();
        }
    }
    
    // Escape to close modals
    if (e.key === 'Escape') {
        const openModals = document.querySelectorAll('.modal.show');
        openModals.forEach(modal => {
            const bsModal = bootstrap.Modal.getInstance(modal);
            if (bsModal) {
                bsModal.hide();
            }
        });
    }
});

// Confirmation dialogs for dangerous actions
function confirmDelete(callback) {
    if (confirm('Are you sure you want to delete this record? This action cannot be undone.')) {
        callback();
    }
}

// Bulk operations (future enhancement)
function selectAll() {
    const checkboxes = document.querySelectorAll('input[type="checkbox"][name="selected[]"]');
    const selectAllCheckbox = document.querySelector('#selectAll');
    
    checkboxes.forEach(checkbox => {
        checkbox.checked = selectAllCheckbox.checked;
    });
    
    updateBulkActions();
}

function updateBulkActions() {
    const selectedCheckboxes = document.querySelectorAll('input[type="checkbox"][name="selected[]"]:checked');
    const bulkActionsDiv = document.querySelector('#bulkActions');
    
    if (bulkActionsDiv) {
        if (selectedCheckboxes.length > 0) {
            bulkActionsDiv.style.display = 'block';
            bulkActionsDiv.querySelector('.selected-count').textContent = selectedCheckboxes.length;
        } else {
            bulkActionsDiv.style.display = 'none';
        }
    }
}

// Utility functions
function formatDate(dateString) {
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', {
        year: 'numeric',
        month: 'short',
        day: 'numeric'
    });
}

function formatNumber(number) {
    return new Intl.NumberFormat().format(number);
}

function showToast(message, type = 'success') {
    // Create toast element
    const toast = document.createElement('div');
    toast.className = `toast align-items-center text-white bg-${type} border-0`;
    toast.setAttribute('role', 'alert');
    toast.innerHTML = `
        <div class="d-flex">
            <div class="toast-body">
                ${message}
            </div>
            <button type="button" class="btn-close btn-close-white me-2 m-auto" data-bs-dismiss="toast"></button>
        </div>
    `;
    
    // Add to toast container
    let toastContainer = document.querySelector('.toast-container');
    if (!toastContainer) {
        toastContainer = document.createElement('div');
        toastContainer.className = 'toast-container position-fixed top-0 end-0 p-3';
        document.body.appendChild(toastContainer);
    }
    
    toastContainer.appendChild(toast);
    
    // Show toast
    const bsToast = new bootstrap.Toast(toast);
    bsToast.show();
    
    // Remove from DOM after hiding
    toast.addEventListener('hidden.bs.toast', function() {
        toast.remove();
    });
}
