// Expense Form JavaScript

// Helper function to get correct API URL based on current page location
function getApiUrl(filename) {
    const currentPath = window.location.pathname;

    // If we're in a subdirectory (like expenses/), use ../api/
    if (currentPath.includes('/expenses/') || currentPath.includes('/multi_')) {
        return '../api/' + filename;
    }
    // If we're in the root directory, use api/
    else {
        return 'api/' + filename;
    }
}

// Reset Form Function
function resetForm() {
    if (confirm('คุณแน่ใจหรือไม่ที่จะล้างข้อมูลทั้งหมด? การกระทำนี้ไม่สามารถยกเลิกได้')) {
        // Reset the main form
        $('.expense-form')[0].reset();

        // Clear all text inputs
        $('input[type="text"], input[type="date"], input[type="number"], textarea').val('');

        // Reset all select dropdowns to first option
        $('select').prop('selectedIndex', 0);

        // Clear file inputs
        $('input[type="file"]').val('');

        // Reset datalist inputs
        $('#item_input, #customer_input, #driver_input').val('');
        $('#item_id, #customer_id, #driver_id').val('');
        $('#new_item, #new_customer').val('');

        // Clear receipt numbers table
        $('#receipt-numbers-table tbody').empty();
        if (typeof updateReceiptTotal === 'function') {
            updateReceiptTotal();
        }

        // Reset any validation states
        $('.is-invalid').removeClass('is-invalid');
        $('.invalid-feedback').remove();

        // Clear localStorage saved data
        localStorage.removeItem('expense_form_data');

        // Reset job_open_date to today
        $('#job_open_date').val(new Date().toISOString().split('T')[0]);

        // Clear any preview images
        $('.image-preview').empty();

        // Reset transfer amount
        $('#transfer_amount').val('');

        // Clear receipt cards container and deductions data
        $('#receipt-numbers-container').addClass('d-none');
        $('#receipt-numbers').empty();

        // Clear deductions data
        window.receiptDeductions = {};
        window.receiptFiles = [];
        window.currentReceiptIndex = -1;
        window.currentDeductionIndex = -1;

        // Clear deductions summary container
        $('#deductions-summary-container').addClass('d-none');

        // Reset all amount displays
        $('#total-amount').text('0.00');
        $('#display-receipt-total').text('0.00');
        $('#display-transfer-amount').text('0.00');
        $('#total-gross-amount').text('0.00');
        $('#total-deductions-amount').text('0.00');
        $('#total-net-amount').text('0.00');

        // Clear amount comparison
        $('#amount-comparison').html('<small class="text-muted">Enter amounts to compare</small>');

        // Clear deduction modal data
        $('#deduction-form')[0].reset();
        $('#deduction-form').removeData('mode').removeData('index');
        $('#image-upload-status').empty();
        $('#deduction_image').removeData('uploaded-filename').removeData('uploaded-url');
        $('#deduction_image_preview').hide();

        // Clear any deduction form errors
        $('#deduction-form-errors').empty();

        // Show success message
        showAlert('ฟอร์มถูกล้างข้อมูลเรียบร้อยแล้ว!', 'success');

        // Scroll to top
        $('html, body').animate({scrollTop: 0}, 500);
    }
}

// Show alert function
function showAlert(message, type = 'info') {
    const alertHtml = `
        <div class="alert alert-${type} alert-dismissible fade show" role="alert">
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    `;

    // Remove existing alerts
    $('.alert').remove();

    // Add new alert at the top of the form
    $('.expense-form').prepend(alertHtml);

    // Auto-dismiss after 3 seconds
    setTimeout(function() {
        $('.alert').fadeOut();
    }, 3000);
}

$(document).ready(function() {
    console.log('Document ready - expense-form.js loaded');
    console.log('jQuery version:', $.fn.jquery);

    // Debounce timer for receipt number validation
    let receiptValidationTimer;

    // Event delegation for receipt number validation (for dynamically created inputs)
    $(document).on('input blur', '.receipt-number-input', function() {
        // Receipt number input event triggered

        // Clear previous timer
        clearTimeout(receiptValidationTimer);

        // Set new timer to debounce validation
        receiptValidationTimer = setTimeout(function() {
            validateReceiptNumbers();
        }, 300); // Wait 300ms after last input
    });

    // Event listener for transfer number validation
    $('#transfer_no').on('input blur', function() {
        // Transfer number input event triggered
        validateTransferNumber();
    });

    // Driver selection change handler
    $('#driver_id').change(function() {
        const selectedOption = $(this).find('option:selected');
        const vehiclePlate = selectedOption.data('vehicle') || '';
        const accountNo = selectedOption.data('account') || '';
        const driverName = selectedOption.data('name') || '';
        
        $('#vehicle_plate').val(vehiclePlate);
        $('#payment_account_no').val(accountNo);
        $('#requester').val(driverName);
        $('#receiver').val(driverName);
    });
    
    // Item selection handler - show/hide new item input
    $('#item_id').change(function() {
        if ($(this).val() === 'new') {
            $('#new_item').removeClass('d-none').focus();
            $(this).addClass('d-none');
        }
    });
    
    // Customer selection handler - show/hide new customer input
    $('#customer_id').change(function() {
        if ($(this).val() === 'new') {
            $('#new_customer').removeClass('d-none').focus();
            $(this).addClass('d-none');
        }
    });
    
    // Add "Add New" options to dropdowns
    $('#item_id').append('<option value="new">+ Add New Item</option>');
    $('#customer_id').append('<option value="new">+ Add New Customer</option>');
    
    // Handle new item input
    $('#new_item').on('blur', function() {
        const newItemName = $(this).val().trim();
        if (newItemName) {
            // Clear the item_id selection since we're using new_item
            $('#item_id').val('');
            // Keep the input visible and filled
            $(this).removeClass('d-none');
        } else {
            $(this).addClass('d-none');
            $('#item_id').removeClass('d-none');
        }
    });

    // Handle new customer input
    $('#new_customer').on('blur', function() {
        const newCustomerName = $(this).val().trim();
        if (newCustomerName) {
            // Clear the customer_id selection since we're using new_customer
            $('#customer_id').val('');
            // Keep the input visible and filled
            $(this).removeClass('d-none');
        } else {
            $(this).addClass('d-none');
            $('#customer_id').removeClass('d-none');
        }
    });
    
    // Handle receipt file selection
    $('#receipts').change(function() {
        console.log('Receipt files changed, files count:', this.files.length);
        const files = this.files;
        const container = $('#receipt-numbers-container');
        const numbersDiv = $('#receipt-numbers');

        if (files.length > 0) {
            console.log('Creating receipt number inputs for', files.length, 'files');
            container.removeClass('d-none');
            numbersDiv.empty();

            for (let i = 0; i < files.length; i++) {
                const file = files[i];
                const inputGroup = $(`
                    <div class="card mb-3 receipt-card" data-receipt-index="${i}">
                        <div class="card-header">
                            <div class="row align-items-center">
                                <div class="col">
                                    <h6 class="mb-0"><i class="fas fa-receipt"></i> ${file.name}</h6>
                                </div>
                                <div class="col-auto">
                                    <div class="form-check">
                                        <input class="form-check-input has-deductions-checkbox"
                                               type="checkbox" id="hasDeductions${i}" data-receipt-index="${i}">
                                        <label class="form-check-label" for="hasDeductions${i}" title="มีรายการหัก">
                                            <i class="fas fa-minus-circle text-warning"></i>
                                            <small class="ms-1">มีรายการหัก</small>
                                        </label>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-4">
                                    <label class="form-label">Receipt Number *</label>
                                    <input type="text" class="form-control receipt-number-input" name="receipt_numbers[]"
                                           placeholder="เลขที่ใบเสร็จ" required>
                                    <div class="invalid-feedback"></div>
                                    <div class="valid-feedback"></div>
                                </div>
                                <div class="col-md-4">
                                    <label class="form-label">Amount (บาท) *</label>
                                    <input type="number" class="form-control receipt-amount" name="receipt_amounts[]"
                                           placeholder="0.00" step="0.01" min="0" required>
                                </div>
                                <div class="col-md-4">
                                    <label class="form-label">Description</label>
                                    <input type="text" class="form-control" name="receipt_descriptions[]"
                                           list="item_list" placeholder="เลือกจาก Item หรือพิมพ์รายละเอียด..."
                                           autocomplete="off">
                                </div>
                            </div>

                            <!-- Deductions Section (แสดงเมื่อ checkbox ถูกเลือก) -->
                            <div class="deductions-section d-none mt-3" id="deductionsSection${i}">
                                <div class="card border-warning">
                                    <div class="card-header bg-warning bg-opacity-10">
                                        <h6 class="mb-0">
                                            <i class="fas fa-minus-circle text-warning"></i>
                                            รายการหักสำหรับใบเสร็จนี้
                                        </h6>
                                    </div>
                                    <div class="card-body">
                                        <!-- Deductions List -->
                                        <div class="deductions-list" id="deductionsList${i}">
                                            <div class="no-deductions-message text-muted text-center py-2">
                                                <i class="fas fa-info-circle"></i> ยังไม่มีรายการหัก
                                            </div>
                                        </div>

                                        <!-- Add Deduction Button -->
                                        <button type="button" class="btn btn-outline-warning btn-sm add-deduction-btn"
                                                data-receipt-index="${i}">
                                            <i class="fas fa-plus"></i> เพิ่มรายการหัก
                                        </button>

                                        <!-- Net Amount Display -->
                                        <div class="net-amount-display mt-2">
                                            <small class="text-muted">ยอดสุทธิ: </small>
                                            <span class="net-amount fw-bold text-success" id="netAmount${i}">0.00 บาท</span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                `);
                numbersDiv.append(inputGroup);
            }

            console.log('Receipt number inputs created. Total inputs:', $('.receipt-number-input').length);

            // Add event listeners for amount calculation
            $('.receipt-amount').on('input', calculateTotal);

            // Event listeners are handled by event delegation in document ready

            calculateTotal();

            // Initialize checkbox event handlers for new receipts
            initializeCheckboxHandlers();
        } else {
            container.addClass('d-none');

            // Hide receipt cards container when no receipts
            if (typeof hideReceiptCardsContainer === 'function') {
                hideReceiptCardsContainer();
            }
        }
    });

    // Validate receipt numbers for duplicates
    function validateReceiptNumbers() {
        const receiptNumbers = [];
        const inputs = $('.receipt-number-input');
        let hasDuplicates = false;

        // Clear previous validation states
        inputs.removeClass('is-invalid').siblings('.invalid-feedback').text('');

        inputs.each(function() {
            const value = $(this).val().trim();
            const input = $(this);

            if (value) {
                // Check if this receipt number already exists in the form
                const duplicateIndex = receiptNumbers.indexOf(value);
                if (duplicateIndex !== -1) {
                    // Mark both inputs as invalid
                    input.addClass('is-invalid');
                    input.siblings('.invalid-feedback').text('Receipt number already used in this form');

                    // Also mark the first occurrence
                    inputs.eq(duplicateIndex).addClass('is-invalid');
                    inputs.eq(duplicateIndex).siblings('.invalid-feedback').text('Receipt number already used in this form');

                    hasDuplicates = true;
                } else {
                    // Check if receipt number exists in database
                    checkReceiptNumberInDatabase(value, input);
                }

                receiptNumbers.push(value);
            }
        });

        // Update submit button state
        updateSubmitButtonState();

        return !hasDuplicates;
    }

    // Check if receipt number exists in database
    function checkReceiptNumberInDatabase(receiptNumber, inputElement) {
        // Add loading indicator
        inputElement.addClass('receipt-number-checking');

        // Clear previous timeout if exists
        const timeoutId = inputElement.data('timeout-id');
        if (timeoutId) {
            clearTimeout(timeoutId);
        }

        // Debounce the API call
        const newTimeoutId = setTimeout(function() {
            $.ajax({
                url: getApiUrl('check_receipt_number.php'),
                method: 'POST',
                data: { receipt_number: receiptNumber },
                dataType: 'json',
                success: function(response) {
                    inputElement.removeClass('receipt-number-checking');

                    if (response.exists) {
                        inputElement.addClass('is-invalid').removeClass('is-valid');
                        inputElement.siblings('.invalid-feedback').text(`Receipt number already exists in expense: ${response.expense_no}`);
                    } else {
                        inputElement.removeClass('is-invalid').addClass('is-valid');
                        inputElement.siblings('.invalid-feedback').text('');
                        inputElement.siblings('.valid-feedback').text('Receipt number is available');
                    }
                    updateSubmitButtonState();
                },
                error: function(xhr, status, error) {
                    inputElement.removeClass('receipt-number-checking');
                    console.error('Error checking receipt number in database:', error);

                    // Show error but don't block submission
                    inputElement.removeClass('is-invalid is-valid');
                    inputElement.siblings('.invalid-feedback').text('Unable to verify receipt number. Please check manually.');
                    updateSubmitButtonState();
                }
            });
        }, 500); // 500ms debounce

        inputElement.data('timeout-id', newTimeoutId);
    }

    // Validate transfer number for duplicates
    function validateTransferNumber() {
        // validateTransferNumber called
        const transferInput = $('#transfer_no');
        const transferNumber = transferInput.val().trim();

        // Clear previous validation states
        transferInput.removeClass('is-invalid is-valid transfer-number-checking');
        transferInput.siblings('.invalid-feedback, .valid-feedback').text('');

        if (transferNumber) {
            // Check if transfer number exists in database
            checkTransferNumberInDatabase(transferNumber, transferInput);
        }

        updateSubmitButtonState();
    }

    // Check if transfer number exists in database
    function checkTransferNumberInDatabase(transferNumber, inputElement) {
        // Checking transfer number in database

        // Add loading indicator
        inputElement.addClass('transfer-number-checking');

        // Clear previous timeout if exists
        const timeoutId = inputElement.data('timeout-id');
        if (timeoutId) {
            clearTimeout(timeoutId);
        }

        // Debounce the API call
        const newTimeoutId = setTimeout(function() {
            $.ajax({
                url: getApiUrl('check_transfer_number.php'),
                method: 'POST',
                data: { transfer_no: transferNumber },
                dataType: 'json',
                success: function(response) {
                    inputElement.removeClass('transfer-number-checking');
                    console.log('Transfer API response for', transferNumber, ':', response);

                    if (response.exists) {
                        inputElement.addClass('is-invalid').removeClass('is-valid');
                        inputElement.siblings('.invalid-feedback').text(`Transfer number already exists in expense: ${response.expense_no}`);
                        console.log('Transfer number exists:', transferNumber, 'in expense:', response.expense_no);
                    } else {
                        inputElement.removeClass('is-invalid').addClass('is-valid');
                        inputElement.siblings('.invalid-feedback').text('');
                        inputElement.siblings('.valid-feedback').text('Transfer number is available');
                        console.log('Transfer number available:', transferNumber);
                    }
                    updateSubmitButtonState();
                },
                error: function(xhr, status, error) {
                    inputElement.removeClass('transfer-number-checking');
                    console.error('Error checking transfer number in database:', error);

                    // Show error but don't block submission
                    inputElement.removeClass('is-invalid is-valid');
                    inputElement.siblings('.invalid-feedback').text('Unable to verify transfer number. Please check manually.');
                    updateSubmitButtonState();
                }
            });
        }, 500); // 500ms debounce

        inputElement.data('timeout-id', newTimeoutId);
    }

    // Update submit button state based on validation
    function updateSubmitButtonState() {
        const hasInvalidReceiptInputs = $('.receipt-number-input.is-invalid').length > 0;
        const hasInvalidTransferInput = $('#transfer_no.is-invalid').length > 0;
        const hasInvalidInputs = hasInvalidReceiptInputs || hasInvalidTransferInput;
        const submitButton = $('button[type="submit"]');

        if (hasInvalidInputs) {
            submitButton.prop('disabled', true);
            if (hasInvalidReceiptInputs && hasInvalidTransferInput) {
                submitButton.html('<i class="fas fa-exclamation-triangle me-1"></i>Fix Receipt & Transfer Numbers');
            } else if (hasInvalidReceiptInputs) {
                submitButton.html('<i class="fas fa-exclamation-triangle me-1"></i>Fix Receipt Numbers');
            } else {
                submitButton.html('<i class="fas fa-exclamation-triangle me-1"></i>Fix Transfer Number');
            }
            submitButton.removeClass('btn-primary').addClass('btn-danger');
        } else {
            submitButton.prop('disabled', false);
            submitButton.html('<i class="fas fa-save me-1"></i>Create Expense');
            submitButton.removeClass('btn-danger').addClass('btn-primary');
        }
    }

    // Calculate total amount
    function calculateTotal() {
        let total = 0;
        $('.receipt-amount').each(function() {
            const amount = parseFloat($(this).val()) || 0;
            total += amount;
        });
        $('#total-amount').text(total.toFixed(2));

        // Update per-receipt calculations if function exists
        if (typeof updateAllCalculations === 'function') {
            updateAllCalculations();
            // compareAmounts will be called from updateAllCalculations after display-receipt-total is updated
        } else {
            // Fallback if no deductions system
            $('#display-receipt-total').text(total.toFixed(2));
            compareAmounts();
        }
    }

    // Handle transfer amount input
    $('#transfer_amount').on('input', function() {
        const transferAmount = parseFloat($(this).val()) || 0;
        $('#display-transfer-amount').text(transferAmount.toFixed(2));
        compareAmounts();
    });

    // Compare transfer amount with receipt total
    function compareAmounts() {
        const transferAmount = parseFloat($('#transfer_amount').val()) || 0;

        // Get the correct receipt total - use net amount if deductions exist
        let receiptTotal;

        // Check if deductions exist by looking at total-net-amount
        const netAmountText = $('#total-net-amount').text();
        const hasDeductions = netAmountText && netAmountText !== '0.00' && netAmountText.trim() !== '';

        if (hasDeductions) {
            // Use net amount (remove commas and parse)
            receiptTotal = parseFloat(netAmountText.replace(/,/g, '')) || 0;
        } else {
            // No deductions, use display-receipt-total
            receiptTotal = parseFloat($('#display-receipt-total').text()) || 0;
        }

        const comparisonDiv = $('#amount-comparison');

        if (transferAmount === 0 && receiptTotal === 0) {
            comparisonDiv.html('<small class="text-muted">Enter amounts to compare</small>');
        } else if (Math.abs(transferAmount - receiptTotal) < 0.01) {
            comparisonDiv.html('<i class="fas fa-check-circle text-success"></i> <span class="text-success">Amounts match!</span>');
        } else if (transferAmount > receiptTotal) {
            const difference = transferAmount - receiptTotal;
            comparisonDiv.html('<i class="fas fa-exclamation-triangle text-danger"></i> <span class="text-danger">Transfer is ' + difference.toFixed(2) + ' บาท higher - Must be equal!</span>');
        } else {
            const difference = receiptTotal - transferAmount;
            comparisonDiv.html('<i class="fas fa-exclamation-triangle text-danger"></i> <span class="text-danger">Receipts are ' + difference.toFixed(2) + ' บาท higher - Must be equal!</span>');
        }
    }

    // Make compareAmounts global for per-receipt-deductions.js
    window.compareAmounts = compareAmounts;
    
    // Form validation
    $('form').submit(function(e) {
        let isValid = true;
        const errors = [];
        
        // Check required fields
        const jobOpenDate = $('#job_open_date').val();
        const withdrawalDate = $('#withdrawal_date').val();
        
        if (!jobOpenDate) {
            errors.push('Job Open Date is required');
            isValid = false;
        }
        
        if (!withdrawalDate) {
            errors.push('Withdrawal Date is required');
            isValid = false;
        }
        
        // Validate withdrawal date is not more than 1 month in the past
        if (withdrawalDate) {
            const oneMonthAgo = new Date();
            oneMonthAgo.setMonth(oneMonthAgo.getMonth() - 1);
            const withdrawalDateObj = new Date(withdrawalDate);
            
            if (withdrawalDateObj < oneMonthAgo) {
                errors.push('Withdrawal date cannot be more than 1 month in the past');
                isValid = false;
            }
        }
        
        // Validate file sizes
        const transferSlip = $('#transfer_slip')[0].files[0];
        const receipts = $('#receipts')[0].files;
        const maxSize = 5 * 1024 * 1024; // 5MB
        
        if (transferSlip && transferSlip.size > maxSize) {
            errors.push('Transfer slip file is too large (max 5MB)');
            isValid = false;
        }
        
        for (let i = 0; i < receipts.length; i++) {
            if (receipts[i].size > maxSize) {
                errors.push(`Receipt file "${receipts[i].name}" is too large (max 5MB)`);
                isValid = false;
            }
        }

        // Validate receipt numbers for duplicates
        const hasInvalidReceiptNumbers = $('.receipt-number-input.is-invalid').length > 0;
        if (hasInvalidReceiptNumbers) {
            errors.push('Please fix duplicate or existing receipt numbers');
            isValid = false;
        }

        // Check for empty receipt numbers
        const emptyReceiptNumbers = $('.receipt-number-input').filter(function() {
            return !$(this).val().trim();
        });
        if (emptyReceiptNumbers.length > 0) {
            errors.push('All receipt numbers are required');
            isValid = false;
        }

        // Validate transfer number for duplicates
        const hasInvalidTransferNumber = $('#transfer_no.is-invalid').length > 0;
        if (hasInvalidTransferNumber) {
            errors.push('Please fix duplicate or existing transfer number');
            isValid = false;
        }

        // Check for empty transfer number
        const transferNumber = $('#transfer_no').val().trim();
        if (!transferNumber) {
            errors.push('Transfer number is required');
            isValid = false;
        }

        // Prevent default form submission first
        e.preventDefault();

        // Force update all calculations before validation
        console.log('Force update all calculations before validation');
        if (typeof window.updateAllCalculations === 'function') {
            window.updateAllCalculations();
        }

        // Simple validation: Use DOM values directly
        const transferAmount = parseFloat($('#transfer_amount').val()) || 0;
        const currentForm = $(this);

        // Wait a moment for DOM to update, then get the net amount
        setTimeout(function() {
            // Get values from DOM
            const grossAmountText = $('#total-gross-amount').text().replace(/,/g, '');
            const deductionsAmountText = $('#total-deductions-amount').text().replace(/,/g, '');
            const netAmountText = $('#total-net-amount').text().replace(/,/g, '');

            const grossAmount = parseFloat(grossAmountText) || 0;
            const totalDeductions = parseFloat(deductionsAmountText) || 0;
            const receiptTotal = parseFloat(netAmountText) || 0;

            // Calculate expected net amount (JavaScript calculation)
            const calculatedNetAmount = grossAmount - totalDeductions;

            console.log('=== VALIDATION DEBUG ===');
            console.log('JavaScript Calculation:');
            console.log('  grossAmount:', grossAmount);
            console.log('  totalDeductions:', totalDeductions);
            console.log('  calculatedNetAmount (JS):', calculatedNetAmount);
            console.log('DOM Values:');
            console.log('  receiptTotal (from DOM):', receiptTotal);
            console.log('  transferAmount (user input):', transferAmount);
            console.log('Differences:');
            console.log('  JS vs DOM difference:', Math.abs(calculatedNetAmount - receiptTotal));
            console.log('  Transfer vs Receipt difference:', Math.abs(transferAmount - receiptTotal));

            // Check if JavaScript calculation matches DOM display
            if (Math.abs(calculatedNetAmount - receiptTotal) > 0.01) {
                console.log('⚠️ WARNING: JavaScript calculation does not match DOM display!');
                errors.push('⚠️ Calculation Mismatch: JavaScript calculated ' + calculatedNetAmount.toFixed(2) + ' บาท but DOM shows ' + receiptTotal.toFixed(2) + ' บาท. Please refresh and try again.');
                isValid = false;
            }

            // Check transfer amount validation
            if (transferAmount > 0 && receiptTotal > 0 && Math.abs(transferAmount - receiptTotal) > 0.01) {
                console.log('VALIDATION FAILED!');
                errors.push('Transfer Amount (' + transferAmount.toFixed(2) + ' บาท) must equal Receipt Total (' + receiptTotal.toFixed(2) + ' บาท). Please check your amounts.');
                isValid = false;
            }

            // Show errors if validation failed
            if (errors.length > 0) {
                let errorHtml = '<div class="alert alert-danger"><ul class="mb-0">';
                errors.forEach(function(error) {
                    errorHtml += '<li>' + error + '</li>';
                });
                errorHtml += '</ul></div>';
                $('#form-errors').html(errorHtml);
                $('html, body').animate({
                    scrollTop: $('#form-errors').offset().top - 100
                }, 500);
                return false;
            } else {
                console.log('VALIDATION PASSED!');
                console.log('Submitting with values:');
                console.log('  Transfer Amount:', transferAmount);
                console.log('  Receipt Total (Net):', receiptTotal);
                console.log('  Gross Amount:', grossAmount);
                console.log('  Total Deductions:', totalDeductions);

                // Submit the form
                currentForm.off('submit').submit();
            }
        }, 100);

        // Prevent default submission - validation happens in setTimeout above
        return false;


    });
    
    // File preview functionality
    $('#transfer_slip').change(function() {
        previewFile(this, 'transfer-slip-preview');
    });
    
    function previewFile(input, previewId) {
        const file = input.files[0];
        if (file) {
            const reader = new FileReader();
            reader.onload = function(e) {
                let preview = $('#' + previewId);
                if (preview.length === 0) {
                    preview = $('<div id="' + previewId + '" class="mt-2"></div>');
                    $(input).parent().append(preview);
                }
                
                if (file.type.startsWith('image/')) {
                    preview.html(`
                        <img src="${e.target.result}" class="image-preview" style="max-width: 200px; max-height: 200px;">
                        <div class="mt-1">
                            <small class="text-muted">${file.name} (${formatFileSize(file.size)})</small>
                        </div>
                    `);
                } else {
                    preview.html(`
                        <div class="alert alert-info">
                            <i class="fas fa-file-pdf me-2"></i>
                            ${file.name} (${formatFileSize(file.size)})
                        </div>
                    `);
                }
            };
            reader.readAsDataURL(file);
        }
    }
    
    function formatFileSize(bytes) {
        if (bytes === 0) return '0 Bytes';
        const k = 1024;
        const sizes = ['Bytes', 'KB', 'MB', 'GB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    }
    
    function showErrors(errors) {
        const errorHtml = `
            <div class="alert alert-danger alert-dismissible fade show" role="alert">
                <i class="fas fa-exclamation-triangle me-2"></i>
                <strong>Please fix the following errors:</strong>
                <ul class="mb-0 mt-2">
                    ${errors.map(error => `<li>${error}</li>`).join('')}
                </ul>
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        `;
        
        // Remove existing error alerts
        $('.alert-danger').remove();
        
        // Add new error alert at the top of the form
        $('form').prepend(errorHtml);
        
        // Scroll to top
        $('html, body').animate({ scrollTop: 0 }, 300);
    }
    
    // Auto-save functionality (optional) - DISABLED to prevent form pre-population issues
    /*
    let autoSaveTimer;
    $('form input, form select, form textarea').on('input change', function() {
        clearTimeout(autoSaveTimer);
        autoSaveTimer = setTimeout(function() {
            saveFormData();
        }, 2000); // Auto-save after 2 seconds of inactivity
    });

    function saveFormData() {
        const formData = {};
        $('form input, form select, form textarea').each(function() {
            if (this.type !== 'file' && this.type !== 'submit') {
                formData[this.name] = $(this).val();
            }
        });

        localStorage.setItem('expense_form_data', JSON.stringify(formData));
    }

    function loadFormData() {
        const savedData = localStorage.getItem('expense_form_data');
        if (savedData) {
            try {
                const formData = JSON.parse(savedData);
                Object.keys(formData).forEach(function(key) {
                    const element = $(`[name="${key}"]`);
                    if (element.length && formData[key]) {
                        element.val(formData[key]);
                    }
                });
            } catch (e) {
                console.error('Error loading saved form data:', e);
            }
        }
    }

    // Load saved form data on page load
    loadFormData();

    // Clear saved data on successful form submission
    $('form').on('submit', function() {
        localStorage.removeItem('expense_form_data');
    });
    */
    
    // Clear any existing auto-save data from localStorage (cleanup)
    localStorage.removeItem('expense_form_data');

    // Setup datalist functionality
    setupDatalist();

    function setupDatalist() {
        // Item input handling
        $('#item_input').on('input change', function() {
            const inputValue = $(this).val();
            const option = $('#item_list option[value="' + inputValue + '"]');

            if (option.length > 0) {
                // Existing item selected
                $('#item_id').val(option.data('id'));
                $('#new_item').val('');
            } else {
                // New item typed
                $('#item_id').val('');
                $('#new_item').val(inputValue);
            }
        });

        // Customer input handling
        $('#customer_input').on('input change', function() {
            const inputValue = $(this).val();
            const option = $('#customer_list option[value="' + inputValue + '"]');

            if (option.length > 0) {
                // Existing customer selected
                $('#customer_id').val(option.data('id'));
                $('#new_customer').val('');
            } else {
                // New customer typed
                $('#customer_id').val('');
                $('#new_customer').val(inputValue);
            }
        });

        // Driver input handling with auto-fill
        $('#driver_input').on('input change', function() {
            const inputValue = $(this).val();
            const option = $('#driver_list option[value="' + inputValue + '"]');

            if (option.length > 0) {
                // Existing driver selected
                $('#driver_id').val(option.data('id'));

                // Auto-fill driver information
                $('#vehicle_plate').val(option.data('vehicle') || '');
                $('#payment_account_no').val(option.data('account') || '');
                $('#requester').val(inputValue);
                $('#receiver').val(inputValue);
            } else {
                // Clear driver fields if no match
                $('#driver_id').val('');
                $('#vehicle_plate').val('');
                $('#payment_account_no').val('');
                $('#requester').val('');
                $('#receiver').val('');
            }
        });

        // Add New Item button
        $('#add_new_item_btn').on('click', function() {
            const newItemName = prompt('Enter new item name:');
            if (newItemName && newItemName.trim()) {
                $('#item_input').val(newItemName.trim());
                $('#item_id').val('');
                $('#new_item').val(newItemName.trim());
            }
        });

        // Add New Customer button
        $('#add_new_customer_btn').on('click', function() {
            const newCustomerName = prompt('Enter new customer name:');
            if (newCustomerName && newCustomerName.trim()) {
                $('#customer_input').val(newCustomerName.trim());
                $('#customer_id').val('');
                $('#new_customer').val(newCustomerName.trim());
            }
        });
    }

    // Legacy autocomplete functionality (keeping for backward compatibility)
    function setupAutocomplete(selector, endpoint) {
        $(selector).on('input', function() {
            const query = $(this).val();
            if (query.length >= 2) {
                $.ajax({
                    url: `../api/autocomplete.php?type=${endpoint}&q=${encodeURIComponent(query)}`,
                    method: 'GET',
                    dataType: 'json',
                    success: function(data) {
                        showSuggestions(selector, data);
                    }
                });
            } else {
                hideSuggestions(selector);
            }
        });
    }

    function showSuggestions(selector, suggestions) {
        const container = $(selector).parent();
        let suggestionsDiv = container.find('.autocomplete-suggestions');

        if (suggestionsDiv.length === 0) {
            suggestionsDiv = $('<div class="autocomplete-suggestions"></div>');
            container.append(suggestionsDiv);
        }

        suggestionsDiv.empty();

        suggestions.forEach(function(item) {
            const suggestion = $(`<div class="autocomplete-suggestion" data-id="${item.id}">${item.name}</div>`);
            suggestion.click(function() {
                $(selector).val(item.id);
                hideSuggestions(selector);
            });
            suggestionsDiv.append(suggestion);
        });

        suggestionsDiv.show();
    }

    function hideSuggestions(selector) {
        $(selector).parent().find('.autocomplete-suggestions').hide();
    }

    // Hide suggestions when clicking outside
    $(document).click(function(e) {
        if (!$(e.target).closest('.autocomplete-container, .searchable-select-container').length) {
            $('.autocomplete-suggestions').hide();
        }
    });

    // ===== RECEIPT DEDUCTIONS MANAGEMENT =====

    // Global variables for per-receipt deductions
    window.receiptDeductions = {}; // Object with receipt index as key
    window.receiptFiles = []; // Array to store receipt file info
    window.currentDeductionIndex = -1;
    window.currentReceiptIndex = -1;

    // Show receipt cards container when receipts are uploaded
    function showReceiptCardsContainer() {
        $('#receipt-cards-container').removeClass('d-none');
        createReceiptCards();
    }

    // Hide receipt cards container when no receipts
    function hideReceiptCardsContainer() {
        $('#receipt-cards-container').addClass('d-none');
        window.receiptDeductions = {};
        window.receiptFiles = [];
        updateAllCalculations();
    }

    // Legacy function names for backward compatibility
    function showDeductionsContainer() {
        showReceiptCardsContainer();
    }

    function hideDeductionsContainer() {
        hideReceiptCardsContainer();
    }

    // Initialize checkbox handlers for deductions
    function initializeCheckboxHandlers() {
        // Handle checkbox change events
        $(document).off('change', '.has-deductions-checkbox').on('change', '.has-deductions-checkbox', function() {
            const receiptIndex = $(this).data('receipt-index');
            const isChecked = $(this).is(':checked');
            const receiptCard = $(this).closest('.receipt-card');
            const deductionsSection = $(`#deductionsSection${receiptIndex}`);

            if (isChecked) {
                // แสดง Deductions Section
                deductionsSection.removeClass('d-none');
                receiptCard.addClass('has-deductions');

                // Initialize deductions array for this receipt
                if (!window.receiptDeductions[receiptIndex]) {
                    window.receiptDeductions[receiptIndex] = [];
                }

                // Show summary container if any receipt has deductions
                showDeductionsSummary();
            } else {
                // ซ่อน Deductions Section
                deductionsSection.addClass('d-none');
                receiptCard.removeClass('has-deductions');

                // ลบ deductions ของ receipt นี้
                delete window.receiptDeductions[receiptIndex];

                // อัพเดตการคำนวณ
                updateAllCalculations();

                // ซ่อน summary container ถ้าไม่มี receipt ไหนมี deductions
                checkAndHideDeductionsSummary();
            }
        });

        // Handle add deduction button clicks
        $(document).off('click', '.add-deduction-btn').on('click', '.add-deduction-btn', function() {
            const receiptIndex = $(this).data('receipt-index');
            window.currentReceiptIndex = receiptIndex;
            window.currentDeductionIndex = -1; // -1 means adding new

            // Reset modal
            $('#deductionModal').find('form')[0].reset();
            $('#deduction-form').data('mode', 'add');
            $('#deductionModal .modal-title').text('เพิ่มรายการหัก');

            // Clear upload status
            $('#image-upload-status').empty();
            $('#deduction_image').removeData('uploaded-filename').removeData('uploaded-url');

            // Show amount input, hide percentage input
            $('#amount-input').show();
            $('#percentage-input').hide();

            $('#deductionModal').modal('show');
        });
    }

    // Show deductions summary container
    function showDeductionsSummary() {
        $('#deductions-summary-container').removeClass('d-none');
    }

    // Hide deductions summary container if no deductions exist
    function checkAndHideDeductionsSummary() {
        const hasAnyDeductions = Object.keys(window.receiptDeductions).length > 0;
        if (!hasAnyDeductions) {
            $('#deductions-summary-container').addClass('d-none');
        }
    }

    // Create receipt cards
    function createReceiptCards() {
        const container = $('#receipt-cards-list');
        container.empty();

        $('.receipt-number-input').each(function(index) {
            const receiptNumber = $(this).val() || `Receipt ${index + 1}`;
            const receiptAmount = parseFloat($('.receipt-amount-input').eq(index).val()) || 0;
            const receiptDescription = $('.receipt-description-input').eq(index).val() || '';

            // Initialize deductions array for this receipt if not exists
            if (!window.receiptDeductions[index]) {
                window.receiptDeductions[index] = [];
            }

            const card = createReceiptCard(index, receiptNumber, receiptAmount, receiptDescription);
            container.append(card);
        });

        updateAllCalculations();
    }

    // Create individual receipt card
    function createReceiptCard(index, receiptNumber, amount, description) {
        const deductions = window.receiptDeductions[index] || [];
        const totalDeductions = deductions.reduce((sum, d) => sum + d.amount, 0);
        const netAmount = amount - totalDeductions;

        return `
            <div class="card mb-3 receipt-card" data-receipt-index="${index}">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h6 class="mb-0">
                        📄 ${receiptNumber}
                        <span class="badge bg-secondary">${amount.toLocaleString()} บาท</span>
                    </h6>
                    <button type="button" class="btn btn-sm btn-outline-primary"
                            onclick="showAddDeductionModal(${index})">
                        + เพิ่มรายการหัก
                    </button>
                </div>
                <div class="card-body">
                    ${description ? `<p class="text-muted mb-2">${description}</p>` : ''}

                    <!-- Deductions List -->
                    <div class="deductions-list" id="deductions-list-${index}">
                        ${renderDeductionsForReceipt(index)}
                    </div>

                    <!-- Net Amount -->
                    <div class="row mt-3 pt-2 border-top">
                        <div class="col-6">
                            <strong>ยอดก่อนหัก:</strong> ${amount.toLocaleString()} บาท
                        </div>
                        <div class="col-6 text-end">
                            <strong class="text-primary">ยอดสุทธิ:
                                <span id="net-amount-${index}">${netAmount.toLocaleString()}</span> บาท
                            </strong>
                        </div>
                    </div>

                    ${totalDeductions > 0 ? `
                    <div class="row">
                        <div class="col-6">
                            <span class="text-danger">รายการหัก:</span> ${totalDeductions.toLocaleString()} บาท
                        </div>
                    </div>
                    ` : ''}
                </div>
            </div>
        `;
    }

    // Show add deduction modal for specific receipt
    function showAddDeductionModal(receiptIndex) {
        window.currentReceiptIndex = receiptIndex;
        resetDeductionForm();
        $('#deduction-form').data('mode', 'add');
        $('#target_receipt_index').val(receiptIndex);
        $('#deductionModalTitle').text(`เพิ่มรายการหัก - Receipt ${receiptIndex + 1}`);
        $('#deductionModal').modal('show');
    }

    // Legacy showEditDeductionModal removed - functionality moved to per-receipt-deductions.js

    // Reset deduction form
    function resetDeductionForm() {
        $('#deduction-form')[0].reset();
        $('#deduction-form').removeData('mode').removeData('index');
        window.currentDeductionIndex = -1;
        togglePercentageFields(false);
        clearDeductionFormErrors();

        // Clear image upload status and preview
        $('#image-upload-status').empty();
        $('#deduction_image').val('');

        // Clear any image preview if exists
        $('.image-preview').remove();

        // Reset file input
        const fileInput = document.getElementById('deduction_image');
        if (fileInput) {
            fileInput.value = '';
        }
    }

    // Toggle percentage fields
    function togglePercentageFields(isPercentageBased) {
        if (isPercentageBased) {
            $('#percentage-field').show();
            $('#amount-field').hide();
            $('#deduction_amount').removeAttr('required');
            $('#deduction_percentage').attr('required', true);
        } else {
            $('#percentage-field').hide();
            $('#amount-field').show();
            $('#deduction_percentage').removeAttr('required');
            $('#deduction_amount').attr('required', true);
        }
    }

    // Percentage checkbox change
    $('#is_percentage_based').change(function() {
        togglePercentageFields(this.checked);
        if (this.checked) {
            calculateAmountFromPercentage();
        }
    });

    // Percentage input change
    $('#deduction_percentage').on('input', function() {
        if ($('#is_percentage_based').is(':checked')) {
            calculateAmountFromPercentage();
        }
    });

    // Calculate amount from percentage
    function calculateAmountFromPercentage() {
        const grossAmount = parseFloat($('#total-amount').text().replace(/,/g, '')) || 0;
        const percentage = parseFloat($('#deduction_percentage').val()) || 0;
        const calculatedAmount = (grossAmount * percentage / 100);

        $('#calculated-amount-display').text(calculatedAmount.toLocaleString('th-TH', {
            minimumFractionDigits: 2,
            maximumFractionDigits: 2
        }) + ' บาท');
    }

    // Save deduction button handler removed - handled by per-receipt-deductions.js

    // Save deduction function removed - functionality moved to per-receipt-deductions.js

    // Legacy deduction functions removed - functionality moved to per-receipt-deductions.js

    // Show deduction form error
    function showDeductionFormError(message) {
        clearDeductionFormErrors();
        $('#deduction-form-errors').html(`<div class="alert alert-danger">${message}</div>`);
    }

    // Clear deduction form errors
    function clearDeductionFormErrors() {
        $('#deduction-form-errors').empty();
    }

    // Handle form submission to include per-receipt deductions data
    $('.expense-form').on('submit', function(e) {
        // Prepare per-receipt deductions data for submission
        if (window.receiptDeductions && Object.keys(window.receiptDeductions).length > 0) {
            const deductionsWithReceipts = [];

            Object.keys(window.receiptDeductions).forEach(receiptIndex => {
                const receiptDeductions = window.receiptDeductions[receiptIndex];
                const receiptNumber = $('.receipt-number-input').eq(receiptIndex).val().trim();

                if (receiptNumber && receiptDeductions.length > 0) {
                    receiptDeductions.forEach(deduction => {
                        const deductionCopy = {...deduction};
                        deductionCopy.receipt_index = parseInt(receiptIndex);
                        deductionCopy.receipt_number = receiptNumber;
                        deductionsWithReceipts.push(deductionCopy);
                    });
                }
            });

            // Add hidden input with deductions data
            const existingInput = $('input[name="deductions_data"]');
            if (existingInput.length) {
                existingInput.remove();
            }

            if (deductionsWithReceipts.length > 0) {
                const hiddenInput = $('<input type="hidden" name="deductions_data">');
                hiddenInput.val(JSON.stringify(deductionsWithReceipts));
                $(this).append(hiddenInput);

                console.log('Per-receipt deductions data prepared for submission:', deductionsWithReceipts);

                // Add debug data for PHP comparison
                const debugData = {
                    js_gross_amount: grossAmount,
                    js_total_deductions: totalDeductions,
                    js_calculated_net: calculatedNetAmount,
                    js_transfer_amount: transferAmount,
                    deductions_count: deductionsWithReceipts.length
                };

                const debugInput = $('<input type="hidden" name="js_debug_data">');
                debugInput.val(JSON.stringify(debugData));
                $(this).append(debugInput);

                console.log('JavaScript debug data for PHP comparison:', debugData);
            }
        }
    });

    // Make functions global
    window.showDeductionsContainer = showDeductionsContainer;
    window.hideDeductionsContainer = hideDeductionsContainer;
});
