/**
 * Receipt Deductions JavaScript Library
 * 
 * Handles frontend operations for receipt deductions
 * including adding, editing, deleting, and calculating deductions
 */

class ReceiptDeductionManager {
    constructor() {
        this.apiUrl = '../api/receipt_deductions.php';
        this.deductionTypes = {};
        this.currentReceiptId = null;
        this.init();
    }
    
    init() {
        this.bindEvents();
        this.loadDeductionTypes();
    }
    
    bindEvents() {
        // Add deduction button
        $(document).on('click', '.add-deduction-btn', (e) => {
            e.preventDefault();
            this.showAddDeductionModal();
        });
        
        // Edit deduction button
        $(document).on('click', '.edit-deduction-btn', (e) => {
            e.preventDefault();
            const deductionId = $(e.target).closest('.edit-deduction-btn').data('deduction-id');
            this.showEditDeductionModal(deductionId);
        });
        
        // Delete deduction button
        $(document).on('click', '.delete-deduction-btn', (e) => {
            e.preventDefault();
            const deductionId = $(e.target).closest('.delete-deduction-btn').data('deduction-id');
            this.deleteDeduction(deductionId);
        });
        
        // Percentage checkbox change
        $(document).on('change', '#is_percentage_based', (e) => {
            this.togglePercentageFields(e.target.checked);
        });
        
        // Gross amount change
        $(document).on('change', '#gross_amount', (e) => {
            this.updateCalculations();
        });
        
        // Percentage input change
        $(document).on('input', '#deduction_percentage', (e) => {
            if ($('#is_percentage_based').is(':checked')) {
                this.calculateAmountFromPercentage();
            }
        });
        
        // Amount input change
        $(document).on('input', '#deduction_amount', (e) => {
            if (!$('#is_percentage_based').is(':checked')) {
                this.updateCalculations();
            }
        });

        // Save deduction form
        $(document).on('submit', '#deduction-form', (e) => {
            e.preventDefault();
            this.saveDeduction();
        });

        // Save deduction button
        $(document).on('click', '#save-deduction-btn', (e) => {
            e.preventDefault();
            this.saveDeduction();
        });
    }
    
    async loadDeductionTypes() {
        try {
            const response = await fetch(`${this.apiUrl}?receipt_id=1`);
            const data = await response.json();
            if (data.success) {
                this.deductionTypes = data.deduction_types;
            }
        } catch (error) {
            console.error('Error loading deduction types:', error);
        }
    }
    
    async loadReceiptDeductions(receiptId) {
        this.currentReceiptId = receiptId;
        
        try {
            const response = await fetch(`${this.apiUrl}?receipt_id=${receiptId}`);
            const data = await response.json();
            
            if (data.success) {
                this.renderDeductions(data.deductions);
                this.updateSummary(data.summary);
                return data;
            } else {
                this.showError('ไม่สามารถโหลดข้อมูลรายการหักได้');
                return null;
            }
        } catch (error) {
            console.error('Error loading deductions:', error);
            this.showError('เกิดข้อผิดพลาดในการโหลดข้อมูล');
            return null;
        }
    }
    
    renderDeductions(deductions) {
        const container = $('#deductions-container');
        
        if (deductions.length === 0) {
            container.html(`
                <div class="text-center text-muted py-3">
                    <i class="fas fa-info-circle me-2"></i>ไม่มีรายการหัก
                </div>
            `);
            return;
        }
        
        let html = '';
        deductions.forEach((deduction, index) => {
            html += this.renderDeductionItem(deduction, index);
        });
        
        container.html(html);
    }
    
    renderDeductionItem(deduction, index) {
        const typeName = this.deductionTypes[deduction.deduction_type] || deduction.deduction_type;
        const amount = parseFloat(deduction.amount).toLocaleString('th-TH', {
            minimumFractionDigits: 2,
            maximumFractionDigits: 2
        });
        
        return `
            <div class="deduction-item card mb-2" data-deduction-id="${deduction.id}">
                <div class="card-body p-3">
                    <div class="row align-items-center">
                        <div class="col-md-3">
                            <strong>${typeName}</strong>
                            ${deduction.is_percentage_based ? 
                                `<br><small class="text-muted">${deduction.percentage}%</small>` : 
                                ''
                            }
                        </div>
                        <div class="col-md-3">
                            <span class="text-success fw-bold">${amount} บาท</span>
                        </div>
                        <div class="col-md-4">
                            <small class="text-muted">${deduction.description || '-'}</small>
                        </div>
                        <div class="col-md-2 text-end">
                            <div class="btn-group btn-group-sm">
                                <button type="button" class="btn btn-outline-primary edit-deduction-btn" 
                                        data-deduction-id="${deduction.id}" title="แก้ไข">
                                    <i class="fas fa-edit"></i>
                                </button>
                                <button type="button" class="btn btn-outline-danger delete-deduction-btn" 
                                        data-deduction-id="${deduction.id}" title="ลบ">
                                    <i class="fas fa-trash"></i>
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        `;
    }
    
    updateSummary(summary) {
        if (!summary) return;
        
        const grossAmount = parseFloat(summary.gross_amount || 0);
        const totalDeductions = parseFloat(summary.total_deductions || 0);
        const netAmount = parseFloat(summary.calculated_net_amount || 0);
        
        $('#gross-amount-display').text(grossAmount.toLocaleString('th-TH', {
            minimumFractionDigits: 2,
            maximumFractionDigits: 2
        }));
        
        $('#total-deductions-display').text(totalDeductions.toLocaleString('th-TH', {
            minimumFractionDigits: 2,
            maximumFractionDigits: 2
        }));
        
        $('#net-amount-display').text(netAmount.toLocaleString('th-TH', {
            minimumFractionDigits: 2,
            maximumFractionDigits: 2
        }));
        
        // Update progress bar
        const deductionPercentage = grossAmount > 0 ? (totalDeductions / grossAmount * 100) : 0;
        $('#deduction-progress').css('width', `${deductionPercentage}%`);
        $('#deduction-percentage').text(`${deductionPercentage.toFixed(1)}%`);
    }
    
    showAddDeductionModal() {
        this.resetDeductionForm();
        $('#deductionModalTitle').text('เพิ่มรายการหัก');
        $('#deduction-form').data('mode', 'add');
        $('#deductionModal').modal('show');
    }
    
    async showEditDeductionModal(deductionId) {
        // Get deduction data
        const deductions = await this.loadReceiptDeductions(this.currentReceiptId);
        const deduction = deductions.deductions.find(d => d.id == deductionId);
        
        if (!deduction) {
            this.showError('ไม่พบข้อมูลรายการหัก');
            return;
        }
        
        // Populate form
        $('#deduction_type').val(deduction.deduction_type);
        $('#deduction_amount').val(deduction.amount);
        $('#deduction_percentage').val(deduction.percentage || '');
        $('#deduction_description').val(deduction.description || '');
        $('#is_percentage_based').prop('checked', deduction.is_percentage_based == 1);
        
        this.togglePercentageFields(deduction.is_percentage_based == 1);
        
        $('#deductionModalTitle').text('แก้ไขรายการหัก');
        $('#deduction-form').data('mode', 'edit').data('deduction-id', deductionId);
        $('#deductionModal').modal('show');
    }
    
    resetDeductionForm() {
        $('#deduction-form')[0].reset();
        $('#deduction-form').removeData('mode').removeData('deduction-id');
        this.togglePercentageFields(false);
        this.clearFormErrors();
    }
    
    togglePercentageFields(isPercentageBased) {
        if (isPercentageBased) {
            $('#percentage-field').show();
            $('#amount-field').hide();
            $('#deduction_amount').removeAttr('required');
            $('#deduction_percentage').attr('required', true);
        } else {
            $('#percentage-field').hide();
            $('#amount-field').show();
            $('#deduction_percentage').removeAttr('required');
            $('#deduction_amount').attr('required', true);
        }
    }
    
    calculateAmountFromPercentage() {
        const grossAmount = parseFloat($('#gross_amount').val() || 0);
        const percentage = parseFloat($('#deduction_percentage').val() || 0);
        const calculatedAmount = (grossAmount * percentage / 100);
        
        $('#calculated-amount-display').text(calculatedAmount.toLocaleString('th-TH', {
            minimumFractionDigits: 2,
            maximumFractionDigits: 2
        }) + ' บาท');
    }
    
    async saveDeduction() {
        const form = $('#deduction-form');
        const mode = form.data('mode');
        const deductionId = form.data('deduction-id');
        
        // Collect form data
        const deductionData = {
            deduction_type: $('#deduction_type').val(),
            amount: parseFloat($('#deduction_amount').val() || 0),
            percentage: parseFloat($('#deduction_percentage').val() || 0),
            description: $('#deduction_description').val(),
            is_percentage_based: $('#is_percentage_based').is(':checked')
        };
        
        // Calculate amount for percentage-based deductions
        if (deductionData.is_percentage_based) {
            const grossAmount = parseFloat($('#gross_amount').val() || 0);
            deductionData.amount = (grossAmount * deductionData.percentage / 100);
        }
        
        try {
            let response;
            
            if (mode === 'add') {
                response = await fetch(this.apiUrl, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        receipt_id: this.currentReceiptId,
                        deduction_data: deductionData
                    })
                });
            } else {
                response = await fetch(this.apiUrl, {
                    method: 'PUT',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        deduction_id: deductionId,
                        deduction_data: deductionData
                    })
                });
            }
            
            const result = await response.json();
            
            if (result.success) {
                $('#deductionModal').modal('hide');
                this.showSuccess(result.message);
                
                // Reload deductions
                await this.loadReceiptDeductions(this.currentReceiptId);
            } else {
                this.showFormErrors(result.errors || [result.error]);
            }
            
        } catch (error) {
            console.error('Error saving deduction:', error);
            this.showError('เกิดข้อผิดพลาดในการบันทึกข้อมูล');
        }
    }
    
    async deleteDeduction(deductionId) {
        if (!confirm('คุณต้องการลบรายการหักนี้หรือไม่?')) {
            return;
        }
        
        try {
            const response = await fetch(`${this.apiUrl}?deduction_id=${deductionId}`, {
                method: 'DELETE'
            });
            
            const result = await response.json();
            
            if (result.success) {
                this.showSuccess(result.message);
                await this.loadReceiptDeductions(this.currentReceiptId);
            } else {
                this.showError(result.error || 'เกิดข้อผิดพลาดในการลบข้อมูล');
            }
            
        } catch (error) {
            console.error('Error deleting deduction:', error);
            this.showError('เกิดข้อผิดพลาดในการลบข้อมูล');
        }
    }
    
    showSuccess(message) {
        // Implementation depends on your notification system
        alert(message); // Replace with your preferred notification method
    }
    
    showError(message) {
        // Implementation depends on your notification system
        alert(message); // Replace with your preferred notification method
    }
    
    showFormErrors(errors) {
        this.clearFormErrors();
        
        if (Array.isArray(errors)) {
            errors.forEach(error => {
                $('#form-errors').append(`<div class="alert alert-danger">${error}</div>`);
            });
        } else {
            $('#form-errors').append(`<div class="alert alert-danger">${errors}</div>`);
        }
    }
    
    clearFormErrors() {
        $('#form-errors').empty();
    }
}

// Initialize when document is ready
$(document).ready(function() {
    window.receiptDeductionManager = new ReceiptDeductionManager();
});
