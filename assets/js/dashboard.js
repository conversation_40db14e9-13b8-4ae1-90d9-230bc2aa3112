// Dashboard JavaScript
$(document).ready(function() {
    loadDashboardStats();
    loadRecentExpenses();
    
    // Refresh data every 30 seconds
    setInterval(function() {
        loadDashboardStats();
        loadRecentExpenses();
    }, 30000);
});

function loadDashboardStats() {
    $.ajax({
        url: 'api/dashboard_stats.php',
        method: 'GET',
        dataType: 'json',
        success: function(data) {
            $('#total-expenses').text(data.total || 0);
            $('#pending-expenses').text(data.pending || 0);
            $('#approved-expenses').text(data.success || 0);
            $('#open-expenses').text(data.open || 0);
        },
        error: function(xhr, status, error) {
            console.error('Error loading dashboard stats:', error);
        }
    });
}

function loadRecentExpenses() {
    $.ajax({
        url: 'api/recent_expenses.php',
        method: 'GET',
        dataType: 'json',
        success: function(data) {
            const tbody = $('#recent-expenses-table tbody');
            tbody.empty();

            if (data.length === 0) {
                tbody.append('<tr><td colspan="9" class="text-center">No expenses found</td></tr>');
                return;
            }

            data.forEach(function(expense) {
                const statusClass = getStatusClass(expense.status);
                const statusText = getStatusText(expense.status);

                // Format transfer amount
                const transferAmount = expense.transfer_amount ?
                    `<span class="text-primary fw-bold">${parseFloat(expense.transfer_amount).toLocaleString('en-US', {minimumFractionDigits: 2, maximumFractionDigits: 2})}</span><br><small class="text-muted">บาท</small>` :
                    '<span class="text-muted">-</span>';

                // Format receipt numbers
                let receiptNumbers = '<span class="text-muted">No receipts</span>';
                if (expense.receipt_numbers && expense.receipt_numbers.length > 0) {
                    const badges = expense.receipt_numbers.map(receipt =>
                        `<span class="badge bg-light text-dark me-1 mb-1">${receipt.receipt_number} <small class="text-success">(${parseFloat(receipt.amount).toLocaleString('en-US', {minimumFractionDigits: 2, maximumFractionDigits: 2})})</small></span>`
                    ).join('');
                    const totalAmount = expense.receipt_numbers.reduce((sum, receipt) => sum + parseFloat(receipt.amount || 0), 0);
                    receiptNumbers = `<div class="receipt-numbers">${badges}<br><small class="text-muted">Total: <span class="text-success fw-bold">${totalAmount.toLocaleString('en-US', {minimumFractionDigits: 2, maximumFractionDigits: 2})} บาท</span></small></div>`;
                }

                // Format withdrawal date
                const withdrawalDate = expense.withdrawal_date ?
                    formatDate(expense.withdrawal_date) :
                    '<span class="text-muted">-</span>';

                const row = `
                    <tr>
                        <td><strong>${expense.exno}</strong></td>
                        <td>${formatDate(expense.job_open_date)}</td>
                        <td>${expense.item_name || '-'}</td>
                        <td>${expense.driver_name || '-'}</td>
                        <td class="text-end">${transferAmount}</td>
                        <td style="max-width: 200px;">${receiptNumbers}</td>
                        <td>${withdrawalDate}</td>
                        <td><span class="badge ${statusClass}"><i class="${getStatusIcon(expense.status)} me-1"></i>${statusText}</span></td>
                        <td>
                            <div class="btn-group btn-group-sm">
                                <a href="expenses/view.php?id=${expense.id}" class="btn btn-outline-primary btn-sm" title="View Details">
                                    <i class="fas fa-eye"></i>
                                </a>
                                ${expense.can_edit ? `
                                    <a href="expenses/edit.php?id=${expense.id}" class="btn btn-outline-secondary btn-sm" title="Edit">
                                        <i class="fas fa-edit"></i>
                                    </a>
                                ` : ''}
                            </div>
                        </td>
                    </tr>
                `;
                tbody.append(row);
            });
        },
        error: function(xhr, status, error) {
            console.error('Error loading recent expenses:', error);
            $('#recent-expenses-table tbody').html('<tr><td colspan="9" class="text-center text-danger">Error loading data</td></tr>');
        }
    });
}

function getStatusClass(status) {
    switch(status) {
        case 'open':
            return 'bg-info';
        case 'checked':
            return 'bg-primary';
        case 'pending':
            return 'bg-warning text-dark';
        case 'success':
            return 'bg-success';
        case 'rejected':
            return 'bg-danger';
        case 'returned':
            return 'bg-secondary';
        default:
            return 'bg-dark';
    }
}

function getStatusText(status) {
    switch(status) {
        case 'open':
            return 'Open';
        case 'checked':
            return 'Checked';
        case 'pending':
            return 'Pending';
        case 'success':
            return 'Approved';
        case 'rejected':
            return 'Rejected';
        case 'returned':
            return 'Returned';
        default:
            return status.charAt(0).toUpperCase() + status.slice(1);
    }
}

function getStatusIcon(status) {
    switch(status) {
        case 'open':
            return 'fas fa-folder-open';
        case 'checked':
            return 'fas fa-check';
        case 'pending':
            return 'fas fa-clock';
        case 'success':
            return 'fas fa-check-circle';
        case 'rejected':
            return 'fas fa-times-circle';
        case 'returned':
            return 'fas fa-undo';
        default:
            return 'fas fa-question-circle';
    }
}

function formatDate(dateString) {
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', {
        year: 'numeric',
        month: 'short',
        day: 'numeric'
    });
}

// Toast notification function
function showToast(message, type = 'info') {
    const toastHtml = `
        <div class="toast align-items-center text-white bg-${type} border-0" role="alert" aria-live="assertive" aria-atomic="true">
            <div class="d-flex">
                <div class="toast-body">
                    ${message}
                </div>
                <button type="button" class="btn-close btn-close-white me-2 m-auto" data-bs-dismiss="toast" aria-label="Close"></button>
            </div>
        </div>
    `;
    
    // Create toast container if it doesn't exist
    if (!$('.toast-container').length) {
        $('body').append('<div class="toast-container position-fixed top-0 end-0 p-3"></div>');
    }
    
    const $toast = $(toastHtml);
    $('.toast-container').append($toast);
    
    const toast = new bootstrap.Toast($toast[0]);
    toast.show();
    
    // Remove toast element after it's hidden
    $toast.on('hidden.bs.toast', function() {
        $(this).remove();
    });
}

// Global error handler for AJAX requests
$(document).ajaxError(function(event, xhr, settings, thrownError) {
    if (xhr.status === 401) {
        showToast('Session expired. Please login again.', 'danger');
        setTimeout(function() {
            window.location.href = 'login.php';
        }, 2000);
    } else if (xhr.status === 403) {
        showToast('Access denied. You do not have permission to perform this action.', 'danger');
    } else if (xhr.status >= 500) {
        showToast('Server error. Please try again later.', 'danger');
    }
});

// Global CSRF token handling
$.ajaxSetup({
    beforeSend: function(xhr, settings) {
        if (!/^(GET|HEAD|OPTIONS|TRACE)$/i.test(settings.type) && !this.crossDomain) {
            const token = $('meta[name=csrf-token]').attr('content');
            if (token) {
                xhr.setRequestHeader("X-CSRF-TOKEN", token);
            }
        }
    }
});
