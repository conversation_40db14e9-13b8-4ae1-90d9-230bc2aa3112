// Image Viewer JavaScript
class ImageViewer {
    constructor() {
        this.currentImage = null;
        this.images = [];
        this.currentIndex = 0;
        this.init();
    }
    
    init() {
        this.createModal();
        this.bindEvents();
    }
    
    createModal() {
        const modalHtml = `
            <div class="modal fade" id="imageViewerModal" tabindex="-1" aria-labelledby="imageViewerModalLabel" aria-hidden="true">
                <div class="modal-dialog modal-xl modal-dialog-centered">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h5 class="modal-title" id="imageViewerModalLabel">Image Viewer</h5>
                            <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                        </div>
                        <div class="modal-body p-0">
                            <div class="image-viewer-container">
                                <div class="image-viewer-toolbar">
                                    <div class="btn-group" role="group" aria-label="Image viewer controls">
                                        <button type="button" class="btn btn-outline-secondary" id="zoomOut" aria-label="Zoom out" title="Zoom out (-)">
                                            <i class="fas fa-search-minus" aria-hidden="true"></i>
                                        </button>
                                        <button type="button" class="btn btn-outline-secondary" id="zoomIn" aria-label="Zoom in" title="Zoom in (+)">
                                            <i class="fas fa-search-plus" aria-hidden="true"></i>
                                        </button>
                                        <button type="button" class="btn btn-outline-secondary" id="resetZoom" aria-label="Reset zoom" title="Reset zoom (0)">
                                            <i class="fas fa-expand-arrows-alt" aria-hidden="true"></i>
                                        </button>
                                        <button type="button" class="btn btn-outline-secondary" id="rotateLeft" aria-label="Rotate left" title="Rotate left (L)">
                                            <i class="fas fa-undo" aria-hidden="true"></i>
                                        </button>
                                        <button type="button" class="btn btn-outline-secondary" id="rotateRight" aria-label="Rotate right" title="Rotate right (R)">
                                            <i class="fas fa-redo" aria-hidden="true"></i>
                                        </button>
                                        <button type="button" class="btn btn-outline-secondary" id="downloadImage" aria-label="Download image" title="Download image (D)">
                                            <i class="fas fa-download" aria-hidden="true"></i>
                                        </button>
                                    </div>
                                    <div class="image-info">
                                        <span id="imageCounter">1 of 1</span>
                                    </div>
                                </div>
                                <div class="image-viewer-content" role="img" aria-live="polite">
                                    <div class="image-container">
                                        <img id="viewerImage" src="" alt="Image" role="img" />
                                        <div class="image-loading" aria-label="Loading image">
                                            <div class="spinner-border" role="status">
                                                <span class="visually-hidden">Loading...</span>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="navigation-buttons">
                                        <button type="button" class="btn btn-primary btn-nav" id="prevImage" aria-label="Previous image" title="Previous image (←)">
                                            <i class="fas fa-chevron-left" aria-hidden="true"></i>
                                        </button>
                                        <button type="button" class="btn btn-primary btn-nav" id="nextImage" aria-label="Next image" title="Next image (→)">
                                            <i class="fas fa-chevron-right" aria-hidden="true"></i>
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        `;
        
        $('body').append(modalHtml);
        
        // Add CSS
        const css = `
            <style>
                .image-viewer-container {
                    position: relative;
                    height: 80vh;
                    background: #000;
                }
                
                .image-viewer-toolbar {
                    position: absolute;
                    top: 10px;
                    left: 50%;
                    transform: translateX(-50%);
                    z-index: 1000;
                    background: rgba(0, 0, 0, 0.7);
                    border-radius: 5px;
                    padding: 10px;
                    display: flex;
                    justify-content: space-between;
                    align-items: center;
                    min-width: 300px;
                }
                
                .image-viewer-toolbar .btn {
                    color: white;
                    border-color: rgba(255, 255, 255, 0.3);
                }
                
                .image-viewer-toolbar .btn:hover {
                    background-color: rgba(255, 255, 255, 0.1);
                    border-color: rgba(255, 255, 255, 0.5);
                    color: white;
                }
                
                .image-info {
                    color: white;
                    font-size: 14px;
                }
                
                .image-viewer-content {
                    position: relative;
                    width: 100%;
                    height: 100%;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                }
                
                .image-container {
                    position: relative;
                    max-width: 100%;
                    max-height: 100%;
                    overflow: hidden;
                    cursor: grab;
                }
                
                .image-container:active {
                    cursor: grabbing;
                }
                
                #viewerImage {
                    max-width: 100%;
                    max-height: 100%;
                    object-fit: contain;
                    transition: transform 0.3s ease;
                    user-select: none;
                    -webkit-user-drag: none;
                }
                
                .image-loading {
                    position: absolute;
                    top: 50%;
                    left: 50%;
                    transform: translate(-50%, -50%);
                    color: white;
                }
                
                .navigation-buttons {
                    position: absolute;
                    top: 50%;
                    width: 100%;
                    display: flex;
                    justify-content: space-between;
                    padding: 0 20px;
                    pointer-events: none;
                }
                
                .btn-nav {
                    pointer-events: all;
                    border-radius: 50%;
                    width: 50px;
                    height: 50px;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    opacity: 0.7;
                    transition: opacity 0.3s ease;
                }
                
                .btn-nav:hover {
                    opacity: 1;
                }
                
                .btn-nav:disabled {
                    opacity: 0.3;
                }
            </style>
        `;
        
        $('head').append(css);
    }
    
    bindEvents() {
        const self = this;
        
        // Zoom controls
        $('#zoomIn').click(() => this.zoom(1.2));
        $('#zoomOut').click(() => this.zoom(0.8));
        $('#resetZoom').click(() => this.resetZoom());
        
        // Rotation controls
        $('#rotateLeft').click(() => this.rotate(-90));
        $('#rotateRight').click(() => this.rotate(90));
        
        // Navigation
        $('#prevImage').click(() => this.previousImage());
        $('#nextImage').click(() => this.nextImage());
        
        // Download
        $('#downloadImage').click(() => this.downloadImage());
        
        // Keyboard controls
        $(document).on('keydown', function(e) {
            if ($('#imageViewerModal').hasClass('show')) {
                // Prevent default for handled keys
                const handledKeys = ['ArrowLeft', 'ArrowRight', '+', '=', '-', '0', 'l', 'L', 'r', 'R', 'd', 'D', 'Escape'];
                if (handledKeys.includes(e.key)) {
                    e.preventDefault();
                }

                switch(e.key) {
                    case 'ArrowLeft':
                        self.previousImage();
                        break;
                    case 'ArrowRight':
                        self.nextImage();
                        break;
                    case '+':
                    case '=':
                        self.zoom(1.2);
                        break;
                    case '-':
                        self.zoom(0.8);
                        break;
                    case '0':
                        self.resetZoom();
                        break;
                    case 'l':
                    case 'L':
                        self.rotateLeft();
                        break;
                    case 'r':
                    case 'R':
                        self.rotateRight();
                        break;
                    case 'd':
                    case 'D':
                        self.downloadImage();
                        break;
                    case 'Escape':
                        $('#imageViewerModal').modal('hide');
                        break;
                }
            }
        });
        
        // Mouse wheel zoom
        $('#viewerImage').on('wheel', function(e) {
            e.preventDefault();
            const delta = e.originalEvent.deltaY;
            if (delta > 0) {
                self.zoom(0.9);
            } else {
                self.zoom(1.1);
            }
        });
        
        // Pan functionality
        let isPanning = false;
        let startX, startY, initialX, initialY;
        
        $('#viewerImage').on('mousedown', function(e) {
            isPanning = true;
            startX = e.clientX;
            startY = e.clientY;
            const transform = $(this).css('transform');
            if (transform !== 'none') {
                const matrix = transform.match(/matrix\((.+)\)/);
                if (matrix) {
                    const values = matrix[1].split(', ');
                    initialX = parseFloat(values[4]) || 0;
                    initialY = parseFloat(values[5]) || 0;
                }
            } else {
                initialX = 0;
                initialY = 0;
            }
        });
        
        $(document).on('mousemove', function(e) {
            if (isPanning) {
                const deltaX = e.clientX - startX;
                const deltaY = e.clientY - startY;
                const newX = initialX + deltaX;
                const newY = initialY + deltaY;
                
                const currentTransform = $('#viewerImage').css('transform');
                let scale = 1, rotation = 0;
                
                if (currentTransform !== 'none') {
                    const matrix = currentTransform.match(/matrix\((.+)\)/);
                    if (matrix) {
                        const values = matrix[1].split(', ');
                        scale = Math.sqrt(values[0] * values[0] + values[1] * values[1]);
                        rotation = Math.atan2(values[1], values[0]) * (180 / Math.PI);
                    }
                }
                
                $('#viewerImage').css('transform', 
                    `translate(${newX}px, ${newY}px) scale(${scale}) rotate(${rotation}deg)`);
            }
        });
        
        $(document).on('mouseup', function() {
            isPanning = false;
        });
        
        // Image load event
        $('#viewerImage').on('load', function() {
            $('.image-loading').hide();
            $(this).show();
        });
        
        $('#viewerImage').on('error', function() {
            $('.image-loading').hide();
            this.src = 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjAwIiBoZWlnaHQ9IjIwMCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48cmVjdCB3aWR0aD0iMTAwJSIgaGVpZ2h0PSIxMDAlIiBmaWxsPSIjZGRkIi8+PHRleHQgeD0iNTAlIiB5PSI1MCUiIGZvbnQtZmFtaWx5PSJBcmlhbCwgc2Fucy1zZXJpZiIgZm9udC1zaXplPSIxNCIgZmlsbD0iIzk5OSIgdGV4dC1hbmNob3I9Im1pZGRsZSIgZHk9Ii4zZW0iPkVycm9yIGxvYWRpbmcgaW1hZ2U8L3RleHQ+PC9zdmc+';
            $(this).show();
        });

        // Bootstrap modal event handlers for proper accessibility management
        $('#imageViewerModal').on('show.bs.modal', function() {
            // Remove aria-hidden when modal is about to show
            $(this).removeAttr('aria-hidden');
        });

        $('#imageViewerModal').on('shown.bs.modal', function() {
            // Focus management - focus the close button for keyboard navigation
            $(this).find('.btn-close').focus();
        });

        $('#imageViewerModal').on('hide.bs.modal', function() {
            // Reset any transformations when modal is hidden
            $('#viewerImage').css('transform', '');
        });

        $('#imageViewerModal').on('hidden.bs.modal', function() {
            // Restore aria-hidden when modal is completely hidden
            $(this).attr('aria-hidden', 'true');
        });
    }
    
    showImage(imageSrc, images = [], currentIndex = 0) {
        this.images = images;
        this.currentIndex = currentIndex;

        // Check if modal exists, if not create a simple fallback
        if ($('#imageViewerModal').length === 0) {
            // Fallback: open image in new window
            window.open(imageSrc, '_blank');
            return;
        }

        this.loadImage(imageSrc);
        this.updateNavigation();

        $('#imageViewerModal').modal('show');
    }
    
    loadImage(src) {
        $('.image-loading').show();
        $('#viewerImage').hide();

        // Check if src is valid
        if (!src) {
            $('.image-loading').hide();
            return;
        }

        // Update alt text with more descriptive information
        const filename = src.split('/').pop();
        $('#viewerImage').attr('alt', `Image: ${filename}`);

        $('#viewerImage')[0].src = src;
        this.resetZoom();
    }
    
    zoom(factor) {
        const img = $('#viewerImage');
        const currentTransform = img.css('transform');
        let scale = 1, translateX = 0, translateY = 0, rotation = 0;
        
        if (currentTransform !== 'none') {
            const matrix = currentTransform.match(/matrix\((.+)\)/);
            if (matrix) {
                const values = matrix[1].split(', ');
                scale = Math.sqrt(values[0] * values[0] + values[1] * values[1]);
                translateX = parseFloat(values[4]) || 0;
                translateY = parseFloat(values[5]) || 0;
                rotation = Math.atan2(values[1], values[0]) * (180 / Math.PI);
            }
        }
        
        const newScale = Math.max(0.1, Math.min(5, scale * factor));
        img.css('transform', 
            `translate(${translateX}px, ${translateY}px) scale(${newScale}) rotate(${rotation}deg)`);
    }
    
    resetZoom() {
        $('#viewerImage').css('transform', 'translate(0px, 0px) scale(1) rotate(0deg)');
    }
    
    rotate(degrees) {
        const img = $('#viewerImage');
        const currentTransform = img.css('transform');
        let scale = 1, translateX = 0, translateY = 0, rotation = 0;
        
        if (currentTransform !== 'none') {
            const matrix = currentTransform.match(/matrix\((.+)\)/);
            if (matrix) {
                const values = matrix[1].split(', ');
                scale = Math.sqrt(values[0] * values[0] + values[1] * values[1]);
                translateX = parseFloat(values[4]) || 0;
                translateY = parseFloat(values[5]) || 0;
                rotation = Math.atan2(values[1], values[0]) * (180 / Math.PI);
            }
        }
        
        const newRotation = rotation + degrees;
        img.css('transform', 
            `translate(${translateX}px, ${translateY}px) scale(${scale}) rotate(${newRotation}deg)`);
    }
    
    previousImage() {
        if (this.images.length > 1 && this.currentIndex > 0) {
            this.currentIndex--;
            this.loadImage(this.images[this.currentIndex]);
            this.updateNavigation();
        }
    }
    
    nextImage() {
        if (this.images.length > 1 && this.currentIndex < this.images.length - 1) {
            this.currentIndex++;
            this.loadImage(this.images[this.currentIndex]);
            this.updateNavigation();
        }
    }
    
    updateNavigation() {
        $('#imageCounter').text(`${this.currentIndex + 1} of ${this.images.length}`);
        
        $('#prevImage').prop('disabled', this.currentIndex === 0);
        $('#nextImage').prop('disabled', this.currentIndex === this.images.length - 1);
        
        if (this.images.length <= 1) {
            $('.navigation-buttons').hide();
        } else {
            $('.navigation-buttons').show();
        }
    }
    
    downloadImage() {
        const img = $('#viewerImage');
        const src = img[0].src;
        
        if (src) {
            const link = document.createElement('a');
            link.href = src;
            link.download = 'image';
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);
        }
    }
}

// Initialize image viewer when document is ready
$(document).ready(function() {
    window.imageViewer = new ImageViewer();
    
    // Bind click events to image thumbnails
    $(document).on('click', '.image-thumbnail', function(e) {
        e.preventDefault();
        const src = $(this).data('src') || $(this).attr('href');
        const images = [];
        let currentIndex = 0;

        // Collect all images in the same container
        const $container = $(this).closest('.images-container');
        if ($container.length > 0) {
            $container.find('.image-thumbnail').each(function(index) {
                const imgSrc = $(this).data('src') || $(this).attr('href');
                images.push(imgSrc);
                if (this === e.currentTarget) {
                    currentIndex = index;
                }
            });
        } else {
            // If no container found, just use this single image
            images.push(src);
            currentIndex = 0;
        }

        window.imageViewer.showImage(src, images, currentIndex);
    });
});
