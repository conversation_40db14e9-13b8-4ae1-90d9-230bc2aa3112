/**
 * Batch Operations JavaScript
 * Handles multi-select functionality for verification and review
 */

class BatchOperations {
    constructor(operationType) {
        this.operationType = operationType; // 'verification' or 'review'
        this.selectedExpenses = new Map();
        this.currentPage = 1;
        this.totalPages = 1;
        this.isLoading = false;
        
        this.init();
    }
    
    init() {
        this.bindEvents();
        this.loadExpenses();
    }
    
    bindEvents() {
        // Search button
        $('#searchBtn').on('click', () => {
            this.currentPage = 1;
            this.loadExpenses();
        });
        
        // Select all checkbox
        $('#selectAll').on('change', (e) => {
            this.handleSelectAll(e.target.checked);
        });
        
        // Create batch button
        $('#createBatchBtn').on('click', () => {
            this.createBatch();
        });
        
        // Search on Enter key
        $('#searchInput').on('keypress', (e) => {
            if (e.which === 13) {
                $('#searchBtn').click();
            }
        });
        
        // Filter changes
        $('#dateFrom, #dateTo, #amountMin, #amountMax').on('change', () => {
            this.currentPage = 1;
            this.loadExpenses();
        });
    }
    
    loadExpenses() {
        if (this.isLoading) return;
        
        this.isLoading = true;
        this.showLoading(true);
        
        const params = new URLSearchParams({
            operation_type: this.operationType,
            page: this.currentPage,
            limit: 20,
            search: $('#searchInput').val(),
            date_from: $('#dateFrom').val(),
            date_to: $('#dateTo').val(),
            amount_min: $('#amountMin').val(),
            amount_max: $('#amountMax').val()
        });
        
        $.ajax({
            url: '../api/get_expenses_for_batch.php?' + params.toString(),
            method: 'GET',
            success: (response) => {
                this.isLoading = false;
                this.showLoading(false);
                
                if (response.success) {
                    this.renderExpensesTable(response.data.expenses);
                    this.renderPagination(response.data.pagination);
                    this.updateSummaryInfo(response.data.summary);
                } else {
                    this.showError('Error: ' + response.error);
                }
            },
            error: (xhr) => {
                this.isLoading = false;
                this.showLoading(false);
                this.showError('Error loading expenses: ' + (xhr.responseJSON?.error || 'Unknown error'));
            }
        });
    }
    
    renderExpensesTable(expenses) {
        const tbody = $('#expensesTableBody');
        tbody.empty();
        
        if (expenses.length === 0) {
            tbody.append(`
                <tr>
                    <td colspan="9" class="text-center py-4">
                        <i class="fas fa-inbox fa-2x text-muted mb-2"></i>
                        <div>No expenses found for ${this.operationType}</div>
                    </td>
                </tr>
            `);
            return;
        }
        
        expenses.forEach(expense => {
            const isSelected = this.selectedExpenses.has(expense.id);
            const amountField = this.operationType === 'verification' ? 'transfer_amount' : 'verification_amount';
            const verifiedByField = this.operationType === 'review' ? 'verified_by_name' : 'created_by_name';
            
            let extraInfo = '';
            if (this.operationType === 'review') {
                extraInfo = `
                    <div class="verification-info">
                        <small><i class="fas fa-check-circle text-primary"></i> Verified: ${parseFloat(expense.verification_amount).toLocaleString('th-TH', {minimumFractionDigits: 2})} บาท</small>
                    </div>
                `;
            }
            
            const row = $(`
                <tr class="expense-row ${isSelected ? 'selected' : ''}" data-id="${expense.id}">
                    <td>
                        <input type="checkbox" class="form-check-input expense-checkbox" 
                               ${isSelected ? 'checked' : ''} data-expense='${JSON.stringify(expense)}'>
                    </td>
                    <td>
                        <strong>${expense.exno}</strong>
                    </td>
                    <td>${new Date(expense.expense_date).toLocaleDateString('th-TH')}</td>
                    <td>${expense.customer_name || '-'}</td>
                    <td>
                        <div class="text-truncate" style="max-width: 200px;" title="${expense.description}">
                            ${expense.description}
                        </div>
                        ${extraInfo}
                    </td>
                    <td>
                        <strong class="text-${this.operationType === 'verification' ? 'primary' : 'success'}">
                            ${parseFloat(expense[amountField]).toLocaleString('th-TH', {minimumFractionDigits: 2})} บาท
                        </strong>
                    </td>
                    ${this.operationType === 'review' ? `<td><small>${expense[verifiedByField] || '-'}</small></td>` : ''}
                    <td>
                        <span class="badge bg-${expense.status === 'open' ? 'warning' : 'info'}">${expense.status}</span>
                    </td>
                    <td>
                        ${this.operationType === 'verification' ? `<td>${expense[verifiedByField] || '-'}</td>` : ''}
                        <a href="view.php?id=${expense.id}" class="btn btn-sm btn-outline-${this.operationType === 'verification' ? 'primary' : 'success'}" title="View Details">
                            <i class="fas fa-eye"></i>
                        </a>
                    </td>
                </tr>
            `);
            
            tbody.append(row);
        });
        
        // Add event listeners for checkboxes
        $('.expense-checkbox').on('change', (e) => {
            this.handleExpenseSelection(e);
        });
    }
    
    handleSelectAll(isChecked) {
        $('.expense-checkbox').prop('checked', isChecked);
        
        if (isChecked) {
            $('.expense-checkbox').each((index, element) => {
                const expenseData = $(element).data('expense');
                this.selectedExpenses.set(expenseData.id, expenseData);
            });
        } else {
            this.selectedExpenses.clear();
        }
        
        this.updateSelectedSummary();
        this.updateRowSelection();
    }
    
    handleExpenseSelection(event) {
        const checkbox = $(event.target);
        const expenseData = checkbox.data('expense');
        const row = checkbox.closest('tr');
        
        if (checkbox.is(':checked')) {
            this.selectedExpenses.set(expenseData.id, expenseData);
            row.addClass('selected');
        } else {
            this.selectedExpenses.delete(expenseData.id);
            row.removeClass('selected');
        }
        
        this.updateSelectedSummary();
        this.updateSelectAllCheckbox();
    }
    
    updateSelectedSummary() {
        const count = this.selectedExpenses.size;
        let totalAmount = 0;
        let minDate = null;
        let maxDate = null;
        
        this.selectedExpenses.forEach(expense => {
            const amountField = this.operationType === 'verification' ? 'transfer_amount' : 'verification_amount';
            totalAmount += parseFloat(expense[amountField]);
            const expenseDate = new Date(expense.expense_date);
            
            if (!minDate || expenseDate < minDate) minDate = expenseDate;
            if (!maxDate || expenseDate > maxDate) maxDate = expenseDate;
        });
        
        $('#selectedCount').text(count);
        $('#selectedAmount').text(totalAmount.toLocaleString('th-TH', {minimumFractionDigits: 2}));
        
        if (count > 0) {
            const dateRangeText = minDate && maxDate ? 
                (minDate.getTime() === maxDate.getTime() ? 
                    minDate.toLocaleDateString('th-TH') : 
                    `${minDate.toLocaleDateString('th-TH')} - ${maxDate.toLocaleDateString('th-TH')}`) : '-';
            $('#dateRange').text(dateRangeText);
            $('#selectedSummary').show();
        } else {
            $('#selectedSummary').hide();
        }
    }
    
    updateSelectAllCheckbox() {
        const totalCheckboxes = $('.expense-checkbox').length;
        const checkedCheckboxes = $('.expense-checkbox:checked').length;
        
        $('#selectAll').prop('indeterminate', checkedCheckboxes > 0 && checkedCheckboxes < totalCheckboxes);
        $('#selectAll').prop('checked', checkedCheckboxes === totalCheckboxes && totalCheckboxes > 0);
    }
    
    updateRowSelection() {
        $('.expense-row').each((index, row) => {
            const expenseId = parseInt($(row).data('id'));
            if (this.selectedExpenses.has(expenseId)) {
                $(row).addClass('selected');
            } else {
                $(row).removeClass('selected');
            }
        });
    }
    
    renderPagination(pagination) {
        this.currentPage = pagination.current_page;
        this.totalPages = pagination.total_pages;
        
        $('#paginationInfo').html(`
            Showing ${((this.currentPage - 1) * pagination.limit) + 1} to ${Math.min(this.currentPage * pagination.limit, pagination.total_records)} 
            of ${pagination.total_records} entries
        `);
        
        const nav = $('#paginationNav');
        nav.empty();
        
        // Previous button
        nav.append(`
            <li class="page-item ${!pagination.has_prev ? 'disabled' : ''}">
                <a class="page-link" href="#" data-page="${this.currentPage - 1}">Previous</a>
            </li>
        `);
        
        // Page numbers
        const startPage = Math.max(1, this.currentPage - 2);
        const endPage = Math.min(this.totalPages, this.currentPage + 2);
        
        for (let i = startPage; i <= endPage; i++) {
            nav.append(`
                <li class="page-item ${i === this.currentPage ? 'active' : ''}">
                    <a class="page-link" href="#" data-page="${i}">${i}</a>
                </li>
            `);
        }
        
        // Next button
        nav.append(`
            <li class="page-item ${!pagination.has_next ? 'disabled' : ''}">
                <a class="page-link" href="#" data-page="${this.currentPage + 1}">Next</a>
            </li>
        `);
        
        // Add click handlers
        $('.page-link').on('click', (e) => {
            e.preventDefault();
            const page = parseInt($(e.target).data('page'));
            if (page && page !== this.currentPage && page >= 1 && page <= this.totalPages) {
                this.currentPage = page;
                this.loadExpenses();
            }
        });
    }
    
    updateSummaryInfo(summary) {
        // Can be extended for additional summary information
    }
    
    createBatch() {
        if (this.selectedExpenses.size === 0) {
            this.showError('Please select at least one expense');
            return;
        }
        
        const expenseIds = Array.from(this.selectedExpenses.keys());
        const amountField = this.operationType === 'verification' ? 'transfer_amount' : 'verification_amount';
        const totalAmount = Array.from(this.selectedExpenses.values())
            .reduce((sum, expense) => sum + parseFloat(expense[amountField]), 0);
        
        const formData = new FormData();
        formData.append('action', 'create_batch');
        formData.append('expense_ids', JSON.stringify(expenseIds));
        formData.append('total_amount', totalAmount.toFixed(2));
        formData.append('notes', `Batch ${this.operationType} for ${expenseIds.length} expenses`);
        
        this.showLoading(true);
        
        $.ajax({
            url: `../api/batch_${this.operationType}.php`,
            method: 'POST',
            data: formData,
            processData: false,
            contentType: false,
            success: (response) => {
                this.showLoading(false);
                
                if (response.success) {
                    this.showSuccess(`Batch created successfully!\nBatch ID: ${response.batch_id}\n\nRedirecting to batch processing...`);
                    setTimeout(() => {
                        window.location.href = `batch_process.php?batch_id=${response.batch_id}&type=${this.operationType}`;
                    }, 2000);
                } else {
                    this.showError('Error: ' + response.error);
                }
            },
            error: (xhr) => {
                this.showLoading(false);
                this.showError('Error creating batch: ' + (xhr.responseJSON?.error || 'Unknown error'));
            }
        });
    }
    
    showLoading(show) {
        if (show) {
            $('#loadingOverlay').show();
        } else {
            $('#loadingOverlay').hide();
        }
    }
    
    showError(message) {
        alert('Error: ' + message);
    }
    
    showSuccess(message) {
        alert(message);
    }
}

// Export for use in other files
window.BatchOperations = BatchOperations;
