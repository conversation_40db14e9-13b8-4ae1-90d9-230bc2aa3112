/**
 * Per-Receipt Deductions Management
 * Handles deductions for individual receipts with image upload support
 */

// Global variables for per-receipt deductions
window.receiptDeductions = {}; // Object with receipt index as key
window.receiptFiles = []; // Array to store receipt file info
window.currentDeductionIndex = -1;
window.currentReceiptIndex = -1;

// Show receipt cards container when receipts are uploaded
function showReceiptCardsContainer() {
    $('#receipt-cards-container').removeClass('d-none');
    createReceiptCards();
}

// Hide receipt cards container when no receipts
function hideReceiptCardsContainer() {
    $('#receipt-cards-container').addClass('d-none');
    window.receiptDeductions = {};
    window.receiptFiles = [];
    updateAllCalculations();
}

// Create receipt cards
function createReceiptCards() {
    const container = $('#receipt-cards-list');
    container.empty();
    
    $('.receipt-number-input').each(function(index) {
        const receiptNumber = $(this).val() || `Receipt ${index + 1}`;
        const receiptAmount = parseFloat($('.receipt-amount-input').eq(index).val()) || 0;
        const receiptDescription = $('.receipt-description-input').eq(index).val() || '';
        
        // Initialize deductions array for this receipt if not exists
        if (!window.receiptDeductions[index]) {
            window.receiptDeductions[index] = [];
        }
        
        const card = createReceiptCard(index, receiptNumber, receiptAmount, receiptDescription);
        container.append(card);
    });
    
    updateAllCalculations();
}

// Create individual receipt card
function createReceiptCard(index, receiptNumber, amount, description) {
    const deductions = window.receiptDeductions[index] || [];
    const totalDeductions = deductions.reduce((sum, d) => sum + d.amount, 0);
    const netAmount = amount - totalDeductions;
    
    return `
        <div class="card mb-3 receipt-card" data-receipt-index="${index}">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h6 class="mb-0">
                    📄 ${receiptNumber} 
                    <span class="badge bg-secondary">${amount.toLocaleString()} บาท</span>
                </h6>
                <button type="button" class="btn btn-sm btn-outline-primary" 
                        onclick="showAddDeductionModal(${index})">
                    + เพิ่มรายการหัก
                </button>
            </div>
            <div class="card-body">
                ${description ? `<p class="text-muted mb-2">${description}</p>` : ''}
                
                <!-- Deductions List -->
                <div class="deductions-list" id="deductions-list-${index}">
                    ${renderDeductionsForReceipt(index)}
                </div>
                
                <!-- Net Amount -->
                <div class="row mt-3 pt-2 border-top">
                    <div class="col-6">
                        <strong>ยอดก่อนหัก:</strong> ${amount.toLocaleString()} บาท
                    </div>
                    <div class="col-6 text-end">
                        <strong class="text-primary">ยอดสุทธิ: 
                            <span id="net-amount-${index}">${netAmount.toLocaleString()}</span> บาท
                        </strong>
                    </div>
                </div>
                
                ${totalDeductions > 0 ? `
                <div class="row">
                    <div class="col-6">
                        <span class="text-danger">รายการหัก:</span> ${totalDeductions.toLocaleString()} บาท
                    </div>
                </div>
                ` : ''}
            </div>
        </div>
    `;
}

// Render deductions for specific receipt
function renderDeductionsForReceipt(receiptIndex) {
    const deductions = window.receiptDeductions[receiptIndex] || [];
    
    if (deductions.length === 0) {
        return '<p class="text-muted mb-0"><em>ไม่มีรายการหัก</em></p>';
    }
    
    return deductions.map((deduction, index) => `
        <div class="deduction-item border rounded p-2 mb-2">
            <div class="row align-items-center">
                <div class="col-md-6">
                    <strong>${deduction.typeName}</strong><br>
                    <small class="text-muted">${deduction.description}</small>
                </div>
                <div class="col-md-3 text-center">
                    <span class="badge bg-danger">${deduction.amount.toLocaleString()} บาท</span>
                    ${deduction.isPercentageBased ? `<br><small>(${deduction.percentage}%)</small>` : ''}
                </div>
                <div class="col-md-3 text-end">
                    ${deduction.image ? `
                        <button type="button" class="btn btn-sm btn-outline-info me-1" 
                                onclick="viewDeductionImage('${deduction.image}')">
                            🖼️ ดู
                        </button>
                    ` : ''}
                    <button type="button" class="btn btn-sm btn-outline-warning me-1" 
                            onclick="editDeduction(${receiptIndex}, ${index})">
                        ✏️
                    </button>
                    <button type="button" class="btn btn-sm btn-outline-danger" 
                            onclick="deleteDeduction(${receiptIndex}, ${index})">
                        🗑️
                    </button>
                </div>
            </div>
        </div>
    `).join('');
}

// Show add deduction modal for specific receipt
function showAddDeductionModal(receiptIndex) {
    window.currentReceiptIndex = receiptIndex;
    resetDeductionForm();
    $('#deduction-form').data('mode', 'add');
    $('#target_receipt_index').val(receiptIndex);
    $('#deductionModalTitle').text(`เพิ่มรายการหัก - Receipt ${receiptIndex + 1}`);
    $('#deductionModal').modal('show');
}

// Show edit deduction modal
function editDeduction(receiptIndex, deductionIndex) {
    const deduction = window.receiptDeductions[receiptIndex][deductionIndex];
    if (!deduction) return;

    window.currentReceiptIndex = receiptIndex;
    window.currentDeductionIndex = deductionIndex;

    // Populate form
    $('#deduction_type').val(deduction.type);
    $('#deduction_amount').val(deduction.amount);
    $('#deduction_percentage').val(deduction.percentage || '');
    $('#deduction_description').val(deduction.description || '');
    $('#is_percentage_based').prop('checked', deduction.isPercentageBased || false);
    $('#target_receipt_index').val(receiptIndex);

    // Show image preview if exists
    if (deduction.image) {
        $('#deduction_image_preview img').attr('src', deduction.imageUrl || `uploads/deductions/${deduction.image}`);
        $('#deduction_image_preview').show();
    }

    // Toggle fields
    togglePercentageFields(deduction.isPercentageBased || false);

    // Set modal mode
    $('#deduction-form').data('mode', 'edit');
    $('#deductionModalTitle').text(`แก้ไขรายการหัก - Receipt ${receiptIndex + 1}`);
    $('#deductionModal').modal('show');
}

// Delete deduction
function deleteDeduction(receiptIndex, deductionIndex) {
    if (confirm('คุณต้องการลบรายการหักนี้หรือไม่?')) {
        window.receiptDeductions[receiptIndex].splice(deductionIndex, 1);
        
        // Re-render the specific receipt card
        const receiptNumber = $('.receipt-number-input').eq(receiptIndex).val() || `Receipt ${receiptIndex + 1}`;
        const receiptAmount = parseFloat($('.receipt-amount-input').eq(receiptIndex).val()) || 0;
        const receiptDescription = $('.receipt-description-input').eq(receiptIndex).val() || '';
        
        const newCard = createReceiptCard(receiptIndex, receiptNumber, receiptAmount, receiptDescription);
        $(`.receipt-card[data-receipt-index="${receiptIndex}"]`).replaceWith(newCard);
        
        updateAllCalculations();
    }
}

// View deduction image
function viewDeductionImage(imageName) {
    const imageUrl = `uploads/deductions/${imageName}`;
    window.open(imageUrl, '_blank');
}

// Calculate all amounts
function updateAllCalculations() {
    let totalGross = 0;
    let totalDeductions = 0;
    let totalNet = 0;

    $('.receipt-amount').each(function(index) {
        const grossAmount = parseFloat($(this).val()) || 0;
        const deductions = window.receiptDeductions[index] || [];
        const deductionAmount = deductions.reduce((sum, d) => sum + d.amount, 0);
        const netAmount = grossAmount - deductionAmount;

        totalGross += grossAmount;
        totalDeductions += deductionAmount;
        totalNet += netAmount;

        // Update individual receipt net amount display
        updateReceiptNetAmount(index);
    });

    // Update summary displays
    $('#total-gross-amount').text(totalGross.toLocaleString('th-TH', {
        minimumFractionDigits: 2,
        maximumFractionDigits: 2
    }));
    $('#total-deductions-amount').text(totalDeductions.toLocaleString('th-TH', {
        minimumFractionDigits: 2,
        maximumFractionDigits: 2
    }));
    $('#total-net-amount').text(totalNet.toLocaleString('th-TH', {
        minimumFractionDigits: 2,
        maximumFractionDigits: 2
    }));

    // Update display-receipt-total to match total-net-amount
    $('#display-receipt-total').text(totalNet.toFixed(2));

    // Force update the display values immediately
    setTimeout(function() {
        // Call compareAmounts after updating display-receipt-total
        if (typeof compareAmounts === 'function') {
            compareAmounts();
        }
    }, 10);

    // Validate against transfer amount
    const transferAmount = parseFloat($('#transfer_amount').val()) || 0;
    $('#transfer-amount-display').text(transferAmount.toLocaleString('th-TH', {
        minimumFractionDigits: 2,
        maximumFractionDigits: 2
    }));

    validateTotalAmount(totalNet, transferAmount);
}

// Validate total amount
function validateTotalAmount(netTotal, transferAmount) {
    const difference = Math.abs(netTotal - transferAmount);
    const isValid = difference < 0.01;
    const statusElement = $('#amount-validation-status');

    if (transferAmount === 0) {
        statusElement.html('');
        return;
    }

    if (isValid) {
        statusElement.html('<small class="text-success">✅ ยอดตรงต้อง</small>');
    } else {
        statusElement.html(`
            <small class="text-danger">
                ❌ ยอดไม่ตรงกัน<br>
                ต่างกัน: ${difference.toLocaleString()} บาท
            </small>
        `);
    }
}

// Reset deduction form
function resetDeductionForm() {
    $('#deduction-form')[0].reset();
    $('#deduction-form').removeData('mode').removeData('index');

    // Clear image preview completely
    $('#deduction_image_preview').hide();
    $('#deduction_image_preview img').attr('src', '');
    $('#deduction_image').removeData('uploaded-filename').removeData('uploaded-url');

    // Clear image upload status
    $('#image-upload-status').empty();
    $('#deduction_image').val('');

    // Reset variables
    window.currentDeductionIndex = -1;
    window.currentReceiptIndex = -1;

    togglePercentageFields(false);
    clearDeductionFormErrors();
}

// Toggle percentage fields
function togglePercentageFields(isPercentageBased) {
    if (isPercentageBased) {
        // Support both field naming conventions
        $('#percentage-input, #percentage-field').show();
        $('#amount-input, #amount-field').hide();
        $('#deduction_amount').removeAttr('required');
        $('#deduction_percentage').attr('required', true);
    } else {
        $('#percentage-input, #percentage-field').hide();
        $('#amount-input, #amount-field').show();
        $('#deduction_percentage').removeAttr('required');
        $('#deduction_amount').attr('required', true);
    }
}

// Clear form errors
function clearDeductionFormErrors() {
    $('#deduction-form-errors').empty();
}

// Show form error
function showDeductionFormError(message) {
    $('#deduction-form-errors').html(`
        <div class="alert alert-danger alert-dismissible fade show" role="alert">
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    `);
}

// Calculate amount from percentage
function calculateAmountFromPercentage() {
    const percentage = parseFloat($('#deduction_percentage').val()) || 0;
    const receiptIndex = parseInt($('#target_receipt_index').val()) || 0;
    const grossAmount = parseFloat($('.receipt-amount-input').eq(receiptIndex).val()) || 0;
    const calculatedAmount = (grossAmount * percentage / 100);

    $('#calculated-amount-display').text(calculatedAmount.toLocaleString() + ' บาท');
    return calculatedAmount;
}

// Document ready functions
$(document).ready(function() {

    // Handle percentage checkbox change
    $('#is_percentage_based').change(function() {
        togglePercentageFields($(this).is(':checked'));
    });

    // Handle percentage input change
    $('#deduction_percentage').on('input', function() {
        if ($('#is_percentage_based').is(':checked')) {
            calculateAmountFromPercentage();
        }
    });

    // Handle deduction image upload
    $('#deduction_image').on('change', function() {
        const file = this.files[0];
        if (!file) return;

        // Show preview
        const reader = new FileReader();
        reader.onload = function(e) {
            $('#deduction_image_preview img').attr('src', e.target.result);
            $('#deduction_image_preview').show();
        };
        reader.readAsDataURL(file);

        // Upload file
        const formData = new FormData();
        formData.append('deduction_image', file);

        // Show uploading status
        $('#image-upload-status').html(`
            <div class="alert alert-info">
                <i class="fas fa-spinner fa-spin"></i> กำลังอัพโหลด...
            </div>
        `);

        // Determine correct API path based on current location
        const apiPath = window.location.pathname.includes('/expenses/')
            ? '../api/upload_deduction_image.php'
            : 'api/upload_deduction_image.php';

        $.ajax({
            url: apiPath,
            type: 'POST',
            data: formData,
            processData: false,
            contentType: false,
            success: function(response) {
                if (response.success) {
                    // Store filename for later use
                    $('#deduction_image').data('uploaded-filename', response.filename);
                    $('#deduction_image').data('uploaded-url', response.url);

                    // Show success message
                    $('#image-upload-status').html(`
                        <div class="alert alert-success">
                            <i class="fas fa-check"></i> อัพโหลดสำเร็จ: ${response.original_name}
                            <br><small>ไฟล์: ${response.filename}</small>
                        </div>
                    `);
                } else {
                    $('#image-upload-status').html(`
                        <div class="alert alert-danger">
                            <i class="fas fa-times"></i> อัพโหลดล้มเหลว: ${response.error}
                        </div>
                    `);
                    showDeductionFormError('การอัพโหลดรูปล้มเหลว: ' + response.error);
                }
            },
            error: function(xhr, status, error) {
                let errorMessage = 'เกิดข้อผิดพลาดในการอัพโหลด';

                if (xhr.responseJSON && xhr.responseJSON.error) {
                    errorMessage = xhr.responseJSON.error;
                } else if (xhr.responseText) {
                    try {
                        const response = JSON.parse(xhr.responseText);
                        errorMessage = response.error || errorMessage;
                    } catch (e) {
                        errorMessage = `HTTP ${xhr.status}: ${error}`;
                    }
                }

                $('#image-upload-status').html(`
                    <div class="alert alert-danger">
                        <i class="fas fa-times"></i> ${errorMessage}
                        <br><small>Status: ${xhr.status} ${error}</small>
                    </div>
                `);
                showDeductionFormError(errorMessage);
            }
        });
    });

    // Handle save deduction button
    $('#save-deduction-btn').click(function() {
        saveDeduction();
    });

});

// Save deduction function
function saveDeduction() {
    clearDeductionFormErrors();

    const form = $('#deduction-form');
    const mode = form.data('mode');
    const receiptIndex = window.currentReceiptIndex;

    // Validate form
    if (!$('#deduction_type').val()) {
        showDeductionFormError('กรุณาเลือกประเภทการหัก');
        return;
    }

    const isPercentageBased = $('#is_percentage_based').is(':checked');
    let amount = 0;
    let percentage = 0;

    if (isPercentageBased) {
        percentage = parseFloat($('#deduction_percentage').val());
        if (!percentage || percentage <= 0) {
            showDeductionFormError('กรุณาระบุเปอร์เซ็นต์ที่ถูกต้อง');
            return;
        }
        amount = calculateAmountFromPercentage();
    } else {
        amount = parseFloat($('#deduction_amount').val());
        if (!amount || amount <= 0) {
            showDeductionFormError('กรุณาระบุจำนวนเงินที่ถูกต้อง');
            return;
        }
    }

    // Check if image is uploaded
    const uploadedFilename = $('#deduction_image').data('uploaded-filename');
    const uploadedUrl = $('#deduction_image').data('uploaded-url');

    if (!uploadedFilename) {
        showDeductionFormError('กรุณาแนบรูปหลักฐานการหัก');
        return;
    }

    // Ensure imageUrl is absolute path
    let finalImageUrl = uploadedUrl;
    if (uploadedUrl && !uploadedUrl.startsWith('http') && !uploadedUrl.startsWith('/')) {
        // Convert relative path to absolute
        finalImageUrl = '../' + uploadedUrl;
    }

    console.log('Deduction image data:', {
        filename: uploadedFilename,
        originalUrl: uploadedUrl,
        finalUrl: finalImageUrl
    });

    // Prepare deduction data
    const deductionData = {
        type: $('#deduction_type').val(),
        amount: amount,
        percentage: percentage,
        description: $('#deduction_description').val(),
        isPercentageBased: isPercentageBased,
        image: uploadedFilename,
        imageUrl: finalImageUrl
    };

    // Add type name for display
    const typeNames = {
        'tax_vat': 'ภาษีมูลค่าเพิ่ม (VAT)',
        'tax_withholding': 'ภาษีหัก ณ ที่จ่าย',
        'service_fee': 'ค่าธรรมเนียม',
        'discount': 'ส่วนลด',
        'penalty': 'ค่าปรับ',
        'commission': 'ค่าคอมมิชชั่น',
        'other': 'อื่นๆ'
    };
    deductionData.typeName = typeNames[deductionData.type] || deductionData.type;

    // Initialize deductions array if not exists
    if (!window.receiptDeductions[receiptIndex]) {
        window.receiptDeductions[receiptIndex] = [];
    }

    // Save to array
    if (mode === 'add') {
        window.receiptDeductions[receiptIndex].push(deductionData);
    } else if (mode === 'edit') {
        window.receiptDeductions[receiptIndex][window.currentDeductionIndex] = deductionData;
    }

    // Update the deductions list for this receipt
    renderReceiptDeductionsList(receiptIndex);

    // Update calculations
    updateAllCalculations();

    // Close modal
    $('#deductionModal').modal('hide');
}

// Render deductions list for a specific receipt
function renderReceiptDeductionsList(receiptIndex) {
    const deductionsList = $(`#deductionsList${receiptIndex}`);
    const noMessage = deductionsList.find('.no-deductions-message');
    const deductions = window.receiptDeductions[receiptIndex] || [];

    // Clear existing deduction items
    deductionsList.find('.deduction-item').remove();

    if (deductions.length === 0) {
        noMessage.show();
    } else {
        noMessage.hide();

        deductions.forEach((deduction, index) => {
            const deductionItem = $(`
                <div class="deduction-item">
                    <div class="row align-items-center">
                        <div class="col-md-6">
                            <div class="deduction-type">${deduction.typeName}</div>
                            <div class="deduction-amount">${deduction.amount.toLocaleString('th-TH', {
                                minimumFractionDigits: 2,
                                maximumFractionDigits: 2
                            })} บาท</div>
                            ${deduction.isPercentageBased ? `<small class="text-muted">(${deduction.percentage}%)</small>` : ''}
                            ${deduction.description ? `<div class="deduction-description">${deduction.description}</div>` : ''}
                        </div>
                        <div class="col-md-4">
                            ${deduction.imageUrl ? `
                                <button type="button" class="btn btn-xs btn-outline-info image-viewer-trigger"
                                        data-image-src="${deduction.imageUrl}"
                                        data-image-title="Deduction: ${deduction.typeName}">
                                    <i class="fas fa-image"></i> ดูหลักฐาน
                                </button>
                            ` : ''}
                        </div>
                        <div class="col-md-2 text-end">
                            <div class="btn-group btn-group-sm">
                                <button type="button" class="btn btn-outline-primary edit-deduction-btn"
                                        data-receipt-index="${receiptIndex}" data-deduction-index="${index}" title="แก้ไข">
                                    <i class="fas fa-edit"></i>
                                </button>
                                <button type="button" class="btn btn-outline-danger delete-deduction-btn"
                                        data-receipt-index="${receiptIndex}" data-deduction-index="${index}" title="ลบ">
                                    <i class="fas fa-trash"></i>
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            `);
            deductionsList.append(deductionItem);
        });
    }

    // Update net amount for this receipt
    updateReceiptNetAmount(receiptIndex);
}

// Update net amount display for a specific receipt
function updateReceiptNetAmount(receiptIndex) {
    const receiptAmount = parseFloat($(`.receipt-amount[data-receipt-index="${receiptIndex}"]`).val()) ||
                         parseFloat($(`.receipt-amount`).eq(receiptIndex).val()) || 0;
    const deductions = window.receiptDeductions[receiptIndex] || [];
    const totalDeductions = deductions.reduce((sum, deduction) => sum + deduction.amount, 0);
    const netAmount = receiptAmount - totalDeductions;

    $(`#netAmount${receiptIndex}`).text(netAmount.toLocaleString('th-TH', {
        minimumFractionDigits: 2,
        maximumFractionDigits: 2
    }) + ' บาท');
}

// Add event handlers for edit and delete deduction buttons
$(document).on('click', '.edit-deduction-btn', function() {
    const receiptIndex = $(this).data('receipt-index');
    const deductionIndex = $(this).data('deduction-index');
    const deduction = window.receiptDeductions[receiptIndex][deductionIndex];

    window.currentReceiptIndex = receiptIndex;
    window.currentDeductionIndex = deductionIndex;

    // Populate modal with deduction data
    $('#deduction_type').val(deduction.type);
    $('#deduction_description').val(deduction.description);
    $('#is_percentage_based').prop('checked', deduction.isPercentageBased);

    if (deduction.isPercentageBased) {
        $('#deduction_percentage').val(deduction.percentage);
        $('#percentage-input').show();
        $('#amount-input').hide();
    } else {
        $('#deduction_amount').val(deduction.amount);
        $('#percentage-input').hide();
        $('#amount-input').show();
    }

    // Set uploaded image data
    if (deduction.image) {
        $('#deduction_image').data('uploaded-filename', deduction.image);
        $('#deduction_image').data('uploaded-url', deduction.imageUrl);
        $('#image-upload-status').html(`
            <div class="alert alert-success">
                <i class="fas fa-check"></i> ไฟล์: ${deduction.image}
            </div>
        `);
    }

    $('#deduction-form').data('mode', 'edit');
    $('#deductionModal .modal-title').text('แก้ไขรายการหัก');
    $('#deductionModal').modal('show');
});

$(document).on('click', '.delete-deduction-btn', function() {
    const receiptIndex = $(this).data('receipt-index');
    const deductionIndex = $(this).data('deduction-index');

    if (confirm('คุณต้องการลบรายการหักนี้หรือไม่?')) {
        // Remove deduction from array
        window.receiptDeductions[receiptIndex].splice(deductionIndex, 1);

        // If no deductions left, remove the array
        if (window.receiptDeductions[receiptIndex].length === 0) {
            delete window.receiptDeductions[receiptIndex];

            // Uncheck the checkbox
            $(`.has-deductions-checkbox[data-receipt-index="${receiptIndex}"]`).prop('checked', false).trigger('change');
        } else {
            // Re-render the deductions list
            renderReceiptDeductionsList(receiptIndex);
        }

        // Update calculations
        updateAllCalculations();
    }
});

// Make functions global
window.updateAllCalculations = updateAllCalculations;
window.renderReceiptDeductionsList = renderReceiptDeductionsList;
window.updateReceiptNetAmount = updateReceiptNetAmount;
