/* Custom CSS for Expenses Management System */

:root {
    --primary-color: #0d6efd;
    --secondary-color: #6c757d;
    --success-color: #198754;
    --warning-color: #ffc107;
    --danger-color: #dc3545;
    --info-color: #0dcaf0;
    --light-color: #f8f9fa;
    --dark-color: #212529;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background-color: #f8f9fa;
}

/* Navigation */
.navbar-brand {
    font-weight: bold;
    font-size: 1.5rem;
}

/* Cards */
.card {
    border: none;
    border-radius: 10px;
    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
    transition: box-shadow 0.15s ease-in-out;
}

.card:hover {
    box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
}

.card-header {
    background-color: transparent;
    border-bottom: 1px solid rgba(0, 0, 0, 0.125);
    font-weight: 600;
}

/* Forms */
.form-control, .form-select {
    border-radius: 8px;
    border: 1px solid #ced4da;
    transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
}

/* Reset Button Styling */
.btn-outline-warning {
    border-color: #ffc107;
    color: #ffc107;
    transition: all 0.3s ease;
}

.btn-outline-warning:hover {
    background-color: #ffc107;
    border-color: #ffc107;
    color: #000;
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(255, 193, 7, 0.3);
}

.btn-outline-warning:active {
    transform: translateY(0);
    box-shadow: 0 2px 4px rgba(255, 193, 7, 0.3);
}

/* Form Section Styling */
.form-section {
    margin-bottom: 2rem;
    padding: 1.5rem;
    background: white;
    border-radius: 10px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

/* Edit Form Specific Styles */
.current-file {
    padding: 0.5rem;
    background-color: #f8f9fa;
    border-radius: 5px;
    margin-bottom: 0.5rem;
}

.current-receipts {
    max-height: 200px;
    overflow-y: auto;
    padding: 0.5rem;
    background-color: #f8f9fa;
    border-radius: 5px;
}

.receipt-item {
    padding: 0.5rem;
    border: 1px solid #dee2e6;
    border-radius: 3px;
    background-color: white;
}

.receipt-item:last-child {
    margin-bottom: 0 !important;
}

.form-section:last-child {
    margin-bottom: 0;
}

.form-control:focus, .form-select:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 0.2rem rgba(13, 110, 253, 0.25);
}

.form-label {
    font-weight: 500;
    color: var(--dark-color);
}

/* Buttons */
.btn {
    border-radius: 8px;
    font-weight: 500;
    transition: all 0.15s ease-in-out;
}

.btn-primary {
    background-color: var(--primary-color);
    border-color: var(--primary-color);
}

.btn-primary:hover {
    background-color: #0b5ed7;
    border-color: #0a58ca;
}

/* Tables */
.table {
    border-radius: 8px;
    overflow: hidden;
}

.table thead th {
    background-color: var(--light-color);
    border-bottom: 2px solid #dee2e6;
    font-weight: 600;
    color: var(--dark-color);
}

.table-striped > tbody > tr:nth-of-type(odd) > td {
    background-color: rgba(0, 0, 0, 0.025);
}

/* Status badges */
.status-open {
    background-color: var(--info-color);
    color: white;
}

.status-pending {
    background-color: var(--warning-color);
    color: var(--dark-color);
}

.status-success {
    background-color: var(--success-color);
    color: white;
}

/* File upload */
.file-upload-area {
    border: 2px dashed #ced4da;
    border-radius: 8px;
    padding: 2rem;
    text-align: center;
    transition: border-color 0.15s ease-in-out;
    cursor: pointer;
}

.file-upload-area:hover {
    border-color: var(--primary-color);
}

.file-upload-area.dragover {
    border-color: var(--primary-color);
    background-color: rgba(13, 110, 253, 0.1);
}

/* Image preview */
.image-preview {
    max-width: 200px;
    max-height: 200px;
    border-radius: 8px;
    cursor: pointer;
    transition: transform 0.15s ease-in-out;
}

.image-preview:hover {
    transform: scale(1.05);
}

/* Modal */
.modal-content {
    border-radius: 10px;
    border: none;
}

.modal-header {
    border-bottom: 1px solid #dee2e6;
}

.modal-footer {
    border-top: 1px solid #dee2e6;
}

/* Loading spinner */
.spinner-border-sm {
    width: 1rem;
    height: 1rem;
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .container-fluid {
        padding-left: 15px;
        padding-right: 15px;
    }
    
    .card-body {
        padding: 1rem;
    }
    
    .table-responsive {
        font-size: 0.875rem;
    }
    
    .btn {
        padding: 0.375rem 0.75rem;
        font-size: 0.875rem;
    }
}

/* Print styles */
@media print {
    .navbar,
    .btn,
    .no-print {
        display: none !important;
    }
    
    .card {
        border: 1px solid #000;
        box-shadow: none;
    }
    
    .table {
        font-size: 12px;
    }
}

/* Custom autocomplete */
.autocomplete-container {
    position: relative;
}

.autocomplete-suggestions {
    position: absolute;
    top: 100%;
    left: 0;
    right: 0;
    background: white;
    border: 1px solid #ced4da;
    border-top: none;
    border-radius: 0 0 8px 8px;
    max-height: 200px;
    overflow-y: auto;
    z-index: 1000;
    display: none;
}

.autocomplete-suggestion {
    padding: 0.5rem 0.75rem;
    cursor: pointer;
    border-bottom: 1px solid #f8f9fa;
}

.autocomplete-suggestion:hover,
.autocomplete-suggestion.active {
    background-color: var(--primary-color);
    color: white;
}

.autocomplete-suggestion:last-child {
    border-bottom: none;
}

/* Searchable Select styles */
.searchable-select-container {
    position: relative;
}

.searchable-select-container .dropdown-menu {
    max-height: 300px;
    overflow-y: auto;
    width: 100% !important;
}

.searchable-select-container .dropdown-item {
    cursor: pointer;
    padding: 8px 16px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.searchable-select-container .dropdown-item:hover {
    background-color: #f8f9fa;
}

.searchable-select-container .dropdown-item.active {
    background-color: #0d6efd;
    color: white;
}

.searchable-select-container .dropdown-item.text-primary:hover {
    background-color: #e7f3ff;
}

.searchable-select-container .searchable-input {
    border-right: 0;
}

.searchable-select-container .dropdown-toggle {
    border-left: 0;
    padding: 0.375rem 0.5rem;
}

.searchable-select-container .dropdown-toggle:focus {
    box-shadow: none;
}

/* Hide dropdown items that don't match search */
.searchable-select-container .dropdown-item.d-none {
    display: none !important;
}

/* Datalist styles */
.datalist-container {
    position: relative;
}

.datalist-container input[type="text"] {
    border-radius: 8px;
}

.datalist-container .input-group input[type="text"] {
    border-top-right-radius: 0;
    border-bottom-right-radius: 0;
}

.datalist-container .input-group .btn {
    border-top-left-radius: 0;
    border-bottom-left-radius: 0;
    border-left: 0;
}

.datalist-container .input-group .btn:focus {
    box-shadow: none;
    border-color: #86b7fe;
}

/* Date picker custom styles */
.datepicker {
    border-radius: 8px;
}

/* Toast notifications */
.toast-container {
    position: fixed;
    top: 20px;
    right: 20px;
    z-index: 1055;
}

.toast {
    border-radius: 8px;
    border: none;
}

/* Activity log styles */
.activity-log-item {
    border-left: 3px solid var(--primary-color);
    padding-left: 1rem;
    margin-bottom: 1rem;
}

.activity-log-time {
    font-size: 0.875rem;
    color: var(--secondary-color);
}

/* Dashboard stats cards */
.stats-card {
    background: linear-gradient(135deg, var(--primary-color), #0b5ed7);
    border-radius: 15px;
    color: white;
    transition: transform 0.15s ease-in-out;
}

.stats-card:hover {
    transform: translateY(-5px);
}

.stats-number {
    font-size: 2.5rem;
    font-weight: bold;
}

/* Search and filter bar */
.search-filter-bar {
    background-color: white;
    border-radius: 10px;
    padding: 1.5rem;
    margin-bottom: 2rem;
    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
}

/* Expense form specific styles */
.expense-form .form-section {
    background-color: white;
    border-radius: 10px;
    padding: 1.5rem;
    margin-bottom: 1.5rem;
    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
}

.expense-form .section-title {
    font-size: 1.25rem;
    font-weight: 600;
    color: var(--primary-color);
    margin-bottom: 1rem;
    border-bottom: 2px solid var(--primary-color);
    padding-bottom: 0.5rem;
}
