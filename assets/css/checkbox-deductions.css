/* Checkbox Deductions System Styling */

/* Checkbox styling */
.has-deductions-checkbox {
    transform: scale(1.2);
    margin-right: 0.5rem;
}

.has-deductions-checkbox:checked + label i {
    color: #dc3545 !important; /* เปลี่ยนเป็นสีแดงเมื่อเลือก */
    animation: pulse 0.3s ease-in-out;
}

.has-deductions-checkbox + label {
    cursor: pointer;
    transition: all 0.2s ease;
}

.has-deductions-checkbox + label:hover {
    color: #dc3545;
}

.has-deductions-checkbox + label small {
    font-size: 0.75rem;
    font-weight: 500;
}

/* Receipt card styling */
.receipt-card {
    transition: all 0.3s ease;
}

.receipt-card.has-deductions {
    border-color: #ffc107 !important;
    box-shadow: 0 0 0 0.2rem rgba(255, 193, 7, 0.25);
}

.receipt-card.has-deductions .card-header {
    background-color: rgba(255, 193, 7, 0.1);
    border-bottom-color: #ffc107;
}

/* Deductions section animations */
.deductions-section {
    overflow: hidden;
    transition: all 0.4s ease-in-out;
}

.deductions-section.d-none {
    max-height: 0;
    opacity: 0;
    margin-top: 0 !important;
    padding-top: 0;
    padding-bottom: 0;
}

.deductions-section:not(.d-none) {
    max-height: 1000px;
    opacity: 1;
    animation: slideDown 0.4s ease-in-out;
}

@keyframes slideDown {
    from {
        max-height: 0;
        opacity: 0;
        transform: translateY(-10px);
    }
    to {
        max-height: 1000px;
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes pulse {
    0% { transform: scale(1); }
    50% { transform: scale(1.1); }
    100% { transform: scale(1); }
}

/* Deduction items styling */
.deduction-item {
    border: 1px solid #dee2e6;
    border-radius: 0.375rem;
    padding: 0.75rem;
    margin-bottom: 0.5rem;
    background-color: #f8f9fa;
    transition: all 0.2s ease;
}

.deduction-item:hover {
    border-color: #ffc107;
    background-color: #fff;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.deduction-item .deduction-type {
    font-weight: 600;
    color: #495057;
}

.deduction-item .deduction-amount {
    font-weight: 700;
    color: #dc3545;
    font-size: 1.1rem;
}

.deduction-item .deduction-description {
    color: #6c757d;
    font-size: 0.9rem;
    margin-top: 0.25rem;
}

/* Add deduction button */
.add-deduction-btn {
    border-style: dashed;
    border-width: 2px;
    transition: all 0.2s ease;
}

.add-deduction-btn:hover {
    border-style: solid;
    background-color: #ffc107;
    border-color: #ffc107;
    color: #000;
    transform: translateY(-1px);
}

/* Net amount display */
.net-amount-display {
    background-color: rgba(25, 135, 84, 0.1);
    border: 1px solid rgba(25, 135, 84, 0.2);
    border-radius: 0.375rem;
    padding: 0.5rem;
    text-align: center;
}

.net-amount {
    font-size: 1.1rem;
}

/* No deductions message */
.no-deductions-message {
    font-style: italic;
    background-color: rgba(108, 117, 125, 0.1);
    border-radius: 0.375rem;
    padding: 1rem;
}

/* Summary container */
#deductions-summary-container {
    animation: fadeIn 0.5s ease-in-out;
}

@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Validation status styling */
#amount-validation-status .badge {
    font-size: 0.8rem;
    padding: 0.4rem 0.8rem;
}

#amount-validation-status .badge.bg-success {
    background-color: #198754 !important;
}

#amount-validation-status .badge.bg-danger {
    background-color: #dc3545 !important;
}

#amount-validation-status .badge.bg-warning {
    background-color: #ffc107 !important;
    color: #000 !important;
}

/* Mobile responsiveness */
@media (max-width: 768px) {
    .has-deductions-checkbox + label small {
        display: none;
    }
    
    .deduction-item {
        padding: 0.5rem;
    }
    
    .deduction-item .row > div {
        margin-bottom: 0.5rem;
    }
    
    .net-amount-display {
        margin-top: 1rem;
    }
}

/* Loading states */
.deduction-loading {
    opacity: 0.6;
    pointer-events: none;
}

.deduction-loading::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 20px;
    height: 20px;
    margin: -10px 0 0 -10px;
    border: 2px solid #f3f3f3;
    border-top: 2px solid #ffc107;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Focus states for accessibility */
.has-deductions-checkbox:focus + label {
    outline: 2px solid #0d6efd;
    outline-offset: 2px;
}

.add-deduction-btn:focus {
    box-shadow: 0 0 0 0.2rem rgba(255, 193, 7, 0.5);
}

/* Print styles */
@media print {
    .deductions-section {
        page-break-inside: avoid;
    }
    
    .add-deduction-btn {
        display: none;
    }
    
    .has-deductions-checkbox {
        display: none;
    }
}
