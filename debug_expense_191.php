<?php
/**
 * Debug Expense 191 Images
 */
session_start();
require_once 'config/database.php';

// Check if user is logged in
if (!isset($_SESSION['user_id'])) {
    header('Location: login.php');
    exit();
}

try {
    $database = new Database();
    $db = $database->getConnection();
    
    $expense_id = 191;
    
    echo "<h2>🔍 Debug Expense ID: {$expense_id}</h2>";
    echo "<hr>";
    
    // Get expense data
    $stmt = $db->prepare("
        SELECT id, exno, transfer_slip_image, verification_slip_image, reviewer_slip_image, 
               transfer_amount, created_at, created_by
        FROM expenses 
        WHERE id = ?
    ");
    $stmt->execute([$expense_id]);
    $expense = $stmt->fetch();
    
    if ($expense) {
        echo "<h3>📋 Expense Information:</h3>";
        echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
        echo "<tr><th>Field</th><th>Value</th><th>File Status</th></tr>";
        
        // Transfer slip
        echo "<tr>";
        echo "<td>Transfer Slip Image</td>";
        echo "<td>" . ($expense['transfer_slip_image'] ?: 'NULL') . "</td>";
        if ($expense['transfer_slip_image']) {
            $file_path = 'uploads/transfer_slips/' . $expense['transfer_slip_image'];
            $exists = file_exists($file_path);
            echo "<td style='color: " . ($exists ? 'green' : 'red') . "'>";
            echo $exists ? '✅ EXISTS' : '❌ MISSING';
            if ($exists) {
                echo " (" . number_format(filesize($file_path) / 1024, 2) . " KB)";
                echo "<br><a href='api/view_file.php?file=" . urlencode($expense['transfer_slip_image']) . "&type=transfer_slip' target='_blank'>View File</a>";
            }
            echo "</td>";
        } else {
            echo "<td>-</td>";
        }
        echo "</tr>";
        
        // Verification slip
        echo "<tr>";
        echo "<td>Verification Slip Image</td>";
        echo "<td>" . ($expense['verification_slip_image'] ?: 'NULL') . "</td>";
        if ($expense['verification_slip_image']) {
            $file_path = 'uploads/verification_slips/' . $expense['verification_slip_image'];
            $exists = file_exists($file_path);
            echo "<td style='color: " . ($exists ? 'green' : 'red') . "'>";
            echo $exists ? '✅ EXISTS' : '❌ MISSING';
            if ($exists) {
                echo " (" . number_format(filesize($file_path) / 1024, 2) . " KB)";
                echo "<br><a href='api/view_file.php?file=" . urlencode($expense['verification_slip_image']) . "&type=verification_slip' target='_blank'>View File</a>";
            }
            echo "</td>";
        } else {
            echo "<td>-</td>";
        }
        echo "</tr>";
        
        // Reviewer slip
        echo "<tr>";
        echo "<td>Reviewer Slip Image</td>";
        echo "<td>" . ($expense['reviewer_slip_image'] ?: 'NULL') . "</td>";
        if ($expense['reviewer_slip_image']) {
            $file_path = 'uploads/review_slips/' . $expense['reviewer_slip_image'];
            $exists = file_exists($file_path);
            echo "<td style='color: " . ($exists ? 'green' : 'red') . "'>";
            echo $exists ? '✅ EXISTS' : '❌ MISSING';
            if ($exists) {
                echo " (" . number_format(filesize($file_path) / 1024, 2) . " KB)";
                echo "<br><a href='api/view_file.php?file=" . urlencode($expense['reviewer_slip_image']) . "&type=reviewer_slip' target='_blank'>View File</a>";
            }
            echo "</td>";
        } else {
            echo "<td>-</td>";
        }
        echo "</tr>";
        
        echo "</table>";
        
        // Get receipt data
        echo "<h3>🧾 Receipt Information:</h3>";
        $stmt = $db->prepare("
            SELECT id, receipt_number, amount, description, receipt_image
            FROM receipt_numbers 
            WHERE expense_id = ?
            ORDER BY id
        ");
        $stmt->execute([$expense_id]);
        $receipts = $stmt->fetchAll();
        
        if ($receipts) {
            echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
            echo "<tr><th>Receipt ID</th><th>Receipt Number</th><th>Amount</th><th>Image File</th><th>File Status</th></tr>";
            
            foreach ($receipts as $receipt) {
                echo "<tr>";
                echo "<td>{$receipt['id']}</td>";
                echo "<td>{$receipt['receipt_number']}</td>";
                echo "<td>" . number_format($receipt['amount'], 2) . "</td>";
                echo "<td>" . ($receipt['receipt_image'] ?: 'NULL') . "</td>";
                
                if ($receipt['receipt_image']) {
                    $file_path = 'uploads/receipts/' . $receipt['receipt_image'];
                    $exists = file_exists($file_path);
                    echo "<td style='color: " . ($exists ? 'green' : 'red') . "'>";
                    echo $exists ? '✅ EXISTS' : '❌ MISSING';
                    if ($exists) {
                        echo " (" . number_format(filesize($file_path) / 1024, 2) . " KB)";
                        echo "<br><a href='api/view_file.php?file=" . urlencode($receipt['receipt_image']) . "&type=receipt' target='_blank'>View File</a>";
                    }
                    echo "</td>";
                } else {
                    echo "<td>-</td>";
                }
                echo "</tr>";
            }
            echo "</table>";
        } else {
            echo "<p>❌ No receipts found for this expense</p>";
        }
        
        // Get deduction data
        echo "<h3>💰 Deduction Information:</h3>";
        $stmt = $db->prepare("
            SELECT rd.*, rn.receipt_number
            FROM receipt_deductions rd
            JOIN receipt_numbers rn ON rd.receipt_number_id = rn.id
            WHERE rn.expense_id = ?
            ORDER BY rd.id
        ");
        $stmt->execute([$expense_id]);
        $deductions = $stmt->fetchAll();
        
        if ($deductions) {
            echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
            echo "<tr><th>Deduction ID</th><th>Receipt</th><th>Type</th><th>Amount</th><th>Image File</th><th>File Status</th></tr>";
            
            foreach ($deductions as $deduction) {
                echo "<tr>";
                echo "<td>{$deduction['id']}</td>";
                echo "<td>{$deduction['receipt_number']}</td>";
                echo "<td>{$deduction['deduction_type']}</td>";
                echo "<td>" . number_format($deduction['amount'], 2) . "</td>";
                echo "<td>" . ($deduction['deduction_image'] ?: 'NULL') . "</td>";
                
                if ($deduction['deduction_image']) {
                    $file_path = 'uploads/deductions/' . $deduction['deduction_image'];
                    $exists = file_exists($file_path);
                    echo "<td style='color: " . ($exists ? 'green' : 'red') . "'>";
                    echo $exists ? '✅ EXISTS' : '❌ MISSING';
                    if ($exists) {
                        echo " (" . number_format(filesize($file_path) / 1024, 2) . " KB)";
                        echo "<br><a href='api/view_file.php?file=" . urlencode($deduction['deduction_image']) . "&type=deduction' target='_blank'>View File</a>";
                    }
                    echo "</td>";
                } else {
                    echo "<td>-</td>";
                }
                echo "</tr>";
            }
            echo "</table>";
        } else {
            echo "<p>❌ No deductions found for this expense</p>";
        }
        
        // Check upload directories
        echo "<h3>📁 Upload Directory Status:</h3>";
        $directories = [
            'uploads/transfer_slips/',
            'uploads/receipts/',
            'uploads/deductions/',
            'uploads/verification_slips/',
            'uploads/review_slips/'
        ];
        
        echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
        echo "<tr><th>Directory</th><th>Exists</th><th>Writable</th><th>File Count</th></tr>";
        
        foreach ($directories as $dir) {
            $exists = is_dir($dir);
            $writable = $exists ? is_writable($dir) : false;
            $file_count = $exists ? count(glob($dir . '*')) : 0;
            
            echo "<tr>";
            echo "<td>{$dir}</td>";
            echo "<td style='color: " . ($exists ? 'green' : 'red') . "'>" . ($exists ? '✅ YES' : '❌ NO') . "</td>";
            echo "<td style='color: " . ($writable ? 'green' : 'red') . "'>" . ($writable ? '✅ YES' : '❌ NO') . "</td>";
            echo "<td>{$file_count}</td>";
            echo "</tr>";
        }
        echo "</table>";
        
    } else {
        echo "<p>❌ Expense ID {$expense_id} not found</p>";
    }
    
} catch (Exception $e) {
    echo "<div style='background: #f8d7da; border: 1px solid #f5c6cb; padding: 10px; margin: 10px 0; border-radius: 5px;'>";
    echo "<strong>❌ Error:</strong> " . $e->getMessage();
    echo "</div>";
}

echo "<hr>";
echo "<p><a href='expenses/view.php?id={$expense_id}'>← Back to Expense View</a></p>";
?>
