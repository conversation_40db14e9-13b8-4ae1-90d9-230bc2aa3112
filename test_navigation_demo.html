<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Navigation System Demo</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        .navigation-bar {
            border: 1px solid #dee2e6;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        
        .navigation-bar .btn {
            min-width: 100px;
        }
        
        .navigation-bar .position-info {
            font-weight: 600;
            color: #495057;
        }
        
        .navigation-bar .small {
            font-size: 0.875rem;
        }
        
        .keyboard-hint {
            position: fixed;
            bottom: 20px;
            right: 20px;
            background: rgba(0,0,0,0.8);
            color: white;
            padding: 10px 15px;
            border-radius: 5px;
            font-size: 12px;
            z-index: 1000;
            display: none;
        }
        
        .keyboard-hint.show {
            display: block;
            animation: fadeIn 0.3s ease-in;
        }
        
        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(10px); }
            to { opacity: 1; transform: translateY(0); }
        }
        
        .demo-content {
            min-height: 400px;
            background: #f8f9fa;
            border: 2px dashed #dee2e6;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.2rem;
            color: #6c757d;
        }
    </style>
</head>
<body>
    <div class="container-fluid mt-4">
        <div class="row">
            <div class="col-12">
                <h1><i class="fas fa-demo me-2"></i>Navigation System Demo</h1>
                <p class="text-muted">This demonstrates the navigation system for expense view pages</p>
                
                <!-- Top Navigation Bar -->
                <div class="navigation-bar bg-light p-3 rounded mb-3">
                    <div class="row align-items-center">
                        <!-- Left side: Back to list button -->
                        <div class="col-md-4">
                            <a href="#" class="btn btn-secondary" onclick="alert('Back to List clicked!')">
                                <i class="fas fa-arrow-left me-1"></i>Back to List
                            </a>
                        </div>
                        
                        <!-- Center: Position info and search criteria -->
                        <div class="col-md-4 text-center">
                            <div class="fw-bold position-info">2 of 5 results</div>
                            <div class="small">
                                <span class="text-muted">Search: <strong>John Driver</strong></span>
                            </div>
                        </div>
                        
                        <!-- Right side: Previous/Next buttons -->
                        <div class="col-md-4 text-end">
                            <a href="#" class="btn btn-outline-primary me-2" id="prev-btn" 
                               title="Previous: 20251020-001 (← Arrow Key)" onclick="alert('Previous clicked!')">
                                <i class="fas fa-chevron-left me-1"></i>Previous
                            </a>
                            <a href="#" class="btn btn-outline-primary" id="next-btn" 
                               title="Next: 20251020-003 (→ Arrow Key)" onclick="alert('Next clicked!')">
                                Next<i class="fas fa-chevron-right ms-1"></i>
                            </a>
                        </div>
                    </div>
                    
                    <!-- Additional info row -->
                    <div class="row mt-2">
                        <div class="col-12">
                            <div class="d-flex justify-content-between small text-muted">
                                <div>
                                    <i class="fas fa-chevron-left me-1"></i>
                                    Previous: <strong>20251020-001</strong> - John Driver (ABC Company)
                                </div>
                                <div class="text-end">
                                    Next: <strong>20251020-003</strong> - John Driver (XYZ Company)
                                    <i class="fas fa-chevron-right ms-1"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- Demo Content Area -->
                <div class="demo-content rounded">
                    <div class="text-center">
                        <i class="fas fa-file-invoice-dollar fa-3x mb-3"></i>
                        <div>Expense Details Content Would Be Here</div>
                        <div class="mt-3">
                            <strong>Current Expense:</strong> 20251020-002<br>
                            <strong>Driver:</strong> John Driver<br>
                            <strong>Customer:</strong> DEF Company
                        </div>
                    </div>
                </div>
                
                <!-- Bottom Navigation Bar -->
                <div class="navigation-bar bg-light p-3 rounded mt-3">
                    <div class="row align-items-center">
                        <!-- Left side: Back to list button -->
                        <div class="col-md-4">
                            <a href="#" class="btn btn-secondary" onclick="alert('Back to List clicked!')">
                                <i class="fas fa-arrow-left me-1"></i>Back to List
                            </a>
                        </div>
                        
                        <!-- Center: Position info and search criteria -->
                        <div class="col-md-4 text-center">
                            <div class="fw-bold position-info">2 of 5 results</div>
                            <div class="small">
                                <span class="text-muted">Search: <strong>John Driver</strong></span>
                            </div>
                        </div>
                        
                        <!-- Right side: Previous/Next buttons -->
                        <div class="col-md-4 text-end">
                            <a href="#" class="btn btn-outline-primary me-2" 
                               title="Previous: 20251020-001 (← Arrow Key)" onclick="alert('Previous clicked!')">
                                <i class="fas fa-chevron-left me-1"></i>Previous
                            </a>
                            <a href="#" class="btn btn-outline-primary" 
                               title="Next: 20251020-003 (→ Arrow Key)" onclick="alert('Next clicked!')">
                                Next<i class="fas fa-chevron-right ms-1"></i>
                            </a>
                        </div>
                    </div>
                    
                    <!-- Additional info row -->
                    <div class="row mt-2">
                        <div class="col-12">
                            <div class="d-flex justify-content-between small text-muted">
                                <div>
                                    <i class="fas fa-chevron-left me-1"></i>
                                    Previous: <strong>20251020-001</strong> - John Driver (ABC Company)
                                </div>
                                <div class="text-end">
                                    Next: <strong>20251020-003</strong> - John Driver (XYZ Company)
                                    <i class="fas fa-chevron-right ms-1"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- Instructions -->
                <div class="alert alert-info mt-4">
                    <h6><i class="fas fa-keyboard me-2"></i>Keyboard Shortcuts:</h6>
                    <div class="row">
                        <div class="col-md-6">
                            <h6 class="text-primary"><i class="fab fa-apple me-1"></i>Mac:</h6>
                            <ul class="mb-0">
                                <li><strong>← →</strong> Previous/Next expense</li>
                                <li><strong>Cmd + ,</strong> Previous expense</li>
                                <li><strong>Cmd + .</strong> Next expense</li>
                                <li><strong>Cmd + L</strong> Back to List</li>
                                <li><strong>Esc</strong> Back to List</li>
                            </ul>
                        </div>
                        <div class="col-md-6">
                            <h6 class="text-success"><i class="fab fa-windows me-1"></i>Windows:</h6>
                            <ul class="mb-0">
                                <li><strong>← →</strong> Previous/Next expense</li>
                                <li><strong>Ctrl + ,</strong> Previous expense</li>
                                <li><strong>Ctrl + .</strong> Next expense</li>
                                <li><strong>Ctrl + L</strong> Back to List</li>
                                <li><strong>Esc</strong> Back to List</li>
                            </ul>
                        </div>
                    </div>
                    <div class="mt-2">
                        <small class="text-muted">System automatically detects your OS and shows appropriate shortcuts!</small>
                    </div>
                </div>
                
                <!-- Features List -->
                <div class="row mt-4">
                    <div class="col-md-6">
                        <div class="card">
                            <div class="card-header">
                                <h6><i class="fas fa-check-circle me-2"></i>Features Implemented</h6>
                            </div>
                            <div class="card-body">
                                <ul class="list-unstyled">
                                    <li><i class="fas fa-check text-success me-2"></i>Search criteria preservation</li>
                                    <li><i class="fas fa-check text-success me-2"></i>Previous/Next navigation</li>
                                    <li><i class="fas fa-check text-success me-2"></i>Position indicator (2 of 5)</li>
                                    <li><i class="fas fa-check text-success me-2"></i>Driver/Customer names display</li>
                                    <li><i class="fas fa-check text-success me-2"></i>Keyboard shortcuts</li>
                                    <li><i class="fas fa-check text-success me-2"></i>Top & Bottom navigation bars</li>
                                    <li><i class="fas fa-check text-success me-2"></i>Responsive design</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="card">
                            <div class="card-header">
                                <h6><i class="fas fa-cog me-2"></i>How It Works</h6>
                            </div>
                            <div class="card-body">
                                <ol>
                                    <li>User searches for "John Driver" in list.php</li>
                                    <li>Search parameters are passed to view.php</li>
                                    <li>System finds all matching expenses</li>
                                    <li>Current position is calculated</li>
                                    <li>Previous/Next links are generated</li>
                                    <li>Navigation preserves search criteria</li>
                                    <li>Keyboard shortcuts provide quick navigation</li>
                                </ol>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Keyboard Shortcuts Hint -->
    <div class="keyboard-hint" id="keyboard-hint">
        <div><strong>Keyboard Shortcuts:</strong></div>
        <div id="keyboard-hint-content">← Previous | → Next | Esc Back to List</div>
    </div>

    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script>
        $(document).ready(function() {
            // Detect operating system
            const isMac = navigator.platform.toUpperCase().indexOf('MAC') >= 0;

            // Update keyboard hint content based on OS
            const shortcutText = isMac
                ? '← Previous | → Next | Cmd+, Previous | Cmd+. Next | Cmd+L List | Esc Back'
                : '← Previous | → Next | Ctrl+, Previous | Ctrl+. Next | Ctrl+L List | Esc Back';
            $('#keyboard-hint-content').text(shortcutText);

            // Show current OS detection
            const osText = isMac ? 'macOS' : 'Windows/Linux';
            $('body').prepend(`<div class="alert alert-secondary text-center mb-0"><small>Detected OS: <strong>${osText}</strong> - Shortcuts adjusted automatically</small></div>`);

            // Keyboard navigation shortcuts
            $(document).keydown(function(e) {
                // Only trigger if not typing in an input field
                if (!$(e.target).is('input, textarea, select')) {
                    // Check for modifier keys (Cmd on Mac, Ctrl on Windows)
                    const modifierKey = isMac ? e.metaKey : e.ctrlKey;

                    switch(e.which) {
                        case 37: // Left arrow key
                            e.preventDefault();
                            alert('Previous expense navigation triggered! (Arrow Key)');
                            break;
                        case 39: // Right arrow key
                            e.preventDefault();
                            alert('Next expense navigation triggered! (Arrow Key)');
                            break;
                        case 27: // Escape key
                            e.preventDefault();
                            alert('Back to list navigation triggered! (Esc Key)');
                            break;
                        case 188: // Comma key (,) for Previous
                            if (modifierKey) {
                                e.preventDefault();
                                const modName = isMac ? 'Cmd' : 'Ctrl';
                                alert(`Previous expense navigation triggered! (${modName}+,)`);
                            }
                            break;
                        case 190: // Period key (.) for Next
                            if (modifierKey) {
                                e.preventDefault();
                                const modName = isMac ? 'Cmd' : 'Ctrl';
                                alert(`Next expense navigation triggered! (${modName}+.)`);
                            }
                            break;
                        case 76: // L key for List (Cmd/Ctrl + L)
                            if (modifierKey) {
                                e.preventDefault();
                                const modName = isMac ? 'Cmd' : 'Ctrl';
                                alert(`Back to list navigation triggered! (${modName}+L)`);
                            }
                            break;
                    }
                }
            });
            
            // Show keyboard hint for 3 seconds on page load
            setTimeout(function() {
                $('#keyboard-hint').addClass('show');
                setTimeout(function() {
                    $('#keyboard-hint').removeClass('show');
                }, 3000);
            }, 1000);
            
            // Show keyboard hint on hover over navigation buttons
            $('.navigation-bar .btn').hover(
                function() {
                    $('#keyboard-hint').addClass('show');
                },
                function() {
                    setTimeout(function() {
                        if (!$('.navigation-bar .btn:hover').length) {
                            $('#keyboard-hint').removeClass('show');
                        }
                    }, 500);
                }
            );
        });
    </script>
</body>
</html>
