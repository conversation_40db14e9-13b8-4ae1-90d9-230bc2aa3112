<?php
/**
 * Server Environment Check
 * ตรวจสอบการตั้งค่า server และ database
 * ลบไฟล์นี้หลังจากตรวจสอบเสร็จแล้ว
 */
?>
<!DOCTYPE html>
<html lang="th">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Server Check - Expenses System</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        .check-item { margin: 5px 0; }
        .status-ok { color: #28a745; }
        .status-warning { color: #ffc107; }
        .status-error { color: #dc3545; }
    </style>
</head>
<body class="bg-light">
    <div class="container mt-4">
        <div class="row justify-content-center">
            <div class="col-md-10">
                <div class="card shadow">
                    <div class="card-header bg-primary text-white">
                        <h3><i class="fas fa-server me-2"></i>Server Environment Check</h3>
                        <p class="mb-0">ตรวจสอบการตั้งค่า Production Server</p>
                    </div>
                    <div class="card-body">
                        
<?php
$all_ok = true;
$warnings = [];
$errors = [];

// 1. PHP Environment
echo "<h4><i class='fas fa-code me-2'></i>1. PHP Environment</h4>";
echo "<div class='check-item'><strong>PHP Version:</strong> " . PHP_VERSION;
if (version_compare(PHP_VERSION, '8.1.0', '>=')) {
    echo " <span class='status-ok'><i class='fas fa-check'></i> OK</span>";
} else {
    echo " <span class='status-warning'><i class='fas fa-exclamation-triangle'></i> Warning</span>";
    $warnings[] = "PHP version should be 8.1+";
}
echo "</div>";

// Check required extensions
$required_extensions = ['pdo', 'pdo_mysql', 'gd', 'json', 'mbstring', 'fileinfo'];
echo "<div class='mt-2'><strong>PHP Extensions:</strong></div>";
foreach ($required_extensions as $ext) {
    echo "<div class='check-item ms-3'>";
    if (extension_loaded($ext)) {
        echo "<span class='status-ok'><i class='fas fa-check'></i></span> {$ext}";
    } else {
        echo "<span class='status-error'><i class='fas fa-times'></i></span> {$ext} - Missing";
        $errors[] = "Missing PHP extension: {$ext}";
        $all_ok = false;
    }
    echo "</div>";
}

// 2. Database Connection
echo "<hr><h4><i class='fas fa-database me-2'></i>2. Database Connection</h4>";
try {
    require_once 'config/database.php';
    $database = new Database();
    $db = $database->getConnection();
    
    echo "<div class='check-item'><span class='status-ok'><i class='fas fa-check'></i></span> Database connection successful</div>";
    
    // Get database version
    $stmt = $db->query("SELECT VERSION() as version");
    $version = $stmt->fetch()['version'];
    echo "<div class='check-item'><strong>Database Version:</strong> {$version}</div>";
    
    // Check if MariaDB
    $is_mariadb = stripos($version, 'mariadb') !== false;
    echo "<div class='check-item'><strong>Database Type:</strong> " . ($is_mariadb ? 'MariaDB' : 'MySQL');
    if ($is_mariadb) {
        echo " <span class='status-ok'><i class='fas fa-check'></i> Compatible</span>";
    } else {
        echo " <span class='status-warning'><i class='fas fa-exclamation-triangle'></i> Should be MariaDB</span>";
        $warnings[] = "Recommended to use MariaDB for better compatibility";
    }
    echo "</div>";
    
} catch (Exception $e) {
    echo "<div class='check-item'><span class='status-error'><i class='fas fa-times'></i></span> Database connection failed: " . $e->getMessage() . "</div>";
    $errors[] = "Database connection failed";
    $all_ok = false;
    $db = null;
}

// 3. Database Tables
if ($db) {
    echo "<hr><h4><i class='fas fa-table me-2'></i>3. Database Tables</h4>";
    try {
        $required_tables = [
            'users', 'items', 'customers', 'drivers', 'expenses',
            'activity_logs', 'receipt_numbers', 'batch_operations',
            'batch_items', 'batch_documents', 'batch_performance_logs',
            'expense_workflow_history'
        ];
        
        $existing_tables = [];
        $missing_tables = [];
        
        // Get all tables first
        $stmt = $db->query("SHOW TABLES");
        $all_tables = $stmt->fetchAll(PDO::FETCH_COLUMN);

        foreach ($required_tables as $table) {
            if (in_array($table, $all_tables)) {
                $existing_tables[] = $table;
                echo "<div class='check-item ms-3'><span class='status-ok'><i class='fas fa-check'></i></span> {$table}</div>";
            } else {
                $missing_tables[] = $table;
                echo "<div class='check-item ms-3'><span class='status-error'><i class='fas fa-times'></i></span> {$table} - Missing</div>";
                $errors[] = "Missing table: {$table}";
                $all_ok = false;
            }
        }
        
        echo "<div class='check-item'><strong>Tables Found:</strong> " . count($existing_tables) . "/" . count($required_tables);
        if (count($missing_tables) == 0) {
            echo " <span class='status-ok'><i class='fas fa-check'></i> All tables exist</span>";
        } else {
            echo " <span class='status-error'><i class='fas fa-times'></i> " . count($missing_tables) . " tables missing</span>";
        }
        echo "</div>";
        
    } catch (Exception $e) {
        echo "<div class='check-item'><span class='status-error'><i class='fas fa-times'></i></span> Table check failed: " . $e->getMessage() . "</div>";
        $errors[] = "Table check failed";
        $all_ok = false;
    }

    // 4. Stored Procedures
    echo "<hr><h4><i class='fas fa-cogs me-2'></i>4. Stored Procedures</h4>";
    try {
        $stmt = $db->query("SHOW PROCEDURE STATUS WHERE Db = 'expenses_system'");
        $procedures = $stmt->fetchAll();
        
        $required_procedures = ['CreateBatchOperation', 'AddWorkflowHistory'];
        $found_procedures = array_column($procedures, 'Name');
        
        foreach ($required_procedures as $proc) {
            if (in_array($proc, $found_procedures)) {
                echo "<div class='check-item ms-3'><span class='status-ok'><i class='fas fa-check'></i></span> {$proc}</div>";
            } else {
                echo "<div class='check-item ms-3'><span class='status-error'><i class='fas fa-times'></i></span> {$proc} - Missing</div>";
                $errors[] = "Missing procedure: {$proc}";
                $all_ok = false;
            }
        }
        
        echo "<div class='check-item'><strong>Procedures Found:</strong> " . count($procedures);
        if (count($procedures) >= 2) {
            echo " <span class='status-ok'><i class='fas fa-check'></i> OK</span>";
        } else {
            echo " <span class='status-warning'><i class='fas fa-exclamation-triangle'></i> Should have 2+ procedures</span>";
            $warnings[] = "Missing stored procedures";
        }
        echo "</div>";
        
    } catch (Exception $e) {
        echo "<div class='check-item'><span class='status-error'><i class='fas fa-times'></i></span> Procedure check failed: " . $e->getMessage() . "</div>";
        $errors[] = "Procedure check failed";
    }

    // 5. Sample Data
    echo "<hr><h4><i class='fas fa-database me-2'></i>5. Sample Data</h4>";
    try {
        $tables_to_check = ['users', 'customers', 'drivers', 'items'];
        
        foreach ($tables_to_check as $table) {
            if (in_array($table, $existing_tables ?? [])) {
                $stmt = $db->query("SELECT COUNT(*) as count FROM {$table}");
                $count = $stmt->fetch()['count'];
                echo "<div class='check-item ms-3'>";
                if ($count > 0) {
                    echo "<span class='status-ok'><i class='fas fa-check'></i></span> {$table}: {$count} records";
                } else {
                    echo "<span class='status-warning'><i class='fas fa-exclamation-triangle'></i></span> {$table}: {$count} records (empty)";
                    $warnings[] = "Table {$table} is empty";
                }
                echo "</div>";
            }
        }
        
    } catch (Exception $e) {
        echo "<div class='check-item'><span class='status-error'><i class='fas fa-times'></i></span> Sample data check failed: " . $e->getMessage() . "</div>";
        $warnings[] = "Could not check sample data";
    }
}

// 6. Directory Structure
echo "<hr><h4><i class='fas fa-folder me-2'></i>6. Directory Structure</h4>";
$required_dirs = [
    'uploads/receipts',
    'uploads/transfer_slips', 
    'uploads/verification_slips',
    'uploads/review_slips',
    'uploads/batch_documents',
    'uploads/bulk_operations',
    'backups'
];

foreach ($required_dirs as $dir) {
    echo "<div class='check-item ms-3'>";
    if (is_dir($dir)) {
        if (is_writable($dir)) {
            echo "<span class='status-ok'><i class='fas fa-check'></i></span> {$dir} - OK";
        } else {
            echo "<span class='status-warning'><i class='fas fa-exclamation-triangle'></i></span> {$dir} - Not writable";
            $warnings[] = "Directory {$dir} is not writable";
        }
    } else {
        echo "<span class='status-error'><i class='fas fa-times'></i></span> {$dir} - Missing";
        $errors[] = "Missing directory: {$dir}";
        $all_ok = false;
    }
    echo "</div>";
}

// Summary
echo "<hr><h4><i class='fas fa-clipboard-check me-2'></i>Summary</h4>";

if ($all_ok && empty($errors)) {
    echo "<div class='alert alert-success'>";
    echo "<h5><i class='fas fa-check-circle me-2'></i>System Ready!</h5>";
    echo "<p>ระบบพร้อมใช้งานแล้ว ไม่พบปัญหาร้ายแรง</p>";
    if (!empty($warnings)) {
        echo "<p><strong>คำเตือน:</strong></p><ul>";
        foreach ($warnings as $warning) {
            echo "<li>{$warning}</li>";
        }
        echo "</ul>";
    }
    echo "</div>";
} else {
    echo "<div class='alert alert-danger'>";
    echo "<h5><i class='fas fa-exclamation-triangle me-2'></i>Issues Found!</h5>";
    echo "<p>พบปัญหาที่ต้องแก้ไข:</p><ul>";
    foreach ($errors as $error) {
        echo "<li>{$error}</li>";
    }
    echo "</ul>";
    if (!empty($warnings)) {
        echo "<p><strong>คำเตือน:</strong></p><ul>";
        foreach ($warnings as $warning) {
            echo "<li>{$warning}</li>";
        }
        echo "</ul>";
    }
    echo "</div>";
}

echo "<div class='alert alert-warning mt-3'>";
echo "<h6><i class='fas fa-shield-alt me-2'></i>Security Notice</h6>";
echo "<p><strong>ลบไฟล์นี้ (server_check.php) หลังจากตรวจสอบเสร็จแล้ว!</strong></p>";
echo "<p>ไฟล์นี้มีข้อมูลสำคัญและไม่ควรเข้าถึงได้ใน production</p>";
echo "</div>";

echo "<div class='text-center mt-4'>";
echo "<a href='index.php' class='btn btn-primary btn-lg me-3'>";
echo "<i class='fas fa-home me-2'></i>เข้าสู่ระบบ";
echo "</a>";
echo "<button onclick='window.location.reload()' class='btn btn-secondary'>";
echo "<i class='fas fa-sync me-2'></i>ตรวจสอบใหม่";
echo "</button>";
echo "</div>";
?>

                    </div>
                </div>
            </div>
        </div>
    </div>
</body>
</html>
