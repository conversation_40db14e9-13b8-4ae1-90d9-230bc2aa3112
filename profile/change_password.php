<?php
session_start();
require_once '../config/database.php';
require_once '../includes/functions.php';

// Check if user is logged in
if (!isset($_SESSION['user_id'])) {
    header('Location: ../login.php');
    exit();
}

$database = new Database();
$db = $database->getConnection();

// Get user info
$stmt = $db->prepare("SELECT * FROM users WHERE id = ?");
$stmt->execute([$_SESSION['user_id']]);
$user = $stmt->fetch();

if (!$user || !$user['is_active']) {
    session_destroy();
    header('Location: ../login.php');
    exit();
}

$success = '';
$error = '';

// Handle form submission
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    try {
        $current_password = $_POST['current_password'] ?? '';
        $new_password = $_POST['new_password'] ?? '';
        $confirm_password = $_POST['confirm_password'] ?? '';
        
        // Validation
        if (empty($current_password) || empty($new_password) || empty($confirm_password)) {
            throw new Exception('All password fields are required.');
        }
        
        if (strlen($new_password) < 6) {
            throw new Exception('New password must be at least 6 characters long.');
        }
        
        if ($new_password !== $confirm_password) {
            throw new Exception('New password and confirmation do not match.');
        }
        
        // Verify current password
        if (!password_verify($current_password, $user['password_hash'])) {
            throw new Exception('Current password is incorrect.');
        }
        
        // Check if new password is different from current
        if (password_verify($new_password, $user['password_hash'])) {
            throw new Exception('New password must be different from current password.');
        }
        
        // Hash new password
        $new_password_hash = password_hash($new_password, PASSWORD_DEFAULT);
        
        // Update password
        $stmt = $db->prepare("UPDATE users SET password_hash = ?, updated_at = NOW() WHERE id = ?");
        $stmt->execute([$new_password_hash, $user['id']]);
        
        // Log activity
        logActivity(
            $db,
            $user['id'],
            'update',
            'users',
            $user['id'],
            "Changed password",
            $_SERVER['REMOTE_ADDR'],
            $_SERVER['HTTP_USER_AGENT']
        );
        
        $success = 'Password changed successfully! Please use your new password for future logins.';
        
    } catch (Exception $e) {
        $error = $e->getMessage();
    }
}
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Change Password - Expenses System</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="../assets/css/style.css" rel="stylesheet">
    <style>
        .password-header {
            background: linear-gradient(135deg, #dc3545 0%, #fd7e14 100%);
            color: white;
            padding: 2rem 0;
            margin-bottom: 2rem;
        }
        .password-card {
            border: none;
            border-radius: 15px;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
        }
        .password-strength {
            height: 5px;
            border-radius: 3px;
            margin-top: 5px;
            transition: all 0.3s;
        }
        .strength-weak { background-color: #dc3545; }
        .strength-medium { background-color: #ffc107; }
        .strength-strong { background-color: #28a745; }
        .password-requirements {
            font-size: 0.875rem;
        }
        .requirement {
            transition: color 0.3s;
        }
        .requirement.met {
            color: #28a745;
        }
        .requirement.unmet {
            color: #6c757d;
        }
    </style>
</head>
<body class="bg-light">
    <!-- Navigation -->
    <?php include '../includes/navbar.php'; ?>

    <!-- Header -->
    <div class="password-header">
        <div class="container">
            <div class="row align-items-center">
                <div class="col-md-8">
                    <h1 class="mb-2">
                        <i class="fas fa-key me-3"></i>Change Password
                    </h1>
                    <p class="mb-0 opacity-75">Update your account password for security</p>
                </div>
                <div class="col-md-4 text-md-end">
                    <a href="../profile.php" class="btn btn-light">
                        <i class="fas fa-arrow-left me-2"></i>Back to Profile
                    </a>
                </div>
            </div>
        </div>
    </div>

    <div class="container">
        <div class="row justify-content-center">
            <div class="col-md-8 col-lg-6">
                <!-- Success/Error Messages -->
                <?php if ($success): ?>
                    <div class="alert alert-success alert-dismissible fade show" role="alert">
                        <i class="fas fa-check-circle me-2"></i><?php echo $success; ?>
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                <?php endif; ?>

                <?php if ($error): ?>
                    <div class="alert alert-danger alert-dismissible fade show" role="alert">
                        <i class="fas fa-exclamation-circle me-2"></i><?php echo $error; ?>
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                <?php endif; ?>

                <!-- Change Password Card -->
                <div class="card password-card">
                    <div class="card-header bg-danger text-white">
                        <h5 class="mb-0">
                            <i class="fas fa-shield-alt me-2"></i>Password Security
                        </h5>
                    </div>
                    <div class="card-body p-4">
                        <form method="POST" id="passwordForm">
                            <!-- Current Password -->
                            <div class="mb-4">
                                <label for="current_password" class="form-label">
                                    <i class="fas fa-lock me-1"></i>Current Password
                                </label>
                                <div class="input-group">
                                    <input type="password" class="form-control" id="current_password" 
                                           name="current_password" required>
                                    <button class="btn btn-outline-secondary" type="button" id="toggleCurrent">
                                        <i class="fas fa-eye"></i>
                                    </button>
                                </div>
                            </div>

                            <!-- New Password -->
                            <div class="mb-3">
                                <label for="new_password" class="form-label">
                                    <i class="fas fa-key me-1"></i>New Password
                                </label>
                                <div class="input-group">
                                    <input type="password" class="form-control" id="new_password" 
                                           name="new_password" required>
                                    <button class="btn btn-outline-secondary" type="button" id="toggleNew">
                                        <i class="fas fa-eye"></i>
                                    </button>
                                </div>
                                <div class="password-strength" id="passwordStrength"></div>
                            </div>

                            <!-- Password Requirements -->
                            <div class="password-requirements mb-4">
                                <h6 class="text-muted mb-2">Password Requirements:</h6>
                                <ul class="list-unstyled">
                                    <li class="requirement" id="req-length">
                                        <i class="fas fa-circle me-2"></i>At least 6 characters long
                                    </li>
                                    <li class="requirement" id="req-different">
                                        <i class="fas fa-circle me-2"></i>Different from current password
                                    </li>
                                </ul>
                            </div>

                            <!-- Confirm Password -->
                            <div class="mb-4">
                                <label for="confirm_password" class="form-label">
                                    <i class="fas fa-check-double me-1"></i>Confirm New Password
                                </label>
                                <div class="input-group">
                                    <input type="password" class="form-control" id="confirm_password" 
                                           name="confirm_password" required>
                                    <button class="btn btn-outline-secondary" type="button" id="toggleConfirm">
                                        <i class="fas fa-eye"></i>
                                    </button>
                                </div>
                                <div class="form-text" id="passwordMatch"></div>
                            </div>

                            <!-- Submit Button -->
                            <div class="d-grid gap-2">
                                <button type="submit" class="btn btn-danger btn-lg" id="submitBtn" disabled>
                                    <i class="fas fa-key me-2"></i>Change Password
                                </button>
                                <a href="../profile.php" class="btn btn-outline-secondary">
                                    <i class="fas fa-times me-2"></i>Cancel
                                </a>
                            </div>
                        </form>
                    </div>
                </div>

                <!-- Security Tips -->
                <div class="alert alert-info mt-4">
                    <h6><i class="fas fa-lightbulb me-2"></i>Password Security Tips</h6>
                    <ul class="mb-0">
                        <li>Use a combination of letters, numbers, and special characters</li>
                        <li>Avoid using personal information like names or birthdays</li>
                        <li>Don't reuse passwords from other accounts</li>
                        <li>Consider using a password manager</li>
                        <li>Change your password regularly</li>
                    </ul>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Password visibility toggles
        function setupPasswordToggle(inputId, toggleId) {
            const input = document.getElementById(inputId);
            const toggle = document.getElementById(toggleId);
            
            toggle.addEventListener('click', function() {
                const type = input.getAttribute('type') === 'password' ? 'text' : 'password';
                input.setAttribute('type', type);
                
                const icon = this.querySelector('i');
                icon.classList.toggle('fa-eye');
                icon.classList.toggle('fa-eye-slash');
            });
        }

        setupPasswordToggle('current_password', 'toggleCurrent');
        setupPasswordToggle('new_password', 'toggleNew');
        setupPasswordToggle('confirm_password', 'toggleConfirm');

        // Password strength and validation
        const newPassword = document.getElementById('new_password');
        const confirmPassword = document.getElementById('confirm_password');
        const strengthBar = document.getElementById('passwordStrength');
        const submitBtn = document.getElementById('submitBtn');
        const matchText = document.getElementById('passwordMatch');

        function checkPasswordStrength(password) {
            if (password.length < 6) return 'weak';
            if (password.length >= 8) return 'strong';
            return 'medium';
        }

        function updateRequirements() {
            const password = newPassword.value;
            
            // Length requirement
            const lengthReq = document.getElementById('req-length');
            if (password.length >= 6) {
                lengthReq.classList.add('met');
                lengthReq.classList.remove('unmet');
                lengthReq.querySelector('i').className = 'fas fa-check-circle me-2';
            } else {
                lengthReq.classList.add('unmet');
                lengthReq.classList.remove('met');
                lengthReq.querySelector('i').className = 'fas fa-circle me-2';
            }
        }

        function validateForm() {
            const password = newPassword.value;
            const confirm = confirmPassword.value;
            
            const isLengthValid = password.length >= 6;
            const isMatchValid = password === confirm && password !== '';
            
            submitBtn.disabled = !(isLengthValid && isMatchValid);
        }

        newPassword.addEventListener('input', function() {
            const password = this.value;
            const strength = checkPasswordStrength(password);
            
            // Update strength bar
            strengthBar.className = 'password-strength';
            if (password) {
                strengthBar.classList.add('strength-' + strength);
            }
            
            updateRequirements();
            validateForm();
        });

        confirmPassword.addEventListener('input', function() {
            const password = newPassword.value;
            const confirm = this.value;
            
            if (confirm) {
                if (password === confirm) {
                    matchText.textContent = 'Passwords match';
                    matchText.className = 'form-text text-success';
                } else {
                    matchText.textContent = 'Passwords do not match';
                    matchText.className = 'form-text text-danger';
                }
            } else {
                matchText.textContent = '';
            }
            
            validateForm();
        });

        // Initialize requirements
        updateRequirements();
    </script>
</body>
</html>
