<?php
session_start();
require_once 'config/database.php';
require_once 'includes/functions.php';

// Check if user is logged in
if (!isset($_SESSION['user_id'])) {
    header('Location: login.php');
    exit();
}

$user_id = $_SESSION['user_id'];
$user_role = $_SESSION['role'];

// Initialize database connection
$database = new Database();
$db = $database->getConnection();

// Get user information
$stmt = $db->prepare("SELECT * FROM users WHERE id = ?");
$stmt->execute([$user_id]);
$user = $stmt->fetch();

// Get current date ranges
$today = date('Y-m-d');
$this_month_start = date('Y-m-01');
$this_month_end = date('Y-m-t');
$last_month_start = date('Y-m-01', strtotime('-1 month'));
$last_month_end = date('Y-m-t', strtotime('-1 month'));

// Personal Statistics (for all users)
$personal_stats = [];

// Total expenses created by user
$stmt = $db->prepare("SELECT COUNT(*) as count, COALESCE(SUM(total_amount), 0) as total FROM expenses WHERE created_by = ?");
$stmt->execute([$user_id]);
$personal_stats['total_expenses'] = $stmt->fetch();

// This month expenses by user
$stmt = $db->prepare("SELECT COUNT(*) as count, COALESCE(SUM(total_amount), 0) as total FROM expenses WHERE created_by = ? AND DATE(created_at) BETWEEN ? AND ?");
$stmt->execute([$user_id, $this_month_start, $this_month_end]);
$personal_stats['this_month'] = $stmt->fetch();

// User's activity count
$stmt = $db->prepare("SELECT COUNT(*) as count FROM activity_logs WHERE user_id = ?");
$stmt->execute([$user_id]);
$personal_stats['activities'] = $stmt->fetch()['count'];

// Recent activities for user
$stmt = $db->prepare("
    SELECT al.*, e.exno 
    FROM activity_logs al 
    LEFT JOIN expenses e ON al.table_name = 'expenses' AND al.record_id = e.id
    WHERE al.user_id = ? 
    ORDER BY al.created_at DESC 
    LIMIT 5
");
$stmt->execute([$user_id]);
$recent_activities = $stmt->fetchAll();

// System Statistics (for administrators)
$system_stats = [];
if ($user_role === 'administrator') {
    // Total system expenses
    $stmt = $db->query("SELECT COUNT(*) as count, COALESCE(SUM(total_amount), 0) as total FROM expenses");
    $system_stats['total_expenses'] = $stmt->fetch();
    
    // This month system expenses
    $stmt = $db->prepare("SELECT COUNT(*) as count, COALESCE(SUM(total_amount), 0) as total FROM expenses WHERE DATE(created_at) BETWEEN ? AND ?");
    $stmt->execute([$this_month_start, $this_month_end]);
    $system_stats['this_month'] = $stmt->fetch();
    
    // Expenses by status
    $stmt = $db->query("SELECT status, COUNT(*) as count FROM expenses GROUP BY status");
    $status_breakdown = $stmt->fetchAll();
    $system_stats['status_breakdown'] = [];
    foreach ($status_breakdown as $status) {
        $system_stats['status_breakdown'][$status['status']] = $status['count'];
    }
    
    // Active users
    $stmt = $db->query("SELECT COUNT(*) as count FROM users WHERE is_active = 1");
    $system_stats['active_users'] = $stmt->fetch()['count'];
    
    // Users by role
    $stmt = $db->query("SELECT role, COUNT(*) as count FROM users WHERE is_active = 1 GROUP BY role");
    $role_breakdown = $stmt->fetchAll();
    $system_stats['role_breakdown'] = [];
    foreach ($role_breakdown as $role) {
        $system_stats['role_breakdown'][$role['role']] = $role['count'];
    }
    
    // Recent system activities
    $stmt = $db->prepare("
        SELECT al.*, u.full_name as user_name, e.exno 
        FROM activity_logs al 
        LEFT JOIN users u ON al.user_id = u.id
        LEFT JOIN expenses e ON al.table_name = 'expenses' AND al.record_id = e.id
        ORDER BY al.created_at DESC 
        LIMIT 10
    ");
    $stmt->execute();
    $system_activities = $stmt->fetchAll();
}

// Pending items for verification and reviewer roles
$pending_items = [];
if ($user_role === 'verification') {
    $stmt = $db->query("SELECT COUNT(*) as count FROM expenses WHERE status = 'open'");
    $pending_items['verification_pending'] = $stmt->fetch()['count'];
    
    // Get pending expenses for verification
    $stmt = $db->prepare("
        SELECT e.*, u.full_name as created_by_name 
        FROM expenses e 
        LEFT JOIN users u ON e.created_by = u.id 
        WHERE e.status = 'open' 
        ORDER BY e.created_at ASC 
        LIMIT 5
    ");
    $stmt->execute();
    $pending_items['verification_list'] = $stmt->fetchAll();
} elseif ($user_role === 'reviewer') {
    $stmt = $db->query("SELECT COUNT(*) as count FROM expenses WHERE status = 'pending'");
    $pending_items['review_pending'] = $stmt->fetch()['count'];
    
    // Get pending expenses for review
    $stmt = $db->prepare("
        SELECT e.*, u.full_name as created_by_name 
        FROM expenses e 
        LEFT JOIN users u ON e.created_by = u.id 
        WHERE e.status = 'pending' 
        ORDER BY e.created_at ASC 
        LIMIT 5
    ");
    $stmt->execute();
    $pending_items['review_list'] = $stmt->fetchAll();
}

// Performance metrics for verification and reviewer
$performance_metrics = [];
if ($user_role === 'verification' || $user_role === 'reviewer') {
    if ($user_role === 'verification') {
        // Verification performance
        $stmt = $db->prepare("SELECT COUNT(*) as count FROM expenses WHERE verification_by = ?");
        $stmt->execute([$user_id]);
        $performance_metrics['total_processed'] = $stmt->fetch()['count'];
        
        $stmt = $db->prepare("SELECT COUNT(*) as count FROM expenses WHERE verification_by = ? AND DATE(verification_date) BETWEEN ? AND ?");
        $stmt->execute([$user_id, $this_month_start, $this_month_end]);
        $performance_metrics['this_month_processed'] = $stmt->fetch()['count'];
    } elseif ($user_role === 'reviewer') {
        // Review performance
        $stmt = $db->prepare("SELECT COUNT(*) as count FROM expenses WHERE reviewer_by = ?");
        $stmt->execute([$user_id]);
        $performance_metrics['total_processed'] = $stmt->fetch()['count'];
        
        $stmt = $db->prepare("SELECT COUNT(*) as count FROM expenses WHERE reviewer_by = ? AND DATE(reviewer_date) BETWEEN ? AND ?");
        $stmt->execute([$user_id, $this_month_start, $this_month_end]);
        $performance_metrics['this_month_processed'] = $stmt->fetch()['count'];
    }
}

// Recent Expenses (Role-based filtering)
$recent_expenses_query = "
    SELECT e.*, u.full_name as created_by_name
    FROM expenses e
    LEFT JOIN users u ON e.created_by = u.id";

$recent_expenses_params = [];

// Apply role-based filtering (same as list.php)
if ($user_role === 'data_entry') {
    $recent_expenses_query .= " WHERE e.created_by = ?";
    $recent_expenses_params[] = $user_id;
}
// report_viewer, verification, reviewer, and administrator can see all data

$recent_expenses_query .= " ORDER BY e.created_at DESC LIMIT 10";

$stmt = $db->prepare($recent_expenses_query);
$stmt->execute($recent_expenses_params);
$recent_expenses = $stmt->fetchAll();

// Master Data Statistics (All Users)
$master_data_stats = [];
try {
    // Items count
    $stmt = $db->query("SELECT COUNT(*) as count FROM items WHERE is_active = 1");
    $master_data_stats['items'] = $stmt->fetch()['count'];

    // Customers count
    $stmt = $db->query("SELECT COUNT(*) as count FROM customers WHERE is_active = 1");
    $master_data_stats['customers'] = $stmt->fetch()['count'];

    // Drivers count
    $stmt = $db->query("SELECT COUNT(*) as count FROM drivers WHERE is_active = 1");
    $master_data_stats['drivers'] = $stmt->fetch()['count'];

    // Total master data records
    $master_data_stats['total'] = $master_data_stats['items'] + $master_data_stats['customers'] + $master_data_stats['drivers'];
} catch (Exception $e) {
    // If master data tables don't exist or error occurs, set defaults
    $master_data_stats = [
        'items' => 0,
        'customers' => 0,
        'drivers' => 0,
        'total' => 0
    ];
}
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Dashboard - Expense Management System</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="assets/css/style.css" rel="stylesheet">
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <style>
        .stat-card {
            transition: transform 0.2s;
        }
        .stat-card:hover {
            transform: translateY(-2px);
        }
        .activity-item {
            border-left: 3px solid #007bff;
            padding-left: 15px;
            margin-bottom: 15px;
        }
        .chart-container {
            position: relative;
            height: 300px;
        }
        .quick-action-btn {
            transition: all 0.2s;
        }
        .quick-action-btn:hover {
            transform: scale(1.05);
        }
    </style>
</head>
<body>
    <?php include 'includes/navbar.php'; ?>
    
    <div class="container-fluid mt-4">
        <!-- Page Header -->
        <div class="row mb-4">
            <div class="col">
                <h2><i class="fas fa-tachometer-alt me-2"></i>Dashboard</h2>
                <p class="text-muted">Welcome back, <?php echo htmlspecialchars($user['full_name']); ?>!</p>
            </div>
        </div>

        <!-- Error Messages -->
        <?php if (isset($_GET['error']) && $_GET['error'] === 'access_denied'): ?>
        <div class="row mb-4">
            <div class="col">
                <div class="alert alert-warning alert-dismissible fade show" role="alert">
                    <i class="fas fa-exclamation-triangle me-2"></i>
                    <strong>Access Denied:</strong> You do not have permission to access that page. Your role is <strong><?php echo ucfirst(str_replace('_', ' ', $user_role)); ?></strong> which has read-only access.
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            </div>
        </div>
        <?php endif; ?>

        <!-- Quick Overview (Compact) -->
        <div class="row mb-4">
            <div class="col-md-4">
                <div class="card stat-card h-100 border-primary">
                    <div class="card-body text-center">
                        <i class="fas fa-file-invoice-dollar fa-2x text-primary mb-2"></i>
                        <h4 class="text-primary"><?php echo number_format($personal_stats['total_expenses']['count']); ?></h4>
                        <p class="text-muted mb-1">My Total Expenses</p>
                        <small class="text-success">
                            ฿<?php echo number_format($personal_stats['total_expenses']['total'], 2); ?>
                        </small>
                    </div>
                </div>
            </div>
            <div class="col-md-4">
                <div class="card stat-card h-100 border-success">
                    <div class="card-body text-center">
                        <i class="fas fa-calendar-month fa-2x text-success mb-2"></i>
                        <h4 class="text-success"><?php echo number_format($personal_stats['this_month']['count']); ?></h4>
                        <p class="text-muted mb-1">This Month</p>
                        <small class="text-success">
                            ฿<?php echo number_format($personal_stats['this_month']['total'], 2); ?>
                        </small>
                    </div>
                </div>
            </div>
            <div class="col-md-4">
                <div class="card stat-card h-100 border-warning">
                    <div class="card-body text-center">
                        <i class="fas fa-user-circle fa-2x text-warning mb-2"></i>
                        <h4 class="text-warning"><?php echo ucfirst($user_role); ?></h4>
                        <p class="text-muted mb-1">My Role</p>
                        <small class="text-muted"><?php echo number_format($personal_stats['activities']); ?> activities</small>
                    </div>
                </div>
            </div>
        </div>

        <!-- Recent Pending Items for Verification/Reviewer -->
        <?php if (($user_role === 'verification' && !empty($pending_items['verification_list'])) ||
                  ($user_role === 'reviewer' && !empty($pending_items['review_list']))): ?>
        <div class="card mb-4">
            <div class="card-header">
                <h6 class="mb-0">
                    <i class="fas fa-clock me-1"></i>
                    Recent Pending <?php echo ucfirst($user_role); ?>
                    <span class="badge bg-warning ms-2">
                        <?php echo $user_role === 'verification' ? count($pending_items['verification_list']) : count($pending_items['review_list']); ?>
                    </span>
                </h6>
            </div>
            <div class="card-body">
                <?php
                $pending_list = $user_role === 'verification' ? $pending_items['verification_list'] : $pending_items['review_list'];
                foreach ($pending_list as $item):
                ?>
                <div class="d-flex justify-content-between align-items-center border-bottom py-2">
                    <div>
                        <strong><?php echo htmlspecialchars($item['exno']); ?></strong>
                        <br>
                        <small class="text-muted">
                            <i class="fas fa-user me-1"></i>by <?php echo htmlspecialchars($item['created_by_name']); ?>
                        </small>
                        <?php if (!empty($item['total_amount'])): ?>
                        <br>
                        <small class="text-success">
                            <i class="fas fa-money-bill me-1"></i><?php echo number_format($item['total_amount'], 2); ?> บาท
                        </small>
                        <?php endif; ?>
                    </div>
                    <div class="text-end">
                        <small class="text-muted d-block">
                            <i class="fas fa-calendar me-1"></i><?php echo formatDateTime($item['created_at'], 'M d, H:i'); ?>
                        </small>
                        <div class="mt-1">
                            <a href="expenses/view.php?id=<?php echo $item['id']; ?>" class="btn btn-sm btn-outline-primary me-1">
                                <i class="fas fa-eye me-1"></i>View
                            </a>
                            <?php if ($user_role === 'verification'): ?>
                            <a href="expenses/multi_verification.php" class="btn btn-sm btn-warning">
                                <i class="fas fa-check me-1"></i>Verify
                            </a>
                            <?php elseif ($user_role === 'reviewer'): ?>
                            <a href="expenses/multi_review.php" class="btn btn-sm btn-success">
                                <i class="fas fa-clipboard-check me-1"></i>Review
                            </a>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
                <?php endforeach; ?>
                <div class="text-center mt-3">
                    <a href="expenses/list.php?status=<?php echo $user_role === 'verification' ? 'open' : 'pending'; ?>"
                       class="btn btn-outline-primary btn-sm">
                        <i class="fas fa-list me-1"></i>View All Pending by Items
                    </a>
                    <?php if ($user_role === 'verification'): ?>
                        <a href="expenses/multi_verification.php" class="btn btn-sm btn-warning">
                            <i class="fas fa-check me-1"></i>Verify
                        </a>
                        <?php elseif ($user_role === 'reviewer'): ?>
                        <a href="expenses/multi_review.php" class="btn btn-sm btn-success">
                            <i class="fas fa-clipboard-check me-1"></i>Review
                        </a>
                    <?php endif; ?>
                </div>
            </div>
        </div>
        <?php endif; ?>

        <!-- Recent Expenses (Moved Up) -->
        <div class="card mb-4">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="card-title mb-0">
                    <i class="fas fa-receipt me-2"></i>Recent Expenses
                </h5>
                <a href="expenses/list.php" class="btn btn-outline-primary btn-sm">
                    <i class="fas fa-eye me-1"></i>View All
                </a>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-striped" id="recent-expenses-table">
                        <thead>
                            <tr>
                                <th>Expense No.</th>
                                <th>Job Date</th>
                                <th>Item</th>
                                <th>Driver</th>
                                <th>Transfer Amount</th>
                                <th>Receipt Numbers</th>
                                <th>Withdrawal Date</th>
                                <th>Status</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php if (!empty($recent_expenses)): ?>
                                <?php foreach ($recent_expenses as $expense): ?>
                                <tr>
                                    <td>
                                        <strong><?php echo htmlspecialchars($expense['exno']); ?></strong>
                                    </td>
                                    <td>
                                        <span class="text-success">
                                            ฿<?php echo number_format($expense['total_amount'], 2); ?>
                                        </span>
                                    </td>
                                    <td><?php echo getStatusBadge($expense['status']); ?></td>
                                    <td>
                                        <small class="text-muted">
                                            <?php echo date('M j, Y', strtotime($expense['created_at'])); ?>
                                        </small>
                                    </td>
                                    <td>
                                        <a href="expenses/view.php?id=<?php echo $expense['id']; ?>"
                                           class="btn btn-outline-primary btn-sm">
                                            <i class="fas fa-eye"></i>
                                        </a>
                                    </td>
                                </tr>
                                <?php endforeach; ?>
                            <?php else: ?>
                                <tr>
                                    <td colspan="5" class="text-center text-muted py-4">
                                        <i class="fas fa-inbox fa-2x mb-2 d-block"></i>
                                        No recent expenses found
                                    </td>
                                </tr>
                            <?php endif; ?>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>

        <!-- Administrator System Overview - Collapsible -->
        <?php if ($user_role === 'administrator'): ?>
        <div class="card mb-4">
            <div class="card-header">
                <h5 class="mb-0">
                    <button class="btn btn-link text-decoration-none p-0 w-100 text-start" type="button"
                            data-bs-toggle="collapse" data-bs-target="#systemOverview"
                            aria-expanded="false" aria-controls="systemOverview">
                        <i class="fas fa-chart-line me-2"></i>System Overview
                        <i class="fas fa-chevron-down float-end mt-1"></i>
                    </button>
                </h5>
            </div>
            <div class="collapse" id="systemOverview">
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-3">
                            <div class="card stat-card h-100 border-primary">
                                <div class="card-body text-center">
                                    <i class="fas fa-building fa-2x text-primary mb-2"></i>
                                    <h4 class="text-primary"><?php echo number_format($system_stats['total_expenses']['count']); ?></h4>
                                    <p class="text-muted mb-1">Total System Expenses</p>
                                    <small class="text-success">
                                        ฿<?php echo number_format($system_stats['total_expenses']['total'], 2); ?>
                                    </small>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="card stat-card h-100 border-success">
                                <div class="card-body text-center">
                                    <i class="fas fa-calendar-check fa-2x text-success mb-2"></i>
                                    <h4 class="text-success"><?php echo number_format($system_stats['this_month']['count']); ?></h4>
                                    <p class="text-muted mb-1">This Month (System)</p>
                                    <small class="text-success">
                                        ฿<?php echo number_format($system_stats['this_month']['total'], 2); ?>
                                    </small>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="card stat-card h-100 border-info">
                                <div class="card-body text-center">
                                    <i class="fas fa-users fa-2x text-info mb-2"></i>
                                    <h4 class="text-info"><?php echo number_format($system_stats['active_users']); ?></h4>
                                    <p class="text-muted mb-1">Active Users</p>
                                    <small class="text-muted">Currently active</small>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="card stat-card h-100 border-warning">
                                <div class="card-body text-center">
                                    <i class="fas fa-tasks fa-2x text-warning mb-2"></i>
                                    <h4 class="text-warning">
                                        <?php echo isset($system_stats['status_breakdown']['open']) ? $system_stats['status_breakdown']['open'] : 0; ?>
                                    </h4>
                                    <p class="text-muted mb-1">Pending Verification</p>
                                    <small class="text-muted">Awaiting action</small>
                                </div>
                            </div>
                        </div>

                        <!-- Checked Status Card -->
                        <div class="col-md-3">
                            <div class="card border-primary">
                                <div class="card-body text-center">
                                    <i class="fas fa-check-circle fa-2x text-primary mb-2"></i>
                                    <h4 class="text-primary">
                                        <?php echo isset($system_stats['status_breakdown']['checked']) ? $system_stats['status_breakdown']['checked'] : 0; ?>
                                    </h4>
                                    <p class="text-muted mb-1">Checked Items</p>
                                    <small class="text-muted">Ready for verification</small>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Charts for Administrator - Collapsible -->
        <div class="card mb-4">
            <div class="card-header">
                <h5 class="mb-0">
                    <button class="btn btn-link text-decoration-none p-0 w-100 text-start" type="button"
                            data-bs-toggle="collapse" data-bs-target="#chartsSection"
                            aria-expanded="false" aria-controls="chartsSection">
                        <i class="fas fa-chart-pie me-2"></i>Analytics & Charts
                        <i class="fas fa-chevron-down float-end mt-1"></i>
                    </button>
                </h5>
            </div>
            <div class="collapse" id="chartsSection">
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="card">
                                <div class="card-header">
                                    <h6 class="mb-0"><i class="fas fa-chart-pie me-1"></i>Expenses by Status</h6>
                                </div>
                                <div class="card-body">
                                    <div class="chart-container">
                                        <canvas id="statusChart"></canvas>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="card">
                                <div class="card-header">
                                    <h6 class="mb-0"><i class="fas fa-chart-bar me-1"></i>Users by Role</h6>
                                </div>
                                <div class="card-body">
                                    <div class="chart-container">
                                        <canvas id="roleChart"></canvas>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <?php endif; ?>

        <!-- Pending Items Cards for Verification/Reviewer -->
        <?php if ($user_role === 'verification' && isset($pending_items['verification_pending'])): ?>
        <div class="row mb-4">
            <div class="col-12">
                <h4><i class="fas fa-clock me-2"></i>Verification Dashboard</h4>
            </div>
        </div>
        <div class="row mb-4">
            <div class="col-md-4">
                <div class="card border-warning h-100">
                    <div class="card-body text-center">
                        <i class="fas fa-clock fa-2x text-warning mb-2"></i>
                        <h4 class="text-warning"><?php echo $pending_items['verification_pending']; ?></h4>
                        <p class="text-muted mb-1">Pending Verification</p>
                        <small class="text-muted">Awaiting your review</small>
                        <div class="mt-2">
                            <a href="expenses/list.php?status=open" class="btn btn-warning btn-sm">
                                <i class="fas fa-eye me-1"></i>View All
                            </a>
                        </div>
                    </div>
                </div>
            </div>
            <?php if (!empty($performance_metrics)): ?>
            <div class="col-md-4">
                <div class="card border-success h-100">
                    <div class="card-body text-center">
                        <i class="fas fa-check-circle fa-2x text-success mb-2"></i>
                        <h4 class="text-success"><?php echo $performance_metrics['total_processed']; ?></h4>
                        <p class="text-muted mb-1">Total Processed</p>
                        <small class="text-muted">All time</small>
                    </div>
                </div>
            </div>
            <div class="col-md-4">
                <div class="card border-info h-100">
                    <div class="card-body text-center">
                        <i class="fas fa-calendar-check fa-2x text-info mb-2"></i>
                        <h4 class="text-info"><?php echo $performance_metrics['this_month_processed']; ?></h4>
                        <p class="text-muted mb-1">This Month</p>
                        <small class="text-muted">Current month</small>
                    </div>
                </div>
            </div>
            <?php endif; ?>
        </div>
        <?php elseif ($user_role === 'reviewer' && isset($pending_items['review_pending'])): ?>
        <div class="row mb-4">
            <div class="col-12">
                <h4><i class="fas fa-clipboard-check me-2"></i>Review Dashboard</h4>
            </div>
        </div>
        <div class="row mb-4">
            <div class="col-md-4">
                <div class="card border-primary h-100">
                    <div class="card-body text-center">
                        <i class="fas fa-clipboard-list fa-2x text-primary mb-2"></i>
                        <h4 class="text-primary"><?php echo $pending_items['review_pending']; ?></h4>
                        <p class="text-muted mb-1">Pending Review</p>
                        <small class="text-muted">Awaiting your approval</small>
                        <div class="mt-2">
                            <a href="expenses/list.php?status=pending" class="btn btn-primary btn-sm">
                                <i class="fas fa-eye me-1"></i>View All
                            </a>
                        </div>
                    </div>
                </div>
            </div>
            <?php if (!empty($performance_metrics)): ?>
            <div class="col-md-4">
                <div class="card border-success h-100">
                    <div class="card-body text-center">
                        <i class="fas fa-check-circle fa-2x text-success mb-2"></i>
                        <h4 class="text-success"><?php echo $performance_metrics['total_processed']; ?></h4>
                        <p class="text-muted mb-1">Total Reviewed</p>
                        <small class="text-muted">All time</small>
                    </div>
                </div>
            </div>
            <div class="col-md-4">
                <div class="card border-info h-100">
                    <div class="card-body text-center">
                        <i class="fas fa-calendar-check fa-2x text-info mb-2"></i>
                        <h4 class="text-info"><?php echo $performance_metrics['this_month_processed']; ?></h4>
                        <p class="text-muted mb-1">This Month</p>
                        <small class="text-muted">Current month</small>
                    </div>
                </div>
            </div>
            <?php endif; ?>
        </div>
        <?php endif; ?>

        <!-- Master Data - Collapsible -->
        <div class="card mb-4">
            <div class="card-header">
                <h5 class="mb-0">
                    <button class="btn btn-link text-decoration-none p-0 w-100 text-start" type="button"
                            data-bs-toggle="collapse" data-bs-target="#masterDataSection"
                            aria-expanded="false" aria-controls="masterDataSection">
                        <i class="fas fa-database me-2"></i>Master Data Overview
                        <i class="fas fa-chevron-down float-end mt-1"></i>
                    </button>
                </h5>
            </div>
            <div class="collapse" id="masterDataSection">
                <div class="card-body">
                    <!-- Statistics -->
                    <div class="row mb-4">
                        <div class="col-md-3">
                            <div class="card stat-card h-100 border-primary">
                                <div class="card-body text-center">
                                    <i class="fas fa-boxes fa-2x text-primary mb-2"></i>
                                    <h4 class="text-primary"><?php echo number_format($master_data_stats['items']); ?></h4>
                                    <p class="text-muted mb-1">Active Items</p>
                                    <small class="text-muted">System inventory</small>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="card stat-card h-100 border-success">
                                <div class="card-body text-center">
                                    <i class="fas fa-building fa-2x text-success mb-2"></i>
                                    <h4 class="text-success"><?php echo number_format($master_data_stats['customers']); ?></h4>
                                    <p class="text-muted mb-1">Active Customers</p>
                                    <small class="text-muted">Business partners</small>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="card stat-card h-100 border-warning">
                                <div class="card-body text-center">
                                    <i class="fas fa-truck fa-2x text-warning mb-2"></i>
                                    <h4 class="text-warning"><?php echo number_format($master_data_stats['drivers']); ?></h4>
                                    <p class="text-muted mb-1">Active Drivers</p>
                                    <small class="text-muted">Transportation team</small>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="card stat-card h-100 border-info">
                                <div class="card-body text-center">
                                    <i class="fas fa-chart-bar fa-2x text-info mb-2"></i>
                                    <h4 class="text-info"><?php echo number_format($master_data_stats['total']); ?></h4>
                                    <p class="text-muted mb-1">Total Records</p>
                                    <small class="text-muted">All master data</small>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Quick Access -->
                    <h6 class="mb-3"><i class="fas fa-bolt me-2"></i>Quick Access</h6>
                    <div class="row">
                        <div class="col-md-4">
                            <a href="master_data/items.php" class="btn btn-outline-primary btn-lg w-100 quick-action-btn">
                                <i class="fas fa-boxes fa-2x d-block mb-2"></i>
                                Items
                                <small class="d-block text-muted">View all system items</small>
                            </a>
                        </div>
                        <div class="col-md-4">
                            <a href="master_data/customers.php" class="btn btn-outline-success btn-lg w-100 quick-action-btn">
                                <i class="fas fa-building fa-2x d-block mb-2"></i>
                                Customers
                                <small class="d-block text-muted">Browse customer information</small>
                            </a>
                        </div>
                        <div class="col-md-4">
                            <a href="master_data/drivers.php" class="btn btn-outline-warning btn-lg w-100 quick-action-btn">
                                <i class="fas fa-truck fa-2x d-block mb-2"></i>
                                Drivers
                                <small class="d-block text-muted">Access driver profiles</small>
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Role-Specific Quick Actions -->
        <?php if ($user_role === 'data_entry'): ?>
        <!-- Data Entry: Focus on creating and viewing own expenses -->
        <div class="row mb-4">
            <div class="col-12">
                <h4><i class="fas fa-bolt me-2"></i>Quick Actions</h4>
            </div>
        </div>
        <div class="row mb-4">
            <div class="col-md-4">
                <a href="expenses/create.php" class="btn btn-primary btn-lg w-100 quick-action-btn">
                    <i class="fas fa-plus-circle fa-2x d-block mb-2"></i>
                    Create New Expense
                    <small class="d-block text-muted">Add new expense record</small>
                </a>
            </div>
            <div class="col-md-4">
                <a href="expenses/list.php" class="btn btn-info btn-lg w-100 quick-action-btn">
                    <i class="fas fa-list fa-2x d-block mb-2"></i>
                    My Expenses
                    <small class="d-block text-muted">View my expense list</small>
                </a>
            </div>
            <div class="col-md-4">
                <a href="profile.php" class="btn btn-secondary btn-lg w-100 quick-action-btn">
                    <i class="fas fa-user fa-2x d-block mb-2"></i>
                    My Profile
                    <small class="d-block text-muted">Update profile info</small>
                </a>
            </div>
        </div>

        <?php elseif ($user_role === 'verification' || $user_role === 'reviewer'): ?>
        <!-- Verification/Reviewer: Focus on multi-processing -->
        <div class="row mb-4">
            <div class="col-12">
                <h4><i class="fas fa-bolt me-2"></i>Quick Actions</h4>
            </div>
        </div>
        <div class="row mb-4">
            <div class="col-md-3">
                <a href="expenses/<?php echo $user_role === 'verification' ? 'multi_verification' : 'multi_review'; ?>.php"
                   class="btn btn-<?php echo $user_role === 'verification' ? 'primary' : 'success'; ?> btn-lg w-100 quick-action-btn">
                    <i class="fas fa-<?php echo $user_role === 'verification' ? 'check-double' : 'clipboard-list'; ?> fa-2x d-block mb-2"></i>
                    Multi <?php echo ucfirst($user_role); ?>
                    <small class="d-block text-muted">Process multiple items</small>
                </a>
            </div>
            <div class="col-md-3">
                <a href="expenses/list.php?status=<?php echo $user_role === 'verification' ? 'open' : 'pending'; ?>"
                   class="btn btn-<?php echo $user_role === 'verification' ? 'warning' : 'info'; ?> btn-lg w-100 quick-action-btn">
                    <i class="fas fa-<?php echo $user_role === 'verification' ? 'search' : 'clipboard-check'; ?> fa-2x d-block mb-2"></i>
                    Single <?php echo $user_role === 'verification' ? 'Verify' : 'Review'; ?>
                    <small class="d-block text-muted">Process one by one</small>
                </a>
            </div>
            <?php if ($user_role === 'administrator'): ?>
            <div class="col-md-3">
                <div class="dropdown">
                    <button class="btn btn-secondary btn-lg w-100 quick-action-btn dropdown-toggle"
                            type="button"
                            id="batchDropdown"
                            data-bs-toggle="dropdown"
                            aria-expanded="false">
                        <i class="fas fa-history fa-2x d-block mb-2"></i>
                        All Batches
                        <small class="d-block text-muted">View batch history</small>
                    </button>
                    <ul class="dropdown-menu w-100" aria-labelledby="batchDropdown">
                        <li><a class="dropdown-item" href="expenses/my_batches.php?type=verification">
                            <i class="fas fa-search me-2"></i>Verification Batches
                        </a></li>
                        <li><a class="dropdown-item" href="expenses/my_batches.php?type=review">
                            <i class="fas fa-clipboard-check me-2"></i>Review Batches
                        </a></li>
                        <li><hr class="dropdown-divider"></li>
                        <li><a class="dropdown-item" href="admin/batch_logs.php">
                            <i class="fas fa-list-alt me-2"></i>Batch Logs
                        </a></li>
                    </ul>
                </div>
            </div>
            <?php else: ?>
            <div class="col-md-3">
                <a href="expenses/my_batches.php?type=<?php echo $user_role === 'reviewer' ? 'review' : $user_role; ?>"
                   class="btn btn-secondary btn-lg w-100 quick-action-btn">
                    <i class="fas fa-history fa-2x d-block mb-2"></i>
                    My Batches
                    <small class="d-block text-muted">View batch history</small>
                </a>
            </div>
            <?php endif; ?>
            <div class="col-md-3">
                <a href="reports/" class="btn btn-dark btn-lg w-100 quick-action-btn">
                    <i class="fas fa-chart-bar fa-2x d-block mb-2"></i>
                    Reports
                    <small class="d-block text-muted">View performance</small>
                </a>
            </div>
        </div>

        <?php elseif ($user_role === 'report_viewer'): ?>
        <!-- Report Viewer: Focus on reports only -->
        <div class="row mb-4">
            <div class="col-12">
                <h4><i class="fas fa-chart-bar me-2"></i>Reports & Analytics</h4>
            </div>
        </div>
        <div class="row mb-4">
            <div class="col-md-4">
                <a href="reports/" class="btn btn-primary btn-lg w-100 quick-action-btn">
                    <i class="fas fa-home fa-2x d-block mb-2"></i>
                    Reports Home
                    <small class="d-block text-muted">Main dashboard</small>
                </a>
            </div>
            <div class="col-md-4">
                <a href="reports/expense_summary.php" class="btn btn-success btn-lg w-100 quick-action-btn">
                    <i class="fas fa-chart-pie fa-2x d-block mb-2"></i>
                    Expense Summary
                    <small class="d-block text-muted">Financial overview</small>
                </a>
            </div>
            <div class="col-md-4">
                <a href="search/advanced.php" class="btn btn-info btn-lg w-100 quick-action-btn">
                    <i class="fas fa-search fa-2x d-block mb-2"></i>
                    Advanced Search
                    <small class="d-block text-muted">Find specific data</small>
                </a>
            </div>
        </div>

        <?php else: // administrator ?>
        <!-- Administrator: Full access with Bulk Operations emphasis -->
        <div class="row mb-4">
            <div class="col-12">
                <h4><i class="fas fa-bolt me-2"></i>Quick Actions</h4>
            </div>
        </div>
        <div class="row mb-4">
            <div class="col-md-3">
                <a href="expenses/bulk_operations.php" class="btn btn-primary btn-lg w-100 quick-action-btn">
                    <i class="fas fa-tasks fa-2x d-block mb-2"></i>
                    Bulk Operations
                    <small class="d-block text-muted">Mass process expenses</small>
                </a>
            </div>
            <div class="col-md-3">
                <a href="expenses/multi_verification.php" class="btn btn-warning btn-lg w-100 quick-action-btn">
                    <i class="fas fa-check-double fa-2x d-block mb-2"></i>
                    Multi Verification
                    <small class="d-block text-muted">Verify multiple items</small>
                </a>
            </div>
            <div class="col-md-3">
                <a href="expenses/multi_review.php" class="btn btn-success btn-lg w-100 quick-action-btn">
                    <i class="fas fa-clipboard-list fa-2x d-block mb-2"></i>
                    Multi Review
                    <small class="d-block text-muted">Review multiple items</small>
                </a>
            </div>
            <div class="col-md-3">
                <a href="admin/users.php" class="btn btn-danger btn-lg w-100 quick-action-btn">
                    <i class="fas fa-users-cog fa-2x d-block mb-2"></i>
                    Manage Users
                    <small class="d-block text-muted">User administration</small>
                </a>
            </div>
        </div>

        <!-- Secondary Admin Actions -->
        <div class="row mb-4">
            <div class="col-md-3">
                <a href="search/advanced.php" class="btn btn-info btn-lg w-100 quick-action-btn">
                    <i class="fas fa-search fa-2x d-block mb-2"></i>
                    Advanced Search
                    <small class="d-block text-muted">Find anything</small>
                </a>
            </div>
            <div class="col-md-3">
                <a href="reports/" class="btn btn-dark btn-lg w-100 quick-action-btn">
                    <i class="fas fa-chart-bar fa-2x d-block mb-2"></i>
                    Reports
                    <small class="d-block text-muted">System analytics</small>
                </a>
            </div>
            <div class="col-md-3">
                <a href="admin/expenses.php" class="btn btn-outline-primary btn-lg w-100 quick-action-btn">
                    <i class="fas fa-file-invoice-dollar fa-2x d-block mb-2"></i>
                    Admin Expenses
                    <small class="d-block text-muted">Full expense management</small>
                </a>
            </div>
            <div class="col-md-3">
                <a href="profile.php" class="btn btn-outline-secondary btn-lg w-100 quick-action-btn">
                    <i class="fas fa-user fa-2x d-block mb-2"></i>
                    My Profile
                    <small class="d-block text-muted">Account settings</small>
                </a>
            </div>
        </div>
        <?php endif; ?>

        <!-- Recent Activities -->
        <div class="row">
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h6 class="mb-0">
                            <i class="fas fa-history me-1"></i>My Recent Activities
                        </h6>
                    </div>
                    <div class="card-body">
                        <?php if (!empty($recent_activities)): ?>
                            <?php foreach ($recent_activities as $activity): ?>
                            <div class="activity-item">
                                <div class="d-flex justify-content-between">
                                    <strong><?php echo htmlspecialchars($activity['description']); ?></strong>
                                    <small class="text-muted"><?php echo formatDateTime($activity['created_at']); ?></small>
                                </div>
                                <?php if (!empty($activity['exno'])): ?>
                                <small class="text-muted">Expense: <?php echo htmlspecialchars($activity['exno']); ?></small>
                                <?php endif; ?>
                            </div>
                            <?php endforeach; ?>
                            <div class="text-center mt-3">
                                <a href="reports/activity_logs.php" class="btn btn-outline-primary btn-sm">
                                    View All Activities
                                </a>
                            </div>
                        <?php else: ?>
                            <p class="text-muted text-center">No recent activities</p>
                        <?php endif; ?>
                    </div>
                </div>
            </div>

            <!-- System Activities (Admin Only) -->
            <?php if ($user_role === 'administrator'): ?>
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h6 class="mb-0">
                            <i class="fas fa-globe me-1"></i>Recent System Activities
                        </h6>
                    </div>
                    <div class="card-body">
                        <?php if (!empty($system_activities)): ?>
                            <?php foreach ($system_activities as $activity): ?>
                            <div class="activity-item">
                                <div class="d-flex justify-content-between">
                                    <strong><?php echo htmlspecialchars($activity['user_name']); ?></strong>
                                    <small class="text-muted"><?php echo formatDateTime($activity['created_at']); ?></small>
                                </div>
                                <div><?php echo htmlspecialchars($activity['description']); ?></div>
                                <?php if (!empty($activity['exno'])): ?>
                                <small class="text-muted">Expense: <?php echo htmlspecialchars($activity['exno']); ?></small>
                                <?php endif; ?>
                            </div>
                            <?php endforeach; ?>
                            <div class="text-center mt-3">
                                <a href="reports/activity_logs.php" class="btn btn-outline-primary btn-sm">
                                    View All System Activities
                                </a>
                            </div>
                        <?php else: ?>
                            <p class="text-muted text-center">No recent system activities</p>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
            <?php else: ?>
            <!-- Personal Stats Summary for Non-Admin -->
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h6 class="mb-0">
                            <i class="fas fa-chart-line me-1"></i>My Summary
                        </h6>
                    </div>
                    <div class="card-body">
                        <div class="row text-center">
                            <div class="col-12 mb-3">
                                <h5 class="text-primary">Personal Statistics</h5>
                            </div>
                            <div class="col-6">
                                <div class="border-end">
                                    <h4 class="text-success"><?php echo number_format($personal_stats['total_expenses']['count']); ?></h4>
                                    <small class="text-muted">Total Expenses</small>
                                </div>
                            </div>
                            <div class="col-6">
                                <h4 class="text-info"><?php echo number_format($personal_stats['this_month']['count']); ?></h4>
                                <small class="text-muted">This Month</small>
                            </div>
                        </div>
                        <hr>
                        <div class="text-center">
                            <p class="text-muted mb-2">Total Amount</p>
                            <h4 class="text-success"><?php echo number_format($personal_stats['total_expenses']['total'], 2); ?> บาท</h4>
                        </div>
                    </div>
                </div>
            </div>
            <?php endif; ?>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script src="assets/js/dashboard.js"></script>

    <!-- Charts for Administrator -->
    <?php if ($user_role === 'administrator'): ?>
    <script>
        // Status Chart
        const statusCtx = document.getElementById('statusChart').getContext('2d');
        const statusChart = new Chart(statusCtx, {
            type: 'doughnut',
            data: {
                labels: ['Open', 'Checked', 'Pending', 'Success', 'Rejected', 'Returned'],
                datasets: [{
                    data: [
                        <?php echo isset($system_stats['status_breakdown']['open']) ? $system_stats['status_breakdown']['open'] : 0; ?>,
                        <?php echo isset($system_stats['status_breakdown']['checked']) ? $system_stats['status_breakdown']['checked'] : 0; ?>,
                        <?php echo isset($system_stats['status_breakdown']['pending']) ? $system_stats['status_breakdown']['pending'] : 0; ?>,
                        <?php echo isset($system_stats['status_breakdown']['success']) ? $system_stats['status_breakdown']['success'] : 0; ?>,
                        <?php echo isset($system_stats['status_breakdown']['rejected']) ? $system_stats['status_breakdown']['rejected'] : 0; ?>,
                        <?php echo isset($system_stats['status_breakdown']['returned']) ? $system_stats['status_breakdown']['returned'] : 0; ?>
                    ],
                    backgroundColor: [
                        '#ffc107',
                        '#007bff',
                        '#17a2b8',
                        '#28a745',
                        '#dc3545',
                        '#6c757d'
                    ],
                    borderWidth: 2,
                    borderColor: '#fff'
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        position: 'bottom'
                    }
                }
            }
        });

        // Role Chart
        const roleCtx = document.getElementById('roleChart').getContext('2d');
        const roleChart = new Chart(roleCtx, {
            type: 'bar',
            data: {
                labels: ['Data Entry', 'Verification', 'Reviewer', 'Report Viewer', 'Administrator'],
                datasets: [{
                    label: 'Number of Users',
                    data: [
                        <?php echo isset($system_stats['role_breakdown']['data_entry']) ? $system_stats['role_breakdown']['data_entry'] : 0; ?>,
                        <?php echo isset($system_stats['role_breakdown']['verification']) ? $system_stats['role_breakdown']['verification'] : 0; ?>,
                        <?php echo isset($system_stats['role_breakdown']['reviewer']) ? $system_stats['role_breakdown']['reviewer'] : 0; ?>,
                        <?php echo isset($system_stats['role_breakdown']['report_viewer']) ? $system_stats['role_breakdown']['report_viewer'] : 0; ?>,
                        <?php echo isset($system_stats['role_breakdown']['administrator']) ? $system_stats['role_breakdown']['administrator'] : 0; ?>
                    ],
                    backgroundColor: [
                        '#007bff',
                        '#ffc107',
                        '#28a745',
                        '#6f42c1',
                        '#dc3545'
                    ],
                    borderColor: [
                        '#0056b3',
                        '#e0a800',
                        '#1e7e34',
                        '#59359a',
                        '#c82333'
                    ],
                    borderWidth: 1
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                scales: {
                    y: {
                        beginAtZero: true,
                        ticks: {
                            stepSize: 1
                        }
                    }
                },
                plugins: {
                    legend: {
                        display: false
                    }
                }
            }
        });
    </script>
    <?php endif; ?>

    <script>
        // Auto-refresh dashboard every 5 minutes
        setTimeout(function() {
            location.reload();
        }, 300000); // 5 minutes

        // Add hover effects to stat cards
        document.querySelectorAll('.stat-card').forEach(card => {
            card.addEventListener('mouseenter', function() {
                this.style.boxShadow = '0 4px 8px rgba(0,0,0,0.1)';
            });

            card.addEventListener('mouseleave', function() {
                this.style.boxShadow = '';
            });
        });

        // Add click tracking for quick actions
        document.querySelectorAll('.quick-action-btn').forEach(btn => {
            btn.addEventListener('click', function() {
                // Could add analytics tracking here
                console.log('Quick action clicked:', this.textContent.trim());
            });
        });
    </script>
</body>
</html>
