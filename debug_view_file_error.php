<?php
// Enable error reporting
error_reporting(E_ALL);
ini_set('display_errors', 1);

session_start();

// Simulate logged in user for testing
if (!isset($_SESSION['user_id'])) {
    $_SESSION['user_id'] = 1;
    $_SESSION['role'] = 'admin';
    $_SESSION['username'] = 'test_user';
}

echo "<h2>Debug view_file.php Error</h2>";

$test_file = $_GET['file'] ?? 'transfer_68fcafb1efdee_1761390513.jpg';
$test_type = $_GET['type'] ?? 'transfer_slip';

echo "<h3>Testing: $test_file</h3>";

// Simulate the exact logic from view_file.php
echo "<h3>Step-by-step view_file.php simulation:</h3>";

// Step 1: Check session
echo "<p><strong>Step 1 - Session Check:</strong></p>";
echo "<ul>";
echo "<li>User ID: " . ($_SESSION['user_id'] ?? 'Not set') . "</li>";
echo "<li>Role: " . ($_SESSION['role'] ?? 'Not set') . "</li>";
echo "<li>Session check: " . (isset($_SESSION['user_id']) ? '✅ PASS' : '❌ FAIL') . "</li>";
echo "</ul>";

// Step 2: Parameter validation
echo "<p><strong>Step 2 - Parameter Validation:</strong></p>";
echo "<ul>";
echo "<li>File parameter: '$test_file'</li>";
echo "<li>Type parameter: '$test_type'</li>";
echo "<li>Parameters check: " . (!empty($test_file) && !empty($test_type) ? '✅ PASS' : '❌ FAIL') . "</li>";
echo "</ul>";

// Step 3: Filename sanitization
echo "<p><strong>Step 3 - Filename Sanitization:</strong></p>";
$file = basename($test_file);
$regex_check = preg_match('/^[a-zA-Z0-9_\-\.]+$/', $file);
echo "<ul>";
echo "<li>Original filename: '$test_file'</li>";
echo "<li>After basename(): '$file'</li>";
echo "<li>Regex validation: " . ($regex_check ? '✅ PASS' : '❌ FAIL') . "</li>";
echo "</ul>";

// Step 4: File path determination
echo "<p><strong>Step 4 - File Path Determination:</strong></p>";
$upload_dir = '../uploads/';
$file_path = $upload_dir . 'transfer_slips/' . $file;
echo "<ul>";
echo "<li>Upload directory: '$upload_dir'</li>";
echo "<li>Calculated path: '$file_path'</li>";
echo "<li>File exists: " . (file_exists($file_path) ? '✅ YES' : '❌ NO') . "</li>";
echo "<li>File readable: " . (is_readable($file_path) ? '✅ YES' : '❌ NO') . "</li>";
echo "</ul>";

// Step 5: File info
if (file_exists($file_path)) {
    echo "<p><strong>Step 5 - File Information:</strong></p>";
    $file_info = pathinfo($file_path);
    $file_extension = strtolower($file_info['extension']);
    
    $content_types = [
        'jpg' => 'image/jpeg',
        'jpeg' => 'image/jpeg',
        'png' => 'image/png',
        'gif' => 'image/gif',
        'pdf' => 'application/pdf'
    ];
    
    $content_type = $content_types[$file_extension] ?? 'application/octet-stream';
    
    echo "<ul>";
    echo "<li>File extension: '$file_extension'</li>";
    echo "<li>Content type: '$content_type'</li>";
    echo "<li>File size: " . filesize($file_path) . " bytes</li>";
    echo "</ul>";
}

// Step 6: Database access check (for data_entry users)
echo "<p><strong>Step 6 - Access Control Check:</strong></p>";
$user_role = $_SESSION['role'] ?? 'data_entry';
$user_id = $_SESSION['user_id'];
$has_access = true;

echo "<ul>";
echo "<li>User role: '$user_role'</li>";

if ($user_role === 'data_entry' && $test_type !== 'batch_document') {
    try {
        require_once 'config/database.php';
        $database = new Database();
        $db = $database->getConnection();
        
        echo "<li>Database connection: ✅ SUCCESS</li>";
        
        if ($test_type === 'receipt') {
            $stmt = $db->prepare("
                SELECT e.id, e.created_by
                FROM expenses e
                JOIN receipt_numbers rn ON e.id = rn.expense_id
                WHERE rn.receipt_image = ?
            ");
        } else {
            $stmt = $db->prepare("
                SELECT id, created_by
                FROM expenses
                WHERE transfer_slip_image = ? OR verification_slip_image = ? OR reviewer_slip_image = ?
            ");
        }
        
        if ($test_type === 'receipt') {
            $stmt->execute([$file]);
        } else {
            $stmt->execute([$file, $file, $file]);
        }
        
        $expense = $stmt->fetch();
        
        if ($expense) {
            $has_access = ($expense['created_by'] == $user_id);
            echo "<li>Found expense: ID {$expense['id']}, Created by: {$expense['created_by']}</li>";
            echo "<li>Access granted: " . ($has_access ? '✅ YES' : '❌ NO') . "</li>";
        } else {
            echo "<li>No expense found for this file</li>";
            echo "<li>Access granted: ✅ YES (default)</li>";
        }
        
    } catch (Exception $e) {
        echo "<li>Database error: " . $e->getMessage() . "</li>";
        echo "<li>Access granted: ✅ YES (fallback)</li>";
    }
} else {
    echo "<li>Access control: ✅ BYPASSED (admin or batch_document)</li>";
}
echo "</ul>";

// Step 7: Test actual file output
echo "<p><strong>Step 7 - File Output Test:</strong></p>";

if (file_exists($file_path) && $has_access) {
    echo "<div style='border: 1px solid #ccc; padding: 10px; margin: 10px 0;'>";
    echo "<p>Attempting to output file...</p>";
    
    // Test if we can read the file
    $file_content = file_get_contents($file_path);
    if ($file_content !== false) {
        echo "<p>✅ File content readable (" . strlen($file_content) . " bytes)</p>";
        
        // Test MIME type detection
        if (function_exists('mime_content_type')) {
            $detected_mime = mime_content_type($file_path);
            echo "<p>Detected MIME type: $detected_mime</p>";
        }
        
        // Create a data URL for testing
        $base64_content = base64_encode($file_content);
        $data_url = "data:$content_type;base64,$base64_content";
        
        echo "<p>✅ Base64 encoding successful</p>";
        echo "<p><strong>Direct image test:</strong></p>";
        echo "<img src='$data_url' style='max-width: 300px; border: 1px solid #ccc;' alt='Direct Base64 Image'>";
        
    } else {
        echo "<p>❌ Cannot read file content</p>";
    }
    echo "</div>";
} else {
    echo "<p>❌ Cannot proceed: File doesn't exist or access denied</p>";
}

// Test the actual view_file.php URL with error capture
echo "<p><strong>Step 8 - Test Actual view_file.php:</strong></p>";
$test_url = "api/view_file.php?file=" . urlencode($test_file) . "&type=$test_type";
echo "<p>URL: <a href='$test_url' target='_blank'>$test_url</a></p>";

// Use cURL to test the URL and capture any errors
if (function_exists('curl_init')) {
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, $test_url);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_HEADER, true);
    curl_setopt($ch, CURLOPT_NOBODY, false);
    curl_setopt($ch, CURLOPT_FOLLOWLOCATION, true);
    curl_setopt($ch, CURLOPT_TIMEOUT, 10);
    
    $response = curl_exec($ch);
    $http_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    $content_type_response = curl_getinfo($ch, CURLINFO_CONTENT_TYPE);
    curl_close($ch);
    
    echo "<div style='border: 1px solid #ddd; padding: 10px; margin: 10px 0; background: #f9f9f9;'>";
    echo "<p><strong>cURL Test Results:</strong></p>";
    echo "<ul>";
    echo "<li>HTTP Code: $http_code</li>";
    echo "<li>Content Type: $content_type_response</li>";
    echo "<li>Response length: " . strlen($response) . " bytes</li>";
    echo "</ul>";
    
    if ($http_code !== 200) {
        echo "<p><strong>Response Headers and Content:</strong></p>";
        echo "<pre style='background: #fff; padding: 10px; border: 1px solid #ccc; max-height: 300px; overflow: auto;'>";
        echo htmlspecialchars(substr($response, 0, 2000));
        echo "</pre>";
    } else {
        echo "<p>✅ HTTP 200 - File served successfully</p>";
    }
    echo "</div>";
}

// Check for common issues
echo "<h3>Common Issues Check:</h3>";
echo "<ul>";

// Check if output buffering is on
echo "<li>Output buffering: " . (ob_get_level() > 0 ? '⚠️ ON (level ' . ob_get_level() . ')' : '✅ OFF') . "</li>";

// Check if headers already sent
if (headers_sent($file, $line)) {
    echo "<li>Headers already sent: ❌ YES (in $file at line $line)</li>";
} else {
    echo "<li>Headers already sent: ✅ NO</li>";
}

// Check memory limit
echo "<li>Memory limit: " . ini_get('memory_limit') . "</li>";
echo "<li>Max execution time: " . ini_get('max_execution_time') . "s</li>";

echo "</ul>";

// Test form
echo "<h3>Test Different File:</h3>";
echo "<form method='GET' style='background: #f9f9f9; padding: 15px; border: 1px solid #ddd;'>";
echo "<label>File: <input type='text' name='file' value='$test_file' style='width: 400px;'></label><br><br>";
echo "<label>Type: <input type='text' name='type' value='$test_type' style='width: 200px;'></label><br><br>";
echo "<input type='submit' value='Debug File' style='padding: 10px 20px;'>";
echo "</form>";
?>
