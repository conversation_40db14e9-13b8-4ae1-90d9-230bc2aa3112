<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Checkbox Deductions System</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="assets/css/checkbox-deductions.css" rel="stylesheet">
</head>
<body>
    <div class="container mt-4">
        <h2>🧪 Test Checkbox Deductions System</h2>
        
        <div class="alert alert-info">
            <h5>Test Steps:</h5>
            <ol>
                <li>Upload receipt files (JPG, PNG, GIF, PDF)</li>
                <li>Check checkbox "มีรายการหัก" for receipts that have deductions</li>
                <li>Click "เพิ่มรายการหัก" button</li>
                <li>Fill deduction details and upload proof image (JPG, PNG, GIF, PDF only)</li>
                <li>Click "บันทึก" to save deduction</li>
                <li>Verify calculations and summary</li>
            </ol>
            <div class="alert alert-warning mt-2">
                <strong>⚠️ Note:</strong> For image upload testing, please use actual image files (JPG, PNG, GIF) or PDF files. Text files will be rejected.
            </div>
        </div>

        <!-- Test Receipt Upload -->
        <div class="card mb-4">
            <div class="card-header">
                <h5>1. Upload Receipt Files</h5>
            </div>
            <div class="card-body">
                <input type="file" id="receipts" class="form-control" multiple accept="image/*,.pdf">
                <div class="form-text">Select multiple receipt files to test</div>
            </div>
        </div>

        <!-- Receipt Numbers Container -->
        <div id="receipt-numbers-container" class="d-none">
            <div class="card mb-4">
                <div class="card-header">
                    <h5>2. Receipt Details with Checkbox System</h5>
                </div>
                <div class="card-body">
                    <div id="receipt-numbers"></div>
                    <div class="mt-3">
                        <strong>Total Amount: <span id="total-amount">0.00</span> บาท</strong>
                    </div>
                </div>
            </div>
        </div>

        <!-- Summary Container -->
        <div id="deductions-summary-container" class="d-none">
            <div class="card border-primary">
                <div class="card-header bg-primary text-white">
                    <h6 class="mb-0"><i class="fas fa-calculator me-2"></i>สรุปยอดรวม</h6>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-3">
                            <div class="text-center">
                                <div class="h4 text-success mb-1" id="total-gross-amount">0.00</div>
                                <small class="text-muted">ยอดก่อนหัก (บาท)</small>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="text-center">
                                <div class="h4 text-danger mb-1" id="total-deductions-amount">0.00</div>
                                <small class="text-muted">รายการหัก (บาท)</small>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="text-center">
                                <div class="h4 text-primary mb-1" id="total-net-amount">0.00</div>
                                <small class="text-muted">ยอดสุทธิ (บาท)</small>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="text-center">
                                <div class="h4 text-info mb-1" id="transfer-amount-display">0.00</div>
                                <small class="text-muted">Transfer Amount (บาท)</small>
                                <div id="amount-validation-status" class="mt-1"></div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Test Transfer Amount -->
        <div class="card mt-4">
            <div class="card-header">
                <h5>3. Test Transfer Amount Validation</h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <label class="form-label">Transfer Amount (บาท)</label>
                        <input type="number" id="transfer_amount" class="form-control" 
                               placeholder="0.00" step="0.01" min="0">
                        <div class="form-text">Enter transfer amount to test validation</div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Debug Info -->
        <div class="card mt-4">
            <div class="card-header">
                <h5>4. Debug Information</h5>
            </div>
            <div class="card-body">
                <button type="button" class="btn btn-info" onclick="showDebugInfo()">
                    <i class="fas fa-bug"></i> Show Debug Info
                </button>
                <pre id="debug-output" class="mt-3 d-none"></pre>
            </div>
        </div>
    </div>

    <!-- Deduction Modal -->
    <div class="modal fade" id="deductionModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">เพิ่มรายการหัก</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <form id="deduction-form">
                        <div class="row">
                            <div class="col-md-6">
                                <label class="form-label">ประเภทการหัก *</label>
                                <select class="form-control" id="deduction_type" required>
                                    <option value="">เลือกประเภท</option>
                                    <option value="tax_vat">ภาษีมูลค่าเพิ่ม (VAT)</option>
                                    <option value="tax_withholding">ภาษีหัก ณ ที่จ่าย</option>
                                    <option value="service_fee">ค่าธรรมเนียม</option>
                                    <option value="discount">ส่วนลด</option>
                                    <option value="penalty">ค่าปรับ</option>
                                    <option value="commission">ค่าคอมมิชชั่น</option>
                                    <option value="other">อื่นๆ</option>
                                </select>
                            </div>
                            <div class="col-md-6">
                                <label class="form-label">คำอธิบาย</label>
                                <input type="text" class="form-control" id="deduction_description" 
                                       placeholder="รายละเอียดเพิ่มเติม">
                            </div>
                        </div>
                        
                        <div class="row mt-3">
                            <div class="col-md-12">
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="is_percentage_based">
                                    <label class="form-check-label" for="is_percentage_based">
                                        คำนวณจากเปอร์เซ็นต์
                                    </label>
                                </div>
                            </div>
                        </div>
                        
                        <div class="row mt-3">
                            <div class="col-md-6" id="amount-input">
                                <label class="form-label">จำนวนเงิน (บาท) *</label>
                                <input type="number" class="form-control" id="deduction_amount" 
                                       placeholder="0.00" step="0.01" min="0">
                            </div>
                            <div class="col-md-6 d-none" id="percentage-input">
                                <label class="form-label">เปอร์เซ็นต์ *</label>
                                <input type="number" class="form-control" id="deduction_percentage" 
                                       placeholder="0.00" step="0.01" min="0" max="100">
                            </div>
                        </div>
                        
                        <div class="row mt-3">
                            <div class="col-md-12">
                                <label class="form-label">แนบหลักฐาน *</label>
                                <input type="file" class="form-control" id="deduction_image"
                                       accept="image/*,.pdf">
                                <div id="image-upload-status" class="mt-2"></div>

                                <!-- Image Preview -->
                                <div id="deduction_image_preview" class="mt-2" style="display: none;">
                                    <img src="" alt="Preview" class="img-thumbnail" style="max-width: 200px; max-height: 200px;">
                                </div>
                            </div>
                        </div>
                        
                        <div id="deduction-form-errors" class="mt-3"></div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">ยกเลิก</button>
                    <button type="button" class="btn btn-primary" id="save-deduction-btn">บันทึก</button>
                </div>
            </div>
        </div>
    </div>

    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script src="assets/js/expense-form.js"></script>
    <script src="assets/js/per-receipt-deductions.js"></script>

    <script>
        // Override upload URL for testing
        $(document).ready(function() {
            // Override the deduction image upload handler for testing
            $('#deduction_image').off('change').on('change', function() {
                const file = this.files[0];
                if (!file) return;

                // Show preview
                const reader = new FileReader();
                reader.onload = function(e) {
                    $('#deduction_image_preview img').attr('src', e.target.result);
                    $('#deduction_image_preview').show();
                };
                reader.readAsDataURL(file);

                // Upload file using test API
                const formData = new FormData();
                formData.append('deduction_image', file);

                $('#image-upload-status').html('<div class="alert alert-info"><i class="fas fa-spinner fa-spin"></i> กำลังอัพโหลด...</div>');

                $.ajax({
                    url: 'api/test_upload_deduction_image.php',
                    type: 'POST',
                    data: formData,
                    processData: false,
                    contentType: false,
                    success: function(response) {
                        if (response.success) {
                            // Store uploaded file info
                            $('#deduction_image').data('uploaded-filename', response.filename);
                            $('#deduction_image').data('uploaded-url', response.url);

                            $('#image-upload-status').html(`
                                <div class="alert alert-success">
                                    <i class="fas fa-check"></i> อัพโหลดสำเร็จ: ${response.original_name}
                                    <br><small>ไฟล์: ${response.filename}</small>
                                </div>
                            `);
                        } else {
                            $('#image-upload-status').html(`
                                <div class="alert alert-danger">
                                    <i class="fas fa-times"></i> อัพโหลดล้มเหลว: ${response.error}
                                </div>
                            `);
                        }
                    },
                    error: function(xhr, status, error) {
                        let errorMessage = 'เกิดข้อผิดพลาดในการอัพโหลด';

                        if (xhr.responseJSON && xhr.responseJSON.error) {
                            errorMessage = xhr.responseJSON.error;
                        } else if (xhr.responseText) {
                            try {
                                const response = JSON.parse(xhr.responseText);
                                errorMessage = response.error || errorMessage;
                            } catch (e) {
                                errorMessage = `HTTP ${xhr.status}: ${error}`;
                            }
                        }

                        $('#image-upload-status').html(`
                            <div class="alert alert-danger">
                                <i class="fas fa-times"></i> ${errorMessage}
                                <br><small>Status: ${xhr.status} ${error}</small>
                            </div>
                        `);
                    }
                });
            });
        });
    </script>
    
    <script>
        function showDebugInfo() {
            const debugInfo = {
                receiptDeductions: window.receiptDeductions,
                receiptFiles: window.receiptFiles,
                currentReceiptIndex: window.currentReceiptIndex,
                currentDeductionIndex: window.currentDeductionIndex,
                receiptCardsCount: $('.receipt-card').length,
                deductionsListElements: $('[id^="deductionsList"]').length,
                checkboxes: $('.has-deductions-checkbox').length
            };

            $('#debug-output').removeClass('d-none').text(JSON.stringify(debugInfo, null, 2));
        }
        
        // Test transfer amount validation
        $('#transfer_amount').on('input', function() {
            if (typeof updateAllCalculations === 'function') {
                updateAllCalculations();
            }
        });

        // Handle receipt file upload for testing
        $('#receipts').on('change', function() {
            const files = this.files;
            if (files.length === 0) return;

            // Store files in global variable
            window.receiptFiles = Array.from(files);

            // Show receipt numbers container
            $('#receipt-numbers-container').removeClass('d-none');

            // Generate receipt cards
            const container = $('#receipt-numbers');
            container.empty();

            Array.from(files).forEach((file, index) => {
                const receiptCard = $(`
                    <div class="card mb-3 receipt-card" data-receipt-index="${index}">
                        <div class="card-header">
                            <div class="row align-items-center">
                                <div class="col">
                                    <h6 class="mb-0"><i class="fas fa-receipt"></i> ${file.name}</h6>
                                </div>
                                <div class="col-auto">
                                    <div class="form-check">
                                        <input class="form-check-input has-deductions-checkbox"
                                               type="checkbox" id="hasDeductions${index}" data-receipt-index="${index}">
                                        <label class="form-check-label" for="hasDeductions${index}" title="มีรายการหัก">
                                            <i class="fas fa-minus-circle text-warning"></i>
                                            <small class="ms-1">มีรายการหัก</small>
                                        </label>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-4">
                                    <label class="form-label">Receipt Number *</label>
                                    <input type="text" class="form-control receipt-number" name="receipt_numbers[]"
                                           placeholder="เลขที่ใบเสร็จ" value="REC${String(index + 1).padStart(3, '0')}" required>
                                </div>
                                <div class="col-md-4">
                                    <label class="form-label">Amount (บาท) *</label>
                                    <input type="number" class="form-control receipt-amount" name="receipt_amounts[]"
                                           placeholder="0.00" step="0.01" min="0" value="${(index + 1) * 100}" required>
                                </div>
                                <div class="col-md-4">
                                    <label class="form-label">Description</label>
                                    <input type="text" class="form-control" name="receipt_descriptions[]"
                                           placeholder="รายละเอียด" value="Test Receipt ${index + 1}">
                                </div>
                            </div>

                            <!-- Deductions Section (แสดงเมื่อ checkbox ถูกเลือก) -->
                            <div class="deductions-section d-none mt-3" id="deductionsSection${index}">
                                <div class="card border-warning">
                                    <div class="card-header bg-warning bg-opacity-10">
                                        <h6 class="mb-0">
                                            <i class="fas fa-minus-circle text-warning"></i>
                                            รายการหักสำหรับใบเสร็จนี้
                                        </h6>
                                    </div>
                                    <div class="card-body">
                                        <!-- Deductions List -->
                                        <div class="deductions-list" id="deductionsList${index}">
                                            <div class="no-deductions-message text-muted text-center py-2">
                                                <i class="fas fa-info-circle"></i> ยังไม่มีรายการหัก
                                            </div>
                                        </div>

                                        <!-- Add Deduction Button -->
                                        <button type="button" class="btn btn-outline-warning btn-sm add-deduction-btn"
                                                data-receipt-index="${index}">
                                            <i class="fas fa-plus"></i> เพิ่มรายการหัก
                                        </button>

                                        <!-- Net Amount Display -->
                                        <div class="net-amount-display mt-2">
                                            <small class="text-muted">ยอดสุทธิ: </small>
                                            <span class="net-amount fw-bold text-success" id="netAmount${index}">0.00 บาท</span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                `);

                container.append(receiptCard);
            });

            // Initialize checkbox handlers
            if (typeof initializeCheckboxHandlers === 'function') {
                initializeCheckboxHandlers();
            }

            // Calculate total
            calculateTotal();

            console.log('Receipt cards created:', files.length);
        });

        // Calculate total function for test page
        function calculateTotal() {
            let total = 0;
            $('.receipt-amount').each(function() {
                const amount = parseFloat($(this).val()) || 0;
                total += amount;
            });
            $('#total-amount').text(total.toFixed(2));

            // Update per-receipt calculations if function exists
            if (typeof updateAllCalculations === 'function') {
                updateAllCalculations();
            }
        }

        // Add event listener for amount changes
        $(document).on('input', '.receipt-amount', calculateTotal);
    </script>
</body>
</html>
