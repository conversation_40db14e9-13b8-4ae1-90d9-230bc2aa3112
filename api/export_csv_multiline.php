<?php
session_start();
require_once '../config/database.php';
require_once '../includes/functions.php';

// Check if user is logged in
if (!isset($_SESSION['user_id'])) {
    header('Location: ../login.php');
    exit();
}

$database = new Database();
$db = $database->getConnection();

$user_id = $_SESSION['user_id'];
$user_role = $_SESSION['role'];

// Get filter parameters
$search = $_GET['search'] ?? '';
$status_filter = $_GET['status'] ?? '';
$date_from = $_GET['date_from'] ?? '';
$date_to = $_GET['date_to'] ?? '';

// Build query based on user role and filters
$where_conditions = [];
$params = [];

// Role-based access
if ($user_role === 'data_entry') {
    $where_conditions[] = 'e.created_by = ?';
    $params[] = $user_id;
}

// Search filter
if (!empty($search)) {
    $where_conditions[] = '(e.exno LIKE ? OR e.bookingno LIKE ? OR i.name LIKE ? OR c.name LIKE ? OR d.name LIKE ?)';
    $search_param = '%' . $search . '%';
    $params = array_merge($params, [$search_param, $search_param, $search_param, $search_param, $search_param]);
}

// Status filter
if (!empty($status_filter)) {
    $where_conditions[] = 'e.status = ?';
    $params[] = $status_filter;
}

// Date range filter
if (!empty($date_from)) {
    $where_conditions[] = 'e.job_open_date >= ?';
    $params[] = $date_from;
}

if (!empty($date_to)) {
    $where_conditions[] = 'e.job_open_date <= ?';
    $params[] = $date_to;
}

// Build WHERE clause
$where_clause = '';
if (!empty($where_conditions)) {
    $where_clause = 'WHERE ' . implode(' AND ', $where_conditions);
}

// Main query
$query = "
    SELECT
        e.*,
        i.name as item_name,
        c.name as customer_name,
        d.name as driver_name,
        u.full_name as created_by_name
    FROM expenses e
    LEFT JOIN items i ON e.item_id = i.id
    LEFT JOIN customers c ON e.customer_id = c.id
    LEFT JOIN drivers d ON e.driver_id = d.id
    LEFT JOIN users u ON e.created_by = u.id
    $where_clause
    ORDER BY e.created_at DESC
";

$stmt = $db->prepare($query);
$stmt->execute($params);
$expenses = $stmt->fetchAll();

// Set headers for CSV download
$filename = 'expenses_multiline_' . date('Y-m-d_H-i-s') . '.csv';
header('Content-Type: text/csv; charset=utf-8');
header('Content-Disposition: attachment; filename="' . $filename . '"');
header('Cache-Control: no-cache, must-revalidate');
header('Pragma: no-cache');

// Create file pointer
$output = fopen('php://output', 'w');

// Add BOM for UTF-8
fprintf($output, chr(0xEF).chr(0xBB).chr(0xBF));

// CSV Headers
$headers = [
    'Expense No',
    'Booking No', 
    'Job Open Date',
    'Item',
    'Customer',
    'Container No',
    'Driver',
    'Vehicle Plate',
    'Requester',
    'Receiver',
    'Payer',
    'Withdrawal Date',
    'Receipt Numbers (Multi-line)',
    'Receipt Total',
    'Transfer Amount',
    'Amount Match',
    'Status',
    'Verification Transfer No',
    'Review Transfer No',
    'Created By',
    'Created Date'
];

fputcsv($output, $headers);

// Process each expense
foreach ($expenses as $expense) {
    // Get receipt numbers for this expense
    $receipt_stmt = $db->prepare("SELECT receipt_number, amount FROM receipt_numbers WHERE expense_id = ? ORDER BY id");
    $receipt_stmt->execute([$expense['id']]);
    $receipts = $receipt_stmt->fetchAll();
    
    $receipt_numbers = [];
    $receipt_total = 0;
    
    foreach ($receipts as $receipt) {
        $receipt_numbers[] = $receipt['receipt_number'] . ' (' . number_format($receipt['amount'], 2) . ')';
        $receipt_total += $receipt['amount'];
    }
    
    // Use CRLF separator for multi-line display in CSV (Excel compatible)
    $receipt_numbers_str = implode("\r\n", $receipt_numbers);
    $amount_match = (abs($expense['transfer_amount'] - $receipt_total) <= 0.01) ? 'Match' : 'Mismatch';
    
    $row = [
        $expense['exno'],
        $expense['bookingno'],
        $expense['job_open_date'],
        $expense['item_name'],
        $expense['customer_name'],
        $expense['containerno'],
        $expense['driver_name'],
        $expense['vehicle_plate'],
        $expense['requester'],
        $expense['receiver'],
        $expense['payer'],
        $expense['withdrawal_date'],
        $receipt_numbers_str,  // Multi-line receipt numbers
        number_format($receipt_total, 2),
        number_format($expense['transfer_amount'], 2),
        $amount_match,
        ucfirst($expense['status']),
        $expense['reviewer_transfer_no'] ?? '',
        $expense['verification_transfer_no'] ?? '',
        $expense['created_by_name'],
        $expense['created_at']
    ];
    
    fputcsv($output, $row);
}

fclose($output);
exit();
?>
