<?php
session_start();
require_once '../config/database.php';
require_once '../includes/functions.php';
require_once '../includes/BatchOperations.php';
require_once '../includes/ImageUploadHelper.php';

header('Content-Type: application/json');

// Check if user is logged in
if (!isset($_SESSION['user_id'])) {
    http_response_code(401);
    echo json_encode(['success' => false, 'error' => 'Not authenticated']);
    exit();
}

// Check if user has reviewer role (or admin override)
$admin_override = isset($_POST['admin_override']) && $_POST['admin_override'] === '1' && $_SESSION['role'] === 'administrator';
if (!$admin_override && $_SESSION['role'] !== 'reviewer' && $_SESSION['role'] !== 'administrator') {
    http_response_code(403);
    echo json_encode(['success' => false, 'error' => 'Access denied. Reviewer role required.']);
    exit();
}

try {
    $database = new Database();
    $db = $database->getConnection();
    $batchOps = new BatchOperations($db);
    
    $user_id = $_SESSION['user_id'];
    $action = $_POST['action'] ?? '';
    
    switch ($action) {
        case 'create_batch':
            // สร้าง Batch ใหม่จาก verification batches
            $batch_ids = json_decode($_POST['batch_ids'] ?? '[]', true);
            $total_amount = floatval($_POST['total_amount'] ?? 0);
            $notes = $_POST['notes'] ?? null;



            if (empty($batch_ids)) {
                throw new Exception('No batches selected');
            }

            if ($total_amount <= 0) {
                throw new Exception('Invalid total amount: ' . $total_amount);
            }

            // ตรวจสอบว่า verification batches ที่เลือกไม่ได้อยู่ใน pending review batch แล้ว
            $placeholders_check = str_repeat('?,', count($batch_ids) - 1) . '?';
            $check_sql = "
                SELECT DISTINCT bi_source.batch_id, bo_review.batch_id as review_batch_id
                FROM batch_items bi_review
                JOIN expenses e_review ON bi_review.expense_id = e_review.id
                JOIN batch_items bi_source ON e_review.id = bi_source.expense_id
                JOIN batch_operations bo_review ON bi_review.batch_id = bo_review.batch_id
                WHERE bo_review.operation_type = 'review'
                AND bo_review.status IN ('pending', 'processing')
                AND bi_source.batch_id IN ($placeholders_check)
                AND bi_source.batch_id != bi_review.batch_id
            ";

            $check_stmt = $db->prepare($check_sql);
            $check_stmt->execute($batch_ids);
            $existing_batches = $check_stmt->fetchAll();

            if (!empty($existing_batches)) {
                $existing_batch_ids = array_column($existing_batches, 'batch_id');
                $review_batch_ids = array_column($existing_batches, 'review_batch_id');
                throw new Exception('Some verification batches are already included in pending review batches: ' .
                                  implode(', ', $existing_batch_ids) . ' (Review batches: ' .
                                  implode(', ', array_unique($review_batch_ids)) . ')');
            }

            // ดึง expenses จาก verification batches ที่เลือก
            $placeholders = str_repeat('?,', count($batch_ids) - 1) . '?';
            $sql = "
                SELECT e.id, e.exno, e.verification_amount, e.status, e.verification_by, bi.batch_id
                FROM expenses e
                JOIN batch_items bi ON e.id = bi.expense_id
                WHERE bi.batch_id IN ($placeholders) AND e.status = 'pending' AND e.verification_by IS NOT NULL
            ";

            $stmt = $db->prepare($sql);
            $stmt->execute($batch_ids);
            $expenses = $stmt->fetchAll();

            if (empty($expenses)) {
                // ลองดูข้อมูลที่มีอยู่ใน batch เพื่อ debug
                $debug_sql = "
                    SELECT e.id, e.exno, e.verification_amount, e.status, e.verification_by, bi.batch_id,
                           bo.operation_type, bo.status as batch_status
                    FROM expenses e
                    JOIN batch_items bi ON e.id = bi.expense_id
                    JOIN batch_operations bo ON bi.batch_id = bo.batch_id
                    WHERE bi.batch_id IN ($placeholders)
                ";
                $debug_stmt = $db->prepare($debug_sql);
                $debug_stmt->execute($batch_ids);
                $debug_expenses = $debug_stmt->fetchAll();

                $debug_info = [];
                foreach ($debug_expenses as $exp) {
                    $debug_info[] = "Batch: {$exp['batch_id']}, Expense: {$exp['exno']}, Status: {$exp['status']}, Verified: " .
                                   ($exp['verification_by'] ? 'Yes' : 'No') . ", Batch Type: {$exp['operation_type']}, Batch Status: {$exp['batch_status']}";
                }

                throw new Exception('No valid expenses found in selected batches (must be verified and in pending status). Found: ' .
                                  implode('; ', $debug_info));
            }
            
            // เตรียมข้อมูล expense items
            $expense_items = [];
            $expense_ids = [];
            $calculated_total = 0;
            
            foreach ($expenses as $expense) {
                $verification_amount = floatval($expense['verification_amount']);
                $expense_items[] = [
                    'expense_id' => $expense['id'],
                    'amount' => $verification_amount
                ];
                $expense_ids[] = $expense['id'];
                $calculated_total += $verification_amount;
            }

            // ตรวจสอบยอดรวม (ผ่อนปรนเป็น 1 บาท เพื่อรองรับการปัดเศษ)
            if (abs($calculated_total - $total_amount) > 1.00) {
                throw new Exception("Total amount mismatch. Expected: " . number_format($calculated_total, 2) . ", Got: " . number_format($total_amount, 2));
            }
            
            // สร้าง Batch
            $result = $batchOps->createBatch('review', $user_id, $expense_items, $total_amount, $notes);

            if (!$result['success']) {
                throw new Exception($result['error']);
            }
            
            echo json_encode([
                'success' => true,
                'batch_id' => $result['batch_id'],
                'message' => 'Review batch created successfully',
                'data' => [
                    'batch_id' => $result['batch_id'],
                    'item_count' => count($expense_items),
                    'total_amount' => $total_amount
                ]
            ]);
            break;
            
        case 'process_batch':
            // ประมวลผล Batch (Multi-file format like verification)
            $batch_id = $_POST['batch_id'] ?? '';
            $files = $_FILES['files'] ?? [];
            $transfer_numbers = $_POST['transfer_numbers'] ?? [];
            $amounts = $_POST['amounts'] ?? [];

            if (empty($batch_id)) {
                throw new Exception('Batch ID is required');
            }

            if (empty($files) || !is_array($files['name'])) {
                throw new Exception('Files are required');
            }

            if (empty($transfer_numbers) || !is_array($transfer_numbers)) {
                throw new Exception('Transfer numbers are required');
            }

            if (empty($amounts) || !is_array($amounts)) {
                throw new Exception('Amounts are required');
            }

            if (count($files['name']) !== count($transfer_numbers) || count($files['name']) !== count($amounts)) {
                throw new Exception('Files, transfer numbers, and amounts count mismatch');
            }
            
            // ตรวจสอบว่า batch มีอยู่และเป็นของ user นี้
            $batch_info = $batchOps->getBatchInfo($batch_id);
            if (!$batch_info) {
                throw new Exception('Batch not found');
            }

            // Debug logging
            error_log("Batch Review Debug - Batch ID: $batch_id");
            error_log("Batch Info: " . print_r($batch_info, true));
            error_log("User ID: $user_id, Admin Override: " . ($admin_override ? 'true' : 'false'));
            
            if (!$admin_override && $batch_info['user_id'] != $user_id) {
                throw new Exception('Access denied to this batch');
            }
            
            if ($batch_info['status'] !== 'pending') {
                throw new Exception('Batch is not in pending status. Current status: ' . $batch_info['status']);
            }

            // ตรวจสอบยอดรวมจากไฟล์ที่อัปโหลด
            $calculated_total = 0;
            foreach ($amounts as $amount) {
                $amount = floatval($amount);
                if ($amount <= 0) {
                    throw new Exception("Invalid amount: $amount");
                }
                $calculated_total += $amount;
            }

            // ตรวจสอบยอดรวม
            if (abs($calculated_total - $batch_info['total_amount']) > 0.01) {
                throw new Exception('Upload amount total (' . number_format($calculated_total, 2) . ') does not match expected total (' . number_format($batch_info['total_amount'], 2) . ')');
            }

            // จัดการไฟล์อัปโหลดหลายไฟล์
            $uploaded_files = [];
            for ($i = 0; $i < count($files['name']); $i++) {
                if ($files['error'][$i] !== UPLOAD_ERR_OK) {
                    throw new Exception("Error uploading file: " . $files['name'][$i]);
                }

                // สร้าง temporary file array สำหรับ ImageUploadHelper
                $temp_file = [
                    'name' => $files['name'][$i],
                    'type' => $files['type'][$i],
                    'tmp_name' => $files['tmp_name'][$i],
                    'error' => $files['error'][$i],
                    'size' => $files['size'][$i]
                ];

                // ใช้ ImageUploadHelper สำหรับการบีบอัดรูปภาพ
                $upload_result = ImageUploadHelper::uploadBatchReviewSlip($temp_file, $batch_id);

                if (!$upload_result['success']) {
                    throw new Exception('Failed to upload file ' . $files['name'][$i] . ': ' . $upload_result['error']);
                }

                $file_path = 'batch_documents/review/' . $upload_result['filename'];

                // Log compression results
                error_log("Batch review file compressed: {$upload_result['filename']} - " .
                         "Original: " . ImageCompressor::formatFileSize($upload_result['original_size']) .
                         ", Compressed: " . ImageCompressor::formatFileSize($upload_result['compressed_size']) .
                         " (Saved {$upload_result['compression_ratio']}%)");

                $uploaded_files[] = [
                    'file_path' => $file_path,
                    'original_filename' => $files['name'][$i],
                    'transfer_number' => $transfer_numbers[$i],
                    'amount' => floatval($amounts[$i]),
                    'file_size' => $files['size'][$i],
                    'mime_type' => $files['type'][$i]
                ];
            }
            
            // ประมวลผล Batch (Multi-file format)
            $result = $batchOps->processBatchReviewMultiFile($batch_id, $uploaded_files, $user_id);
            
            if (!$result['success']) {
                throw new Exception($result['error']);
            }
            
            // Log activity
            logActivity(
                $db,
                $user_id,
                'batch_review',
                'expenses',
                null,
                "Processed batch review: {$batch_id}, Items: {$result['processed']}, Success: {$result['success_count']}, Errors: {$result['error_count']}",
                $_SERVER['REMOTE_ADDR'],
                $_SERVER['HTTP_USER_AGENT']
            );
            
            echo json_encode([
                'success' => true,
                'message' => 'Batch review processed successfully',
                'data' => $result
            ]);
            break;
            
        case 'get_batch_info':
            // ดึงข้อมูล Batch
            $batch_id = $_GET['batch_id'] ?? '';
            
            if (empty($batch_id)) {
                throw new Exception('Batch ID is required');
            }
            
            $batch_info = $batchOps->getBatchInfo($batch_id);
            if (!$batch_info) {
                throw new Exception('Batch not found');
            }
            
            $batch_items = $batchOps->getBatchItems($batch_id);
            
            echo json_encode([
                'success' => true,
                'data' => [
                    'batch_info' => $batch_info,
                    'batch_items' => $batch_items
                ]
            ]);
            break;
            
        default:
            throw new Exception('Invalid action');
    }
    
} catch (Exception $e) {
    http_response_code(400);
    echo json_encode(['success' => false, 'error' => $e->getMessage()]);
}
?>
