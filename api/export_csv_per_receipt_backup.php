<?php
// Enable error reporting for debugging
error_reporting(E_ALL);
ini_set('display_errors', 1);
ini_set('log_errors', 1);

session_start();
require_once '../config/database.php';

// Don't require functions.php if not needed
if (file_exists('../includes/functions.php')) {
    require_once '../includes/functions.php';
}

// Check if user is logged in
if (!isset($_SESSION['user_id'])) {
    header('Location: ../login.php');
    exit();
}

try {
    $database = new Database();
    $db = $database->getConnection();
} catch (Exception $e) {
    die('Database connection failed: ' . $e->getMessage());
}

$user_id = $_SESSION['user_id'];
$user_role = $_SESSION['role'];

// Get filter parameters
$search = $_GET['search'] ?? '';
$status_filter = $_GET['status'] ?? '';
$date_from = $_GET['date_from'] ?? '';
$date_to = $_GET['date_to'] ?? '';

// Build query based on user role and filters
$where_conditions = [];
$params = [];

// Role-based access
if ($user_role === 'data_entry') {
    $where_conditions[] = 'e.created_by = ?';
    $params[] = $user_id;
}

// Search filter
if (!empty($search)) {
    $where_conditions[] = '(e.exno LIKE ? OR e.bookingno LIKE ? OR i.name LIKE ? OR c.name LIKE ? OR d.name LIKE ?)';
    $search_param = '%' . $search . '%';
    $params = array_merge($params, [$search_param, $search_param, $search_param, $search_param, $search_param]);
}

// Status filter
if (!empty($status_filter)) {
    $where_conditions[] = 'e.status = ?';
    $params[] = $status_filter;
}

// Date range filter
if (!empty($date_from)) {
    $where_conditions[] = 'e.job_open_date >= ?';
    $params[] = $date_from;
}

if (!empty($date_to)) {
    $where_conditions[] = 'e.job_open_date <= ?';
    $params[] = $date_to;
}

$where_clause = !empty($where_conditions) ? 'WHERE ' . implode(' AND ', $where_conditions) : '';

// Check if transfer_amount column exists
try {
    $stmt = $db->prepare("SHOW COLUMNS FROM expenses LIKE 'transfer_amount'");
    $stmt->execute();
    $transfer_amount_exists = $stmt->rowCount() > 0;
} catch (Exception $e) {
    die('Error checking transfer_amount column: ' . $e->getMessage());
}

$transfer_amount_field = $transfer_amount_exists ? 'e.transfer_amount' : '0 as transfer_amount';

// Get expenses with receipt numbers (JOIN to get all receipt rows)
// Note: MariaDB 5.5.68 doesn't support ROW_NUMBER() window function
$stmt = $db->prepare("
    SELECT
        e.id as expense_id,
        e.exno,
        e.job_open_date,
        e.bookingno,
        e.containerno,
        e.vehicle_plate,
        e.payment_account_no,
        e.additional_details,
        e.requester,
        e.receiver,
        e.payer,
        e.withdrawal_date,
        e.transfer_no,
        $transfer_amount_field,
        COALESCE(e.total_amount, 0) as total_amount,
        e.status,
        e.created_at,
        i.name as item_name,
        c.name as customer_name,
        d.name as driver_name,
        u.full_name as created_by_name,
        rn.id as receipt_id,
        rn.receipt_number,
        rn.amount as receipt_amount,
        rn.description as receipt_description
    FROM expenses e
    LEFT JOIN items i ON e.item_id = i.id
    LEFT JOIN customers c ON e.customer_id = c.id
    LEFT JOIN drivers d ON e.driver_id = d.id
    LEFT JOIN users u ON e.created_by = u.id
    LEFT JOIN receipt_numbers rn ON e.id = rn.expense_id
    $where_clause
    ORDER BY e.created_at DESC, e.id ASC, rn.id ASC
");

try {
    $stmt->execute($params);
    $results = $stmt->fetchAll();
} catch (Exception $e) {
    die('Error executing query: ' . $e->getMessage());
}

// Check if we have data
if (empty($results)) {
    die('No data found to export. Please check your filters.');
}

// Set CSV headers
$filename = 'expenses_per_receipt_' . date('Y-m-d_H-i-s') . '.csv';
header('Content-Type: text/csv; charset=utf-8');
header('Content-Disposition: attachment; filename="' . $filename . '"');

// Create output stream
$output = fopen('php://output', 'w');
if (!$output) {
    die('Error: Cannot create output stream');
}

// Add BOM for UTF-8
fprintf($output, chr(0xEF).chr(0xBB).chr(0xBF));

// CSV Headers
$headers = [
    'Expense No',
    'Job Date',
    'Booking No',
    'Item',
    'Customer',
    'Driver',
    'Container No',
    'Vehicle Plate',
    'Payment Account',
    'Requester',
    'Receiver',
    'Payer',
    'Withdrawal Date',
    'Transfer No',
    'Transfer Amount',
    'Receipt Number',
    'Receipt Amount',
    'Receipt Description',
    'Receipt Total',
    'Amount Match',
    'Status',
    'Additional Details',
    'Created By',
    'Created Date'
];

fputcsv($output, $headers);

// Process results - group by expense and show expense data only on first receipt row
$current_expense_id = null;
$expense_receipt_totals = [];
$first_receipt_ids = []; // Track first receipt ID for each expense

// First pass: calculate receipt totals and find first receipt for each expense
foreach ($results as $row) {
    if (!isset($expense_receipt_totals[$row['expense_id']])) {
        $expense_receipt_totals[$row['expense_id']] = 0;
        // Store the first receipt ID for this expense (or null if no receipts)
        $first_receipt_ids[$row['expense_id']] = $row['receipt_id'];
    }
    if ($row['receipt_amount']) {
        $expense_receipt_totals[$row['expense_id']] += $row['receipt_amount'];
    }
}

// Second pass: output CSV rows
foreach ($results as $row) {
    // Check if this is the first receipt for this expense
    $is_first_receipt = ($row['receipt_id'] === $first_receipt_ids[$row['expense_id']]) ||
                       ($row['receipt_id'] === null && $first_receipt_ids[$row['expense_id']] === null);

    // For expenses with no receipts, show expense data once
    if ($row['receipt_id'] === null && $current_expense_id === $row['expense_id']) {
        continue; // Skip duplicate expense rows with no receipts
    }

    if ($row['receipt_id'] === null) {
        $current_expense_id = $row['expense_id'];
        $is_first_receipt = true;
    }
    
    // Calculate amount match for this expense
    $receipt_total = $expense_receipt_totals[$row['expense_id']];
    $amount_match = (abs($row['transfer_amount'] - $receipt_total) <= 0.01) ? 'Match' : 'Mismatch';
    
    if ($is_first_receipt) {
        // First receipt row: show all expense data
        $csv_row = [
            $row['exno'],
            $row['job_open_date'],
            $row['bookingno'],
            $row['item_name'],
            $row['customer_name'],
            $row['driver_name'],
            $row['containerno'],
            $row['vehicle_plate'],
            $row['payment_account_no'],
            $row['requester'],
            $row['receiver'],
            $row['payer'],
            $row['withdrawal_date'],
            $row['transfer_no'],
            number_format($row['transfer_amount'], 2),
            $row['receipt_number'] ?? '',
            $row['receipt_amount'] ? number_format($row['receipt_amount'], 2) : '',
            $row['receipt_description'] ?? '',
            number_format($receipt_total, 2),
            $amount_match,
            ucfirst($row['status']),
            $row['additional_details'],
            $row['created_by_name'],
            $row['created_at']
        ];
    } else {
        // Subsequent receipt rows: empty expense data, show only receipt data
        $csv_row = [
            '', // Expense No
            '', // Job Date
            '', // Booking No
            '', // Item
            '', // Customer
            '', // Driver
            '', // Container No
            '', // Vehicle Plate
            '', // Payment Account
            '', // Requester
            '', // Receiver
            '', // Payer
            '', // Withdrawal Date
            '', // Transfer No
            '', // Transfer Amount
            $row['receipt_number'] ?? '',
            $row['receipt_amount'] ? number_format($row['receipt_amount'], 2) : '',
            $row['receipt_description'] ?? '',
            '', // Receipt Total (empty for subsequent rows)
            '', // Amount Match (empty for subsequent rows)
            '', // Status
            '', // Additional Details
            '', // Created By
            ''  // Created Date
        ];
    }
    
    fputcsv($output, $csv_row);
}

fclose($output);
exit();
?>
