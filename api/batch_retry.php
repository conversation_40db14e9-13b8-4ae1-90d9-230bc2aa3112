<?php
session_start();
require_once '../config/database.php';
require_once '../includes/functions.php';
require_once '../includes/BatchOperations.php';

// Check if user is logged in
if (!isset($_SESSION['user_id'])) {
    http_response_code(401);
    echo json_encode(['error' => 'Unauthorized']);
    exit();
}

// Check request method
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode(['error' => 'Method not allowed']);
    exit();
}

try {
    $database = new Database();
    $db = $database->getConnection();
    $batchOps = new BatchOperations($db);
    
    $user_id = $_SESSION['user_id'];
    $user_role = $_SESSION['role'];
    
    // Get JSON input
    $input = json_decode(file_get_contents('php://input'), true);
    $batch_id = $input['batch_id'] ?? '';
    
    if (empty($batch_id)) {
        throw new Exception('Batch ID is required');
    }
    
    // Get batch info
    $batch_info = $batchOps->getBatchInfo($batch_id);
    if (!$batch_info) {
        throw new Exception('Batch not found');
    }
    
    // Check permissions
    $can_retry = false;
    if ($user_role === 'administrator') {
        $can_retry = true;
    } elseif ($batch_info['user_id'] == $user_id) {
        // Users can retry their own failed batches
        $can_retry = ($batch_info['status'] === 'failed');
        
        // Check role permissions for batch type
        if ($batch_info['operation_type'] === 'verification' && !in_array($user_role, ['verification', 'administrator'])) {
            $can_retry = false;
        }
        if ($batch_info['operation_type'] === 'review' && !in_array($user_role, ['reviewer', 'administrator'])) {
            $can_retry = false;
        }
    }
    
    if (!$can_retry) {
        throw new Exception('You do not have permission to retry this batch');
    }
    
    // Check if batch can be retried
    if ($batch_info['status'] !== 'failed') {
        throw new Exception('Only failed batches can be retried');
    }
    
    // Start transaction
    $db->beginTransaction();
    
    // Reset batch status to pending
    $stmt = $db->prepare("
        UPDATE batch_operations 
        SET status = 'pending', 
            started_at = NULL,
            completed_at = NULL,
            notes = CONCAT(COALESCE(notes, ''), '\nRetried by: ', ?, ' at ', NOW())
        WHERE batch_id = ?
    ");
    $stmt->execute([$_SESSION['full_name'], $batch_id]);
    
    // Reset all failed batch items to pending
    $stmt = $db->prepare("
        UPDATE batch_items 
        SET status = 'pending',
            error_message = NULL,
            processed_at = NULL
        WHERE batch_id = ? AND status IN ('failed', 'completed')
    ");
    $stmt->execute([$batch_id]);
    
    // Log the activity
    $action_type = $user_role === 'administrator' ? 'admin_override' : 'batch_retry';
    logActivity(
        $db,
        $user_id,
        $action_type,
        'batch_operations',
        null,
        "Retried batch: {$batch_id} (Operation: {$batch_info['operation_type']})",
        $_SERVER['REMOTE_ADDR'],
        $_SERVER['HTTP_USER_AGENT']
    );
    
    $db->commit();
    
    echo json_encode([
        'success' => true,
        'message' => 'Batch reset successfully and ready for reprocessing'
    ]);
    
} catch (Exception $e) {
    if (isset($db)) {
        $db->rollback();
    }
    error_log('Batch retry error: ' . $e->getMessage());
    http_response_code(500);
    echo json_encode(['error' => $e->getMessage()]);
}
?>
