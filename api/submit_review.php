<?php
session_start();
require_once '../config/database.php';
require_once '../includes/functions.php';
require_once '../includes/ImageUploadHelper.php';

header('Content-Type: application/json');

// Check if user is logged in
if (!isset($_SESSION['user_id'])) {
    http_response_code(401);
    echo json_encode(['success' => false, 'error' => 'Not authenticated']);
    exit();
}

// Check if this is an admin override
$admin_override = isset($_POST['admin_override']) && $_POST['admin_override'] === '1' && $_SESSION['role'] === 'administrator';

// Check if user has reviewer role (or admin override)
if (!$admin_override && $_SESSION['role'] !== 'reviewer' && $_SESSION['role'] !== 'administrator') {
    http_response_code(403);
    echo json_encode(['success' => false, 'error' => 'Access denied. Reviewer role required.']);
    exit();
}

try {
    $database = new Database();
    $db = $database->getConnection();
    
    $user_id = $_SESSION['user_id'];
    $expense_id = $_POST['expense_id'] ?? null;
    $reviewer_amount = $_POST['reviewer_amount'] ?? null;
    $reviewer_transfer_no = $_POST['reviewer_transfer_no'] ?? null;

    // Validate input
    if (!$expense_id || !$reviewer_amount) {
        throw new Exception('Expense ID and review amount are required');
    }

    if (!$admin_override && empty($reviewer_transfer_no)) {
        throw new Exception('Review transfer number is required');
    }
    
    if (!is_numeric($reviewer_amount) || $reviewer_amount <= 0) {
        throw new Exception('Invalid review amount');
    }
    
    // Get expense details
    $stmt = $db->prepare("SELECT * FROM expenses WHERE id = ?");
    $stmt->execute([$expense_id]);
    $expense = $stmt->fetch();
    
    if (!$expense) {
        throw new Exception('Expense not found');
    }
    
    // Check if expense is in correct status (admin can override)
    if (!$admin_override && $expense['status'] !== 'pending') {
        throw new Exception('Expense must be in "pending" status for review');
    }

    // Check if verification exists (admin can override)
    if (!$admin_override && empty($expense['verification_by'])) {
        throw new Exception('Expense must be verified before review');
    }

    // Check if review already exists (admin can override)
    if (!$admin_override && !empty($expense['reviewer_by'])) {
        throw new Exception('Expense has already been reviewed');
    }
    
    // Validate amount consistency with verification (admin can override)
    if (!$admin_override && !empty($expense['verification_amount']) && abs($expense['verification_amount'] - $reviewer_amount) > 0.01) {
        throw new Exception('Review amount must match verification amount (Verification: ' .
                          number_format($expense['verification_amount'], 2) . ' บาท)');
    }
    
    // Handle file upload (optional for admin override)
    $reviewer_slip_image = null;
    if (isset($_FILES['reviewer_slip']) && $_FILES['reviewer_slip']['error'] === UPLOAD_ERR_OK) {
        $upload_dir = '../uploads/review_slips/';
        
        // Create directory if it doesn't exist
        if (!is_dir($upload_dir)) {
            mkdir($upload_dir, 0755, true);
        }
        
        // Use ImageUploadHelper for compression
        $uploadResult = ImageUploadHelper::uploadReviewSlip($_FILES['reviewer_slip']);

        if (!$uploadResult['success']) {
            throw new Exception('Failed to upload review slip: ' . $uploadResult['error']);
        }

        $reviewer_slip_image = $uploadResult['filename'];

        // Log compression results
        error_log("Review slip compressed: {$uploadResult['filename']} - " .
                 "Original: " . ImageCompressor::formatFileSize($uploadResult['original_size']) .
                 ", Compressed: " . ImageCompressor::formatFileSize($uploadResult['compressed_size']) .
                 " (Saved {$uploadResult['compression_ratio']}%)");
    } else {
        throw new Exception('Review slip image is required');
    }
    
    // Update expense with review data
    $stmt = $db->prepare("
        UPDATE expenses SET
            reviewer_slip_image = ?,
            reviewer_amount = ?,
            reviewer_date = NOW(),
            reviewer_by = ?,
            reviewer_transfer_no = ?
        WHERE id = ?
    ");

    $stmt->execute([
        $reviewer_slip_image,
        $reviewer_amount,
        $user_id,
        $reviewer_transfer_no,
        $expense_id
    ]);
    
    // Log the activity
    $activity_description = $admin_override ?
        "Admin override: Updated review: Amount " . number_format($reviewer_amount, 2) . " บาท" :
        "Submitted review: Amount " . number_format($reviewer_amount, 2) . " บาท, Slip uploaded";

    logActivity(
        $db,
        $user_id,
        $admin_override ? 'admin_override' : 'update',
        'expenses',
        $expense_id,
        $activity_description,
        $_SERVER['REMOTE_ADDR'],
        $_SERVER['HTTP_USER_AGENT']
    );
    
    echo json_encode([
        'success' => true,
        'message' => 'Review submitted successfully',
        'data' => [
            'reviewer_amount' => $reviewer_amount,
            'reviewer_slip_image' => $reviewer_slip_image,
            'reviewer_date' => date('Y-m-d H:i:s')
        ]
    ]);
    
} catch (Exception $e) {
    http_response_code(400);
    echo json_encode(['success' => false, 'error' => $e->getMessage()]);
}
?>
