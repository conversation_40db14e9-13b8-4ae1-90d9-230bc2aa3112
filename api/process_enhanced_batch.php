<?php
/**
 * API: Process Enhanced Batch
 * Handles multiple file uploads with individual transfer numbers
 */

session_start();
require_once '../config/database.php';
require_once '../includes/functions.php';
require_once '../includes/BatchOperations.php';
require_once '../includes/ImageUploadHelper.php';

header('Content-Type: application/json');

// Check authentication
if (!isset($_SESSION['user_id'])) {
    http_response_code(401);
    echo json_encode(['success' => false, 'error' => 'Not authenticated']);
    exit();
}

// Check permissions
if (!in_array($_SESSION['role'], ['verification', 'reviewer', 'administrator'])) {
    http_response_code(403);
    echo json_encode(['success' => false, 'error' => 'Access denied']);
    exit();
}

try {
    $database = new Database();
    $db = $database->getConnection();
    $batchOps = new BatchOperations($db);
    
    $user_id = $_SESSION['user_id'];
    $batch_id = $_POST['batch_id'] ?? '';
    $batch_type = $_POST['batch_type'] ?? '';
    $total_amount = floatval($_POST['total_amount'] ?? 0);
    
    // Validate input
    if (empty($batch_id) || empty($batch_type)) {
        throw new Exception('Missing required parameters');
    }
    
    // Get batch info
    $batch_info = $batchOps->getBatchInfo($batch_id);
    if (!$batch_info) {
        throw new Exception('Batch not found');
    }
    
    // Check ownership
    if ($_SESSION['role'] !== 'administrator' && $batch_info['user_id'] != $user_id) {
        throw new Exception('Access denied to this batch');
    }
    
    // Process uploaded files
    $uploaded_files = [];
    $transfer_numbers = $_POST['transfer_numbers'] ?? [];
    $amounts = $_POST['amounts'] ?? [];
    
    if (empty($_FILES['files']['name'][0])) {
        throw new Exception('No files uploaded');
    }
    
    // Validate amounts match
    $entered_total = array_sum(array_map('floatval', $amounts));
    if (abs($entered_total - $total_amount) > 0.01) {
        throw new Exception('Entered amounts do not match batch total');
    }
    
    $db->beginTransaction();
    
    try {
        // Create upload directory
        $upload_dir = '../uploads/batch_slips/' . $batch_id . '/';
        if (!is_dir($upload_dir)) {
            mkdir($upload_dir, 0755, true);
        }
        
        // Process each file
        for ($i = 0; $i < count($_FILES['files']['name']); $i++) {
            if ($_FILES['files']['error'][$i] !== UPLOAD_ERR_OK) {
                continue;
            }
            
            $file = [
                'name' => $_FILES['files']['name'][$i],
                'type' => $_FILES['files']['type'][$i],
                'tmp_name' => $_FILES['files']['tmp_name'][$i],
                'error' => $_FILES['files']['error'][$i],
                'size' => $_FILES['files']['size'][$i]
            ];
            
            $transfer_number = $transfer_numbers[$i] ?? '';
            $amount = floatval($amounts[$i] ?? 0);
            
            if (empty($transfer_number) || $amount <= 0) {
                throw new Exception("Invalid transfer number or amount for file {$file['name']}");
            }
            
            // Upload file
            $upload_result = uploadFile($file, $upload_dir, ['jpg', 'jpeg', 'png', 'pdf'], 5242880);
            if (!$upload_result['success']) {
                throw new Exception("Failed to upload {$file['name']}: " . $upload_result['error']);
            }
            
            $uploaded_files[] = [
                'filename' => $upload_result['filename'],
                'original_name' => $file['name'],
                'transfer_number' => $transfer_number,
                'amount' => $amount,
                'file_path' => $upload_dir . $upload_result['filename']
            ];
            
            // Log file operation
            $stmt = $db->prepare("
                INSERT INTO file_operations_logs 
                (operation_type, file_path, file_name, file_size_bytes, mime_type, 
                 batch_id, user_id, ip_address, user_agent, success) 
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            ");
            $stmt->execute([
                'UPLOAD',
                $upload_result['filename'],
                $file['name'],
                $file['size'],
                $file['type'],
                $batch_id,
                $user_id,
                $_SERVER['REMOTE_ADDR'],
                $_SERVER['HTTP_USER_AGENT'],
                true
            ]);
        }
        
        if (empty($uploaded_files)) {
            throw new Exception('No files were successfully uploaded');
        }
        
        // Update batch with file information
        $file_data = json_encode($uploaded_files);
        $stmt = $db->prepare("
            UPDATE batch_operations 
            SET status = 'completed', 
                completed_at = NOW(),
                file_data = ?,
                total_files = ?,
                total_amount = ?
            WHERE batch_id = ?
        ");
        $stmt->execute([
            $file_data,
            count($uploaded_files),
            $total_amount,
            $batch_id
        ]);
        
        // Update expenses status based on batch type
        $batch_items = $batchOps->getBatchItems($batch_id);
        $new_status = ($batch_type === 'verification') ? 'pending' : 'success';
        
        foreach ($batch_items as $item) {
            $stmt = $db->prepare("
                UPDATE expenses 
                SET status = ?, 
                    verification_by = ?, 
                    verification_date = NOW(),
                    batch_id = ?
                WHERE id = ?
            ");
            $stmt->execute([$new_status, $user_id, $batch_id, $item['id']]);
            
            // Log batch operation
            $stmt = $db->prepare("
                CALL LogBatchOperation(?, ?, ?, ?, ?, ?, ?)
            ");
            $stmt->execute([
                $batch_id,
                'PROCESS',
                $item['id'],
                $user_id,
                json_encode(['status_change' => $new_status, 'files_count' => count($uploaded_files)]),
                null,
                null
            ]);
        }
        
        // Log system event
        $stmt = $db->prepare("
            CALL LogSystemEvent(?, ?, ?, ?, ?, ?, ?)
        ");
        $stmt->execute([
            'INFO',
            'BATCH_PROCESS',
            "Enhanced batch {$batch_id} processed successfully",
            json_encode([
                'batch_id' => $batch_id,
                'batch_type' => $batch_type,
                'items_count' => count($batch_items),
                'files_count' => count($uploaded_files),
                'total_amount' => $total_amount
            ]),
            $user_id,
            $_SERVER['REMOTE_ADDR'],
            $_SERVER['HTTP_USER_AGENT']
        ]);
        
        $db->commit();
        
        echo json_encode([
            'success' => true,
            'message' => 'Batch processed successfully',
            'batch_id' => $batch_id,
            'files_uploaded' => count($uploaded_files),
            'total_amount' => $total_amount
        ]);
        
    } catch (Exception $e) {
        $db->rollback();
        
        // Clean up uploaded files on error
        foreach ($uploaded_files as $file_info) {
            if (file_exists($file_info['file_path'])) {
                unlink($file_info['file_path']);
            }
        }
        
        throw $e;
    }
    
} catch (Exception $e) {
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'error' => $e->getMessage()
    ]);
    
    // Log error
    error_log("Enhanced batch process error: " . $e->getMessage());
}
?>
