<?php
session_start();
require_once '../config/database.php';
require_once '../includes/functions.php';

header('Content-Type: application/json');

// Check if user is logged in
if (!isset($_SESSION['user_id'])) {
    http_response_code(401);
    echo json_encode(['error' => 'Unauthorized']);
    exit();
}

if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode(['error' => 'Method not allowed']);
    exit();
}

try {
    $input = json_decode(file_get_contents('php://input'), true);
    $name = trim($input['name'] ?? '');
    $license_number = trim($input['license_number'] ?? '');
    $phone = trim($input['phone'] ?? '');
    $vehicle_plate = trim($input['vehicle_plate'] ?? '');
    $payment_account_no = trim($input['payment_account_no'] ?? '');
    
    if (empty($name)) {
        http_response_code(400);
        echo json_encode(['error' => 'Driver name is required']);
        exit();
    }
    
    $database = new Database();
    $db = $database->getConnection();
    
    // Check if driver already exists
    $stmt = $db->prepare("SELECT id FROM drivers WHERE name = ? AND is_active = 1");
    $stmt->execute([$name]);
    
    if ($stmt->fetch()) {
        http_response_code(409);
        echo json_encode(['error' => 'Driver already exists']);
        exit();
    }
    
    // Check if license number already exists (if provided)
    if (!empty($license_number)) {
        $stmt = $db->prepare("SELECT id FROM drivers WHERE license_number = ? AND is_active = 1");
        $stmt->execute([$license_number]);
        
        if ($stmt->fetch()) {
            http_response_code(409);
            echo json_encode(['error' => 'License number already exists']);
            exit();
        }
    }
    
    // Check if vehicle plate already exists (if provided)
    if (!empty($vehicle_plate)) {
        $stmt = $db->prepare("SELECT id FROM drivers WHERE vehicle_plate = ? AND is_active = 1");
        $stmt->execute([$vehicle_plate]);
        
        if ($stmt->fetch()) {
            http_response_code(409);
            echo json_encode(['error' => 'Vehicle plate already exists']);
            exit();
        }
    }
    
    // Insert new driver
    $stmt = $db->prepare("
        INSERT INTO drivers (name, license_number, phone, vehicle_plate, payment_account_no, created_by) 
        VALUES (?, ?, ?, ?, ?, ?)
    ");
    
    $stmt->execute([$name, $license_number, $phone, $vehicle_plate, $payment_account_no, $_SESSION['user_id']]);
    $driver_id = $db->lastInsertId();
    
    // Log the activity
    logActivity(
        $db, 
        $_SESSION['user_id'], 
        'create', 
        'drivers', 
        $driver_id, 
        'Created new driver: ' . $name, 
        $_SERVER['REMOTE_ADDR'], 
        $_SERVER['HTTP_USER_AGENT']
    );
    
    echo json_encode([
        'success' => true,
        'id' => $driver_id,
        'name' => $name,
        'vehicle_plate' => $vehicle_plate,
        'payment_account_no' => $payment_account_no,
        'message' => 'Driver created successfully'
    ]);
    
} catch (Exception $e) {
    error_log('Add driver error: ' . $e->getMessage());
    http_response_code(500);
    echo json_encode(['error' => 'Internal server error']);
}
?>
