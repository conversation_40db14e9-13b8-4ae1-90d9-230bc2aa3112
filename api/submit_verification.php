<?php
session_start();
require_once '../config/database.php';
require_once '../includes/functions.php';
require_once '../includes/ImageUploadHelper.php';

header('Content-Type: application/json');

// Check if user is logged in
if (!isset($_SESSION['user_id'])) {
    http_response_code(401);
    echo json_encode(['success' => false, 'error' => 'Not authenticated']);
    exit();
}

// Check if this is an admin override
$admin_override = isset($_POST['admin_override']) && $_POST['admin_override'] === '1' && $_SESSION['role'] === 'administrator';

// Check if user has verification role (or admin override)
if (!$admin_override && $_SESSION['role'] !== 'verification' && $_SESSION['role'] !== 'administrator') {
    http_response_code(403);
    echo json_encode(['success' => false, 'error' => 'Access denied. Verification role required.']);
    exit();
}

try {
    $database = new Database();
    $db = $database->getConnection();
    
    $user_id = $_SESSION['user_id'];
    $expense_id = $_POST['expense_id'] ?? null;
    $verification_amount = $_POST['verification_amount'] ?? null;
    $verification_transfer_no = $_POST['verification_transfer_no'] ?? null;

    // Validate input
    if (!$expense_id || !$verification_amount) {
        throw new Exception('Expense ID and verification amount are required');
    }

    if (!$admin_override && empty($verification_transfer_no)) {
        throw new Exception('Verification transfer number is required');
    }
    
    if (!is_numeric($verification_amount) || $verification_amount <= 0) {
        throw new Exception('Invalid verification amount');
    }
    
    // Get expense details
    $stmt = $db->prepare("SELECT * FROM expenses WHERE id = ?");
    $stmt->execute([$expense_id]);
    $expense = $stmt->fetch();
    
    if (!$expense) {
        throw new Exception('Expense not found');
    }
    
    // Check if expense is in correct status (admin can override)
    if (!$admin_override && $expense['status'] !== 'open') {
        throw new Exception('Expense must be in "open" status for verification');
    }

    // Check if verification already exists (admin can override)
    if (!$admin_override && !empty($expense['verification_by'])) {
        throw new Exception('Expense has already been verified');
    }
    
    // Handle file upload (optional for admin override)
    $verification_slip_image = null;
    if (isset($_FILES['verification_slip']) && $_FILES['verification_slip']['error'] === UPLOAD_ERR_OK) {
        $upload_dir = '../uploads/verification_slips/';
        
        // Create directory if it doesn't exist
        if (!is_dir($upload_dir)) {
            mkdir($upload_dir, 0755, true);
        }
        
        // Use ImageUploadHelper for compression
        $uploadResult = ImageUploadHelper::uploadVerificationSlip($_FILES['verification_slip']);

        if (!$uploadResult['success']) {
            throw new Exception('Failed to upload verification slip: ' . $uploadResult['error']);
        }

        $verification_slip_image = $uploadResult['filename'];

        // Log compression results
        error_log("Verification slip compressed: {$uploadResult['filename']} - " .
                 "Original: " . ImageCompressor::formatFileSize($uploadResult['original_size']) .
                 ", Compressed: " . ImageCompressor::formatFileSize($uploadResult['compressed_size']) .
                 " (Saved {$uploadResult['compression_ratio']}%)");
    } else {
        throw new Exception('Verification slip image is required');
    }
    
    // Update expense with verification data
    $stmt = $db->prepare("
        UPDATE expenses SET
            verification_slip_image = ?,
            verification_amount = ?,
            verification_date = NOW(),
            verification_by = ?,
            verification_transfer_no = ?
        WHERE id = ?
    ");

    $stmt->execute([
        $verification_slip_image,
        $verification_amount,
        $user_id,
        $verification_transfer_no,
        $expense_id
    ]);
    
    // Log the activity
    $activity_description = $admin_override ?
        "Admin override: Updated verification: Amount " . number_format($verification_amount, 2) . " บาท" :
        "Submitted verification: Amount " . number_format($verification_amount, 2) . " บาท, Slip uploaded";

    logActivity(
        $db,
        $user_id,
        $admin_override ? 'admin_override' : 'update',
        'expenses',
        $expense_id,
        $activity_description,
        $_SERVER['REMOTE_ADDR'],
        $_SERVER['HTTP_USER_AGENT']
    );
    
    echo json_encode([
        'success' => true,
        'message' => 'Verification submitted successfully',
        'data' => [
            'verification_amount' => $verification_amount,
            'verification_slip_image' => $verification_slip_image,
            'verification_date' => date('Y-m-d H:i:s')
        ]
    ]);
    
} catch (Exception $e) {
    http_response_code(400);
    echo json_encode(['success' => false, 'error' => $e->getMessage()]);
}
?>
