<?php
session_start();
require_once '../config/database.php';
require_once '../includes/functions.php';

header('Content-Type: application/json');

// Check if user is logged in
if (!isset($_SESSION['user_id'])) {
    http_response_code(401);
    echo json_encode(['error' => 'Unauthorized']);
    exit();
}

$type = $_GET['type'] ?? '';
$query = $_GET['q'] ?? '';

if (empty($type) || empty($query)) {
    echo json_encode([]);
    exit();
}

try {
    $database = new Database();
    $db = $database->getConnection();
    
    $results = [];
    
    switch ($type) {
        case 'items':
            $stmt = $db->prepare("
                SELECT id, name 
                FROM items 
                WHERE name LIKE ? AND is_active = 1 
                ORDER BY name 
                LIMIT 10
            ");
            $stmt->execute(['%' . $query . '%']);
            $results = $stmt->fetchAll();
            break;
            
        case 'customers':
            $stmt = $db->prepare("
                SELECT id, name 
                FROM customers 
                WHERE name LIKE ? AND is_active = 1 
                ORDER BY name 
                LIMIT 10
            ");
            $stmt->execute(['%' . $query . '%']);
            $results = $stmt->fetchAll();
            break;
            
        case 'drivers':
            $stmt = $db->prepare("
                SELECT id, name, vehicle_plate, payment_account_no 
                FROM drivers 
                WHERE name LIKE ? AND is_active = 1 
                ORDER BY name 
                LIMIT 10
            ");
            $stmt->execute(['%' . $query . '%']);
            $results = $stmt->fetchAll();
            break;
            
        default:
            echo json_encode([]);
            exit();
    }
    
    echo json_encode($results);
    
} catch (Exception $e) {
    error_log('Autocomplete error: ' . $e->getMessage());
    http_response_code(500);
    echo json_encode(['error' => 'Internal server error']);
}
?>
