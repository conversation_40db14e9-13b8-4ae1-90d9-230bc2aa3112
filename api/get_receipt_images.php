<?php
/**
 * API: Get Receipt Images
 * Returns receipt images for a specific expense from receipt_numbers table
 */

session_start();
require_once '../config/database.php';

header('Content-Type: application/json');

// Check authentication
if (!isset($_SESSION['user_id'])) {
    http_response_code(401);
    echo json_encode(['success' => false, 'error' => 'Not authenticated']);
    exit();
}

// Check permissions
if (!in_array($_SESSION['role'], ['verification', 'reviewer', 'administrator'])) {
    http_response_code(403);
    echo json_encode(['success' => false, 'error' => 'Access denied']);
    exit();
}

try {
    $database = new Database();
    $db = $database->getConnection();
    
    $expense_id = $_GET['expense_id'] ?? '';
    
    if (empty($expense_id)) {
        throw new Exception('Expense ID is required');
    }
    
    // Validate expense_id is numeric
    if (!is_numeric($expense_id)) {
        throw new Exception('Invalid expense ID');
    }
    
    // Get receipt images from receipt_numbers table
    $stmt = $db->prepare("
        SELECT 
            id,
            receipt_number,
            amount,
            description,
            receipt_image
        FROM receipt_numbers 
        WHERE expense_id = ? 
        AND receipt_image IS NOT NULL 
        AND receipt_image != ''
        ORDER BY id
    ");
    
    $stmt->execute([$expense_id]);
    $receipts = $stmt->fetchAll();
    
    echo json_encode([
        'success' => true,
        'data' => $receipts
    ]);
    
} catch (Exception $e) {
    http_response_code(400);
    echo json_encode([
        'success' => false,
        'error' => $e->getMessage()
    ]);
    
    error_log("Get receipt images error: " . $e->getMessage());
}
?>
