<?php
session_start();
require_once '../config/database.php';
require_once '../includes/functions.php';

header('Content-Type: application/json');

// Check if user is logged in
if (!isset($_SESSION['user_id'])) {
    http_response_code(401);
    echo json_encode(['error' => 'Unauthorized']);
    exit();
}

if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode(['error' => 'Method not allowed']);
    exit();
}

try {
    // Handle both JSON and form data
    if ($_SERVER['CONTENT_TYPE'] === 'application/json' || strpos($_SERVER['CONTENT_TYPE'], 'application/json') !== false) {
        $input = json_decode(file_get_contents('php://input'), true);
        $expense_id = (int)($input['expense_id'] ?? 0);
        $new_status = trim($input['status'] ?? '');
        $comment = trim($input['comment'] ?? '');
        $admin_override = isset($input['admin_override']) && $input['admin_override'] === '1';
    } else {
        $expense_id = (int)($_POST['expense_id'] ?? 0);
        $new_status = trim($_POST['new_status'] ?? '');
        $comment = trim($_POST['comment'] ?? '');
        $admin_override = isset($_POST['admin_override']) && $_POST['admin_override'] === '1';
    }

    if (empty($expense_id) || empty($new_status)) {
        http_response_code(400);
        echo json_encode(['error' => 'Expense ID and status are required']);
        exit();
    }

    // Check if this is an admin override
    $is_admin = $_SESSION['role'] === 'administrator';
    $admin_override = $admin_override && $is_admin;
    
    // Validate status
    $valid_statuses = ['open', 'pending', 'success'];
    if (!in_array($new_status, $valid_statuses)) {
        http_response_code(400);
        echo json_encode(['error' => 'Invalid status']);
        exit();
    }
    
    $database = new Database();
    $db = $database->getConnection();
    
    $user_id = $_SESSION['user_id'];
    $user_role = $_SESSION['role'];
    
    // Get current expense
    $stmt = $db->prepare("
        SELECT id, status, created_by, exno 
        FROM expenses 
        WHERE id = ?
    ");
    $stmt->execute([$expense_id]);
    $expense = $stmt->fetch();
    
    if (!$expense) {
        http_response_code(404);
        echo json_encode(['error' => 'Expense not found']);
        exit();
    }
    
    $current_status = $expense['status'];
    
    // Check if status change is allowed (admin can override)
    if (!$admin_override && !canChangeStatus($current_status, $new_status, $user_role)) {
        http_response_code(403);
        echo json_encode(['error' => 'You do not have permission to change status from ' . $current_status . ' to ' . $new_status]);
        exit();
    }
    
    // If status is the same, no need to update
    if ($current_status === $new_status) {
        echo json_encode([
            'success' => true,
            'message' => 'Status is already ' . $new_status
        ]);
        exit();
    }
    
    // Update expense status
    $stmt = $db->prepare("
        UPDATE expenses 
        SET status = ?, updated_at = CURRENT_TIMESTAMP 
        WHERE id = ?
    ");
    $stmt->execute([$new_status, $expense_id]);
    
    // Log the status change
    $description = $admin_override ?
        "Admin override: Changed status from '{$current_status}' to '{$new_status}'" :
        "Changed status from '{$current_status}' to '{$new_status}'";
    if (!empty($comment)) {
        $description .= ". Comment: " . $comment;
    }

    logActivity(
        $db,
        $user_id,
        $admin_override ? 'admin_override' : 'status_change',
        'expenses',
        $expense_id,
        $description,
        $_SERVER['REMOTE_ADDR'],
        $_SERVER['HTTP_USER_AGENT'],
        ['status' => $current_status],
        ['status' => $new_status, 'comment' => $comment, 'admin_override' => $admin_override]
    );
    
    echo json_encode([
        'success' => true,
        'message' => 'Status updated successfully',
        'old_status' => $current_status,
        'new_status' => $new_status
    ]);
    
} catch (Exception $e) {
    error_log('Status change error: ' . $e->getMessage());
    http_response_code(500);
    echo json_encode(['error' => 'Internal server error']);
}
?>
