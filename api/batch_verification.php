<?php
session_start();
require_once '../config/database.php';
require_once '../includes/functions.php';
require_once '../includes/BatchOperations.php';
require_once '../includes/ImageUploadHelper.php';

header('Content-Type: application/json');

// Check if user is logged in
if (!isset($_SESSION['user_id'])) {
    http_response_code(401);
    echo json_encode(['success' => false, 'error' => 'Not authenticated']);
    exit();
}

// Check if user has verification role (or admin override)
$admin_override = isset($_POST['admin_override']) && $_POST['admin_override'] === '1' && $_SESSION['role'] === 'administrator';
if (!$admin_override && $_SESSION['role'] !== 'verification' && $_SESSION['role'] !== 'administrator') {
    http_response_code(403);
    echo json_encode(['success' => false, 'error' => 'Access denied. Verification role required.']);
    exit();
}

try {
    $database = new Database();
    $db = $database->getConnection();
    $batchOps = new BatchOperations($db);
    
    $user_id = $_SESSION['user_id'];
    $action = $_POST['action'] ?? '';
    
    switch ($action) {
        case 'create_batch':
            // สร้าง Batch ใหม่
            $expense_ids = json_decode($_POST['expense_ids'] ?? '[]', true);
            $total_amount = floatval($_POST['total_amount'] ?? 0);
            $notes = $_POST['notes'] ?? null;

            if (empty($expense_ids)) {
                throw new Exception('No expenses selected');
            }

            if ($total_amount <= 0) {
                throw new Exception('Invalid total amount');
            }
            
            // ตรวจสอบว่า expenses ทั้งหมดมีสถานะ checked (ผ่านการตรวจสอบแล้ว)
            $placeholders = str_repeat('?,', count($expense_ids) - 1) . '?';
            $stmt = $db->prepare("
                SELECT id, exno, transfer_amount, status
                FROM expenses
                WHERE id IN ($placeholders) AND status = 'checked'
            ");
            $stmt->execute($expense_ids);
            $expenses = $stmt->fetchAll();

            if (count($expenses) !== count($expense_ids)) {
                // ตรวจสอบว่า expenses ไหนที่ไม่ใช่ status 'checked'
                $stmt_check = $db->prepare("
                    SELECT id, exno, status
                    FROM expenses
                    WHERE id IN ($placeholders)
                ");
                $stmt_check->execute($expense_ids);
                $all_expenses = $stmt_check->fetchAll();

                $invalid_expenses = [];
                foreach ($all_expenses as $exp) {
                    if ($exp['status'] !== 'checked') {
                        $invalid_expenses[] = "#{$exp['exno']} (status: {$exp['status']})";
                    }
                }

                if (!empty($invalid_expenses)) {
                    throw new Exception('Some expenses are not available for verification. Only expenses with status "checked" can be verified. Invalid expenses: ' . implode(', ', $invalid_expenses));
                } else {
                    throw new Exception('Some expenses are not available for verification');
                }
            }
            
            // เตรียมข้อมูล expense items
            $expense_items = [];
            $calculated_total = 0;
            
            foreach ($expenses as $expense) {
                $expense_items[] = [
                    'expense_id' => $expense['id'],
                    'amount' => $expense['transfer_amount']
                ];
                $calculated_total += $expense['transfer_amount'];
            }
            
            // ตรวจสอบยอดรวม
            if (abs($calculated_total - $total_amount) > 0.01) {
                throw new Exception('Total amount mismatch. Expected: ' . number_format($calculated_total, 2));
            }
            
            // สร้าง Batch
            $result = $batchOps->createBatch('verification', $user_id, $expense_items, $total_amount, $notes);

            if (!$result['success']) {
                throw new Exception($result['error']);
            }
            
            echo json_encode([
                'success' => true,
                'batch_id' => $result['batch_id'],
                'message' => 'Batch created successfully',
                'data' => [
                    'batch_id' => $result['batch_id'],
                    'item_count' => count($expense_items),
                    'total_amount' => $total_amount
                ]
            ]);
            break;
            
        case 'process_batch':
            // Debug logging
            error_log("Batch verification process_batch called");
            error_log("Current user_id: " . $user_id);
            error_log("User role: " . $_SESSION['role']);
            error_log("Admin override: " . ($admin_override ? 'true' : 'false'));
            error_log("POST data: " . print_r($_POST, true));
            error_log("FILES data: " . print_r($_FILES, true));

            // ประมวลผล Batch
            $batch_id = $_POST['batch_id'] ?? '';

            if (empty($batch_id)) {
                throw new Exception('Batch ID is required');
            }

            // ตรวจสอบว่า batch มีอยู่และเป็นของ user นี้ (ย้ายขึ้นมาก่อน)
            $batch_info = $batchOps->getBatchInfo($batch_id);
            error_log("getBatchInfo result: " . print_r($batch_info, true));

            if (!$batch_info) {
                throw new Exception('Batch not found');
            }

            error_log("Batch info: " . print_r($batch_info, true));
            error_log("Batch user_id: " . $batch_info['user_id'] . ", Current user_id: " . $user_id);

            if (!$admin_override && $batch_info['user_id'] != $user_id) {
                throw new Exception('Access denied to this batch');
            }

            if ($batch_info['status'] !== 'pending') {
                throw new Exception('Batch is not in pending status');
            }

            // ตรวจสอบว่าเป็น multi-file upload หรือ single-file
            $transfer_numbers = $_POST['transfer_numbers'] ?? [];
            $amounts = $_POST['amounts'] ?? [];
            $is_multi_file = !empty($transfer_numbers) && !empty($amounts);

            if ($is_multi_file) {
                // Multi-file upload logic
                error_log("Processing multi-file upload");

                // ตรวจสอบว่ามีไฟล์อัปโหลด
                if (empty($_FILES['files']['name'][0])) {
                    throw new Exception('No files uploaded');
                }

                // ตรวจสอบจำนวนไฟล์ transfer_numbers และ amounts ต้องตรงกัน
                $file_count = count($_FILES['files']['name']);
                if (count($transfer_numbers) !== $file_count || count($amounts) !== $file_count) {
                    throw new Exception('Mismatch between files, transfer numbers, and amounts');
                }

                // ตรวจสอบยอดรวม amounts
                $entered_total = array_sum(array_map('floatval', $amounts));
                error_log("Entered total: " . $entered_total . ", Batch total: " . $batch_info['total_amount']);

                if (abs($entered_total - $batch_info['total_amount']) > 0.01) {
                    throw new Exception('Entered amounts do not match batch total. Entered: ' . $entered_total . ', Expected: ' . $batch_info['total_amount']);
                }

                // ตรวจสอบ transfer_numbers ไม่ซ้ำกัน
                if (count($transfer_numbers) !== count(array_unique($transfer_numbers))) {
                    throw new Exception('Duplicate transfer numbers found');
                }
            } else {
                // Single-file upload logic (legacy)
                error_log("Processing single-file upload (legacy)");

                $transfer_number = $_POST['transfer_number'] ?? '';
                $verification_amounts = $_POST['verification_amounts'] ?? [];

                if (empty($transfer_number)) {
                    throw new Exception('Transfer number is required');
                }

                if (empty($verification_amounts) || !is_array($verification_amounts)) {
                    throw new Exception('Verification amounts are required');
                }
            }

            if ($is_multi_file) {
                // Multi-file processing - ข้ามการตรวจสอบ verification amounts เพราะใช้ amounts แทน
                error_log("Skipping verification amounts check for multi-file upload");
            } else {
                // Single-file processing - ตรวจสอบ verification amounts
                $batch_items = $batchOps->getBatchItems($batch_id);
                $calculated_total = 0;

                // ตรวจสอบว่าทุก expense_id ที่ส่งมามีอยู่ใน batch
                foreach ($verification_amounts as $expense_id => $amount) {
                    $expense_id = intval($expense_id);
                    $amount = floatval($amount);

                    if ($amount < 0) {
                        throw new Exception("Invalid amount for expense ID $expense_id");
                    }

                    // ตรวจสอบว่า expense_id อยู่ใน batch
                    $found = false;
                    foreach ($batch_items as $item) {
                        if ($item['expense_id'] == $expense_id) {
                            $found = true;
                            break;
                        }
                    }

                    if (!$found) {
                        throw new Exception("Expense ID $expense_id not found in this batch");
                    }

                    $calculated_total += $amount;
                }

                // ตรวจสอบว่าจำนวน verification amounts ตรงกับจำนวน items ใน batch
                if (count($verification_amounts) !== count($batch_items)) {
                    throw new Exception('Number of verification amounts does not match batch items');
                }

                // ตรวจสอบยอดรวม
                if (abs($calculated_total - $batch_info['total_amount']) > 0.01) {
                    throw new Exception('Verification amount total (' . number_format($calculated_total, 2) . ') does not match expected total (' . number_format($batch_info['total_amount'], 2) . ')');
                }
            }

            // จัดการไฟล์อัปโหลด
            $uploaded_files = [];

            if ($is_multi_file) {
                // Multi-file upload processing
                error_log("Processing multi-file upload");

                // ตรวจสอบ transfer_numbers ไม่ซ้ำกัน
                if (count($transfer_numbers) !== count(array_unique($transfer_numbers))) {
                    throw new Exception('Duplicate transfer numbers found');
                }

                // สร้างโฟลเดอร์สำหรับเก็บไฟล์
                $upload_dir = '../uploads/batch_documents/verification/' . $batch_id . '/';
                if (!is_dir($upload_dir)) {
                    mkdir($upload_dir, 0755, true);
                }

                // อัปโหลดไฟล์แต่ละไฟล์
                $file_count = count($_FILES['files']['name']);
                for ($i = 0; $i < $file_count; $i++) {
                if ($_FILES['files']['error'][$i] !== UPLOAD_ERR_OK) {
                    throw new Exception("Error uploading file " . ($i + 1));
                }

                $file_info = [
                    'name' => $_FILES['files']['name'][$i],
                    'type' => $_FILES['files']['type'][$i],
                    'tmp_name' => $_FILES['files']['tmp_name'][$i],
                    'error' => $_FILES['files']['error'][$i],
                    'size' => $_FILES['files']['size'][$i]
                ];

                // ใช้ ImageUploadHelper สำหรับการบีบอัดรูปภาพ
                $upload_result = ImageUploadHelper::uploadBatchVerificationSlip($file_info, $batch_id . '_' . ($i + 1));

                if (!$upload_result['success']) {
                    throw new Exception('Failed to upload file ' . ($i + 1) . ': ' . $upload_result['error']);
                }

                $file_path = 'batch_documents/verification/' . $upload_result['filename'];

                // เก็บข้อมูลไฟล์
                $uploaded_files[] = [
                    'file_path' => $file_path,
                    'transfer_number' => $transfer_numbers[$i],
                    'amount' => floatval($amounts[$i]),
                    'original_filename' => $file_info['name'],
                    'file_size' => $file_info['size'],
                    'mime_type' => $file_info['type']
                ];

                // บันทึกข้อมูลเอกสาร
                $batchOps->saveBatchDocument($batch_id, 'verification_slip', [
                    'file_path' => $file_path,
                    'original_filename' => $file_info['name'],
                    'file_size' => $file_info['size'],
                    'mime_type' => $file_info['type'],
                    'transfer_number' => $transfer_numbers[$i],
                    'amount' => floatval($amounts[$i])
                ]);

                    // Log compression results
                    error_log("Batch verification file {$i} compressed: {$upload_result['filename']} - " .
                             "Original: " . ImageCompressor::formatFileSize($upload_result['original_size']) .
                             ", Compressed: " . ImageCompressor::formatFileSize($upload_result['compressed_size']) .
                             " (Saved {$upload_result['compression_ratio']}%)");
                }

                if (empty($uploaded_files)) {
                    throw new Exception('No files were successfully uploaded');
                }

                // ประมวลผล Batch ด้วยข้อมูลไฟล์หลายไฟล์
                $result = $batchOps->processBatchVerificationMultiFile($batch_id, $uploaded_files, $user_id, []);

            } else {
                // Single-file upload processing (legacy)
                error_log("Processing single-file upload (legacy)");

                // ประมวลผล Batch แบบเดิม
                $result = $batchOps->processBatchVerification($batch_id, $transfer_number, $verification_amounts, $user_id);
            }
            
            if (!$result['success']) {
                throw new Exception($result['error']);
            }
            
            // Log activity
            logActivity(
                $db,
                $user_id,
                'batch_verification',
                'expenses',
                null,
                "Processed batch verification: {$batch_id}, Items: {$result['processed']}, Success: {$result['success_count']}, Errors: {$result['error_count']}",
                $_SERVER['REMOTE_ADDR'],
                $_SERVER['HTTP_USER_AGENT']
            );
            
            echo json_encode([
                'success' => true,
                'message' => 'Batch verification processed successfully',
                'data' => $result
            ]);
            break;
            
        case 'get_batch_info':
            // ดึงข้อมูล Batch
            $batch_id = $_GET['batch_id'] ?? '';
            
            if (empty($batch_id)) {
                throw new Exception('Batch ID is required');
            }
            
            $batch_info = $batchOps->getBatchInfo($batch_id);
            if (!$batch_info) {
                throw new Exception('Batch not found');
            }
            
            $batch_items = $batchOps->getBatchItems($batch_id);
            
            echo json_encode([
                'success' => true,
                'data' => [
                    'batch_info' => $batch_info,
                    'batch_items' => $batch_items
                ]
            ]);
            break;
            
        default:
            throw new Exception('Invalid action');
    }
    
} catch (Exception $e) {
    http_response_code(400);
    echo json_encode(['success' => false, 'error' => $e->getMessage()]);
}
?>
