<?php
session_start();
require_once '../config/database.php';
require_once '../includes/functions.php';

header('Content-Type: application/json');

// Check if user is logged in
if (!isset($_SESSION['user_id'])) {
    http_response_code(401);
    echo json_encode(['error' => 'Unauthorized']);
    exit();
}

if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode(['error' => 'Method not allowed']);
    exit();
}

try {
    $input = json_decode(file_get_contents('php://input'), true);
    $name = trim($input['name'] ?? '');
    $contact_person = trim($input['contact_person'] ?? '');
    $phone = trim($input['phone'] ?? '');
    $email = trim($input['email'] ?? '');
    $address = trim($input['address'] ?? '');
    
    if (empty($name)) {
        http_response_code(400);
        echo json_encode(['error' => 'Customer name is required']);
        exit();
    }
    
    // Validate email if provided
    if (!empty($email) && !isValidEmail($email)) {
        http_response_code(400);
        echo json_encode(['error' => 'Invalid email address']);
        exit();
    }
    
    $database = new Database();
    $db = $database->getConnection();
    
    // Check if customer already exists
    $stmt = $db->prepare("SELECT id FROM customers WHERE name = ? AND is_active = 1");
    $stmt->execute([$name]);
    
    if ($stmt->fetch()) {
        http_response_code(409);
        echo json_encode(['error' => 'Customer already exists']);
        exit();
    }
    
    // Insert new customer
    $stmt = $db->prepare("
        INSERT INTO customers (name, contact_person, phone, email, address, created_by) 
        VALUES (?, ?, ?, ?, ?, ?)
    ");
    
    $stmt->execute([$name, $contact_person, $phone, $email, $address, $_SESSION['user_id']]);
    $customer_id = $db->lastInsertId();
    
    // Log the activity
    logActivity(
        $db, 
        $_SESSION['user_id'], 
        'create', 
        'customers', 
        $customer_id, 
        'Created new customer: ' . $name, 
        $_SERVER['REMOTE_ADDR'], 
        $_SERVER['HTTP_USER_AGENT']
    );
    
    echo json_encode([
        'success' => true,
        'id' => $customer_id,
        'name' => $name,
        'message' => 'Customer created successfully'
    ]);
    
} catch (Exception $e) {
    error_log('Add customer error: ' . $e->getMessage());
    http_response_code(500);
    echo json_encode(['error' => 'Internal server error']);
}
?>
