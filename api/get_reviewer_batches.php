<?php
/**
 * API: Get Reviewer Batches
 * Returns completed verification batches ready for review
 */

session_start();
require_once '../config/database.php';
require_once '../includes/functions.php';

header('Content-Type: application/json');

// Check authentication
if (!isset($_SESSION['user_id'])) {
    http_response_code(401);
    echo json_encode(['success' => false, 'error' => 'Not authenticated']);
    exit();
}

// Check permissions
if (!in_array($_SESSION['role'], ['reviewer', 'administrator'])) {
    http_response_code(403);
    echo json_encode(['success' => false, 'error' => 'Access denied. Reviewer role required.']);
    exit();
}

try {
    $database = new Database();
    $db = $database->getConnection();
    
    $user_id = $_SESSION['user_id'];
    $user_role = $_SESSION['role'];
    
    // Pagination parameters
    $page = max(1, intval($_GET['page'] ?? 1));
    $limit = min(100, max(10, intval($_GET['limit'] ?? 20)));
    $offset = ($page - 1) * $limit;
    
    // Search parameters
    $search = $_GET['search'] ?? '';
    $date_from = $_GET['date_from'] ?? '';
    $date_to = $_GET['date_to'] ?? '';
    $amount_min = $_GET['amount_min'] ?? '';
    $amount_max = $_GET['amount_max'] ?? '';
    
    // Build WHERE conditions
    $where_conditions = [];
    $params = [];
    
    // Base conditions for reviewer batches
    $where_conditions[] = "bo.operation_type = 'verification'";
    $where_conditions[] = "bo.status = 'completed'";
    
    // Exclude batches that have already been reviewed OR are in pending review batches
    $where_conditions[] = "bo.batch_id NOT IN (
        SELECT DISTINCT reviewer_batch_id
        FROM expenses
        WHERE reviewer_batch_id IS NOT NULL
    )";

    // Exclude batches that are already included in pending review batches
    $where_conditions[] = "bo.batch_id NOT IN (
        SELECT DISTINCT bi_source.batch_id
        FROM batch_items bi_review
        JOIN expenses e_review ON bi_review.expense_id = e_review.id
        JOIN batch_items bi_source ON e_review.id = bi_source.expense_id
        JOIN batch_operations bo_review ON bi_review.batch_id = bo_review.batch_id
        WHERE bo_review.operation_type = 'review'
        AND bo_review.status IN ('pending', 'processing')
        AND bi_source.batch_id != bi_review.batch_id
    )";
    
    // Search filters
    if (!empty($search)) {
        $where_conditions[] = "(bo.batch_id LIKE ? OR u.username LIKE ?)";
        $search_param = "%{$search}%";
        $params[] = $search_param;
        $params[] = $search_param;
    }

    if (!empty($date_from)) {
        $where_conditions[] = "bo.completed_at >= ?";
        $params[] = $date_from . ' 00:00:00';
    }

    if (!empty($date_to)) {
        $where_conditions[] = "bo.completed_at <= ?";
        $params[] = $date_to . ' 23:59:59';
    }
    
    if (!empty($amount_min)) {
        $where_conditions[] = "bo.total_amount >= ?";
        $params[] = floatval($amount_min);
    }
    
    if (!empty($amount_max)) {
        $where_conditions[] = "bo.total_amount <= ?";
        $params[] = floatval($amount_max);
    }
    
    // Build WHERE clause
    $where_clause = !empty($where_conditions) ? 'WHERE ' . implode(' AND ', $where_conditions) : '';
    
    // Count total records
    $count_sql = "
        SELECT COUNT(DISTINCT bo.id) as total
        FROM batch_operations bo
        LEFT JOIN users u ON bo.user_id = u.id
        {$where_clause}
    ";
    
    $stmt = $db->prepare($count_sql);
    $stmt->execute($params);
    $total_records = $stmt->fetch()['total'];
    $total_pages = ceil($total_records / $limit);
    
    // Get batches data
    $sql = "
        SELECT 
            bo.*,
            u.username as created_by_name,
            COUNT(bi.expense_id) as total_items,
            SUM(e.total_amount) as calculated_total_amount,
            MIN(e.created_at) as earliest_expense_date,
            MAX(e.created_at) as latest_expense_date,
            GROUP_CONCAT(DISTINCT e.status) as expense_statuses
        FROM batch_operations bo
        LEFT JOIN users u ON bo.user_id = u.id
        LEFT JOIN batch_items bi ON bo.batch_id = bi.batch_id
        LEFT JOIN expenses e ON bi.expense_id = e.id
        {$where_clause}
        GROUP BY bo.id, bo.batch_id, bo.operation_type, bo.user_id, bo.total_amount, 
                 bo.item_count, bo.status, bo.notes, bo.transfer_number, 
                 bo.created_at, bo.started_at, bo.completed_at, u.username
        ORDER BY bo.completed_at DESC
        LIMIT {$limit} OFFSET {$offset}
    ";
    
    $stmt = $db->prepare($sql);
    $stmt->execute($params);
    $batches = $stmt->fetchAll();
    
    // Format batch data
    $formatted_batches = [];
    foreach ($batches as $batch) {
        $formatted_batches[] = [
            'id' => $batch['id'],
            'batch_id' => $batch['batch_id'],
            'operation_type' => $batch['operation_type'],
            'user_id' => $batch['user_id'],
            'created_by_name' => $batch['created_by_name'],
            'total_amount' => floatval($batch['total_amount']),
            'calculated_total_amount' => floatval($batch['calculated_total_amount']),
            'item_count' => intval($batch['item_count']),
            'total_items' => intval($batch['total_items']),
            'status' => $batch['status'],
            'notes' => $batch['notes'],
            'transfer_number' => $batch['transfer_number'],
            'created_at' => $batch['created_at'],
            'started_at' => $batch['started_at'],
            'completed_at' => $batch['completed_at'],
            'earliest_expense_date' => $batch['earliest_expense_date'],
            'latest_expense_date' => $batch['latest_expense_date'],
            'expense_statuses' => $batch['expense_statuses'],
            'processing_time' => $batch['started_at'] && $batch['completed_at'] 
                ? (strtotime($batch['completed_at']) - strtotime($batch['started_at'])) 
                : null
        ];
    }
    
    echo json_encode([
        'success' => true,
        'data' => [
            'batches' => $formatted_batches,
            'pagination' => [
                'current_page' => $page,
                'total_pages' => $total_pages,
                'total_records' => $total_records,
                'limit' => $limit,
                'offset' => $offset
            ]
        ]
    ]);
    
} catch (Exception $e) {
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'error' => $e->getMessage()
    ]);
    
    error_log("Get reviewer batches error: " . $e->getMessage());
}
?>
