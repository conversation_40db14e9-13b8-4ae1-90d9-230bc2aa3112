<?php
/**
 * Simple CSV Per Receipt Export (MariaDB 5.5.68 Compatible)
 * แก้ไขปัญหา ROW_NUMBER() ที่ไม่รองรับใน MariaDB 5.5.68
 */

// Enable error reporting for debugging
error_reporting(E_ALL);
ini_set('display_errors', 1);
ini_set('log_errors', 1);

session_start();
require_once '../config/database.php';

// Check if user is logged in
if (!isset($_SESSION['user_id'])) {
    header('Location: ../login.php');
    exit();
}

try {
    $database = new Database();
    $db = $database->getConnection();
} catch (Exception $e) {
    die('Database connection failed: ' . $e->getMessage());
}

$user_id = $_SESSION['user_id'];
$user_role = $_SESSION['role'];

// Get filter parameters
$search = $_GET['search'] ?? '';
$status_filter = $_GET['status'] ?? '';
$date_from = $_GET['date_from'] ?? '';
$date_to = $_GET['date_to'] ?? '';

// Build query based on user role and filters
$where_conditions = [];
$params = [];

// Role-based access
if ($user_role === 'data_entry') {
    $where_conditions[] = 'e.created_by = ?';
    $params[] = $user_id;
}

// Search filter
if (!empty($search)) {
    $where_conditions[] = '(e.exno LIKE ? OR e.bookingno LIKE ? OR i.name LIKE ? OR c.name LIKE ? OR d.name LIKE ?)';
    $search_param = '%' . $search . '%';
    $params = array_merge($params, [$search_param, $search_param, $search_param, $search_param, $search_param]);
}

// Status filter
if (!empty($status_filter)) {
    $where_conditions[] = 'e.status = ?';
    $params[] = $status_filter;
}

// Date range filter
if (!empty($date_from)) {
    $where_conditions[] = 'e.job_open_date >= ?';
    $params[] = $date_from;
}

if (!empty($date_to)) {
    $where_conditions[] = 'e.job_open_date <= ?';
    $params[] = $date_to;
}

$where_clause = !empty($where_conditions) ? 'WHERE ' . implode(' AND ', $where_conditions) : '';

// Helper function to parse deductions - returns arrays for separate rows
function parseDeductions($deductions_string) {
    $types = [];
    $amounts = [];
    $descriptions = [];

    if (empty($deductions_string)) {
        return [[], [], []];
    }

    $deduction_parts = explode('; ', $deductions_string);

    foreach ($deduction_parts as $part) {
        // Parse format: "type: amount (description)"
        if (preg_match('/^(.+?):\s*([0-9,]+\.?\d*)\s*\((.+?)\)$/', $part, $matches)) {
            $types[] = trim($matches[1]);
            $amounts[] = trim($matches[2]);
            $descriptions[] = trim($matches[3]);
        } else {
            // Fallback for different formats
            $types[] = $part;
            $amounts[] = '';
            $descriptions[] = '';
        }
    }

    return [$types, $amounts, $descriptions];
}

// Check which columns exist in the expenses table
try {
    $stmt = $db->prepare("SHOW COLUMNS FROM expenses");
    $stmt->execute();
    $existing_columns = $stmt->fetchAll(PDO::FETCH_COLUMN);
} catch (Exception $e) {
    die('Error checking columns: ' . $e->getMessage());
}

// Define field mappings with fallbacks
$field_mappings = [
    'transfer_amount' => in_array('transfer_amount', $existing_columns) ? 'e.transfer_amount' : 'COALESCE(e.total_amount, 0) as transfer_amount',
    'verification_slip_image' => in_array('verification_slip_image', $existing_columns) ? 'e.verification_slip_image' : 'NULL as verification_slip_image',
    'verification_amount' => in_array('verification_amount', $existing_columns) ? 'e.verification_amount' : 'NULL as verification_amount',
    'verification_transfer_no' => in_array('verification_transfer_no', $existing_columns) ? 'e.verification_transfer_no' : 'NULL as verification_transfer_no',
    'verification_date' => in_array('verification_date', $existing_columns) ? 'e.verification_date' : 'NULL as verification_date',
    'verification_by' => in_array('verification_by', $existing_columns) ? 'e.verification_by' : 'NULL as verification_by',
    'batch_verification_id' => in_array('batch_verification_id', $existing_columns) ? 'e.batch_verification_id' : 'NULL as batch_verification_id',
    'reviewer_slip_image' => in_array('reviewer_slip_image', $existing_columns) ? 'e.reviewer_slip_image' : 'NULL as reviewer_slip_image',
    'reviewer_amount' => in_array('reviewer_amount', $existing_columns) ? 'e.reviewer_amount' : 'NULL as reviewer_amount',
    'reviewer_transfer_no' => in_array('reviewer_transfer_no', $existing_columns) ? 'e.reviewer_transfer_no' : 'NULL as reviewer_transfer_no',
    'reviewer_date' => in_array('reviewer_date', $existing_columns) ? 'e.reviewer_date' : 'NULL as reviewer_date',
    'reviewer_by' => in_array('reviewer_by', $existing_columns) ? 'e.reviewer_by' : 'NULL as reviewer_by',
    'batch_review_id' => in_array('batch_review_id', $existing_columns) ? 'e.batch_review_id' : 'NULL as batch_review_id',
    'rejection_reason' => in_array('rejection_reason', $existing_columns) ? 'e.rejection_reason' : 'NULL as rejection_reason',
    'rejection_date' => in_array('rejection_date', $existing_columns) ? 'e.rejection_date' : 'NULL as rejection_date',
    'rejected_by' => in_array('rejected_by', $existing_columns) ? 'e.rejected_by' : 'NULL as rejected_by',
    'return_reason' => in_array('return_reason', $existing_columns) ? 'e.return_reason' : 'NULL as return_reason',
    'return_date' => in_array('return_date', $existing_columns) ? 'e.return_date' : 'NULL as return_date',
    'returned_by' => in_array('returned_by', $existing_columns) ? 'e.returned_by' : 'NULL as returned_by'
];

// Step 1: Get all expenses with complete data
$expenses_sql = "
    SELECT
        e.id as expense_id,
        e.sequence,
        e.exno,
        e.job_open_date,
        e.bookingno,
        e.containerno,
        e.vehicle_plate,
        e.payment_account_no,
        e.additional_details,
        e.requester,
        e.receiver,
        e.payer,
        e.withdrawal_date,
        e.transfer_no,
        {$field_mappings['transfer_amount']},
        COALESCE(e.total_amount, 0) as total_amount,
        e.status,
        e.created_at,
        e.updated_at,

        -- Verification fields
        {$field_mappings['verification_slip_image']},
        {$field_mappings['verification_amount']},
        {$field_mappings['verification_transfer_no']},
        {$field_mappings['verification_date']},
        {$field_mappings['batch_verification_id']},

        -- Review fields
        {$field_mappings['reviewer_slip_image']},
        {$field_mappings['reviewer_amount']},
        {$field_mappings['reviewer_transfer_no']},
        {$field_mappings['reviewer_date']},
        {$field_mappings['batch_review_id']},

        -- Rejection/Return fields
        {$field_mappings['rejection_reason']},
        {$field_mappings['rejection_date']},
        {$field_mappings['return_reason']},
        {$field_mappings['return_date']},

        -- Related data
        i.name as item_name,
        c.name as customer_name,
        d.name as driver_name,
        u.full_name as created_by_name,
        uv.full_name as verified_by_name,
        ur.full_name as reviewed_by_name,
        uj.full_name as rejected_by_name,
        urt.full_name as returned_by_name
    FROM expenses e
    LEFT JOIN items i ON e.item_id = i.id
    LEFT JOIN customers c ON e.customer_id = c.id
    LEFT JOIN drivers d ON e.driver_id = d.id
    LEFT JOIN users u ON e.created_by = u.id
    LEFT JOIN users uv ON COALESCE({$field_mappings['verification_by']}, 0) = uv.id
    LEFT JOIN users ur ON COALESCE({$field_mappings['reviewer_by']}, 0) = ur.id
    LEFT JOIN users uj ON COALESCE({$field_mappings['rejected_by']}, 0) = uj.id
    LEFT JOIN users urt ON COALESCE({$field_mappings['returned_by']}, 0) = urt.id
    $where_clause
    ORDER BY e.created_at DESC
";

try {
    $stmt = $db->prepare($expenses_sql);
    $stmt->execute($params);
    $expenses = $stmt->fetchAll();
} catch (Exception $e) {
    die('Error executing expenses query: ' . $e->getMessage());
}

// Check if we have data
if (empty($expenses)) {
    die('No data found to export. Please check your filters.');
}

// Step 2: Get receipts with deductions for each expense
$expense_receipts = [];
foreach ($expenses as $expense) {
    $receipt_sql = "
        SELECT
            rn.receipt_number,
            rn.amount as receipt_amount,
            rn.gross_amount,
            rn.net_amount_calculated,
            rn.has_deductions,
            rn.description as receipt_description,
            GROUP_CONCAT(
                CONCAT(rd.deduction_type, ': ', rd.amount, ' (', COALESCE(rd.description, ''), ')')
                SEPARATOR '; '
            ) as deductions
        FROM receipt_numbers rn
        LEFT JOIN receipt_deductions rd ON rn.id = rd.receipt_number_id
        WHERE rn.expense_id = ?
        GROUP BY rn.id
        ORDER BY rn.id ASC
    ";

    $stmt = $db->prepare($receipt_sql);
    $stmt->execute([$expense['expense_id']]);
    $receipts = $stmt->fetchAll();

    $expense_receipts[$expense['expense_id']] = $receipts;
}

// Set CSV headers
$filename = 'expenses_per_receipt_' . date('Y-m-d_H-i-s') . '.csv';
header('Content-Type: text/csv; charset=utf-8');
header('Content-Disposition: attachment; filename="' . $filename . '"');

// Create output stream
$output = fopen('php://output', 'w');
if (!$output) {
    die('Error: Cannot create output stream');
}

// Add BOM for UTF-8
fprintf($output, chr(0xEF).chr(0xBB).chr(0xBF));

// CSV Headers
$headers = [
    'Sequence',
    'Expense No',
    'Job Date',
    'Booking No',
    'Item',
    'Customer',
    'Driver',
    'Container No',
    'Vehicle Plate',
    'Payment Account',
    'Requester',
    'Receiver',
    'Payer',
    'Withdrawal Date',
    'Transfer No',
    'Transfer Amount',
    'Total Amount',
    'Receipt Number',
    'Receipt Amount',
    'Receipt Description',
    'Deduction Type',
    'Deduction Amount',
    'Deduction Description',
    'Receipt Total',
    'Amount Match',
    'Status',
    'Additional Details',

    // Verification fields
    'Verification Amount',
    'Verification Transfer No',
    'Verification Date',
    'Verified By',
    'Verification Batch ID',

    // Review fields
    'Review Amount',
    'Review Transfer No',
    'Review Date',
    'Reviewed By',
    'Review Batch ID',

    // Rejection/Return fields
    'Rejection Reason',
    'Rejection Date',
    'Rejected By',
    'Return Reason',
    'Return Date',
    'Returned By',

    // System fields
    'Created By',
    'Created Date',
    'Updated Date'
];

fputcsv($output, $headers);

// Process each expense
foreach ($expenses as $expense) {
    $receipts = $expense_receipts[$expense['expense_id']];

    // Calculate receipt total for this expense (considering deductions)
    $receipt_total = 0;
    $gross_total = 0;
    $net_total = 0;

    foreach ($receipts as $receipt) {
        if ($receipt['has_deductions']) {
            $net_total += $receipt['net_amount_calculated'];
            $gross_total += $receipt['gross_amount'];
        } else {
            $receipt_total += $receipt['receipt_amount'];
        }
    }

    $final_receipt_total = $net_total > 0 ? $net_total : $receipt_total;

    // Calculate amount match
    $amount_match = (abs($expense['transfer_amount'] - $final_receipt_total) <= 0.01) ? 'Match' : 'Mismatch';
    
    if (empty($receipts)) {
        // Expense with no receipts - show expense data with empty receipt fields
        $csv_row = [
            $expense['sequence'],
            $expense['exno'],
            $expense['job_open_date'],
            $expense['bookingno'],
            $expense['item_name'],
            $expense['customer_name'],
            $expense['driver_name'],
            $expense['containerno'],
            $expense['vehicle_plate'],
            $expense['payment_account_no'],
            $expense['requester'],
            $expense['receiver'],
            $expense['payer'],
            $expense['withdrawal_date'],
            $expense['transfer_no'],
            number_format($expense['transfer_amount'], 2),
            number_format($expense['total_amount'], 2),
            '', // Receipt Number
            '', // Receipt Amount
            '', // Receipt Description
            '', // Deduction Type
            '', // Deduction Amount
            '', // Deduction Description
            number_format($final_receipt_total, 2),
            $amount_match,
            ucfirst($expense['status']),
            $expense['additional_details'],

            // Verification fields
            $expense['verification_amount'] ? number_format($expense['verification_amount'], 2) : '',
            $expense['verification_transfer_no'] ?? '',
            $expense['verification_date'] ?? '',
            $expense['verified_by_name'] ?? '',
            $expense['batch_verification_id'] ?? '',

            // Review fields
            $expense['reviewer_amount'] ? number_format($expense['reviewer_amount'], 2) : '',
            $expense['reviewer_transfer_no'] ?? '',
            $expense['reviewer_date'] ?? '',
            $expense['reviewed_by_name'] ?? '',
            $expense['batch_review_id'] ?? '',

            // Rejection/Return fields
            $expense['rejection_reason'] ?? '',
            $expense['rejection_date'] ?? '',
            $expense['rejected_by_name'] ?? '',
            $expense['return_reason'] ?? '',
            $expense['return_date'] ?? '',
            $expense['returned_by_name'] ?? '',

            // System fields
            $expense['created_by_name'],
            $expense['created_at'],
            $expense['updated_at']
        ];
        fputcsv($output, $csv_row);
    } else {
        // Expense with receipts - show expense data on first receipt row
        $first_expense_row = true;

        foreach ($receipts as $receipt) {
            // Determine receipt amount to display
            $receipt_amount = '';
            if ($receipt['has_deductions']) {
                $receipt_amount = $receipt['net_amount_calculated'] ? number_format($receipt['net_amount_calculated'], 2) : '';
            } else {
                $receipt_amount = $receipt['receipt_amount'] ? number_format($receipt['receipt_amount'], 2) : '';
            }

            // Check if receipt has deductions
            if ($receipt['has_deductions'] && !empty($receipt['deductions'])) {
                // Parse deductions - returns arrays directly
                list($deduction_types, $deduction_amounts, $deduction_descriptions) = parseDeductions($receipt['deductions']);

                // Create rows for each deduction
                for ($i = 0; $i < count($deduction_types); $i++) {
                    $csv_row = [];

                    if ($first_expense_row) {
                        // First row: show all expense data
                        $csv_row = [
                            $expense['sequence'],
                            $expense['exno'],
                            $expense['job_open_date'],
                            $expense['bookingno'],
                            $expense['item_name'],
                            $expense['customer_name'],
                            $expense['driver_name'],
                            $expense['containerno'],
                            $expense['vehicle_plate'],
                            $expense['payment_account_no'],
                            $expense['requester'],
                            $expense['receiver'],
                            $expense['payer'],
                            $expense['withdrawal_date'],
                            $expense['transfer_no'],
                            number_format($expense['transfer_amount'], 2),
                            number_format($expense['total_amount'], 2),
                            $receipt['receipt_number'] ?? '',
                            $receipt_amount,
                            $receipt['receipt_description'] ?? '',
                            $deduction_types[$i] ?? '',
                            $deduction_amounts[$i] ?? '',
                            $deduction_descriptions[$i] ?? '',
                            number_format($final_receipt_total, 2),
                            $amount_match,
                            ucfirst($expense['status']),
                            $expense['additional_details'],

                            // Verification fields
                            $expense['verification_amount'] ? number_format($expense['verification_amount'], 2) : '',
                            $expense['verification_transfer_no'] ?? '',
                            $expense['verification_date'] ?? '',
                            $expense['verified_by_name'] ?? '',
                            $expense['batch_verification_id'] ?? '',

                            // Review fields
                            $expense['reviewer_amount'] ? number_format($expense['reviewer_amount'], 2) : '',
                            $expense['reviewer_transfer_no'] ?? '',
                            $expense['reviewer_date'] ?? '',
                            $expense['reviewed_by_name'] ?? '',
                            $expense['batch_review_id'] ?? '',

                            // Rejection/Return fields
                            $expense['rejection_reason'] ?? '',
                            $expense['rejection_date'] ?? '',
                            $expense['rejected_by_name'] ?? '',
                            $expense['return_reason'] ?? '',
                            $expense['return_date'] ?? '',
                            $expense['returned_by_name'] ?? '',

                            // System fields
                            $expense['created_by_name'],
                            $expense['created_at'],
                            $expense['updated_at']
                        ];
                        $first_expense_row = false;
                    } else {
                        // Subsequent deduction rows: empty expense data, show only receipt and deduction data
                        $csv_row = [
                            '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', // Empty expense fields (17 fields)
                            $receipt['receipt_number'] ?? '',
                            $receipt_amount,
                            $receipt['receipt_description'] ?? '',
                            $deduction_types[$i] ?? '',
                            $deduction_amounts[$i] ?? '',
                            $deduction_descriptions[$i] ?? '',
                            '', '', '', '', // Empty summary fields (4 fields)
                            '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '' // Empty remaining fields (32 fields)
                        ];
                    }

                    fputcsv($output, $csv_row);
                }
            } else {
                // Receipt without deductions
                $csv_row = [];

                if ($first_expense_row) {
                    // First row: show all expense data
                    $csv_row = [
                        $expense['sequence'],
                        $expense['exno'],
                        $expense['job_open_date'],
                        $expense['bookingno'],
                        $expense['item_name'],
                        $expense['customer_name'],
                        $expense['driver_name'],
                        $expense['containerno'],
                        $expense['vehicle_plate'],
                        $expense['payment_account_no'],
                        $expense['requester'],
                        $expense['receiver'],
                        $expense['payer'],
                        $expense['withdrawal_date'],
                        $expense['transfer_no'],
                        number_format($expense['transfer_amount'], 2),
                        number_format($expense['total_amount'], 2),
                        $receipt['receipt_number'] ?? '',
                        $receipt_amount,
                        $receipt['receipt_description'] ?? '',
                        '', // No deduction type
                        '', // No deduction amount
                        '', // No deduction description
                        number_format($final_receipt_total, 2),
                        $amount_match,
                        ucfirst($expense['status']),
                        $expense['additional_details'],

                        // Verification fields
                        $expense['verification_amount'] ? number_format($expense['verification_amount'], 2) : '',
                        $expense['verification_transfer_no'] ?? '',
                        $expense['verification_date'] ?? '',
                        $expense['verified_by_name'] ?? '',
                        $expense['batch_verification_id'] ?? '',

                        // Review fields
                        $expense['reviewer_amount'] ? number_format($expense['reviewer_amount'], 2) : '',
                        $expense['reviewer_transfer_no'] ?? '',
                        $expense['reviewer_date'] ?? '',
                        $expense['reviewed_by_name'] ?? '',
                        $expense['batch_review_id'] ?? '',

                        // Rejection/Return fields
                        $expense['rejection_reason'] ?? '',
                        $expense['rejection_date'] ?? '',
                        $expense['rejected_by_name'] ?? '',
                        $expense['return_reason'] ?? '',
                        $expense['return_date'] ?? '',
                        $expense['returned_by_name'] ?? '',

                        // System fields
                        $expense['created_by_name'],
                        $expense['created_at'],
                        $expense['updated_at']
                    ];
                    $first_expense_row = false;
                } else {
                    // Subsequent receipt rows: empty expense data, show only receipt data
                    $csv_row = [
                        '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', // Empty expense fields (17 fields)
                        $receipt['receipt_number'] ?? '',
                        $receipt_amount,
                        $receipt['receipt_description'] ?? '',
                        '', // No deduction type
                        '', // No deduction amount
                        '', // No deduction description
                        '', '', '', '', // Empty summary fields (4 fields)
                        '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '' // Empty remaining fields (32 fields)
                    ];
                }

                fputcsv($output, $csv_row);
            }
        }
    }
}

fclose($output);
exit();
?>
