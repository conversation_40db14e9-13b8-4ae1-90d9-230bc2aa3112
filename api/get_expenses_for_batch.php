<?php
session_start();
require_once '../config/database.php';
require_once '../includes/functions.php';

header('Content-Type: application/json');

// Check if user is logged in
if (!isset($_SESSION['user_id'])) {
    http_response_code(401);
    echo json_encode(['success' => false, 'error' => 'Not authenticated']);
    exit();
}

try {
    $database = new Database();
    $db = $database->getConnection();
    
    $user_id = $_SESSION['user_id'];
    $user_role = $_SESSION['role'];
    $operation_type = $_GET['operation_type'] ?? '';
    
    // Pagination parameters
    $page = max(1, intval($_GET['page'] ?? 1));
    $limit = min(100, max(10, intval($_GET['limit'] ?? 20)));
    $offset = ($page - 1) * $limit;
    
    // Search parameters
    $search = $_GET['search'] ?? '';
    $date_from = $_GET['date_from'] ?? '';
    $date_to = $_GET['date_to'] ?? '';
    $amount_min = $_GET['amount_min'] ?? '';
    $amount_max = $_GET['amount_max'] ?? '';
    $batch_filter = $_GET['batch_filter'] ?? '';
    $status_filter = $_GET['status_filter'] ?? '';

    if (!in_array($operation_type, ['verification', 'review'])) {
        throw new Exception('Invalid operation type');
    }
    
    // Check user permissions
    if ($operation_type === 'verification' && !in_array($user_role, ['verification', 'administrator'])) {
        throw new Exception('Access denied. Verification role required.');
    }
    
    if ($operation_type === 'review' && !in_array($user_role, ['reviewer', 'administrator'])) {
        throw new Exception('Access denied. Reviewer role required.');
    }
    
    // Build WHERE conditions
    $where_conditions = [];
    $params = [];
    
    if ($operation_type === 'verification') {
        // For verification: filter by status
        if (!empty($status_filter) && in_array($status_filter, ['open', 'checked'])) {
            $where_conditions[] = "e.status = ?";
            $params[] = $status_filter;
        } else {
            // Default: get expenses with status 'checked' (new workflow) or 'open' (backward compatibility)
            $where_conditions[] = "e.status IN ('open', 'checked')";
        }
        $where_conditions[] = "e.verification_by IS NULL";

        // Batch filter for verification
        if ($batch_filter === 'no_batch') {
            $where_conditions[] = "e.batch_verification_id IS NULL";
        } elseif ($batch_filter === 'has_batch') {
            $where_conditions[] = "e.batch_verification_id IS NOT NULL";
        }
    } else {
        // For review: get expenses with status 'pending' and verified
        $where_conditions[] = "e.status = 'pending'";
        $where_conditions[] = "e.verification_by IS NOT NULL";
        $where_conditions[] = "e.reviewer_by IS NULL";

        // Batch filter for review
        if ($batch_filter === 'no_batch') {
            $where_conditions[] = "e.batch_review_id IS NULL";
        } elseif ($batch_filter === 'has_batch') {
            $where_conditions[] = "e.batch_review_id IS NOT NULL";
        }
    }
    
    // Search filters
    if (!empty($search)) {
        $where_conditions[] = "(e.exno LIKE ? OR e.bookingno LIKE ? OR e.containerno LIKE ? OR c.name LIKE ?)";
        $search_param = "%{$search}%";
        $params[] = $search_param;
        $params[] = $search_param;
        $params[] = $search_param;
        $params[] = $search_param;
    }

    if (!empty($date_from)) {
        $where_conditions[] = "e.job_open_date >= ?";
        $params[] = $date_from;
    }

    if (!empty($date_to)) {
        $where_conditions[] = "e.job_open_date <= ?";
        $params[] = $date_to;
    }
    
    if (!empty($amount_min)) {
        $amount_field = ($operation_type === 'verification') ? 'e.transfer_amount' : 'e.verification_amount';
        $where_conditions[] = "{$amount_field} >= ?";
        $params[] = floatval($amount_min);
    }
    
    if (!empty($amount_max)) {
        $amount_field = ($operation_type === 'verification') ? 'e.transfer_amount' : 'e.verification_amount';
        $where_conditions[] = "{$amount_field} <= ?";
        $params[] = floatval($amount_max);
    }
    
    $where_clause = !empty($where_conditions) ? 'WHERE ' . implode(' AND ', $where_conditions) : '';
    
    // Count total records
    $count_sql = "
        SELECT COUNT(*) as total
        FROM expenses e
        LEFT JOIN customers c ON e.customer_id = c.id
        {$where_clause}
    ";
    
    $stmt = $db->prepare($count_sql);
    $stmt->execute($params);
    $total_records = $stmt->fetch()['total'];
    
    // Get expenses data
    $amount_field = ($operation_type === 'verification') ? 'e.transfer_amount' : 'e.verification_amount';
    $sql = "
        SELECT
            e.id,
            e.exno,
            e.job_open_date as expense_date,
            CONCAT(COALESCE(i.name, 'N/A'), ' - ', COALESCE(e.bookingno, ''), ' - ', COALESCE(e.containerno, '')) as description,
            e.transfer_amount,
            e.verification_amount,
            e.status,
            e.created_at,
            e.batch_verification_id,
            e.batch_review_id,
            c.name as customer_name,
            i.name as item_name,
            u.full_name as created_by_name,
            v.full_name as verified_by_name,
            {$amount_field} as batch_amount
        FROM expenses e
        LEFT JOIN customers c ON e.customer_id = c.id
        LEFT JOIN items i ON e.item_id = i.id
        LEFT JOIN users u ON e.created_by = u.id
        LEFT JOIN users v ON e.verification_by = v.id
        {$where_clause}
        ORDER BY e.job_open_date DESC, e.created_at DESC
        LIMIT {$limit} OFFSET {$offset}
    ";
    
    $stmt = $db->prepare($sql);
    $stmt->execute($params);
    $expenses = $stmt->fetchAll();
    
    // Calculate summary
    $summary_sql = "
        SELECT 
            COUNT(*) as total_items,
            SUM({$amount_field}) as total_amount
        FROM expenses e
        LEFT JOIN customers c ON e.customer_id = c.id
        {$where_clause}
    ";
    
    $stmt = $db->prepare($summary_sql);
    $stmt->execute($params);
    $summary = $stmt->fetch();
    
    // Calculate pagination
    $total_pages = ceil($total_records / $limit);
    
    echo json_encode([
        'success' => true,
        'data' => [
            'expenses' => $expenses,
            'pagination' => [
                'current_page' => $page,
                'total_pages' => $total_pages,
                'total_records' => $total_records,
                'limit' => $limit,
                'has_next' => $page < $total_pages,
                'has_prev' => $page > 1
            ],
            'summary' => [
                'total_items' => intval($summary['total_items']),
                'total_amount' => floatval($summary['total_amount'])
            ],
            'filters' => [
                'operation_type' => $operation_type,
                'search' => $search,
                'date_from' => $date_from,
                'date_to' => $date_to,
                'amount_min' => $amount_min,
                'amount_max' => $amount_max
            ]
        ]
    ]);
    
} catch (Exception $e) {
    http_response_code(400);
    echo json_encode(['success' => false, 'error' => $e->getMessage()]);
}
?>
