<?php
/**
 * Receipt Deductions API
 * 
 * Handles CRUD operations for receipt deductions
 * 
 * Methods:
 * - GET: Get deductions for a receipt
 * - POST: Add new deduction
 * - PUT: Update existing deduction  
 * - DELETE: Delete deduction
 */

header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, PUT, DELETE, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization');

// Handle preflight requests
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit();
}

require_once '../config/database.php';
require_once '../includes/ReceiptDeductionManager.php';

// Check if user is logged in
session_start();
if (!isset($_SESSION['user_id'])) {
    http_response_code(401);
    echo json_encode(['error' => 'Unauthorized']);
    exit();
}

$user_id = $_SESSION['user_id'];

try {
    $database = new Database();
    $db = $database->getConnection();
    $deductionManager = new ReceiptDeductionManager($db);
    
    $method = $_SERVER['REQUEST_METHOD'];
    $input = json_decode(file_get_contents('php://input'), true);
    
    switch ($method) {
        case 'GET':
            handleGet($deductionManager);
            break;
            
        case 'POST':
            handlePost($deductionManager, $input, $user_id);
            break;
            
        case 'PUT':
            handlePut($deductionManager, $input, $user_id);
            break;
            
        case 'DELETE':
            handleDelete($deductionManager, $user_id);
            break;
            
        default:
            http_response_code(405);
            echo json_encode(['error' => 'Method not allowed']);
            break;
    }
    
} catch (Exception $e) {
    error_log("Receipt Deductions API Error: " . $e->getMessage());
    http_response_code(500);
    echo json_encode(['error' => 'Internal server error']);
}

/**
 * Handle GET requests
 */
function handleGet($deductionManager) {
    if (isset($_GET['receipt_id'])) {
        // Get deductions for a specific receipt
        $receiptId = (int)$_GET['receipt_id'];
        $deductions = $deductionManager->getReceiptDeductions($receiptId);
        $summary = $deductionManager->getReceiptSummary($receiptId);
        
        echo json_encode([
            'success' => true,
            'deductions' => $deductions,
            'summary' => $summary,
            'deduction_types' => ReceiptDeductionManager::getDeductionTypes()
        ]);
        
    } elseif (isset($_GET['statistics'])) {
        // Get deduction statistics
        $expenseId = isset($_GET['expense_id']) ? (int)$_GET['expense_id'] : null;
        $dateFrom = $_GET['date_from'] ?? null;
        $dateTo = $_GET['date_to'] ?? null;
        
        $statistics = $deductionManager->getDeductionStatistics($expenseId, $dateFrom, $dateTo);
        
        echo json_encode([
            'success' => true,
            'statistics' => $statistics
        ]);
        
    } else {
        http_response_code(400);
        echo json_encode(['error' => 'Missing required parameters']);
    }
}

/**
 * Handle POST requests (Add new deduction)
 */
function handlePost($deductionManager, $input, $user_id) {
    if (!isset($input['receipt_id']) || !isset($input['deduction_data'])) {
        http_response_code(400);
        echo json_encode(['error' => 'Missing required parameters']);
        return;
    }
    
    $receiptId = (int)$input['receipt_id'];
    $deductionData = $input['deduction_data'];
    
    // Validate input data
    $validation = $deductionManager->validateDeductionData($deductionData);
    if (!$validation['valid']) {
        http_response_code(400);
        echo json_encode([
            'error' => 'Validation failed',
            'errors' => $validation['errors']
        ]);
        return;
    }
    
    // Add deduction
    $result = $deductionManager->addDeduction($receiptId, $deductionData, $user_id);
    
    if ($result['success']) {
        // Get updated summary
        $summary = $deductionManager->getReceiptSummary($receiptId);
        $deductions = $deductionManager->getReceiptDeductions($receiptId);
        
        echo json_encode([
            'success' => true,
            'message' => $result['message'],
            'summary' => $summary,
            'deductions' => $deductions
        ]);
    } else {
        http_response_code(400);
        echo json_encode($result);
    }
}

/**
 * Handle PUT requests (Update deduction)
 */
function handlePut($deductionManager, $input, $user_id) {
    if (!isset($input['deduction_id']) || !isset($input['deduction_data'])) {
        http_response_code(400);
        echo json_encode(['error' => 'Missing required parameters']);
        return;
    }
    
    $deductionId = (int)$input['deduction_id'];
    $deductionData = $input['deduction_data'];
    
    // Validate input data
    $validation = $deductionManager->validateDeductionData($deductionData);
    if (!$validation['valid']) {
        http_response_code(400);
        echo json_encode([
            'error' => 'Validation failed',
            'errors' => $validation['errors']
        ]);
        return;
    }
    
    // Update deduction
    $result = $deductionManager->updateDeduction($deductionId, $deductionData, $user_id);
    
    if ($result['success']) {
        echo json_encode([
            'success' => true,
            'message' => $result['message']
        ]);
    } else {
        http_response_code(400);
        echo json_encode($result);
    }
}

/**
 * Handle DELETE requests
 */
function handleDelete($deductionManager, $user_id) {
    if (!isset($_GET['deduction_id'])) {
        http_response_code(400);
        echo json_encode(['error' => 'Missing deduction_id parameter']);
        return;
    }
    
    $deductionId = (int)$_GET['deduction_id'];
    
    // Delete deduction
    $result = $deductionManager->deleteDeduction($deductionId, $user_id);
    
    if ($result['success']) {
        echo json_encode([
            'success' => true,
            'message' => $result['message']
        ]);
    } else {
        http_response_code(400);
        echo json_encode($result);
    }
}
?>
