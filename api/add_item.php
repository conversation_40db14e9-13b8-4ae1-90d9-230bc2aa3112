<?php
session_start();
require_once '../config/database.php';
require_once '../includes/functions.php';

header('Content-Type: application/json');

// Check if user is logged in
if (!isset($_SESSION['user_id'])) {
    http_response_code(401);
    echo json_encode(['error' => 'Unauthorized']);
    exit();
}

if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode(['error' => 'Method not allowed']);
    exit();
}

try {
    $input = json_decode(file_get_contents('php://input'), true);
    $name = trim($input['name'] ?? '');
    $description = trim($input['description'] ?? '');
    
    if (empty($name)) {
        http_response_code(400);
        echo json_encode(['error' => 'Item name is required']);
        exit();
    }
    
    $database = new Database();
    $db = $database->getConnection();
    
    // Check if item already exists
    $stmt = $db->prepare("SELECT id FROM items WHERE name = ? AND is_active = 1");
    $stmt->execute([$name]);
    
    if ($stmt->fetch()) {
        http_response_code(409);
        echo json_encode(['error' => 'Item already exists']);
        exit();
    }
    
    // Insert new item
    $stmt = $db->prepare("
        INSERT INTO items (name, description, created_by) 
        VALUES (?, ?, ?)
    ");
    
    $stmt->execute([$name, $description, $_SESSION['user_id']]);
    $item_id = $db->lastInsertId();
    
    // Log the activity
    logActivity(
        $db, 
        $_SESSION['user_id'], 
        'create', 
        'items', 
        $item_id, 
        'Created new item: ' . $name, 
        $_SERVER['REMOTE_ADDR'], 
        $_SERVER['HTTP_USER_AGENT']
    );
    
    echo json_encode([
        'success' => true,
        'id' => $item_id,
        'name' => $name,
        'message' => 'Item created successfully'
    ]);
    
} catch (Exception $e) {
    error_log('Add item error: ' . $e->getMessage());
    http_response_code(500);
    echo json_encode(['error' => 'Internal server error']);
}
?>
