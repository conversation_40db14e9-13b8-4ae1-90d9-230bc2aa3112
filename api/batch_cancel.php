<?php
session_start();
require_once '../config/database.php';
require_once '../includes/functions.php';
require_once '../includes/BatchOperations.php';

// Check if user is logged in
if (!isset($_SESSION['user_id'])) {
    http_response_code(401);
    echo json_encode(['error' => 'Unauthorized']);
    exit();
}

// Check request method
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode(['error' => 'Method not allowed']);
    exit();
}

try {
    $database = new Database();
    $db = $database->getConnection();
    $batchOps = new BatchOperations($db);

    $user_id = $_SESSION['user_id'];
    $user_role = $_SESSION['role'];

    // Get JSON input
    $input = json_decode(file_get_contents('php://input'), true);
    $batch_id = $input['batch_id'] ?? '';

    if (empty($batch_id)) {
        throw new Exception('Batch ID is required');
    }

    // Get batch info
    $batch_info = $batchOps->getBatchInfo($batch_id);
    if (!$batch_info) {
        throw new Exception('Batch not found');
    }

    // Check permissions
    $can_cancel = false;
    if ($user_role === 'administrator') {
        $can_cancel = true;
    } elseif ($batch_info['user_id'] == $user_id) {
        // Users can cancel their own pending/failed batches
        $can_cancel = in_array($batch_info['status'], ['pending', 'failed']);

        // Check role permissions for batch type
        if ($batch_info['operation_type'] === 'verification' && !in_array($user_role, ['verification', 'administrator'])) {
            $can_cancel = false;
        }
        if ($batch_info['operation_type'] === 'review' && !in_array($user_role, ['reviewer', 'administrator'])) {
            $can_cancel = false;
        }
    }

    if (!$can_cancel) {
        throw new Exception('You do not have permission to cancel this batch');
    }

    // Check if batch can be cancelled
    if (!in_array($batch_info['status'], ['pending', 'failed'])) {
        throw new Exception('Only pending or failed batches can be cancelled');
    }
    
    // Start transaction
    $db->beginTransaction();
    
    // Update batch status to cancelled
    $stmt = $db->prepare("
        UPDATE batch_operations 
        SET status = 'cancelled', 
            completed_at = CURRENT_TIMESTAMP,
            notes = CONCAT(COALESCE(notes, ''), '\nCancelled by admin: ', ?)
        WHERE batch_id = ?
    ");
    $stmt->execute([$_SESSION['full_name'], $batch_id]);
    
    // Update all pending batch items to cancelled
    $stmt = $db->prepare("
        UPDATE batch_items 
        SET status = 'cancelled',
            error_message = 'Cancelled by administrator',
            processed_at = CURRENT_TIMESTAMP
        WHERE batch_id = ? AND status = 'pending'
    ");
    $stmt->execute([$batch_id]);
    
    // Log the activity
    $action_type = $user_role === 'administrator' ? 'admin_override' : 'batch_cancel';
    logActivity(
        $db,
        $user_id,
        $action_type,
        'batch_operations',
        null,
        "Cancelled batch: {$batch_id} (Operation: {$batch_info['operation_type']})",
        $_SERVER['REMOTE_ADDR'],
        $_SERVER['HTTP_USER_AGENT']
    );
    
    $db->commit();
    
    echo json_encode([
        'success' => true,
        'message' => 'Batch cancelled successfully'
    ]);
    
} catch (Exception $e) {
    if (isset($db)) {
        $db->rollback();
    }
    error_log('Batch cancel error: ' . $e->getMessage());
    http_response_code(500);
    echo json_encode(['error' => $e->getMessage()]);
}
?>
