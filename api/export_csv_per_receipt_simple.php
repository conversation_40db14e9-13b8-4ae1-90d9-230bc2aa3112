<?php
/**
 * Simple CSV Per Receipt Export (MariaDB 5.5.68 Compatible)
 * แก้ไขปัญหา ROW_NUMBER() ที่ไม่รองรับใน MariaDB 5.5.68
 */

// Enable error reporting for debugging
error_reporting(E_ALL);
ini_set('display_errors', 1);
ini_set('log_errors', 1);

session_start();
require_once '../config/database.php';

// Check if user is logged in
if (!isset($_SESSION['user_id'])) {
    header('Location: ../login.php');
    exit();
}

try {
    $database = new Database();
    $db = $database->getConnection();
} catch (Exception $e) {
    die('Database connection failed: ' . $e->getMessage());
}

$user_id = $_SESSION['user_id'];
$user_role = $_SESSION['role'];

// Get filter parameters
$search = $_GET['search'] ?? '';
$status_filter = $_GET['status'] ?? '';
$date_from = $_GET['date_from'] ?? '';
$date_to = $_GET['date_to'] ?? '';

// Build query based on user role and filters
$where_conditions = [];
$params = [];

// Role-based access
if ($user_role === 'data_entry') {
    $where_conditions[] = 'e.created_by = ?';
    $params[] = $user_id;
}

// Search filter
if (!empty($search)) {
    $where_conditions[] = '(e.exno LIKE ? OR e.bookingno LIKE ? OR i.name LIKE ? OR c.name LIKE ? OR d.name LIKE ?)';
    $search_param = '%' . $search . '%';
    $params = array_merge($params, [$search_param, $search_param, $search_param, $search_param, $search_param]);
}

// Status filter
if (!empty($status_filter)) {
    $where_conditions[] = 'e.status = ?';
    $params[] = $status_filter;
}

// Date range filter
if (!empty($date_from)) {
    $where_conditions[] = 'e.job_open_date >= ?';
    $params[] = $date_from;
}

if (!empty($date_to)) {
    $where_conditions[] = 'e.job_open_date <= ?';
    $params[] = $date_to;
}

$where_clause = !empty($where_conditions) ? 'WHERE ' . implode(' AND ', $where_conditions) : '';

// Check if transfer_amount column exists
try {
    $stmt = $db->prepare("SHOW COLUMNS FROM expenses LIKE 'transfer_amount'");
    $stmt->execute();
    $transfer_amount_exists = $stmt->rowCount() > 0;
} catch (Exception $e) {
    die('Error checking transfer_amount column: ' . $e->getMessage());
}

$transfer_amount_field = $transfer_amount_exists ? 'e.transfer_amount' : '0 as transfer_amount';

// Step 1: Get all expenses first
$expenses_sql = "
    SELECT 
        e.id as expense_id,
        e.exno,
        e.job_open_date,
        e.bookingno,
        e.containerno,
        e.vehicle_plate,
        e.payment_account_no,
        e.additional_details,
        e.requester,
        e.receiver,
        e.payer,
        e.withdrawal_date,
        e.transfer_no,
        $transfer_amount_field,
        COALESCE(e.total_amount, 0) as total_amount,
        e.status,
        e.created_at,
        i.name as item_name,
        c.name as customer_name,
        d.name as driver_name,
        u.full_name as created_by_name
    FROM expenses e
    LEFT JOIN items i ON e.item_id = i.id
    LEFT JOIN customers c ON e.customer_id = c.id
    LEFT JOIN drivers d ON e.driver_id = d.id
    LEFT JOIN users u ON e.created_by = u.id
    $where_clause
    ORDER BY e.created_at DESC
";

try {
    $stmt = $db->prepare($expenses_sql);
    $stmt->execute($params);
    $expenses = $stmt->fetchAll();
} catch (Exception $e) {
    die('Error executing expenses query: ' . $e->getMessage());
}

// Check if we have data
if (empty($expenses)) {
    die('No data found to export. Please check your filters.');
}

// Step 2: Get receipts for each expense
$expense_receipts = [];
foreach ($expenses as $expense) {
    $receipt_sql = "
        SELECT 
            receipt_number,
            amount as receipt_amount,
            description as receipt_description
        FROM receipt_numbers 
        WHERE expense_id = ?
        ORDER BY id ASC
    ";
    
    $stmt = $db->prepare($receipt_sql);
    $stmt->execute([$expense['expense_id']]);
    $receipts = $stmt->fetchAll();
    
    $expense_receipts[$expense['expense_id']] = $receipts;
}

// Set CSV headers
$filename = 'expenses_per_receipt_' . date('Y-m-d_H-i-s') . '.csv';
header('Content-Type: text/csv; charset=utf-8');
header('Content-Disposition: attachment; filename="' . $filename . '"');

// Create output stream
$output = fopen('php://output', 'w');
if (!$output) {
    die('Error: Cannot create output stream');
}

// Add BOM for UTF-8
fprintf($output, chr(0xEF).chr(0xBB).chr(0xBF));

// CSV Headers
$headers = [
    'Expense No',
    'Job Date',
    'Booking No',
    'Item',
    'Customer',
    'Driver',
    'Container No',
    'Vehicle Plate',
    'Payment Account',
    'Requester',
    'Receiver',
    'Payer',
    'Withdrawal Date',
    'Transfer No',
    'Transfer Amount',
    'Receipt Number',
    'Receipt Amount',
    'Receipt Description',
    'Receipt Total',
    'Amount Match',
    'Status',
    'Additional Details',
    'Created By',
    'Created Date'
];

fputcsv($output, $headers);

// Process each expense
foreach ($expenses as $expense) {
    $receipts = $expense_receipts[$expense['expense_id']];
    
    // Calculate receipt total for this expense
    $receipt_total = 0;
    foreach ($receipts as $receipt) {
        if ($receipt['receipt_amount']) {
            $receipt_total += $receipt['receipt_amount'];
        }
    }
    
    // Calculate amount match
    $amount_match = (abs($expense['transfer_amount'] - $receipt_total) <= 0.01) ? 'Match' : 'Mismatch';
    
    if (empty($receipts)) {
        // Expense with no receipts - show expense data with empty receipt fields
        $csv_row = [
            $expense['exno'],
            $expense['job_open_date'],
            $expense['bookingno'],
            $expense['item_name'],
            $expense['customer_name'],
            $expense['driver_name'],
            $expense['containerno'],
            $expense['vehicle_plate'],
            $expense['payment_account_no'],
            $expense['requester'],
            $expense['receiver'],
            $expense['payer'],
            $expense['withdrawal_date'],
            $expense['transfer_no'],
            number_format($expense['transfer_amount'], 2),
            '', // Receipt Number
            '', // Receipt Amount
            '', // Receipt Description
            number_format($receipt_total, 2),
            $amount_match,
            ucfirst($expense['status']),
            $expense['additional_details'],
            $expense['created_by_name'],
            $expense['created_at']
        ];
        fputcsv($output, $csv_row);
    } else {
        // Expense with receipts - show expense data on first receipt row
        $first_receipt = true;
        foreach ($receipts as $receipt) {
            if ($first_receipt) {
                // First receipt row: show all expense data
                $csv_row = [
                    $expense['exno'],
                    $expense['job_open_date'],
                    $expense['bookingno'],
                    $expense['item_name'],
                    $expense['customer_name'],
                    $expense['driver_name'],
                    $expense['containerno'],
                    $expense['vehicle_plate'],
                    $expense['payment_account_no'],
                    $expense['requester'],
                    $expense['receiver'],
                    $expense['payer'],
                    $expense['withdrawal_date'],
                    $expense['transfer_no'],
                    number_format($expense['transfer_amount'], 2),
                    $receipt['receipt_number'] ?? '',
                    $receipt['receipt_amount'] ? number_format($receipt['receipt_amount'], 2) : '',
                    $receipt['receipt_description'] ?? '',
                    number_format($receipt_total, 2),
                    $amount_match,
                    ucfirst($expense['status']),
                    $expense['additional_details'],
                    $expense['created_by_name'],
                    $expense['created_at']
                ];
                $first_receipt = false;
            } else {
                // Subsequent receipt rows: empty expense data, show only receipt data
                $csv_row = [
                    '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', // Empty expense fields
                    $receipt['receipt_number'] ?? '',
                    $receipt['receipt_amount'] ? number_format($receipt['receipt_amount'], 2) : '',
                    $receipt['receipt_description'] ?? '',
                    '', '', '', '', '', '' // Empty summary fields
                ];
            }
            fputcsv($output, $csv_row);
        }
    }
}

fclose($output);
exit();
?>
