<?php
session_start();
require_once '../config/database.php';
require_once '../includes/functions.php';

header('Content-Type: application/json');

// Check if user is logged in
if (!isset($_SESSION['user_id'])) {
    http_response_code(401);
    echo json_encode(['error' => 'Unauthorized']);
    exit();
}

try {
    $database = new Database();
    $db = $database->getConnection();
    
    $user_id = $_SESSION['user_id'];
    $user_role = $_SESSION['role'];
    
    // Build query based on user role
    $where_clause = '';
    $params = [];
    
    // Data entry users can only see their own records
    if ($user_role === 'data_entry') {
        $where_clause = 'WHERE e.created_by = ?';
        $params[] = $user_id;
    }
    
    // Check if transfer_amount column exists
    $stmt = $db->prepare("SHOW COLUMNS FROM expenses LIKE 'transfer_amount'");
    $stmt->execute();
    $transfer_amount_exists = $stmt->rowCount() > 0;

    $transfer_amount_field = $transfer_amount_exists ? 'e.transfer_amount' : '0 as transfer_amount';

    // Get recent expenses with related data
    $stmt = $db->prepare("
        SELECT
            e.id,
            e.exno,
            e.job_open_date,
            e.withdrawal_date,
            e.status,
            e.created_at,
            e.created_by,
            $transfer_amount_field,
            COALESCE(e.total_amount, 0) as total_amount,
            i.name as item_name,
            c.name as customer_name,
            d.name as driver_name
        FROM expenses e
        LEFT JOIN items i ON e.item_id = i.id
        LEFT JOIN customers c ON e.customer_id = c.id
        LEFT JOIN drivers d ON e.driver_id = d.id
        $where_clause
        ORDER BY e.created_at DESC
        LIMIT 10
    ");

    $stmt->execute($params);
    $expenses = $stmt->fetchAll();

    // Get receipt numbers and permissions for each expense
    foreach ($expenses as &$expense) {
        $stmt = $db->prepare("
            SELECT receipt_number, amount, description
            FROM receipt_numbers
            WHERE expense_id = ?
            ORDER BY id
        ");
        $stmt->execute([$expense['id']]);
        $expense['receipt_numbers'] = $stmt->fetchAll();

        // Add permission flags
        $expense['can_edit'] = canEditExpense($expense, $user_id, $user_role);
        $expense['can_manage_status'] = canManageStatus($expense, $user_id, $user_role);
    }

    echo json_encode($expenses);
    
} catch (Exception $e) {
    error_log('Recent expenses error: ' . $e->getMessage());
    http_response_code(500);
    echo json_encode(['error' => 'Internal server error']);
}
?>
