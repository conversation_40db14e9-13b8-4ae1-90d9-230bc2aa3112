<?php
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: POST');
header('Access-Control-Allow-Headers: Content-Type');

require_once '../config/database.php';

try {
    // Create database connection
    $database = new Database();
    $db = $database->getConnection();
    
    // Check if request method is POST
    if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
        throw new Exception('Only POST method allowed');
    }
    
    // Get transfer number from request
    $transfer_no = trim($_POST['transfer_no'] ?? '');
    
    if (empty($transfer_no)) {
        echo json_encode([
            'exists' => false,
            'message' => 'Transfer number is empty'
        ]);
        exit;
    }
    
    // Check if transfer number exists in multiple tables
    $exists = false;
    $source = '';
    $reference = '';

    // 1. Check in expenses table (individual expense transfer numbers)
    $stmt = $db->prepare("
        SELECT transfer_no, exno, id as expense_id, status
        FROM expenses
        WHERE transfer_no = ? AND transfer_no != ''
        AND status NOT IN ('rejected', 'returned')
        LIMIT 1
    ");
    $stmt->execute([$transfer_no]);
    $expense_result = $stmt->fetch();

    if ($expense_result) {
        $exists = true;
        $source = 'expense';
        $reference = $expense_result['exno'];
    }

    // 2. Check in batch_file_uploads table (batch file transfer numbers)
    if (!$exists) {
        $stmt = $db->prepare("
            SELECT bfu.transfer_number, bfu.batch_id, bo.operation_type
            FROM batch_file_uploads bfu
            JOIN batch_operations bo ON bfu.batch_id = bo.batch_id
            WHERE bfu.transfer_number = ?
            LIMIT 1
        ");
        $stmt->execute([$transfer_no]);
        $file_result = $stmt->fetch();

        if ($file_result) {
            $exists = true;
            $source = 'batch_file';
            $reference = $file_result['batch_id'] . ' (' . $file_result['operation_type'] . ')';
        }
    }

    // 3. Check in batch_operations table (batch transfer numbers)
    if (!$exists) {
        $stmt = $db->prepare("
            SELECT batch_id, operation_type, transfer_number
            FROM batch_operations
            WHERE transfer_number = ? AND transfer_number IS NOT NULL
            LIMIT 1
        ");
        $stmt->execute([$transfer_no]);
        $batch_result = $stmt->fetch();

        if ($batch_result) {
            $exists = true;
            $source = 'batch_operation';
            $reference = $batch_result['batch_id'] . ' (' . $batch_result['operation_type'] . ')';
        }
    }

    if ($exists) {
        // Transfer number exists
        echo json_encode([
            'exists' => true,
            'expense_no' => $reference,
            'source' => $source,
            'message' => "Transfer number '{$transfer_no}' already exists in {$source}: {$reference}"
        ]);
    } else {
        // Transfer number doesn't exist
        echo json_encode([
            'exists' => false,
            'message' => "Transfer number '{$transfer_no}' is available"
        ]);
    }
    
} catch (Exception $e) {
    http_response_code(500);
    echo json_encode([
        'error' => true,
        'message' => $e->getMessage()
    ]);
}
?>
