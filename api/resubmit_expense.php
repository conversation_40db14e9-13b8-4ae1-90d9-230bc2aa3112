<?php
/**
 * API Endpoint: Resubmit Expense
 * Allows data entry user to resubmit a returned or rejected expense
 */

session_start();
require_once '../config/database.php';
require_once '../includes/functions.php';

header('Content-Type: application/json');

// Check if user is logged in
if (!isset($_SESSION['user_id'])) {
    http_response_code(401);
    echo json_encode(['success' => false, 'error' => 'Not authenticated']);
    exit();
}

// Check if this is an admin override
$admin_override = isset($_POST['admin_override']) && $_POST['admin_override'] === '1' && $_SESSION['role'] === 'administrator';

try {
    // Initialize database connection
    $database = new Database();
    $db = $database->getConnection();
    
    // Get input data
    $expense_id = (int)($_POST['expense_id'] ?? 0);
    $resubmit_comment = trim($_POST['resubmit_comment'] ?? '');
    $user_id = $_SESSION['user_id'];
    $user_role = $_SESSION['role'];
    
    // Validate input
    if (empty($expense_id)) {
        throw new Exception('Expense ID is required');
    }
    
    // Get expense details
    $stmt = $db->prepare("SELECT * FROM expenses WHERE id = ?");
    $stmt->execute([$expense_id]);
    $expense = $stmt->fetch();
    
    if (!$expense) {
        throw new Exception('Expense not found');
    }
    
    $current_status = $expense['status'];
    
    // Check permissions
    if (!$admin_override) {
        // Only the creator or admin can resubmit
        if ($expense['created_by'] != $user_id && $user_role !== 'administrator') {
            throw new Exception('You can only resubmit your own expenses');
        }
        
        // Can only resubmit returned or rejected expenses
        if (!in_array($current_status, ['returned', 'rejected'])) {
            throw new Exception('Expense can only be resubmitted if it was returned or rejected. Current status: ' . $current_status);
        }
    }
    
    // Begin transaction
    $db->beginTransaction();
    
    try {
        // Update expense status back to open and clear rejection/return data
        $stmt = $db->prepare("
            UPDATE expenses 
            SET status = 'open',
                rejection_reason = NULL,
                rejection_date = NULL,
                rejected_by = NULL,
                return_reason = NULL,
                return_date = NULL,
                returned_by = NULL,
                updated_at = NOW()
            WHERE id = ?
        ");
        $stmt->execute([$expense_id]);
        
        // Add workflow history manually (avoid nested transaction)
        try {
            $stmt = $db->prepare("
                INSERT INTO activity_logs (
                    user_id, action_type, table_name, record_id,
                    description, created_at
                ) VALUES (?, ?, 'expenses', ?, ?, NOW())
            ");

            $workflow_description = sprintf(
                'Status changed from %s to open. Comment: %s',
                $current_status,
                $resubmit_comment ?: 'Resubmitted for review'
            );

            $stmt->execute([
                $user_id,
                $admin_override ? 'admin_resubmit' : 'resubmit',
                $expense_id,
                $workflow_description
            ]);
        } catch (Exception $e) {
            // If workflow history fails, continue without it
            error_log("Workflow history failed: " . $e->getMessage());
        }
        
        // Log the activity
        $activity_description = $admin_override ? 
            "Admin override: Resubmitted expense" . ($resubmit_comment ? " - " . $resubmit_comment : "") :
            "Resubmitted expense for review" . ($resubmit_comment ? " - " . $resubmit_comment : "");
            
        logActivity(
            $db,
            $user_id,
            $admin_override ? 'admin_override' : 'resubmit',
            'expenses',
            $expense_id,
            $activity_description,
            $_SERVER['REMOTE_ADDR'],
            $_SERVER['HTTP_USER_AGENT']
        );
        
        // Commit transaction
        $db->commit();
        
        echo json_encode([
            'success' => true,
            'message' => 'Expense resubmitted successfully',
            'data' => [
                'expense_id' => $expense_id,
                'status' => 'open',
                'resubmit_comment' => $resubmit_comment,
                'resubmitted_by' => $user_id,
                'resubmit_date' => date('Y-m-d H:i:s')
            ]
        ]);
        
    } catch (Exception $e) {
        $db->rollback();
        throw $e;
    }
    
} catch (Exception $e) {
    http_response_code(400);
    echo json_encode([
        'success' => false,
        'error' => $e->getMessage()
    ]);
} catch (PDOException $e) {
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'error' => 'Database error: ' . $e->getMessage()
    ]);
}
?>
