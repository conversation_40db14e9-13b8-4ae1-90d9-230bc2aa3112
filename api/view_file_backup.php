<?php
session_start();
require_once '../config/database.php';
require_once '../includes/functions.php';

// Check if user is logged in
if (!isset($_SESSION['user_id'])) {
    header('HTTP/1.0 403 Forbidden');
    exit('Access denied');
}

$file = $_GET['file'] ?? '';
$type = $_GET['type'] ?? '';

if (empty($file) || empty($type)) {
    header('HTTP/1.0 400 Bad Request');
    exit('Missing parameters');
}

// Validate file type
$allowed_types = ['transfer_slip', 'receipt', 'verification_slip', 'reviewer_slip', 'bulk_operation_slip', 'batch_document'];
if (!in_array($type, $allowed_types)) {
    header('HTTP/1.0 400 Bad Request');
    exit('Invalid file type');
}

// Sanitize filename - handle paths for batch documents
$original_file = $file;
if ($type === 'batch_document') {
    // For batch documents, allow path separators but sanitize
    $file = str_replace(['../', '../', '..\\'], '', $file); // Remove directory traversal
    if (!preg_match('/^[a-zA-Z0-9_\-\.\/\\\\]+$/', $file)) {
        header('HTTP/1.0 400 Bad Request');
        exit('Invalid filename');
    }
} else {
    // For other types, use basename only
    $file = basename($file);
    if (!preg_match('/^[a-zA-Z0-9_\-\.]+$/', $file)) {
        header('HTTP/1.0 400 Bad Request');
        exit('Invalid filename');
    }
}

// Determine file path
$upload_dir = '../uploads/';
if ($type === 'transfer_slip') {
    $file_path = $upload_dir . 'transfer_slips/' . $file;
} elseif ($type === 'verification_slip') {
    $file_path = $upload_dir . 'verification_slips/' . $file;
} elseif ($type === 'reviewer_slip') {
    $file_path = $upload_dir . 'reviewer_slips/' . $file;
} elseif ($type === 'bulk_operation_slip') {
    $file_path = $upload_dir . 'bulk_operations/' . $file;
} elseif ($type === 'batch_document') {
    // For batch documents, try different possible paths
    $possible_paths = [
        $upload_dir . $file, // Direct path from URL
        $upload_dir . 'batch_documents/' . basename($file),
        $upload_dir . 'batch_documents/verification/' . basename($file),
        $upload_dir . 'batch_documents/review/' . basename($file),
        $upload_dir . 'bulk_operations/' . basename($file)
    ];

    $file_path = null;
    foreach ($possible_paths as $path) {
        if (file_exists($path)) {
            $file_path = $path;
            break;
        }
    }

    // If no file found, use the first path as default (will show 404)
    if (!$file_path) {
        $file_path = $upload_dir . $file;
    }
} else {
    $file_path = $upload_dir . 'receipts/' . $file;
}

// Check if file exists
if (!file_exists($file_path) || !is_file($file_path)) {
    header('HTTP/1.0 404 Not Found');
    exit('File not found');
}

try {
    $database = new Database();
    $db = $database->getConnection();
    
    // Verify user has access to this file
    $user_id = $_SESSION['user_id'];
    $user_role = $_SESSION['role'];
    
    // Check if file belongs to an expense the user can access
    if ($type === 'transfer_slip') {
        $stmt = $db->prepare("
            SELECT e.id, e.created_by
            FROM expenses e
            WHERE e.transfer_slip_image = ?
        ");
    } elseif ($type === 'verification_slip') {
        $stmt = $db->prepare("
            SELECT e.id, e.created_by
            FROM expenses e
            WHERE e.verification_slip_image = ?
        ");
    } elseif ($type === 'reviewer_slip') {
        $stmt = $db->prepare("
            SELECT e.id, e.created_by
            FROM expenses e
            WHERE e.reviewer_slip_image = ?
        ");
    } elseif ($type === 'receipt') {
        // For receipts, check in receipt_numbers table
        $stmt = $db->prepare("
            SELECT e.id, e.created_by
            FROM expenses e
            JOIN receipt_numbers rn ON e.id = rn.expense_id
            WHERE rn.receipt_image = ?
        ");
    } elseif ($type === 'batch_document') {
        // For batch documents, allow access for all roles (simplified)
        $expense = ['id' => 0, 'created_by' => $user_id];
    } else {
        // For other types, check in receipt_images JSON (legacy)
        $stmt = $db->prepare("
            SELECT e.id, e.created_by
            FROM expenses e
            WHERE JSON_SEARCH(e.receipt_images, 'one', ?) IS NOT NULL
        ");
    }

    // Execute query only if not batch_document (already handled above)
    if ($type !== 'batch_document') {
        $stmt->execute([$file]);
        $expense = $stmt->fetch();

        if (!$expense) {
            header('HTTP/1.0 404 Not Found');
            exit('File not found');
        }
    }
    
    // Check access permissions
    $has_access = false;
    
    if ($user_role === 'administrator') {
        $has_access = true;
    } elseif ($user_role === 'data_entry') {
        $has_access = ($expense['created_by'] == $user_id);
    } else {
        // Verification and reviewer roles can access all files
        $has_access = true;
    }
    
    if (!$has_access) {
        header('HTTP/1.0 403 Forbidden');
        exit('Access denied');
    }
    
    // Get file info
    $file_info = pathinfo($file_path);
    $file_extension = strtolower($file_info['extension']);
    
    // Set appropriate content type
    $content_types = [
        'jpg' => 'image/jpeg',
        'jpeg' => 'image/jpeg',
        'png' => 'image/png',
        'gif' => 'image/gif',
        'pdf' => 'application/pdf'
    ];
    
    $content_type = $content_types[$file_extension] ?? 'application/octet-stream';
    
    // Set headers
    header('Content-Type: ' . $content_type);
    header('Content-Length: ' . filesize($file_path));
    header('Content-Disposition: inline; filename="' . $file . '"');
    header('Cache-Control: private, max-age=3600');
    header('Pragma: private');
    
    // Output file
    readfile($file_path);
    

?>
