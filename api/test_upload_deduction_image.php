<?php
/**
 * Test API Endpoint for Uploading Deduction Images
 * Handles file upload for deduction proof images (No authentication required for testing)
 */

// Set JSON response header
header('Content-Type: application/json');

// Only allow POST requests
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode(['error' => 'Method not allowed']);
    exit;
}

// Check if file was uploaded
if (!isset($_FILES['deduction_image']) || $_FILES['deduction_image']['error'] !== UPLOAD_ERR_OK) {
    http_response_code(400);
    echo json_encode([
        'error' => 'No file uploaded or upload error',
        'upload_error_code' => $_FILES['deduction_image']['error'] ?? 'No file'
    ]);
    exit;
}

$file = $_FILES['deduction_image'];

// File validation
$allowedTypes = [
    'image/jpeg',
    'image/jpg', 
    'image/png',
    'image/gif',
    'application/pdf'
];

$allowedExtensions = ['jpg', 'jpeg', 'png', 'gif', 'pdf'];
$maxSize = 5 * 1024 * 1024; // 5MB

// Check file type
$finfo = finfo_open(FILEINFO_MIME_TYPE);
$mimeType = finfo_file($finfo, $file['tmp_name']);
finfo_close($finfo);

if (!in_array($mimeType, $allowedTypes)) {
    http_response_code(400);
    echo json_encode([
        'error' => 'Invalid file type. Allowed: JPG, PNG, GIF, PDF',
        'detected_type' => $mimeType
    ]);
    exit;
}

// Check file extension
$extension = strtolower(pathinfo($file['name'], PATHINFO_EXTENSION));
if (!in_array($extension, $allowedExtensions)) {
    http_response_code(400);
    echo json_encode([
        'error' => 'Invalid file extension. Allowed: ' . implode(', ', $allowedExtensions)
    ]);
    exit;
}

// Check file size
if ($file['size'] > $maxSize) {
    http_response_code(400);
    echo json_encode([
        'error' => 'File too large. Maximum size: 5MB',
        'file_size' => round($file['size'] / 1024 / 1024, 2) . 'MB'
    ]);
    exit;
}

// Create upload directory if it doesn't exist
$uploadDir = '../uploads/deductions/';
if (!is_dir($uploadDir)) {
    if (!mkdir($uploadDir, 0755, true)) {
        http_response_code(500);
        echo json_encode(['error' => 'Failed to create upload directory']);
        exit;
    }
}

// Generate unique filename
$filename = 'test_deduction_' . date('Ymd_His') . '_' . uniqid() . '.' . $extension;
$uploadPath = $uploadDir . $filename;

// Move uploaded file
if (move_uploaded_file($file['tmp_name'], $uploadPath)) {
    // Success response
    echo json_encode([
        'success' => true,
        'filename' => $filename,
        'url' => 'uploads/deductions/' . $filename,
        'original_name' => $file['name'],
        'size' => $file['size'],
        'type' => $mimeType,
        'uploaded_at' => date('Y-m-d H:i:s'),
        'uploaded_by' => 'test_user'
    ]);
} else {
    http_response_code(500);
    echo json_encode([
        'error' => 'Failed to move uploaded file',
        'upload_path' => $uploadPath,
        'upload_dir_writable' => is_writable($uploadDir),
        'upload_dir_exists' => is_dir($uploadDir)
    ]);
}
?>
