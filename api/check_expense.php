<?php
/**
 * API Endpoint: Check Expense
 * Allows verification users to mark an expense as 'checked'
 * This is the first step in the new verification workflow
 */

session_start();
require_once '../config/database.php';
require_once '../includes/functions.php';

header('Content-Type: application/json');

// Check if user is logged in
if (!isset($_SESSION['user_id'])) {
    http_response_code(401);
    echo json_encode(['success' => false, 'error' => 'Not authenticated']);
    exit();
}

// Check if user has permission to check (verification or admin)
if (!in_array($_SESSION['role'], ['verification', 'administrator'])) {
    http_response_code(403);
    echo json_encode(['success' => false, 'error' => 'Access denied. Only verification users can check expenses.']);
    exit();
}

try {
    // Initialize database connection
    $database = new Database();
    $db = $database->getConnection();
    
    // Get input data
    $expense_id = (int)($_POST['expense_id'] ?? 0);
    $check_comment = trim($_POST['check_comment'] ?? '');
    $user_id = $_SESSION['user_id'];
    $user_role = $_SESSION['role'];
    
    // Validate input
    if (empty($expense_id)) {
        throw new Exception('Expense ID is required');
    }
    
    // Get expense details
    $stmt = $db->prepare("SELECT * FROM expenses WHERE id = ?");
    $stmt->execute([$expense_id]);
    $expense = $stmt->fetch();
    
    if (!$expense) {
        throw new Exception('Expense not found');
    }
    
    $current_status = $expense['status'];
    
    // Check if expense can be checked (must be 'open')
    if ($current_status !== 'open') {
        throw new Exception("Cannot check expense with status '{$current_status}'. Only 'open' expenses can be checked.");
    }
    
    // Start transaction
    $db->beginTransaction();
    
    try {
        // Update expense status to checked
        $stmt = $db->prepare("
            UPDATE expenses 
            SET status = 'checked', 
                checked_by = ?, 
                checked_at = NOW(),
                check_comment = ?
            WHERE id = ?
        ");
        $stmt->execute([$user_id, $check_comment, $expense_id]);
        
        // Add workflow history manually (avoid nested transaction)
        try {
            $stmt = $db->prepare("
                INSERT INTO activity_logs (
                    user_id, action_type, table_name, record_id,
                    description, created_at
                ) VALUES (?, ?, 'expenses', ?, ?, NOW())
            ");

            $workflow_description = sprintf(
                'Status changed from %s to checked%s',
                $current_status,
                !empty($check_comment) ? '. Comment: ' . $check_comment : '. Expense checked by verification'
            );

            $stmt->execute([
                $user_id,
                'check',
                $expense_id,
                $workflow_description
            ]);
        } catch (Exception $e) {
            // If workflow history fails, continue without it
            error_log("Workflow history failed: " . $e->getMessage());
        }
        
        // Log the action
        logActivity(
            $db,
            $user_id,
            'check',
            'expenses',
            $expense_id,
            "Checked expense {$expense['exno']}" . ($check_comment ? ". Comment: {$check_comment}" : ""),
            $_SERVER['REMOTE_ADDR'],
            $_SERVER['HTTP_USER_AGENT']
        );
        
        // Commit transaction
        $db->commit();
        
        echo json_encode([
            'success' => true,
            'message' => 'Expense checked successfully',
            'expense_id' => $expense_id,
            'new_status' => 'checked'
        ]);
        
    } catch (Exception $e) {
        $db->rollback();
        throw $e;
    }
    
} catch (Exception $e) {
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'error' => $e->getMessage()
    ]);
}
?>
