<?php
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: POST');
header('Access-Control-Allow-Headers: Content-Type');

require_once '../config/database.php';

try {
    // Create database connection
    $database = new Database();
    $db = $database->getConnection();
    
    // Check if request method is POST
    if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
        throw new Exception('Only POST method allowed');
    }
    
    // Get receipt number from request
    $receipt_number = trim($_POST['receipt_number'] ?? '');
    
    if (empty($receipt_number)) {
        echo json_encode([
            'exists' => false,
            'message' => 'Receipt number is empty'
        ]);
        exit;
    }
    
    // Check if receipt number exists in database
    // Allow duplicates only for rejected/returned expenses
    $stmt = $db->prepare("
        SELECT rn.receipt_number, e.exno, e.id as expense_id, e.status
        FROM receipt_numbers rn
        INNER JOIN expenses e ON rn.expense_id = e.id
        WHERE rn.receipt_number = ?
        AND e.status NOT IN ('rejected', 'returned')
        LIMIT 1
    ");
    
    $stmt->execute([$receipt_number]);
    $result = $stmt->fetch();
    
    if ($result) {
        // Receipt number exists
        echo json_encode([
            'exists' => true,
            'expense_no' => $result['exno'],
            'expense_id' => $result['expense_id'],
            'message' => "Receipt number '{$receipt_number}' already exists in expense {$result['exno']}"
        ]);
    } else {
        // Receipt number doesn't exist
        echo json_encode([
            'exists' => false,
            'message' => "Receipt number '{$receipt_number}' is available"
        ]);
    }
    
} catch (Exception $e) {
    http_response_code(500);
    echo json_encode([
        'error' => true,
        'message' => $e->getMessage()
    ]);
}
?>
