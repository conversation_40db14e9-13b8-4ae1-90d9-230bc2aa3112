<?php
/**
 * API Endpoint: Reject Expense
 * Allows verification or reviewer to reject an expense
 */

session_start();
require_once '../config/database.php';
require_once '../includes/functions.php';

header('Content-Type: application/json');

// Check if user is logged in
if (!isset($_SESSION['user_id'])) {
    http_response_code(401);
    echo json_encode(['success' => false, 'error' => 'Not authenticated']);
    exit();
}

// Check if this is an admin override
$admin_override = isset($_POST['admin_override']) && $_POST['admin_override'] === '1' && $_SESSION['role'] === 'administrator';

// Check if user has permission to reject (verification, reviewer, or admin)
if (!$admin_override && !in_array($_SESSION['role'], ['verification', 'reviewer', 'administrator'])) {
    http_response_code(403);
    echo json_encode(['success' => false, 'error' => 'Access denied. Insufficient permissions.']);
    exit();
}

try {
    // Initialize database connection
    $database = new Database();
    $db = $database->getConnection();
    
    // Get input data
    $expense_id = (int)($_POST['expense_id'] ?? 0);
    $rejection_reason = trim($_POST['rejection_reason'] ?? '');
    $user_id = $_SESSION['user_id'];
    $user_role = $_SESSION['role'];
    
    // Validate input
    if (empty($expense_id)) {
        throw new Exception('Expense ID is required');
    }
    
    if (empty($rejection_reason)) {
        throw new Exception('Rejection reason is required');
    }
    
    // Get expense details
    $stmt = $db->prepare("SELECT * FROM expenses WHERE id = ?");
    $stmt->execute([$expense_id]);
    $expense = $stmt->fetch();
    
    if (!$expense) {
        throw new Exception('Expense not found');
    }
    
    $current_status = $expense['status'];
    
    // Check if expense can be rejected based on current status and user role
    if (!$admin_override) {
        if ($user_role === 'verification' && $current_status !== 'open') {
            throw new Exception('Verification can only reject expenses in "open" status');
        }
        
        if ($user_role === 'reviewer' && $current_status !== 'pending') {
            throw new Exception('Reviewer can only reject expenses in "pending" status');
        }
        
        if (!in_array($current_status, ['open', 'pending'])) {
            throw new Exception('Expense cannot be rejected in current status: ' . $current_status);
        }
    }
    
    // Begin transaction
    $db->beginTransaction();
    
    try {
        // Update expense status to rejected
        $stmt = $db->prepare("
            UPDATE expenses 
            SET status = 'rejected', 
                rejection_reason = ?, 
                rejection_date = NOW(), 
                rejected_by = ? 
            WHERE id = ?
        ");
        $stmt->execute([$rejection_reason, $user_id, $expense_id]);
        
        // Add workflow history manually (avoid nested transaction)
        try {
            $stmt = $db->prepare("
                INSERT INTO activity_logs (
                    user_id, action_type, table_name, record_id,
                    description, created_at
                ) VALUES (?, ?, 'expenses', ?, ?, NOW())
            ");

            $workflow_description = sprintf(
                'Status changed from %s to rejected%s',
                $current_status,
                !empty($rejection_reason) ? '. Comment: ' . $rejection_reason : ''
            );

            $stmt->execute([
                $user_id,
                $admin_override ? 'admin_reject' : 'reject',
                $expense_id,
                $workflow_description
            ]);
        } catch (Exception $e) {
            // If workflow history fails, continue without it
            error_log("Workflow history failed: " . $e->getMessage());
        }
        
        // Log the activity
        $activity_description = $admin_override ? 
            "Admin override: Rejected expense - " . $rejection_reason :
            "Rejected expense - " . $rejection_reason;
            
        logActivity(
            $db,
            $user_id,
            $admin_override ? 'admin_override' : 'reject',
            'expenses',
            $expense_id,
            $activity_description,
            $_SERVER['REMOTE_ADDR'],
            $_SERVER['HTTP_USER_AGENT']
        );
        
        // Commit transaction
        $db->commit();
        
        echo json_encode([
            'success' => true,
            'message' => 'Expense rejected successfully',
            'data' => [
                'expense_id' => $expense_id,
                'status' => 'rejected',
                'rejection_reason' => $rejection_reason,
                'rejected_by' => $user_id,
                'rejection_date' => date('Y-m-d H:i:s')
            ]
        ]);
        
    } catch (Exception $e) {
        $db->rollback();
        throw $e;
    }
    
} catch (Exception $e) {
    http_response_code(400);
    echo json_encode([
        'success' => false,
        'error' => $e->getMessage()
    ]);
} catch (PDOException $e) {
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'error' => 'Database error: ' . $e->getMessage()
    ]);
}
?>
