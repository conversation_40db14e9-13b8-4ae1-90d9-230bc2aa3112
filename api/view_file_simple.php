<?php
session_start();
require_once '../config/database.php';

// Check if user is logged in
if (!isset($_SESSION['user_id'])) {
    header('HTTP/1.0 403 Forbidden');
    exit('Access denied');
}

$file = $_GET['file'] ?? '';
$type = $_GET['type'] ?? '';

if (empty($file) || empty($type)) {
    header('HTTP/1.0 400 Bad Request');
    exit('Missing parameters');
}

// Get user info
$user_id = $_SESSION['user_id'];
$user_role = $_SESSION['role'] ?? 'data_entry';

// Sanitize filename
if ($type === 'batch_document') {
    // For batch documents, allow path separators but sanitize
    $file = str_replace(['../', '../', '..\\'], '', $file);
    if (!preg_match('/^[a-zA-Z0-9_\-\.\/\\\\]+$/', $file)) {
        header('HTTP/1.0 400 Bad Request');
        exit('Invalid filename');
    }
} else {
    // For other types, use basename only
    $file = basename($file);
    if (!preg_match('/^[a-zA-Z0-9_\-\.]+$/', $file)) {
        header('HTTP/1.0 400 Bad Request');
        exit('Invalid filename');
    }
}

$upload_dir = '../uploads/';

// Determine file path based on type
if ($type === 'transfer_slip') {
    $file_path = $upload_dir . 'transfer_slips/' . $file;
} elseif ($type === 'verification_slip') {
    $file_path = $upload_dir . 'verification_slips/' . $file;
} elseif ($type === 'reviewer_slip') {
    $file_path = $upload_dir . 'review_slips/' . $file;
} elseif ($type === 'bulk_operation_slip') {
    $file_path = $upload_dir . 'bulk_operations/' . $file;
} elseif ($type === 'batch_document') {
    // For batch documents, try different possible paths
    $possible_paths = [
        $upload_dir . $file, // Direct path from URL
        $upload_dir . 'batch_documents/' . basename($file),
        $upload_dir . 'batch_documents/verification/' . basename($file),
        $upload_dir . 'batch_documents/review/' . basename($file),
        $upload_dir . 'bulk_operations/' . basename($file)
    ];

    $file_path = null;
    foreach ($possible_paths as $path) {
        if (file_exists($path)) {
            $file_path = $path;
            break;
        }
    }

    // If no file found, use the first path as default (will show 404)
    if (!$file_path) {
        $file_path = $upload_dir . $file;
    }
} elseif ($type === 'receipt') {
    $file_path = $upload_dir . 'receipts/' . $file;
} else {
    $file_path = $upload_dir . 'receipts/' . $file;
}

// Check if file exists
if (!file_exists($file_path) || !is_file($file_path)) {
    header('HTTP/1.0 404 Not Found');
    exit('File not found: ' . basename($file_path));
}

// Simple access control - allow all logged-in users for now
$has_access = true;

// For data_entry users, check if they own the file (simplified)
if ($user_role === 'data_entry' && $type !== 'batch_document') {
    try {
        $database = new Database();
        $db = $database->getConnection();
        
        if ($type === 'receipt') {
            // Check receipt_numbers table
            $stmt = $db->prepare("
                SELECT e.id, e.created_by
                FROM expenses e
                JOIN receipt_numbers rn ON e.id = rn.expense_id
                WHERE rn.receipt_image = ?
            ");
            $stmt->execute([$file]);
            $expense = $stmt->fetch();
            
            if ($expense) {
                $has_access = ($expense['created_by'] == $user_id);
            }
        } else {
            // Check expenses table for other types
            $stmt = $db->prepare("
                SELECT id, created_by
                FROM expenses
                WHERE transfer_slip_image = ? OR verification_slip_image = ? OR reviewer_slip_image = ?
            ");
            $stmt->execute([$file, $file, $file]);
            $expense = $stmt->fetch();
            
            if ($expense) {
                $has_access = ($expense['created_by'] == $user_id);
            }
        }
    } catch (Exception $e) {
        // If database check fails, allow access
        $has_access = true;
    }
}

if (!$has_access) {
    header('HTTP/1.0 403 Forbidden');
    exit('Access denied');
}

// Get file info
$file_info = pathinfo($file_path);
$file_extension = strtolower($file_info['extension']);

// Set appropriate content type
$content_types = [
    'jpg' => 'image/jpeg',
    'jpeg' => 'image/jpeg',
    'png' => 'image/png',
    'gif' => 'image/gif',
    'pdf' => 'application/pdf'
];

$content_type = $content_types[$file_extension] ?? 'application/octet-stream';

// Set headers
header('Content-Type: ' . $content_type);
header('Content-Length: ' . filesize($file_path));
header('Cache-Control: private, max-age=3600');

// Output file
readfile($file_path);
?>
