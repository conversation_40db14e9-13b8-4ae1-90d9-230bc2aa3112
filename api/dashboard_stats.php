<?php
session_start();
require_once '../config/database.php';
require_once '../includes/functions.php';

header('Content-Type: application/json');

// Check if user is logged in
if (!isset($_SESSION['user_id'])) {
    http_response_code(401);
    echo json_encode(['error' => 'Unauthorized']);
    exit();
}

try {
    $database = new Database();
    $db = $database->getConnection();
    
    $user_id = $_SESSION['user_id'];
    $user_role = $_SESSION['role'];
    
    // Build query based on user role
    $where_clause = '';
    $params = [];
    
    // Data entry users can only see their own records
    if ($user_role === 'data_entry') {
        $where_clause = 'WHERE created_by = ?';
        $params[] = $user_id;
    }
    
    // Get expense counts by status
    $stmt = $db->prepare("
        SELECT 
            COUNT(*) as total,
            SUM(CASE WHEN status = 'open' THEN 1 ELSE 0 END) as open,
            SUM(CASE WHEN status = 'pending' THEN 1 ELSE 0 END) as pending,
            SUM(CASE WHEN status = 'success' THEN 1 ELSE 0 END) as success
        FROM expenses 
        $where_clause
    ");
    
    $stmt->execute($params);
    $stats = $stmt->fetch();
    
    echo json_encode([
        'total' => (int)$stats['total'],
        'open' => (int)$stats['open'],
        'pending' => (int)$stats['pending'],
        'success' => (int)$stats['success']
    ]);
    
} catch (Exception $e) {
    error_log('Dashboard stats error: ' . $e->getMessage());
    http_response_code(500);
    echo json_encode(['error' => 'Internal server error']);
}
?>
