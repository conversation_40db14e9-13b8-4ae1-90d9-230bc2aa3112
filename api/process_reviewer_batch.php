<?php
/**
 * API: Process Reviewer Batch
 * Handles approve/reject decisions for verification batches
 */

session_start();
require_once '../config/database.php';
require_once '../includes/functions.php';
require_once '../includes/BatchOperations.php';

header('Content-Type: application/json');

// Check authentication
if (!isset($_SESSION['user_id'])) {
    http_response_code(401);
    echo json_encode(['success' => false, 'error' => 'Not authenticated']);
    exit();
}

// Check permissions
if (!in_array($_SESSION['role'], ['reviewer', 'administrator'])) {
    http_response_code(403);
    echo json_encode(['success' => false, 'error' => 'Access denied. Reviewer role required.']);
    exit();
}

try {
    $database = new Database();
    $db = $database->getConnection();
    $batchOps = new BatchOperations($db);
    
    $user_id = $_SESSION['user_id'];
    $batch_id = $_POST['batch_id'] ?? '';
    $action = $_POST['action'] ?? '';
    $comment = trim($_POST['comment'] ?? '');
    $reason = trim($_POST['reason'] ?? '');
    
    // Validate input
    if (empty($batch_id) || !in_array($action, ['approve', 'reject'])) {
        throw new Exception('Invalid parameters');
    }
    
    if ($action === 'reject' && empty($reason)) {
        throw new Exception('Rejection reason is required');
    }
    
    // Get batch info
    $batch_info = $batchOps->getBatchInfo($batch_id);
    if (!$batch_info) {
        throw new Exception('Batch not found');
    }
    
    // Verify this is a completed verification batch
    if ($batch_info['operation_type'] !== 'verification' || $batch_info['status'] !== 'completed') {
        throw new Exception('Invalid batch for review');
    }
    
    // Get batch items
    $batch_items = $batchOps->getBatchItems($batch_id);
    if (empty($batch_items)) {
        throw new Exception('No items found in batch');
    }
    
    $db->beginTransaction();
    
    try {
        if ($action === 'approve') {
            // Approve all expenses in the batch
            $new_status = 'success';
            $review_comment = $comment ?: 'Batch approved by reviewer';
            
            foreach ($batch_items as $item) {
                // Update expense status
                $stmt = $db->prepare("
                    UPDATE expenses 
                    SET status = ?, 
                        reviewer_by = ?, 
                        review_date = NOW(),
                        reviewer_batch_id = ?,
                        review_comment = ?
                    WHERE id = ?
                ");
                $stmt->execute([$new_status, $user_id, $batch_id, $review_comment, $item['id']]);
                
                // Log activity
                logActivity(
                    $db,
                    $user_id,
                    'status_change',
                    'expenses',
                    $item['id'],
                    "Approved expense {$item['exno']} via batch {$batch_id}",
                    $_SERVER['REMOTE_ADDR'],
                    $_SERVER['HTTP_USER_AGENT']
                );
                
                // Log batch operation
                $stmt = $db->prepare("
                    CALL LogBatchOperation(?, ?, ?, ?, ?, ?, ?)
                ");
                $stmt->execute([
                    $batch_id,
                    'COMPLETE',
                    $item['id'],
                    $user_id,
                    json_encode(['action' => 'approve', 'status_change' => $new_status]),
                    null,
                    null
                ]);
            }
            
            $message = 'Batch approved successfully';
            
        } else {
            // Reject the batch - return items to verification
            $new_status = 'open'; // Return to open for re-verification
            
            foreach ($batch_items as $item) {
                // Update expense status
                $stmt = $db->prepare("
                    UPDATE expenses 
                    SET status = ?, 
                        reviewer_by = ?, 
                        review_date = NOW(),
                        reviewer_batch_id = ?,
                        review_comment = ?,
                        rejection_reason = ?,
                        rejection_date = NOW()
                    WHERE id = ?
                ");
                $stmt->execute([$new_status, $user_id, $batch_id, $reason, $reason, $item['id']]);
                
                // Clear verification data to allow re-verification
                $stmt = $db->prepare("
                    UPDATE expenses 
                    SET verification_by = NULL,
                        verification_date = NULL,
                        batch_verification_id = NULL
                    WHERE id = ?
                ");
                $stmt->execute([$item['id']]);
                
                // Log activity
                logActivity(
                    $db,
                    $user_id,
                    'status_change',
                    'expenses',
                    $item['id'],
                    "Rejected expense {$item['exno']} via batch {$batch_id}. Reason: {$reason}",
                    $_SERVER['REMOTE_ADDR'],
                    $_SERVER['HTTP_USER_AGENT']
                );
                
                // Log batch operation
                $stmt = $db->prepare("
                    CALL LogBatchOperation(?, ?, ?, ?, ?, ?, ?)
                ");
                $stmt->execute([
                    $batch_id,
                    'ERROR',
                    $item['id'],
                    $user_id,
                    json_encode(['action' => 'reject', 'reason' => $reason, 'status_change' => $new_status]),
                    null,
                    null
                ]);
            }
            
            $message = 'Batch rejected successfully';
        }
        
        // Update batch operation status
        $batch_status = ($action === 'approve') ? 'completed' : 'failed';
        $stmt = $db->prepare("
            UPDATE batch_operations 
            SET status = ?,
                notes = CONCAT(COALESCE(notes, ''), '\n', 'Reviewer decision: ', ?, ' by ', ?, ' at ', NOW())
            WHERE batch_id = ?
        ");
        $stmt->execute([
            $batch_status,
            $action,
            $_SESSION['username'],
            $batch_id
        ]);
        
        // Log system event
        $stmt = $db->prepare("
            CALL LogSystemEvent(?, ?, ?, ?, ?, ?, ?)
        ");
        $stmt->execute([
            'INFO',
            'BATCH_REVIEW',
            "Reviewer batch {$batch_id} {$action}d successfully",
            json_encode([
                'batch_id' => $batch_id,
                'action' => $action,
                'items_count' => count($batch_items),
                'comment' => $action === 'approve' ? $comment : $reason
            ]),
            $user_id,
            $_SERVER['REMOTE_ADDR'],
            $_SERVER['HTTP_USER_AGENT']
        ]);
        
        $db->commit();
        
        echo json_encode([
            'success' => true,
            'message' => $message,
            'batch_id' => $batch_id,
            'action' => $action,
            'items_processed' => count($batch_items)
        ]);
        
    } catch (Exception $e) {
        $db->rollback();
        throw $e;
    }
    
} catch (Exception $e) {
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'error' => $e->getMessage()
    ]);
    
    error_log("Process reviewer batch error: " . $e->getMessage());
}
?>
