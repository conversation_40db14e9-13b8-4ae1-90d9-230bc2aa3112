<?php
/**
 * Final System Check for Production Deployment
 * PHP 8.1.33 + MariaDB 5.5.68 Compatibility
 */

session_start();
require_once 'config/database.php';
require_once 'includes/TransactionHelper.php';

// Mock admin session
$_SESSION['user_id'] = 1;
$_SESSION['role'] = 'administrator';

echo "<h1>🔍 Final System Check for Production</h1>";
echo "<p><strong>Target Environment:</strong> PHP 8.1.33 + MariaDB 5.5.68</p>";

$checks = [];
$critical_issues = [];
$warnings = [];

try {
    $database = new Database();
    $db = $database->getConnection();
    
    // 1. Database Connection
    echo "<h2>1. 📊 Database Connection</h2>";
    $checks['database'] = true;
    echo "<p>✅ Database connection successful</p>";
    
    // 2. PHP Version Compatibility
    echo "<h2>2. 🐘 PHP Compatibility</h2>";
    $php_version = PHP_VERSION;
    echo "<p>Current PHP Version: {$php_version}</p>";
    
    if (version_compare($php_version, '8.1.0', '>=')) {
        $checks['php_version'] = true;
        echo "<p>✅ PHP version compatible with 8.1.33</p>";
    } else {
        $critical_issues[] = "PHP version {$php_version} may not be compatible with production PHP 8.1.33";
        echo "<p>⚠️ PHP version may need testing</p>";
    }
    
    // 3. MariaDB Version Check
    echo "<h2>3. 🗄️ Database Version</h2>";
    $stmt = $db->query("SELECT VERSION() as version");
    $db_version = $stmt->fetchColumn();
    echo "<p>Current Database Version: {$db_version}</p>";
    
    if (strpos($db_version, 'MariaDB') !== false) {
        echo "<p>✅ MariaDB detected</p>";
        $checks['mariadb'] = true;
    } else {
        $warnings[] = "Production uses MariaDB 5.5.68, current is MySQL";
        echo "<p>⚠️ Production uses MariaDB, current is MySQL</p>";
    }
    
    // 4. JSON Functions Compatibility
    echo "<h2>4. 🔧 JSON Functions Compatibility</h2>";
    try {
        $stmt = $db->query("SELECT JSON_LENGTH('[1,2,3]') as test");
        $result = $stmt->fetchColumn();
        if ($result == 3) {
            $warnings[] = "JSON functions work in development but not available in MariaDB 5.5.68";
            echo "<p>⚠️ JSON functions available (will need fallback in production)</p>";
        }
    } catch (Exception $e) {
        $checks['json_compatibility'] = true;
        echo "<p>✅ JSON functions not available (compatible with MariaDB 5.5.68)</p>";
    }
    
    // 5. Core Tables Check
    echo "<h2>5. 📋 Core Tables Structure</h2>";
    $required_tables = [
        'users', 'customers', 'items', 'expenses', 'user_activities',
        'batch_operations', 'batch_documents', 'batch_performance_logs'
    ];
    
    $missing_tables = [];
    foreach ($required_tables as $table) {
        try {
            $stmt = $db->query("SHOW TABLES LIKE '{$table}'");
            if ($stmt->rowCount() > 0) {
                echo "<p>✅ {$table}</p>";
            } else {
                $missing_tables[] = $table;
                echo "<p>❌ {$table} - MISSING</p>";
            }
        } catch (Exception $e) {
            $missing_tables[] = $table;
            echo "<p>❌ {$table} - ERROR: " . $e->getMessage() . "</p>";
        }
    }
    
    if (empty($missing_tables)) {
        $checks['tables'] = true;
    } else {
        $critical_issues[] = "Missing tables: " . implode(', ', $missing_tables);
    }
    
    // 6. File Structure Check
    echo "<h2>6. 📁 File Structure</h2>";
    $required_dirs = [
        'uploads/receipts',
        'uploads/transfer_slips', 
        'uploads/verification_slips',
        'uploads/reviewer_slips',
        'uploads/batch_documents',
        'uploads/bulk_operations',
        'backups'
    ];
    
    $missing_dirs = [];
    foreach ($required_dirs as $dir) {
        if (is_dir($dir)) {
            $files = scandir($dir);
            $file_count = count($files) - 2;
            echo "<p>✅ {$dir} ({$file_count} files)</p>";
        } else {
            $missing_dirs[] = $dir;
            echo "<p>❌ {$dir} - MISSING</p>";
        }
    }
    
    if (empty($missing_dirs)) {
        $checks['directories'] = true;
    } else {
        $critical_issues[] = "Missing directories: " . implode(', ', $missing_dirs);
    }
    
    // 7. Core Functionality Tests
    echo "<h2>7. ⚙️ Core Functionality</h2>";
    
    // Test TransactionHelper
    try {
        $transaction = createTransactionHelper($db);
        $transaction->beginTransaction();
        $transaction->rollback();
        echo "<p>✅ TransactionHelper working</p>";
        $checks['transactions'] = true;
    } catch (Exception $e) {
        $critical_issues[] = "TransactionHelper failed: " . $e->getMessage();
        echo "<p>❌ TransactionHelper failed</p>";
    }
    
    // Test Image Compression
    if (function_exists('imagecreatefromjpeg')) {
        echo "<p>✅ GD Library available for image compression</p>";
        $checks['image_processing'] = true;
    } else {
        $warnings[] = "GD Library not available - image compression disabled";
        echo "<p>⚠️ GD Library not available</p>";
    }
    
    // Test CSV Export
    if (function_exists('fputcsv')) {
        echo "<p>✅ CSV functions available</p>";
        $checks['csv_export'] = true;
    } else {
        $critical_issues[] = "CSV functions not available";
        echo "<p>❌ CSV functions not available</p>";
    }
    
    // 8. Security Check
    echo "<h2>8. 🔒 Security Configuration</h2>";
    
    // Check if setup files exist
    $setup_files = ['setup_production.php', 'compatibility_check.php'];
    $setup_exists = [];
    foreach ($setup_files as $file) {
        if (file_exists($file)) {
            $setup_exists[] = $file;
            echo "<p>⚠️ {$file} exists (should be removed after deployment)</p>";
        } else {
            echo "<p>✅ {$file} not found (good for security)</p>";
        }
    }
    
    if (!empty($setup_exists)) {
        $warnings[] = "Setup files exist: " . implode(', ', $setup_exists) . " (remove after deployment)";
    }
    
    // Check config file
    if (file_exists('config/database.php')) {
        echo "<p>✅ Database config exists</p>";
        $checks['config'] = true;
    } else {
        $critical_issues[] = "Database config missing";
        echo "<p>❌ Database config missing</p>";
    }
    
    // 9. Production Files Check
    echo "<h2>9. 🚀 Production Files</h2>";
    
    $production_files = [
        'PRODUCTION_DEPLOYMENT.md',
        'config/database_production.php',
        'database/batch_operations_schema_mariadb55.sql'
    ];
    
    foreach ($production_files as $file) {
        if (file_exists($file)) {
            echo "<p>✅ {$file}</p>";
        } else {
            $warnings[] = "Production file missing: {$file}";
            echo "<p>⚠️ {$file} - Missing</p>";
        }
    }
    
    // 10. Sample Data Check
    echo "<h2>10. 📊 Sample Data</h2>";
    
    $sample_counts = [];
    $tables_to_check = ['users', 'customers', 'items', 'expenses'];
    
    foreach ($tables_to_check as $table) {
        try {
            $stmt = $db->query("SELECT COUNT(*) FROM {$table}");
            $count = $stmt->fetchColumn();
            $sample_counts[$table] = $count;
            echo "<p>📊 {$table}: {$count} records</p>";
        } catch (Exception $e) {
            echo "<p>❌ {$table}: Error - " . $e->getMessage() . "</p>";
        }
    }
    
    if ($sample_counts['users'] > 0 && $sample_counts['customers'] > 0 && $sample_counts['items'] > 0) {
        $checks['sample_data'] = true;
        echo "<p>✅ Basic sample data available</p>";
    } else {
        $warnings[] = "Insufficient sample data for testing";
        echo "<p>⚠️ Insufficient sample data</p>";
    }
    
} catch (Exception $e) {
    $critical_issues[] = "System check failed: " . $e->getMessage();
    echo "<p>❌ <strong>System check failed:</strong> " . $e->getMessage() . "</p>";
}

// Summary
echo "<h2>📋 Summary</h2>";

$total_checks = count($checks);
$passed_checks = array_sum($checks);

echo "<p><strong>Checks Passed:</strong> {$passed_checks}/{$total_checks}</p>";

if (empty($critical_issues)) {
    echo "<h3>✅ Critical Issues: None</h3>";
} else {
    echo "<h3>❌ Critical Issues (" . count($critical_issues) . "):</h3>";
    echo "<ul>";
    foreach ($critical_issues as $issue) {
        echo "<li style='color: red;'>{$issue}</li>";
    }
    echo "</ul>";
}

if (empty($warnings)) {
    echo "<h3>✅ Warnings: None</h3>";
} else {
    echo "<h3>⚠️ Warnings (" . count($warnings) . "):</h3>";
    echo "<ul>";
    foreach ($warnings as $warning) {
        echo "<li style='color: orange;'>{$warning}</li>";
    }
    echo "</ul>";
}

// Final Recommendation
echo "<h2>🎯 Production Readiness</h2>";

if (empty($critical_issues) && $passed_checks >= 8) {
    echo "<div style='background: #d4edda; border: 1px solid #c3e6cb; padding: 15px; border-radius: 5px;'>";
    echo "<h3 style='color: #155724; margin: 0;'>🚀 READY FOR PRODUCTION</h3>";
    echo "<p style='color: #155724; margin: 5px 0 0 0;'>System passes all critical checks and is ready for deployment.</p>";
    echo "</div>";
} elseif (empty($critical_issues)) {
    echo "<div style='background: #fff3cd; border: 1px solid #ffeaa7; padding: 15px; border-radius: 5px;'>";
    echo "<h3 style='color: #856404; margin: 0;'>⚠️ READY WITH WARNINGS</h3>";
    echo "<p style='color: #856404; margin: 5px 0 0 0;'>System is functional but has some warnings to address.</p>";
    echo "</div>";
} else {
    echo "<div style='background: #f8d7da; border: 1px solid #f5c6cb; padding: 15px; border-radius: 5px;'>";
    echo "<h3 style='color: #721c24; margin: 0;'>❌ NOT READY</h3>";
    echo "<p style='color: #721c24; margin: 5px 0 0 0;'>Critical issues must be resolved before production deployment.</p>";
    echo "</div>";
}

echo "<h3>📚 Deployment Resources:</h3>";
echo "<ul>";
echo "<li><a href='PRODUCTION_DEPLOYMENT.md' target='_blank'>📖 Deployment Guide</a></li>";
echo "<li><a href='setup_production.php' target='_blank'>🛠️ Production Setup</a></li>";
echo "<li><a href='compatibility_check.php' target='_blank'>🔍 Compatibility Check</a></li>";
echo "<li><a href='test_cleanup.php' target='_blank'>🧪 Cleanup Test</a></li>";
echo "</ul>";

echo "<p><small>Generated on: " . date('Y-m-d H:i:s') . "</small></p>";
?>
