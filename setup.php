<?php
/**
 * Database Setup Script
 * Run this file once to set up the database
 */

require_once 'config/database.php';

try {
    // First, try to connect without specifying database to create it
    $host = 'localhost';
    $username = 'root';
    $password = 'root';
    
    $pdo = new PDO("mysql:host=$host", $username, $password);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    // Read and execute the schema file
    $schema = file_get_contents('database/schema.sql');
    
    // Split the schema into individual statements
    $statements = array_filter(array_map('trim', explode(';', $schema)));
    
    foreach ($statements as $statement) {
        if (!empty($statement)) {
            $pdo->exec($statement);
        }
    }
    
    echo "<!DOCTYPE html>
    <html lang='en'>
    <head>
        <meta charset='UTF-8'>
        <meta name='viewport' content='width=device-width, initial-scale=1.0'>
        <title>Setup Complete - Expenses Management System</title>
        <link href='https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css' rel='stylesheet'>
        <style>
            body { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); min-height: 100vh; display: flex; align-items: center; justify-content: center; }
            .setup-card { background: white; border-radius: 15px; box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1); padding: 2rem; max-width: 500px; text-align: center; }
        </style>
    </head>
    <body>
        <div class='setup-card'>
            <div class='text-success mb-4'>
                <i class='fas fa-check-circle' style='font-size: 4rem;'></i>
            </div>
            <h2 class='text-success mb-3'>Setup Complete!</h2>
            <p class='mb-4'>The Expenses Management System has been successfully set up.</p>
            
            <div class='alert alert-info'>
                <h6>Default Admin Credentials:</h6>
                <strong>Username:</strong> admin<br>
                <strong>Password:</strong> admin123
            </div>
            
            <div class='d-grid gap-2'>
                <a href='login.php' class='btn btn-primary btn-lg'>
                    <i class='fas fa-sign-in-alt me-2'></i>Go to Login
                </a>
                <small class='text-muted mt-2'>
                    For security, please delete this setup.php file after setup.
                </small>
            </div>
        </div>
        
        <script src='https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/js/all.min.js'></script>
    </body>
    </html>";
    
} catch (PDOException $e) {
    echo "<!DOCTYPE html>
    <html lang='en'>
    <head>
        <meta charset='UTF-8'>
        <meta name='viewport' content='width=device-width, initial-scale=1.0'>
        <title>Setup Error - Expenses Management System</title>
        <link href='https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css' rel='stylesheet'>
        <style>
            body { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); min-height: 100vh; display: flex; align-items: center; justify-content: center; }
            .setup-card { background: white; border-radius: 15px; box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1); padding: 2rem; max-width: 500px; text-align: center; }
        </style>
    </head>
    <body>
        <div class='setup-card'>
            <div class='text-danger mb-4'>
                <i class='fas fa-exclamation-triangle' style='font-size: 4rem;'></i>
            </div>
            <h2 class='text-danger mb-3'>Setup Error</h2>
            <p class='mb-4'>There was an error setting up the database:</p>
            
            <div class='alert alert-danger'>
                " . htmlspecialchars($e->getMessage()) . "
            </div>
            
            <div class='alert alert-info'>
                <h6>Please check:</h6>
                <ul class='text-start mb-0'>
                    <li>MySQL server is running</li>
                    <li>Database credentials in config/database.php are correct</li>
                    <li>User has permission to create databases</li>
                </ul>
            </div>
            
            <button onclick='location.reload()' class='btn btn-primary'>
                <i class='fas fa-redo me-2'></i>Try Again
            </button>
        </div>
        
        <script src='https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/js/all.min.js'></script>
    </body>
    </html>";
}
?>
