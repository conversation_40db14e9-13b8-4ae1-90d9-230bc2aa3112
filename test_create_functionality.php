<?php
session_start();
require_once 'config/database.php';

// Simulate logged in user for testing
if (!isset($_SESSION['user_id'])) {
    $_SESSION['user_id'] = 1;
    $_SESSION['role'] = 'administrator';
    $_SESSION['username'] = 'test_user';
    $_SESSION['full_name'] = 'Test User';
}

echo "<h2>Test Create Expense Functionality</h2>";

$database = new Database();
$db = $database->getConnection();

// Test 1: Check if required includes exist
echo "<h3>1. Required Files Check:</h3>";
$required_files = [
    'config/database.php',
    'includes/functions.php',
    'includes/ImageUploadHelper.php',
    'includes/ReceiptDeductionManager.php'
];

foreach ($required_files as $file) {
    echo "<p><strong>$file:</strong> " . (file_exists($file) ? '✅ EXISTS' : '❌ MISSING') . "</p>";
}

// Test 2: Check database connection
echo "<h3>2. Database Connection:</h3>";
try {
    $stmt = $db->prepare("SELECT COUNT(*) as count FROM expenses");
    $stmt->execute();
    $result = $stmt->fetch();
    echo "<p>✅ Database connected successfully. Total expenses: {$result['count']}</p>";
} catch (Exception $e) {
    echo "<p>❌ Database connection failed: " . $e->getMessage() . "</p>";
}

// Test 3: Check required functions
echo "<h3>3. Required Functions Check:</h3>";
$required_functions = [
    'generateExpenseNumber',
    'logActivity',
    'redirectWithMessage'
];

foreach ($required_functions as $func) {
    echo "<p><strong>$func():</strong> " . (function_exists($func) ? '✅ EXISTS' : '❌ MISSING') . "</p>";
}

// Test 4: Check required classes
echo "<h3>4. Required Classes Check:</h3>";
$required_classes = [
    'ImageUploadHelper',
    'ReceiptDeductionManager'
];

foreach ($required_classes as $class) {
    echo "<p><strong>$class:</strong> " . (class_exists($class) ? '✅ EXISTS' : '❌ MISSING') . "</p>";
}

// Test 5: Check upload directories
echo "<h3>5. Upload Directories Check:</h3>";
$upload_dirs = [
    'uploads/',
    'uploads/transfer_slips/',
    'uploads/receipts/',
    'uploads/deductions/',
    'uploads/verification_slips/',
    'uploads/review_slips/'
];

foreach ($upload_dirs as $dir) {
    $exists = is_dir($dir);
    $writable = is_writable($dir);
    echo "<p><strong>$dir:</strong> ";
    echo ($exists ? '✅ EXISTS' : '❌ MISSING');
    if ($exists) {
        echo " | " . ($writable ? '✅ WRITABLE' : '❌ NOT WRITABLE');
    }
    echo "</p>";
}

// Test 6: Check form processing logic
echo "<h3>6. Form Processing Logic Test:</h3>";

// Simulate a simple form submission
$_POST = [
    'job_open_date' => date('Y-m-d'),
    'withdrawal_date' => date('Y-m-d'),
    'transfer_amount' => '1000.00',
    'bookingno' => 'TEST-' . time(),
    'item_id' => '1',
    'customer_id' => '1'
];

echo "<p>Simulating form data:</p>";
echo "<ul>";
foreach ($_POST as $key => $value) {
    echo "<li><strong>$key:</strong> $value</li>";
}
echo "</ul>";

// Test expense number generation
if (function_exists('generateExpenseNumber')) {
    try {
        $expense_number = generateExpenseNumber($db);
        echo "<p>✅ Expense number generation: {$expense_number['exno']}</p>";
    } catch (Exception $e) {
        echo "<p>❌ Expense number generation failed: " . $e->getMessage() . "</p>";
    }
} else {
    echo "<p>❌ generateExpenseNumber function not found</p>";
}

// Test 7: Check recent expenses
echo "<h3>7. Recent Expenses Check:</h3>";
try {
    $stmt = $db->prepare("SELECT id, exno, created_at, created_by FROM expenses ORDER BY created_at DESC LIMIT 5");
    $stmt->execute();
    $recent_expenses = $stmt->fetchAll();
    
    if ($recent_expenses) {
        echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
        echo "<tr><th>ID</th><th>Expense No</th><th>Created At</th><th>Created By</th></tr>";
        foreach ($recent_expenses as $expense) {
            echo "<tr>";
            echo "<td>{$expense['id']}</td>";
            echo "<td>{$expense['exno']}</td>";
            echo "<td>{$expense['created_at']}</td>";
            echo "<td>{$expense['created_by']}</td>";
            echo "</tr>";
        }
        echo "</table>";
    } else {
        echo "<p>No expenses found</p>";
    }
} catch (Exception $e) {
    echo "<p>❌ Error fetching recent expenses: " . $e->getMessage() . "</p>";
}

// Test 8: Check validation logic
echo "<h3>8. Validation Logic Test:</h3>";

// Test date validation
$test_dates = [
    'valid_date' => date('Y-m-d'),
    'invalid_date' => 'invalid-date',
    'old_date' => date('Y-m-d', strtotime('-2 months'))
];

foreach ($test_dates as $label => $date) {
    echo "<p><strong>$label ($date):</strong> ";
    
    if (!strtotime($date)) {
        echo "❌ Invalid date format";
    } else {
        $one_month_ago = date('Y-m-d', strtotime('-1 month'));
        if ($date < $one_month_ago) {
            echo "❌ Date too old (more than 1 month)";
        } else {
            echo "✅ Valid date";
        }
    }
    echo "</p>";
}

// Test 9: Check JavaScript files
echo "<h3>9. JavaScript Files Check:</h3>";
$js_files = [
    'assets/js/expense-form.js',
    'assets/js/per-receipt-deductions.js',
    'assets/js/image-viewer.js'
];

foreach ($js_files as $file) {
    echo "<p><strong>$file:</strong> " . (file_exists($file) ? '✅ EXISTS' : '❌ MISSING') . "</p>";
}

// Test 10: Check API endpoints
echo "<h3>10. API Endpoints Check:</h3>";
$api_files = [
    'api/upload_deduction_image.php',
    'api/view_file.php'
];

foreach ($api_files as $file) {
    echo "<p><strong>$file:</strong> " . (file_exists($file) ? '✅ EXISTS' : '❌ MISSING') . "</p>";
}

// Test 11: Check session and permissions
echo "<h3>11. Session and Permissions:</h3>";
echo "<ul>";
echo "<li><strong>User ID:</strong> " . ($_SESSION['user_id'] ?? 'NOT SET') . "</li>";
echo "<li><strong>Role:</strong> " . ($_SESSION['role'] ?? 'NOT SET') . "</li>";
echo "<li><strong>Can create expenses:</strong> " . ($_SESSION['role'] !== 'report_viewer' ? '✅ YES' : '❌ NO') . "</li>";
echo "</ul>";

// Test 12: Check for any recent errors
echo "<h3>12. Recent Error Logs:</h3>";
$error_log_file = ini_get('error_log');
if ($error_log_file && file_exists($error_log_file)) {
    $recent_errors = array_slice(file($error_log_file), -10);
    if ($recent_errors) {
        echo "<div style='background: #f8f9fa; padding: 10px; border: 1px solid #dee2e6; max-height: 200px; overflow-y: auto;'>";
        foreach ($recent_errors as $error) {
            echo "<p style='margin: 0; font-size: 12px;'>" . htmlspecialchars(trim($error)) . "</p>";
        }
        echo "</div>";
    } else {
        echo "<p>No recent errors found</p>";
    }
} else {
    echo "<p>Error log file not accessible</p>";
}

// Clean up test data
unset($_POST);
?>

<h3>Summary:</h3>
<p>This test checks the core functionality of the expense creation system. If any items show ❌, those need to be addressed for proper functionality.</p>

<p><a href="expenses/create.php" target="_blank">Test Create Expense Form</a></p>
