# 🚀 Production Deployment Guide

## 📋 Server Requirements

### ✅ Confirmed Compatible Environment
- **PHP:** 8.1.33
- **Database:** MariaDB 5.5.68
- **Web Server:** Apache/Nginx
- **Operating System:** Linux/Windows

### 🔧 Required PHP Extensions
- `pdo`
- `pdo_mysql`
- `gd` (for image processing)
- `json`
- `mbstring`
- `fileinfo`

## 🛠️ Pre-Deployment Checklist

### 1. Database Preparation
```sql
-- Create database
CREATE DATABASE expenses_system CHARACTER SET utf8 COLLATE utf8_unicode_ci;

-- Create database user
CREATE USER 'expenses_user'@'localhost' IDENTIFIED BY 'secure_password';
GRANT ALL PRIVILEGES ON expenses_system.* TO 'expenses_user'@'localhost';
FLUSH PRIVILEGES;
```

### 2. File Upload Configuration
Update `php.ini`:
```ini
upload_max_filesize = 10M
post_max_size = 10M
max_execution_time = 300
memory_limit = 256M
```

### 3. Directory Permissions
```bash
chmod 755 uploads/
chmod 755 uploads/transfer_slips/
chmod 755 uploads/receipts/
chmod 755 uploads/verification_slips/
chmod 755 uploads/reviewer_slips/
chmod 755 uploads/bulk_operations/
```

## 🚀 Deployment Steps

### Step 1: Upload Files
1. Upload all files to web server directory
2. Exclude development files:
   - `compatibility_check.php`
   - `setup.php` (development version)
   - `.git/` directory
   - `README.md` (development)

### Step 2: Configure Database
1. Copy `config/database_production.php` to `config/database.php`
2. Update database credentials:
```php
private $host = 'your_db_host';
private $db_name = 'expenses_system';
private $username = 'expenses_user';
private $password = 'your_secure_password';
```

### Step 3: Run Production Setup
1. Navigate to: `https://yourdomain.com/setup_production.php`
2. Click "Start Production Setup"
3. Wait for completion
4. **Delete `setup_production.php` file immediately**

### Step 4: Security Configuration
1. Set proper file permissions:
```bash
find . -type f -exec chmod 644 {} \;
find . -type d -exec chmod 755 {} \;
chmod 600 config/database.php
```

2. Configure web server security headers
3. Set up SSL certificate
4. Configure firewall rules

## 🔍 Compatibility Fixes Applied

### 1. JSON Functions Replacement
- **Issue:** MariaDB 5.5.68 doesn't support `JSON_LENGTH()`
- **Fix:** Replaced with string manipulation in `search/advanced.php`
```php
// Old (incompatible)
JSON_LENGTH(e.receipt_images)

// New (compatible)
(LENGTH(e.receipt_images) - LENGTH(REPLACE(e.receipt_images, ',', '')) + 1)
```

### 2. JSON Column Types
- **Issue:** JSON column type not available
- **Fix:** Use TEXT columns for JSON data
```sql
-- Old (incompatible)
workflow_history JSON NULL

-- New (compatible)
workflow_history TEXT NULL
```

### 3. Stored Procedures Removed
- **Issue:** JSON functions in stored procedures not supported
- **Fix:** Removed stored procedures from `batch_operations_schema_mariadb55.sql`

## 🧪 Testing Checklist

### After Deployment, Test:
- [ ] Login functionality
- [ ] Create new expense
- [ ] Upload files (transfer slips, receipts)
- [ ] Verification workflow
- [ ] Review workflow
- [ ] Batch operations
- [ ] Reports generation
- [ ] CSV exports
- [ ] Image viewing
- [ ] Search functionality
- [ ] Admin functions

### Performance Tests:
- [ ] Page load times < 3 seconds
- [ ] File uploads work properly
- [ ] Database queries execute efficiently
- [ ] Memory usage within limits

## 🔒 Security Considerations

### 1. File Security
- Remove setup files after deployment
- Set restrictive file permissions
- Configure web server to prevent direct access to sensitive files

### 2. Database Security
- Use strong passwords
- Limit database user privileges
- Enable SSL for database connections
- Regular security updates

### 3. Application Security
- Enable HTTPS
- Configure security headers
- Regular backup procedures
- Monitor error logs

## 📊 Monitoring & Maintenance

### 1. Log Files to Monitor
- PHP error logs
- Web server access/error logs
- Application activity logs
- Database slow query logs

### 2. Regular Maintenance
- Database backups (daily)
- File system backups (weekly)
- Security updates (monthly)
- Performance monitoring (ongoing)

### 3. Backup Strategy
```bash
# Database backup
mysqldump -u expenses_user -p expenses_system > backup_$(date +%Y%m%d).sql

# File backup
tar -czf files_backup_$(date +%Y%m%d).tar.gz uploads/
```

## 🆘 Troubleshooting

### Common Issues:

**1. Database Connection Failed**
- Check credentials in `config/database.php`
- Verify database server is running
- Check firewall settings

**2. File Upload Errors**
- Check PHP upload settings
- Verify directory permissions
- Check disk space

**3. Permission Denied Errors**
- Check file/directory permissions
- Verify web server user ownership
- Check SELinux settings (if applicable)

**4. Slow Performance**
- Check database indexes
- Monitor server resources
- Optimize images
- Enable caching

## 📞 Support

### Production Environment Info:
- **PHP Version:** 8.1.33
- **MariaDB Version:** 5.5.68
- **Compatibility Mode:** Enabled
- **JSON Functions:** Replaced with alternatives

### Emergency Contacts:
- System Administrator: [contact info]
- Database Administrator: [contact info]
- Development Team: [contact info]

---

## ✅ Deployment Complete!

Your Expenses Management System is now ready for production use with full compatibility for PHP 8.1.33 and MariaDB 5.5.68.

**Remember to:**
1. Delete setup files
2. Set up regular backups
3. Monitor system performance
4. Keep security updates current
