<?php
/**
 * Fix Development Issues for Production Readiness
 * Auto-fix script for common development environment issues
 */

session_start();
require_once 'config/database.php';

// Mock admin session
$_SESSION['user_id'] = 1;
$_SESSION['role'] = 'administrator';

echo "<h1>🔧 Fix Development Issues</h1>";
echo "<p>This script will fix common issues found in development environment to prepare for production.</p>";

$fixes_applied = [];
$errors = [];

try {
    $database = new Database();
    $db = $database->getConnection();
    
    echo "<h2>🔍 Analyzing Issues...</h2>";
    
    // Fix 1: Extend exno column length
    echo "<h3>1. Fixing EXNO Column Length</h3>";
    try {
        // Check current exno length
        $stmt = $db->query("SHOW COLUMNS FROM expenses LIKE 'exno'");
        $column = $stmt->fetch();
        
        if ($column && strpos($column['Type'], 'varchar(15)') !== false) {
            echo "<p>⚠️ Current exno type: {$column['Type']} (too short)</p>";
            
            $db->exec("ALTER TABLE expenses MODIFY COLUMN exno VARCHAR(50) NOT NULL COMMENT 'Expense number - extended for production'");
            echo "<p>✅ Extended exno to VARCHAR(50)</p>";
            $fixes_applied[] = "Extended exno column to VARCHAR(50)";
        } else {
            echo "<p>✅ exno column length is adequate</p>";
        }
    } catch (Exception $e) {
        $error = "Failed to fix exno column: " . $e->getMessage();
        echo "<p>❌ {$error}</p>";
        $errors[] = $error;
    }
    
    // Fix 2: Add workflow_history column as TEXT
    echo "<h3>2. Adding/Fixing workflow_history Column</h3>";
    try {
        // Check if workflow_history exists
        $stmt = $db->query("SHOW COLUMNS FROM expenses LIKE 'workflow_history'");
        $column = $stmt->fetch();
        
        if (!$column) {
            $db->exec("ALTER TABLE expenses ADD COLUMN workflow_history TEXT DEFAULT NULL COMMENT 'Workflow history as JSON string (MariaDB 5.5.68 compatible)'");
            echo "<p>✅ Added workflow_history column as TEXT</p>";
            $fixes_applied[] = "Added workflow_history column";
        } else {
            echo "<p>✅ workflow_history column exists: {$column['Type']}</p>";
            
            // If it's JSON type, note that it needs to be TEXT for production
            if (strpos(strtolower($column['Type']), 'json') !== false) {
                echo "<p>⚠️ Note: workflow_history is JSON type. In production (MariaDB 5.5.68), this will be TEXT</p>";
            }
        }
    } catch (Exception $e) {
        $error = "Failed to add workflow_history column: " . $e->getMessage();
        echo "<p>❌ {$error}</p>";
        $errors[] = $error;
    }
    
    // Fix 3: Create missing upload directories
    echo "<h3>3. Creating Missing Upload Directories</h3>";
    $required_dirs = [
        'uploads/receipts',
        'uploads/transfer_slips',
        'uploads/verification_slips',
        'uploads/review_slips',
        'uploads/batch_documents',
        'uploads/batch_documents/verification',
        'uploads/batch_documents/review',
        'uploads/bulk_operations',
        'backups'
    ];
    
    $created_dirs = [];
    foreach ($required_dirs as $dir) {
        if (!is_dir($dir)) {
            if (mkdir($dir, 0755, true)) {
                echo "<p>✅ Created directory: {$dir}</p>";
                $created_dirs[] = $dir;
            } else {
                $error = "Failed to create directory: {$dir}";
                echo "<p>❌ {$error}</p>";
                $errors[] = $error;
            }
        } else {
            echo "<p>✅ Directory exists: {$dir}</p>";
        }
    }
    
    if (!empty($created_dirs)) {
        $fixes_applied[] = "Created directories: " . implode(', ', $created_dirs);
    }
    
    // Fix 4: Rename reviewer_slips to review_slips if needed
    echo "<h3>4. Fixing Directory Names</h3>";
    if (is_dir('uploads/reviewer_slips') && !is_dir('uploads/review_slips')) {
        if (rename('uploads/reviewer_slips', 'uploads/review_slips')) {
            echo "<p>✅ Renamed uploads/reviewer_slips to uploads/review_slips</p>";
            $fixes_applied[] = "Renamed reviewer_slips directory to review_slips";
        } else {
            $error = "Failed to rename uploads/reviewer_slips to uploads/review_slips";
            echo "<p>❌ {$error}</p>";
            $errors[] = $error;
        }
    } else {
        echo "<p>✅ Directory naming is correct</p>";
    }
    
    // Fix 5: Add workflow-related columns for MariaDB 5.5.68 compatibility
    echo "<h3>5. Adding Workflow Columns for MariaDB 5.5.68</h3>";
    $workflow_columns = [
        'rejection_reason' => 'TEXT DEFAULT NULL COMMENT "Reason for rejection"',
        'return_reason' => 'TEXT DEFAULT NULL COMMENT "Reason for return"',
        'rejected_by' => 'INT DEFAULT NULL COMMENT "User who rejected"',
        'returned_by' => 'INT DEFAULT NULL COMMENT "User who returned"',
        'rejected_at' => 'TIMESTAMP NULL DEFAULT NULL COMMENT "When rejected"',
        'returned_at' => 'TIMESTAMP NULL DEFAULT NULL COMMENT "When returned"'
    ];
    
    foreach ($workflow_columns as $column_name => $column_def) {
        try {
            $stmt = $db->query("SHOW COLUMNS FROM expenses LIKE '{$column_name}'");
            if ($stmt->rowCount() == 0) {
                $db->exec("ALTER TABLE expenses ADD COLUMN {$column_name} {$column_def}");
                echo "<p>✅ Added column: {$column_name}</p>";
                $fixes_applied[] = "Added {$column_name} column";
            } else {
                echo "<p>✅ Column exists: {$column_name}</p>";
            }
        } catch (Exception $e) {
            $error = "Failed to add {$column_name} column: " . $e->getMessage();
            echo "<p>❌ {$error}</p>";
            $errors[] = $error;
        }
    }
    
    // Fix 6: Clean up overly long test expense numbers
    echo "<h3>6. Cleaning Up Long Test Expense Numbers</h3>";
    try {
        $stmt = $db->query("SELECT COUNT(*) FROM expenses WHERE CHAR_LENGTH(exno) > 15");
        $long_count = $stmt->fetchColumn();
        
        if ($long_count > 0) {
            echo "<p>⚠️ Found {$long_count} expenses with exno longer than 15 characters</p>";
            
            // Update long test expense numbers
            $stmt = $db->prepare("UPDATE expenses SET exno = CONCAT('EX', id) WHERE CHAR_LENGTH(exno) > 15 AND (exno LIKE 'PROD_TEST_%' OR exno LIKE 'TEST_%')");
            $stmt->execute();
            $updated = $stmt->rowCount();
            
            if ($updated > 0) {
                echo "<p>✅ Updated {$updated} test expense numbers to shorter format</p>";
                $fixes_applied[] = "Shortened {$updated} test expense numbers";
            }
        } else {
            echo "<p>✅ All expense numbers are appropriate length</p>";
        }
    } catch (Exception $e) {
        $error = "Failed to clean up expense numbers: " . $e->getMessage();
        echo "<p>❌ {$error}</p>";
        $errors[] = $error;
    }
    
    // Fix 7: Set proper file permissions
    echo "<h3>7. Setting File Permissions</h3>";
    try {
        $upload_dirs = ['uploads', 'backups'];
        foreach ($upload_dirs as $dir) {
            if (is_dir($dir)) {
                chmod($dir, 0755);
                echo "<p>✅ Set permissions for {$dir}</p>";
            }
        }
        $fixes_applied[] = "Set proper file permissions";
    } catch (Exception $e) {
        $error = "Failed to set file permissions: " . $e->getMessage();
        echo "<p>⚠️ {$error}</p>";
    }
    
} catch (Exception $e) {
    $error = "Critical error: " . $e->getMessage();
    echo "<p>❌ <strong>{$error}</strong></p>";
    $errors[] = $error;
}

// Summary
echo "<h2>📋 Summary</h2>";

if (!empty($fixes_applied)) {
    echo "<h3>✅ Fixes Applied (" . count($fixes_applied) . "):</h3>";
    echo "<ul>";
    foreach ($fixes_applied as $fix) {
        echo "<li style='color: green;'>{$fix}</li>";
    }
    echo "</ul>";
}

if (!empty($errors)) {
    echo "<h3>❌ Errors (" . count($errors) . "):</h3>";
    echo "<ul>";
    foreach ($errors as $error) {
        echo "<li style='color: red;'>{$error}</li>";
    }
    echo "</ul>";
}

if (empty($errors)) {
    echo "<div style='background: #d4edda; border: 1px solid #c3e6cb; padding: 20px; border-radius: 5px; margin: 20px 0;'>";
    echo "<h3 style='color: #155724; margin: 0;'>🎉 All Fixes Applied Successfully!</h3>";
    echo "<p style='color: #155724; margin: 10px 0 0 0;'>Development environment is now ready for production deployment.</p>";
    echo "</div>";
} else {
    echo "<div style='background: #fff3cd; border: 1px solid #ffeaa7; padding: 20px; border-radius: 5px; margin: 20px 0;'>";
    echo "<h3 style='color: #856404; margin: 0;'>⚠️ Some Issues Remain</h3>";
    echo "<p style='color: #856404; margin: 10px 0 0 0;'>Please review the errors above and fix manually if needed.</p>";
    echo "</div>";
}

echo "<h3>🔗 Next Steps:</h3>";
echo "<ul>";
echo "<li><a href='production_readiness_test.php'>🧪 Run Production Readiness Test</a></li>";
echo "<li><a href='check_database_structure.php'>🔍 Check Database Structure</a></li>";
echo "<li><a href='final_system_check.php'>📋 Final System Check</a></li>";
echo "</ul>";

echo "<p><small>Fixes applied on: " . date('Y-m-d H:i:s') . "</small></p>";
?>
