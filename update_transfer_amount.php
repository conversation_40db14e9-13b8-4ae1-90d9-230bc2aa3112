<?php
require_once 'config/database.php';

try {
    $database = new Database();
    $db = $database->getConnection();
    
    echo "<h2>Adding transfer_amount field to expenses table...</h2>";
    
    // Read and execute the SQL file
    $sql = file_get_contents('database/add_transfer_amount.sql');
    
    if ($sql === false) {
        throw new Exception("Could not read SQL file");
    }
    
    // Check if transfer_amount column already exists
    $stmt = $db->prepare("
        SELECT COUNT(*) as count
        FROM INFORMATION_SCHEMA.COLUMNS
        WHERE table_name = 'expenses'
        AND table_schema = DATABASE()
        AND column_name = 'transfer_amount'
    ");
    $stmt->execute();
    $result = $stmt->fetch();

    if ($result['count'] > 0) {
        echo "<p style='color: orange;'>⚠️ transfer_amount column already exists, skipping...</p>";
    } else {
        echo "<p>Adding transfer_amount column...</p>";
        $db->exec("ALTER TABLE expenses ADD COLUMN transfer_amount DECIMAL(15,2) DEFAULT 0.00 AFTER transfer_no");
        echo "<p style='color: green;'>✓ transfer_amount column added successfully</p>";
    }

    // Update existing records
    echo "<p>Updating existing records...</p>";
    $db->exec("UPDATE expenses SET transfer_amount = 0.00 WHERE transfer_amount IS NULL");
    echo "<p style='color: green;'>✓ Existing records updated</p>";
    
    echo "<h3 style='color: green;'>✅ Database updated successfully!</h3>";
    echo "<p>The transfer_amount field has been added to the expenses table.</p>";
    echo "<p><strong>Next steps:</strong></p>";
    echo "<ul>";
    echo "<li>You can now create expenses with transfer amounts</li>";
    echo "<li>The system will compare transfer amounts with receipt totals</li>";
    echo "<li>Delete this file (update_transfer_amount.php) after use</li>";
    echo "</ul>";
    
} catch (Exception $e) {
    echo "<h3 style='color: red;'>❌ Error updating database:</h3>";
    echo "<p style='color: red;'>" . htmlspecialchars($e->getMessage()) . "</p>";
    echo "<p>Please check your database connection and try again.</p>";
}
?>
<!DOCTYPE html>
<html>
<head>
    <title>Database Update - Transfer Amount</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 40px; }
        h2, h3 { color: #333; }
        p { margin: 10px 0; }
        ul { margin: 10px 0 10px 20px; }
    </style>
</head>
<body>
    <a href="index.php">← Back to Dashboard</a>
</body>
</html>
