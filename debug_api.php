<?php
/**
 * Debug API view_file.php
 * ทดสอบ API โดยตรงเพื่อหา error
 * ลบไฟล์นี้หลังจากทดสอบเสร็จแล้ว
 */
session_start();
require_once 'config/database.php';

// Check if user is logged in
if (!isset($_SESSION['user_id'])) {
    echo "❌ User not logged in\n";
    exit();
}

// Test parameters
$test_file = 'batch_documents/verification/BATCH_VER_20251019_224656_verification_68f56a8acd39c_1760914058.png';
$test_type = 'batch_document';

echo "<h2>🔍 Debug API view_file.php</h2>\n";
echo "<pre>\n";

echo "=== BASIC INFO ===\n";
echo "User ID: " . $_SESSION['user_id'] . "\n";
echo "User Role: " . ($_SESSION['role'] ?? 'unknown') . "\n";
echo "Test File: " . $test_file . "\n";
echo "Test Type: " . $test_type . "\n";
echo "\n";

echo "=== PATH TESTING ===\n";
$upload_dir = dirname(__FILE__) . '/uploads/';
echo "Upload Dir: " . $upload_dir . "\n";
echo "Upload Dir Exists: " . (is_dir($upload_dir) ? 'YES' : 'NO') . "\n";

// Sanitize filename like in the API
$file = $test_file;
$file = str_replace(['../', '../', '..\\'], '', $file);
echo "Sanitized File: " . $file . "\n";

// Test path resolution
$possible_paths = [];

if (strpos($file, '/') !== false || strpos($file, '\\') !== false) {
    $possible_paths[] = $upload_dir . $file;
    
    $filename_only = basename($file);
    $possible_paths[] = $upload_dir . 'batch_documents/' . $filename_only;
    $possible_paths[] = $upload_dir . 'batch_documents/verification/' . $filename_only;
    $possible_paths[] = $upload_dir . 'batch_documents/review/' . $filename_only;
} else {
    $possible_paths[] = $upload_dir . $file;
    $possible_paths[] = $upload_dir . 'batch_documents/' . $file;
    $possible_paths[] = $upload_dir . 'batch_documents/verification/' . $file;
    $possible_paths[] = $upload_dir . 'batch_documents/review/' . $file;
}

$possible_paths[] = $upload_dir . 'bulk_operations/' . basename($file);

echo "\n=== PATH RESOLUTION ===\n";
$file_path = null;
foreach ($possible_paths as $index => $path) {
    $exists = file_exists($path);
    echo ($index + 1) . ". " . $path . " - " . ($exists ? "✅ EXISTS" : "❌ MISSING") . "\n";
    if ($exists && !$file_path) {
        $file_path = $path;
        echo "   → SELECTED THIS PATH\n";
    }
}

if (!$file_path) {
    echo "\n❌ NO VALID PATH FOUND!\n";
    echo "</pre>";
    exit();
}

echo "\n=== FILE INFO ===\n";
echo "Selected Path: " . $file_path . "\n";
echo "File Size: " . number_format(filesize($file_path) / 1024, 2) . " KB\n";
echo "File Readable: " . (is_readable($file_path) ? 'YES' : 'NO') . "\n";
echo "File Type: " . mime_content_type($file_path) . "\n";

echo "\n=== DATABASE CHECK ===\n";
try {
    $database = new Database();
    $db = $database->getConnection();
    echo "Database Connection: ✅ OK\n";
    
    // Check batch_documents table
    $filename_only = basename($file);
    $stmt = $db->prepare("
        SELECT bd.batch_id as id, bo.created_by
        FROM batch_documents bd
        JOIN batch_operations bo ON bd.batch_id = bo.batch_id
        WHERE bd.file_path = ? OR bd.file_path LIKE ? OR bd.file_path LIKE ?
    ");
    $stmt->execute([$file, '%' . $filename_only, '%' . $file]);
    $batch_doc = $stmt->fetch();
    
    if ($batch_doc) {
        echo "Batch Document Found: ✅ YES\n";
        echo "Batch ID: " . $batch_doc['id'] . "\n";
        echo "Created By: " . $batch_doc['created_by'] . "\n";
    } else {
        echo "Batch Document Found: ❌ NO\n";
        echo "Will use default access control\n";
    }
    
} catch (Exception $e) {
    echo "Database Error: ❌ " . $e->getMessage() . "\n";
}

echo "\n=== ACCESS CONTROL ===\n";
$user_id = $_SESSION['user_id'];
$user_role = $_SESSION['role'] ?? 'data_entry';
echo "User Role: " . $user_role . "\n";

$has_access = false;
if ($user_role === 'administrator') {
    $has_access = true;
    echo "Access: ✅ Administrator - Full Access\n";
} elseif ($user_role === 'data_entry') {
    if (isset($batch_doc)) {
        $has_access = ($batch_doc['created_by'] == $user_id);
        echo "Access: " . ($has_access ? '✅' : '❌') . " Data Entry - " . ($has_access ? 'Own File' : 'Not Own File') . "\n";
    } else {
        $has_access = false;
        echo "Access: ❌ Data Entry - No Batch Document Found\n";
    }
} else {
    $has_access = true;
    echo "Access: ✅ Verification/Reviewer - Full Access\n";
}

if (!$has_access) {
    echo "\n❌ ACCESS DENIED!\n";
    echo "</pre>";
    exit();
}

echo "\n=== CONTENT TYPE ===\n";
$file_info = pathinfo($file_path);
$file_extension = strtolower($file_info['extension']);
echo "File Extension: " . $file_extension . "\n";

$content_types = [
    'jpg' => 'image/jpeg',
    'jpeg' => 'image/jpeg',
    'png' => 'image/png',
    'gif' => 'image/gif',
    'pdf' => 'application/pdf'
];

$content_type = $content_types[$file_extension] ?? 'application/octet-stream';
echo "Content Type: " . $content_type . "\n";

echo "\n=== FINAL TEST ===\n";
echo "✅ All checks passed!\n";
echo "File should be accessible via API\n";
echo "\n";

echo "=== TEST LINKS ===\n";
$api_url = "api/view_file.php?file=" . urlencode($test_file) . "&type=" . urlencode($test_type);
echo "API URL: <a href='" . $api_url . "' target='_blank'>" . $api_url . "</a>\n";

$direct_url = "uploads/" . $file;
echo "Direct URL: <a href='" . $direct_url . "' target='_blank'>" . $direct_url . "</a>\n";

echo "</pre>\n";

echo "<hr>\n";
echo "<h3>🖼️ Image Test</h3>\n";
echo "<p><strong>Via API:</strong></p>\n";
echo "<img src='" . $api_url . "' style='max-width: 300px; border: 2px solid #ddd; margin: 10px;' onerror=\"this.style.display='none'; this.nextElementSibling.style.display='block';\">\n";
echo "<div style='display:none; color: red; font-weight: bold;'>❌ Failed to load via API</div>\n";

echo "<p><strong>Direct Access:</strong></p>\n";
echo "<img src='" . $direct_url . "' style='max-width: 300px; border: 2px solid #ddd; margin: 10px;' onerror=\"this.style.display='none'; this.nextElementSibling.style.display='block';\">\n";
echo "<div style='display:none; color: red; font-weight: bold;'>❌ Failed to load directly</div>\n";

echo "<hr>\n";
echo "<p><strong>⚠️ ลบไฟล์นี้ (debug_api.php) หลังจากทดสอบเสร็จแล้ว!</strong></p>\n";
echo "<p><a href='test_specific_batch.php'>← Back to Specific Test</a> | <a href='expenses/'>Expenses List</a></p>\n";
?>
