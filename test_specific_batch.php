<?php
/**
 * Test Specific Batch Document Path
 * ทดสอบ path เฉพาะที่พบปัญหา
 * ลบไฟล์นี้หลังจากทดสอบเสร็จแล้ว
 */
session_start();
require_once 'config/database.php';

// Check if user is logged in
if (!isset($_SESSION['user_id'])) {
    header('Location: index.php');
    exit();
}

// Test the specific file path you mentioned
$test_file = 'batch_documents/review/BATCH_REV_20251019_225230_review_68f56bfcec82f_1760914428.jpg';
$expected_path = 'uploads/batch_documents/review/BATCH_REV_20251019_225230_review_68f56bfcec82f_1760914428.jpg';

?>
<!DOCTYPE html>
<html lang="th">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Specific Batch Path - Expenses System</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        .test-image { max-width: 300px; max-height: 200px; margin: 10px; border: 2px solid #ddd; }
        .status-ok { color: #28a745; }
        .status-error { color: #dc3545; }
        .code-block { background: #f8f9fa; padding: 10px; border-radius: 5px; font-family: monospace; }
    </style>
</head>
<body class="bg-light">
    <div class="container mt-4">
        <div class="row justify-content-center">
            <div class="col-md-10">
                <div class="card shadow">
                    <div class="card-header bg-primary text-white">
                        <h3><i class="fas fa-bug me-2"></i>Test Specific Batch Document Path</h3>
                        <p class="mb-0">ทดสอบ path เฉพาะที่พบปัญหา</p>
                    </div>
                    <div class="card-body">
                        
                        <h4><i class="fas fa-info-circle me-2"></i>Problem Analysis</h4>
                        <div class="row">
                            <div class="col-md-6">
                                <h6>🔍 URL Parameter:</h6>
                                <div class="code-block">
                                    file=<?php echo htmlspecialchars($test_file); ?>
                                </div>
                                <p><small>URL Encoded: <code><?php echo urlencode($test_file); ?></code></small></p>
                            </div>
                            <div class="col-md-6">
                                <h6>📁 Expected File Path:</h6>
                                <div class="code-block">
                                    <?php echo htmlspecialchars($expected_path); ?>
                                </div>
                                <p>
                                    <?php if (file_exists($expected_path)): ?>
                                        <span class="status-ok"><i class="fas fa-check"></i> File exists</span>
                                        <br><small><?php echo number_format(filesize($expected_path) / 1024, 1); ?> KB</small>
                                    <?php else: ?>
                                        <span class="status-error"><i class="fas fa-times"></i> File not found</span>
                                    <?php endif; ?>
                                </p>
                            </div>
                        </div>

                        <hr>
                        <h4><i class="fas fa-cogs me-2"></i>Path Resolution Test</h4>
                        
                        <?php
                        // Simulate the logic from api/view_file.php
                        $upload_dir = 'uploads/';
                        $file = $test_file;
                        
                        // Remove directory traversal
                        $file = str_replace(['../', '../', '..\\'], '', $file);
                        
                        $possible_paths = [];
                        
                        // If file already contains path
                        if (strpos($file, '/') !== false || strpos($file, '\\') !== false) {
                            $possible_paths[] = $upload_dir . $file; // uploads/batch_documents/review/filename.jpg
                            $possible_paths[] = '../' . $file; // ../batch_documents/review/filename.jpg
                            
                            // Extract just the filename for fallback searches
                            $filename_only = basename($file);
                            $possible_paths[] = $upload_dir . 'batch_documents/' . $filename_only;
                            $possible_paths[] = $upload_dir . 'batch_documents/verification/' . $filename_only;
                            $possible_paths[] = $upload_dir . 'batch_documents/review/' . $filename_only;
                        } else {
                            // If file is just filename, try all possible locations
                            $possible_paths[] = $upload_dir . $file;
                            $possible_paths[] = $upload_dir . 'batch_documents/' . $file;
                            $possible_paths[] = $upload_dir . 'batch_documents/verification/' . $file;
                            $possible_paths[] = $upload_dir . 'batch_documents/review/' . $file;
                            $possible_paths[] = '../' . $file;
                        }
                        
                        // Add fallback paths
                        $possible_paths[] = $upload_dir . 'bulk_operations/' . basename($file);
                        ?>
                        
                        <div class="table-responsive">
                            <table class="table table-striped">
                                <thead>
                                    <tr>
                                        <th>#</th>
                                        <th>Possible Path</th>
                                        <th>Status</th>
                                        <th>Size</th>
                                        <th>Test</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($possible_paths as $index => $path): ?>
                                        <tr>
                                            <td><?php echo $index + 1; ?></td>
                                            <td><code><?php echo htmlspecialchars($path); ?></code></td>
                                            <td>
                                                <?php if (file_exists($path)): ?>
                                                    <span class="status-ok"><i class="fas fa-check"></i> Found</span>
                                                    <?php $found_path = $path; ?>
                                                <?php else: ?>
                                                    <span class="status-error"><i class="fas fa-times"></i> Missing</span>
                                                <?php endif; ?>
                                            </td>
                                            <td>
                                                <?php if (file_exists($path)): ?>
                                                    <?php echo number_format(filesize($path) / 1024, 1); ?> KB
                                                <?php else: ?>
                                                    -
                                                <?php endif; ?>
                                            </td>
                                            <td>
                                                <?php if (file_exists($path)): ?>
                                                    <a href="<?php echo htmlspecialchars($path); ?>" target="_blank" class="btn btn-sm btn-outline-success">
                                                        <i class="fas fa-external-link-alt"></i> Direct
                                                    </a>
                                                <?php else: ?>
                                                    <span class="text-muted">N/A</span>
                                                <?php endif; ?>
                                            </td>
                                        </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>

                        <hr>
                        <h4><i class="fas fa-test-tube me-2"></i>API Tests</h4>
                        
                        <div class="row">
                            <div class="col-md-6">
                                <h6>🔗 Original URL (ที่มีปัญหา):</h6>
                                <div class="mb-3">
                                    <a href="api/view_file.php?file=<?php echo urlencode($test_file); ?>&type=batch_document" 
                                       target="_blank" class="btn btn-primary">
                                        <i class="fas fa-external-link-alt me-2"></i>Test API Call
                                    </a>
                                </div>
                                <div class="code-block">
                                    api/view_file.php?file=<?php echo urlencode($test_file); ?>&type=batch_document
                                </div>
                            </div>
                            <div class="col-md-6">
                                <h6>🔗 Alternative URL (filename only):</h6>
                                <div class="mb-3">
                                    <a href="api/view_file.php?file=<?php echo urlencode(basename($test_file)); ?>&type=batch_document" 
                                       target="_blank" class="btn btn-secondary">
                                        <i class="fas fa-external-link-alt me-2"></i>Test Filename Only
                                    </a>
                                </div>
                                <div class="code-block">
                                    api/view_file.php?file=<?php echo urlencode(basename($test_file)); ?>&type=batch_document
                                </div>
                            </div>
                        </div>

                        <hr>
                        <h4><i class="fas fa-image me-2"></i>Image Preview Test</h4>
                        
                        <?php if (isset($found_path)): ?>
                            <div class="text-center">
                                <h6>✅ Found Image at: <code><?php echo htmlspecialchars($found_path); ?></code></h6>
                                <div class="mb-3">
                                    <img src="<?php echo htmlspecialchars($found_path); ?>" 
                                         class="test-image img-thumbnail" 
                                         alt="Test Image"
                                         onerror="this.style.display='none'; this.nextElementSibling.style.display='block';">
                                    <div style="display:none;" class="alert alert-danger">
                                        <i class="fas fa-times"></i> Failed to load image directly
                                    </div>
                                </div>
                                
                                <h6>🔄 Via API:</h6>
                                <div class="mb-3">
                                    <img src="api/view_file.php?file=<?php echo urlencode($test_file); ?>&type=batch_document" 
                                         class="test-image img-thumbnail" 
                                         alt="API Test Image"
                                         onerror="this.style.display='none'; this.nextElementSibling.style.display='block';">
                                    <div style="display:none;" class="alert alert-warning">
                                        <i class="fas fa-exclamation-triangle"></i> Failed to load image via API
                                    </div>
                                </div>
                            </div>
                        <?php else: ?>
                            <div class="alert alert-danger">
                                <i class="fas fa-times me-2"></i>
                                No valid path found for the test file!
                            </div>
                        <?php endif; ?>

                        <hr>
                        <h4><i class="fas fa-tools me-2"></i>Debug Information</h4>
                        
                        <div class="row">
                            <div class="col-md-6">
                                <h6>File Processing:</h6>
                                <ul>
                                    <li><strong>Original file:</strong> <code><?php echo htmlspecialchars($test_file); ?></code></li>
                                    <li><strong>After sanitization:</strong> <code><?php echo htmlspecialchars($file); ?></code></li>
                                    <li><strong>Basename:</strong> <code><?php echo htmlspecialchars(basename($file)); ?></code></li>
                                    <li><strong>Contains path:</strong> <?php echo (strpos($file, '/') !== false) ? 'Yes' : 'No'; ?></li>
                                </ul>
                            </div>
                            <div class="col-md-6">
                                <h6>Directory Status:</h6>
                                <ul>
                                    <li><strong>uploads/:</strong> <?php echo is_dir('uploads/') ? '✅ Exists' : '❌ Missing'; ?></li>
                                    <li><strong>uploads/batch_documents/:</strong> <?php echo is_dir('uploads/batch_documents/') ? '✅ Exists' : '❌ Missing'; ?></li>
                                    <li><strong>uploads/batch_documents/review/:</strong> <?php echo is_dir('uploads/batch_documents/review/') ? '✅ Exists' : '❌ Missing'; ?></li>
                                </ul>
                            </div>
                        </div>

                        <div class="alert alert-warning mt-3">
                            <h6><i class="fas fa-shield-alt me-2"></i>Security Notice</h6>
                            <p><strong>ลบไฟล์นี้ (test_specific_batch.php) หลังจากทดสอบเสร็จแล้ว!</strong></p>
                        </div>

                        <div class="text-center mt-4">
                            <a href="test_batch_images.php" class="btn btn-primary me-3">
                                <i class="fas fa-images me-2"></i>Full Batch Test
                            </a>
                            <a href="expenses/" class="btn btn-secondary me-3">
                                <i class="fas fa-list me-2"></i>Expenses List
                            </a>
                            <button onclick="window.location.reload()" class="btn btn-outline-secondary">
                                <i class="fas fa-sync me-2"></i>Refresh
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</body>
</html>
