<?php
/**
 * Test API view_file.php with different file paths
 */

session_start();
require_once 'config/database.php';

// Mock session for testing
$_SESSION['user_id'] = 1;
$_SESSION['role'] = 'administrator';

$database = new Database();
$db = $database->getConnection();

echo "<h1>🧪 Test API view_file.php</h1>";

// Get sample batch documents
try {
    $stmt = $db->query("SELECT * FROM batch_documents ORDER BY id DESC LIMIT 3");
    $batch_docs = $stmt->fetchAll();
    
    if (count($batch_docs) > 0) {
        echo "<h2>Testing Batch Documents</h2>";
        
        foreach ($batch_docs as $doc) {
            echo "<h3>Document ID: {$doc['id']}</h3>";
            echo "<p><strong>Batch ID:</strong> {$doc['batch_id']}</p>";
            echo "<p><strong>Type:</strong> {$doc['document_type']}</p>";
            echo "<p><strong>File Path in DB:</strong> {$doc['file_path']}</p>";
            echo "<p><strong>Original Name:</strong> {$doc['original_filename']}</p>";
            
            // Test different file path constructions
            $test_paths = [
                $doc['file_path'], // As stored
                'uploads/' . $doc['file_path'], // With uploads/ prefix
                'uploads/batch_documents/' . $doc['file_path'], // With batch_documents/ prefix
                'uploads/bulk_operations/' . $doc['file_path'], // Old path
                basename($doc['file_path']) // Just filename
            ];
            
            echo "<h4>File Existence Check:</h4>";
            echo "<ul>";
            foreach ($test_paths as $i => $path) {
                $exists = file_exists($path);
                $size = $exists ? filesize($path) : 0;
                echo "<li>" . ($exists ? '✅' : '❌') . " {$path}" . ($exists ? " (" . number_format($size) . " bytes)" : "") . "</li>";
            }
            echo "</ul>";
            
            // Test API URLs
            echo "<h4>API Test URLs:</h4>";
            $api_tests = [
                "api/view_file.php?file=" . urlencode($doc['file_path']) . "&type=batch_document",
                "api/view_file.php?file=" . urlencode(basename($doc['file_path'])) . "&type=batch_document"
            ];
            
            echo "<ul>";
            foreach ($api_tests as $i => $url) {
                echo "<li><a href='http://localhost:82/expenses_system/{$url}' target='_blank'>Test " . ($i + 1) . ": {$url}</a></li>";
            }
            echo "</ul>";
            
            // Test with curl
            echo "<h4>CURL Test:</h4>";
            $curl_url = "http://localhost:82/expenses_system/api/view_file.php?file=" . urlencode($doc['file_path']) . "&type=batch_document";
            
            $ch = curl_init();
            curl_setopt($ch, CURLOPT_URL, $curl_url);
            curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
            curl_setopt($ch, CURLOPT_HEADER, true);
            curl_setopt($ch, CURLOPT_NOBODY, true); // HEAD request only
            curl_setopt($ch, CURLOPT_FOLLOWLOCATION, true);
            curl_setopt($ch, CURLOPT_TIMEOUT, 10);
            
            // Add session cookie if needed
            curl_setopt($ch, CURLOPT_COOKIE, session_name() . '=' . session_id());
            
            $response = curl_exec($ch);
            $http_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
            curl_close($ch);
            
            if ($http_code == 200) {
                echo "<p>✅ API Response: HTTP {$http_code} - File accessible</p>";
            } else {
                echo "<p>❌ API Response: HTTP {$http_code} - File not accessible</p>";
                echo "<pre>" . htmlspecialchars($response) . "</pre>";
            }
            
            echo "<hr>";
        }
        
    } else {
        echo "<p>⚠️ No batch documents found in database</p>";
    }
    
} catch (Exception $e) {
    echo "<p>❌ Error: " . $e->getMessage() . "</p>";
}

// Test directory structure
echo "<h2>Directory Structure</h2>";

function listDirectory($dir, $level = 0) {
    if (!is_dir($dir)) {
        return "❌ Directory not found: {$dir}";
    }
    
    $result = "";
    $indent = str_repeat("&nbsp;&nbsp;&nbsp;&nbsp;", $level);
    
    $files = scandir($dir);
    foreach ($files as $file) {
        if ($file === '.' || $file === '..') continue;
        
        $path = $dir . '/' . $file;
        if (is_dir($path)) {
            $result .= "{$indent}📁 {$file}/<br>";
            if ($level < 2) { // Limit depth
                $result .= listDirectory($path, $level + 1);
            }
        } else {
            $size = filesize($path);
            $result .= "{$indent}📄 {$file} (" . number_format($size) . " bytes)<br>";
        }
    }
    
    return $result;
}

echo "<h3>uploads/ Directory:</h3>";
echo listDirectory('uploads');

// Manual file test
echo "<h2>Manual File Test</h2>";

$test_file = "batch_documents/review/BATCH_REV_20251019_175531_review_68f526245e645_1760896548.png";
$full_paths = [
    $test_file,
    "uploads/" . $test_file,
    "uploads/batch_documents/review/BATCH_REV_20251019_175531_review_68f526245e645_1760896548.png"
];

echo "<h3>Testing specific file: {$test_file}</h3>";
foreach ($full_paths as $path) {
    $exists = file_exists($path);
    echo "<p>" . ($exists ? '✅' : '❌') . " {$path}</p>";
    
    if ($exists) {
        $api_url = "http://localhost:82/expenses_system/api/view_file.php?file=" . urlencode($test_file) . "&type=batch_document";
        echo "<p><a href='{$api_url}' target='_blank'>🔗 Test API: {$api_url}</a></p>";
        break;
    }
}

echo "<h2>✅ Test Complete</h2>";
echo "<p><a href='http://localhost:82/expenses_system/debug_batch_data.php' target='_blank'>🔍 Debug Batch Data</a></p>";
?>
