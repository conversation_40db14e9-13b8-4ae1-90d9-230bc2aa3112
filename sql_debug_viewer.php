<?php
/**
 * SQL Debug Logs Viewer
 * 
 * This file helps view the latest SQL debug logs in a formatted way
 */

// Check if user is logged in (basic security)
session_start();
if (!isset($_SESSION['user_id'])) {
    die('Access denied. Please login first.');
}

?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>SQL Debug Logs Viewer</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <style>
        .log-entry {
            margin-bottom: 10px;
            padding: 10px;
            border-left: 4px solid #007bff;
            background-color: #f8f9fa;
        }
        .log-sql {
            border-left-color: #28a745;
            background-color: #d4edda;
        }
        .log-error {
            border-left-color: #dc3545;
            background-color: #f8d7da;
        }
        .log-success {
            border-left-color: #28a745;
            background-color: #d1ecf1;
        }
        .log-debug {
            border-left-color: #ffc107;
            background-color: #fff3cd;
        }
        .log-deduction {
            border-left-color: #6f42c1;
            background-color: #e2d9f3;
        }
        pre {
            white-space: pre-wrap;
            word-wrap: break-word;
            font-size: 0.9em;
        }
        .timestamp {
            color: #6c757d;
            font-size: 0.8em;
        }
    </style>
</head>
<body>
    <div class="container-fluid mt-4">
        <div class="row">
            <div class="col-12">
                <h1>SQL Debug Logs Viewer</h1>
                <p class="text-muted">Latest SQL debug logs from receipt deductions</p>
                
                <div class="mb-3">
                    <button class="btn btn-primary" onclick="location.reload()">Refresh Logs</button>
                    <button class="btn btn-secondary" onclick="clearLogs()">Clear Display</button>
                    <button class="btn btn-warning" onclick="clearDebugFile()">Clear Debug File</button>
                    <a href="expenses/create.php" class="btn btn-success">Go to Create Expense</a>
                </div>
                
                <div class="alert alert-info">
                    <strong>Instructions:</strong>
                    <ol>
                        <li>Go to Create Expense page</li>
                        <li>Fill in the form with receipts and deductions</li>
                        <li>Submit the form</li>
                        <li>The page will stop with a debug message</li>
                        <li>Come back here and refresh to see all SQL statements</li>
                    </ol>
                </div>
                
                <div id="logs-container">
                    <?php
                    // Read from custom debug file first, then try system logs
                    $debugFile = __DIR__ . '/debug_sql.log';
                    $logContent = '';
                    $logFile = '';

                    // Try custom debug file first
                    if (@file_exists($debugFile) && @is_readable($debugFile)) {
                        $logFile = $debugFile;
                        $logContent = @file_get_contents($debugFile);
                        if ($logContent) {
                            $lines = explode("\n", $logContent);
                            $logContent = implode("\n", array_slice($lines, -200));
                        }
                    }

                    // If no custom debug file, try system logs
                    if (!$logContent) {
                        $logFiles = [
                            ini_get('error_log'),
                            '/tmp/php_errors.log',
                            '/var/www/vhosts/nkslgroup.com/logs/error_log',
                            '/var/www/vhosts/nkslgroup.com/logs/php_error.log',
                            './error.log',
                            '../error.log'
                        ];

                        foreach ($logFiles as $file) {
                            if ($file && @file_exists($file) && @is_readable($file)) {
                                $logFile = $file;
                                // Get last 200 lines - use PHP instead of shell_exec for better compatibility
                                $logContent = @file_get_contents($file);
                                if ($logContent) {
                                    $lines = explode("\n", $logContent);
                                    $logContent = implode("\n", array_slice($lines, -200));
                                }
                                break;
                            }
                        }
                    }
                    
                    if ($logContent) {
                        echo "<div class='alert alert-info'>Reading from: <code>{$logFile}</code></div>";

                        // Check if we're reading from custom debug file
                        $isCustomDebugFile = (basename($logFile) === 'debug_sql.log');

                        $lines = explode("\n", $logContent);
                        $lines = array_reverse($lines); // Show newest first

                        $filteredLines = [];
                        foreach ($lines as $line) {
                            if (trim($line) === '') continue;

                            // If reading from custom debug file, show all lines
                            // If reading from system logs, filter for relevant messages
                            if ($isCustomDebugFile) {
                                $filteredLines[] = $line;
                            } else {
                                // Filter for relevant debug messages
                                if (strpos($line, 'DEDUCTION') !== false ||
                                    strpos($line, 'SQL DEBUG') !== false ||
                                    strpos($line, 'RECEIPT') !== false ||
                                    strpos($line, 'receipt_deductions') !== false ||
                                    strpos($line, '✅') !== false ||
                                    strpos($line, '❌') !== false ||
                                    strpos($line, '🛑') !== false ||
                                    strpos($line, 'DEBUG BREAKPOINT') !== false ||
                                    strpos($line, 'FINAL DATABASE STATE') !== false) {
                                    $filteredLines[] = $line;
                                }
                            }
                        }
                        
                        if (empty($filteredLines)) {
                            echo "<div class='alert alert-warning'>No relevant debug logs found. Try creating an expense with deductions first.</div>";
                        } else {
                            foreach ($filteredLines as $line) {
                                $class = 'log-entry';
                                if (strpos($line, 'SQL') !== false) {
                                    $class .= ' log-sql';
                                } elseif (strpos($line, 'DEDUCTION') !== false || strpos($line, 'receipt_deductions') !== false) {
                                    $class .= ' log-deduction';
                                } elseif (strpos($line, 'ERROR') !== false || strpos($line, '❌') !== false) {
                                    $class .= ' log-error';
                                } elseif (strpos($line, '✅') !== false || strpos($line, 'SUCCESS') !== false) {
                                    $class .= ' log-success';
                                } elseif (strpos($line, 'DEBUG') !== false || strpos($line, '🛑') !== false) {
                                    $class .= ' log-debug';
                                }
                                
                                echo "<div class='{$class}'>";
                                
                                // Extract timestamp if present
                                if (preg_match('/^\[(.*?)\]/', $line, $matches)) {
                                    echo "<div class='timestamp'>{$matches[1]}</div>";
                                    $line = preg_replace('/^\[.*?\]\s*/', '', $line);
                                }
                                
                                echo "<pre>" . htmlspecialchars($line) . "</pre>";
                                echo "</div>";
                            }
                        }
                    } else {
                        echo "<div class='alert alert-warning'>Could not read error logs. Tried files:</div>";
                        echo "<ul>";
                        foreach ($logFiles as $file) {
                            $status = $file && @file_exists($file) ?
                                (@is_readable($file) ? 'exists, readable' : 'exists, not readable') :
                                'does not exist or access denied';
                            echo "<li><code>{$file}</code> - {$status}</li>";
                        }
                        echo "</ul>";
                        echo "<div class='alert alert-info'>Current error_log setting: <code>" . ini_get('error_log') . "</code></div>";
                        echo "<div class='alert alert-info'>Try checking MAMP logs or enable error logging in PHP.</div>";
                    }
                    ?>
                </div>
            </div>
        </div>
    </div>
    
    <script>
        function clearLogs() {
            document.getElementById('logs-container').innerHTML = '<div class="alert alert-info">Logs cleared from display. Refresh to reload.</div>';
        }

        function clearDebugFile() {
            if (confirm('Are you sure you want to clear the debug file?')) {
                fetch('clear_debug.php', {method: 'POST'})
                    .then(response => response.text())
                    .then(data => {
                        alert('Debug file cleared');
                        location.reload();
                    })
                    .catch(error => {
                        alert('Error clearing debug file: ' + error);
                    });
            }
        }

        // Auto-refresh every 10 seconds
        setTimeout(function() {
            location.reload();
        }, 10000);
    </script>
</body>
</html>
