<?php
/**
 * Test CSV Fix for MariaDB 5.5.68
 * ทดสอบการแก้ไข ROW_NUMBER() ใน export_csv_per_receipt.php
 * ลบไฟล์นี้หลังจากทดสอบเสร็จแล้ว
 */
session_start();

// Check if user is logged in
if (!isset($_SESSION['user_id'])) {
    header('Location: login.php');
    exit();
}

require_once 'config/database.php';

?>
<!DOCTYPE html>
<html lang="th">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>CSV Fix Test - Expenses System</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
</head>
<body class="bg-light">
    <div class="container mt-4">
        <div class="row justify-content-center">
            <div class="col-md-10">
                <div class="card shadow">
                    <div class="card-header bg-success text-white">
                        <h3><i class="fas fa-check-circle me-2"></i>CSV Export Fix Test</h3>
                        <p class="mb-0">ทดสอบการแก้ไข MariaDB 5.5.68 ROW_NUMBER() Error</p>
                    </div>
                    <div class="card-body">
                        
                        <div class="alert alert-danger">
                            <h5><i class="fas fa-bug me-2"></i>ปัญหาที่พบ:</h5>
                            <p><strong>Error:</strong> SQLSTATE[42000]: Syntax error... ROW_NUMBER() OVER()</p>
                            <p><strong>สาเหตุ:</strong> MariaDB 5.5.68 ไม่รองรับ Window Functions</p>
                        </div>

                        <div class="alert alert-success">
                            <h5><i class="fas fa-wrench me-2"></i>การแก้ไข:</h5>
                            <ul class="mb-0">
                                <li>✅ ลบ ROW_NUMBER() OVER() ออก</li>
                                <li>✅ ใช้ PHP logic แทน SQL window function</li>
                                <li>✅ แยก query เป็น 2 ขั้นตอน: expenses แล้วค่อย receipts</li>
                                <li>✅ เพิ่ม error handling ครบถ้วน</li>
                            </ul>
                        </div>

                        <h4><i class="fas fa-database me-2"></i>Database Info</h4>
                        <?php
                        try {
                            $database = new Database();
                            $db = $database->getConnection();
                            
                            // Get MariaDB version
                            $stmt = $db->query("SELECT VERSION() as version");
                            $version = $stmt->fetch();
                            
                            echo "<div class='alert alert-info'>";
                            echo "<strong>Database Version:</strong> " . htmlspecialchars($version['version']) . "<br>";
                            
                            // Check if ROW_NUMBER is supported
                            try {
                                $test_stmt = $db->query("SELECT ROW_NUMBER() OVER (ORDER BY 1) as rn FROM (SELECT 1) t LIMIT 1");
                                echo "<strong>ROW_NUMBER() Support:</strong> <span class='text-success'>✅ Supported</span>";
                            } catch (Exception $e) {
                                echo "<strong>ROW_NUMBER() Support:</strong> <span class='text-danger'>❌ Not Supported</span><br>";
                                echo "<small>Error: " . htmlspecialchars($e->getMessage()) . "</small>";
                            }
                            echo "</div>";
                            
                            // Check expenses and receipts count
                            $stmt = $db->query("SELECT COUNT(*) as count FROM expenses");
                            $expense_count = $stmt->fetch()['count'];
                            
                            $stmt = $db->query("SELECT COUNT(*) as count FROM receipt_numbers");
                            $receipt_count = $stmt->fetch()['count'];
                            
                            echo "<div class='row mb-3'>";
                            echo "<div class='col-md-6'>";
                            echo "<div class='card border-primary'>";
                            echo "<div class='card-body text-center'>";
                            echo "<h5 class='text-primary'>" . number_format($expense_count) . "</h5>";
                            echo "<p class='mb-0'>Total Expenses</p>";
                            echo "</div></div></div>";
                            
                            echo "<div class='col-md-6'>";
                            echo "<div class='card border-info'>";
                            echo "<div class='card-body text-center'>";
                            echo "<h5 class='text-info'>" . number_format($receipt_count) . "</h5>";
                            echo "<p class='mb-0'>Total Receipts</p>";
                            echo "</div></div></div>";
                            echo "</div>";
                            
                        } catch (Exception $e) {
                            echo "<div class='alert alert-danger'>";
                            echo "<strong>Database Error:</strong> " . htmlspecialchars($e->getMessage());
                            echo "</div>";
                        }
                        ?>

                        <h4><i class="fas fa-download me-2"></i>Test CSV Export</h4>
                        
                        <div class="row mb-4">
                            <div class="col-md-4">
                                <div class="card border-success">
                                    <div class="card-header bg-success text-white">
                                        <h6><i class="fas fa-file-csv me-2"></i>Fixed Version</h6>
                                    </div>
                                    <div class="card-body">
                                        <p>เวอร์ชันที่แก้ไขแล้ว (ไม่ใช้ ROW_NUMBER)</p>
                                        <a href="api/export_csv_per_receipt.php" class="btn btn-success btn-sm w-100">
                                            <i class="fas fa-download me-1"></i>Download CSV (Fixed)
                                        </a>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="card border-info">
                                    <div class="card-header bg-info text-white">
                                        <h6><i class="fas fa-file-alt me-2"></i>Regular CSV</h6>
                                    </div>
                                    <div class="card-body">
                                        <p>CSV Export แบบปกติ (ไม่มี per receipt)</p>
                                        <a href="api/export_csv.php" class="btn btn-info btn-sm w-100">
                                            <i class="fas fa-download me-1"></i>Download CSV (Regular)
                                        </a>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="card border-warning">
                                    <div class="card-header bg-warning text-dark">
                                        <h6><i class="fas fa-history me-2"></i>Backup Version</h6>
                                    </div>
                                    <div class="card-body">
                                        <p>เวอร์ชันเดิมที่มีปัญหา (สำหรับเปรียบเทียบ)</p>
                                        <a href="api/export_csv_per_receipt_backup.php" class="btn btn-warning btn-sm w-100">
                                            <i class="fas fa-exclamation-triangle me-1"></i>Test Backup (Error)
                                        </a>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <h4><i class="fas fa-code me-2"></i>Technical Changes</h4>
                        
                        <div class="accordion" id="technicalAccordion">
                            <div class="accordion-item">
                                <h2 class="accordion-header" id="headingOne">
                                    <button class="accordion-button" type="button" data-bs-toggle="collapse" data-bs-target="#collapseOne">
                                        <i class="fas fa-times-circle me-2 text-danger"></i>Before (Error)
                                    </button>
                                </h2>
                                <div id="collapseOne" class="accordion-collapse collapse show" data-bs-parent="#technicalAccordion">
                                    <div class="accordion-body">
                                        <pre class="bg-light p-3"><code>-- ❌ ไม่ทำงานใน MariaDB 5.5.68
ROW_NUMBER() OVER (PARTITION BY e.id ORDER BY rn.id) as receipt_row_num

-- Error: SQLSTATE[42000]: Syntax error
-- MariaDB 5.5.68 ไม่รองรับ Window Functions</code></pre>
                                    </div>
                                </div>
                            </div>
                            <div class="accordion-item">
                                <h2 class="accordion-header" id="headingTwo">
                                    <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#collapseTwo">
                                        <i class="fas fa-check-circle me-2 text-success"></i>After (Fixed)
                                    </button>
                                </h2>
                                <div id="collapseTwo" class="accordion-collapse collapse" data-bs-parent="#technicalAccordion">
                                    <div class="accordion-body">
                                        <pre class="bg-light p-3"><code>// ✅ แก้ไขด้วย PHP Logic
// Step 1: Get expenses first
$expenses_sql = "SELECT e.*, i.name as item_name... FROM expenses e...";

// Step 2: Get receipts for each expense
foreach ($expenses as $expense) {
    $receipt_sql = "SELECT * FROM receipt_numbers WHERE expense_id = ?";
    // Process receipts for this expense
}</code></pre>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <h4><i class="fas fa-clipboard-check me-2"></i>Expected Results</h4>
                        
                        <div class="alert alert-success">
                            <h6><i class="fas fa-check me-2"></i>หากแก้ไขสำเร็จ:</h6>
                            <ul class="mb-0">
                                <li>✅ CSV file จะ download ได้ปกติ</li>
                                <li>✅ ไม่มี SQL error หรือหน้าขาว</li>
                                <li>✅ ข้อมูล expenses และ receipts แสดงครบถ้วน</li>
                                <li>✅ Format เป็น "per receipt" ถูกต้อง</li>
                            </ul>
                        </div>

                        <div class="alert alert-info">
                            <h6><i class="fas fa-info-circle me-2"></i>CSV Format:</h6>
                            <ul class="mb-0">
                                <li><strong>Expense with receipts:</strong> แสดงข้อมูล expense ในแถวแรก, แถวถัดไปแสดงเฉพาะ receipt</li>
                                <li><strong>Expense without receipts:</strong> แสดงข้อมูล expense พร้อม receipt fields ว่าง</li>
                                <li><strong>Amount matching:</strong> เปรียบเทียบ transfer amount กับ receipt total</li>
                            </ul>
                        </div>

                        <h4><i class="fas fa-tools me-2"></i>Debug Tools</h4>
                        
                        <div class="row">
                            <div class="col-md-6">
                                <a href="debug_csv_per_receipt.php" class="btn btn-warning w-100 mb-2">
                                    <i class="fas fa-bug me-2"></i>Debug CSV Per Receipt
                                </a>
                            </div>
                            <div class="col-md-6">
                                <a href="test_csv_simple.php" class="btn btn-info w-100 mb-2">
                                    <i class="fas fa-vial me-2"></i>Simple CSV Test
                                </a>
                            </div>
                        </div>

                        <div class="alert alert-warning mt-4">
                            <h6><i class="fas fa-shield-alt me-2"></i>Security Notice</h6>
                            <p><strong>ลบไฟล์ test_csv_fix.php หลังจากทดสอบเสร็จแล้ว!</strong></p>
                        </div>

                        <div class="text-center mt-4">
                            <a href="reports/" class="btn btn-primary me-3">
                                <i class="fas fa-chart-bar me-2"></i>Reports
                            </a>
                            <a href="dashboard.php" class="btn btn-secondary">
                                <i class="fas fa-home me-2"></i>Dashboard
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
