<?php
session_start();

// Simulate logged in user for testing
if (!isset($_SESSION['user_id'])) {
    $_SESSION['user_id'] = 1;
    $_SESSION['role'] = 'admin';
    $_SESSION['username'] = 'test_user';
}

echo "<h2>Debug File Timestamp Issue</h2>";

$test_file = $_GET['file'] ?? 'transfer_68fcafb1efdee_1761390513.jpg';

echo "<h3>Analyzing File: $test_file</h3>";

// Extract timestamp from filename
if (preg_match('/transfer_[a-f0-9]+_(\d+)\./', $test_file, $matches)) {
    $timestamp = $matches[1];
    echo "<p><strong>Extracted timestamp:</strong> $timestamp</p>";
    echo "<p><strong>Timestamp as date:</strong> " . date('Y-m-d H:i:s', $timestamp) . "</p>";
    echo "<p><strong>Current time:</strong> " . date('Y-m-d H:i:s') . "</p>";
    echo "<p><strong>Time difference:</strong> " . round((time() - $timestamp) / 3600, 1) . " hours ago</p>";
} else {
    echo "<p>Could not extract timestamp from filename</p>";
}

// Check file in current system
$file_path = 'uploads/transfer_slips/' . $test_file;
echo "<h3>Current System (expenses_system):</h3>";
echo "<ul>";
echo "<li><strong>File path:</strong> $file_path</li>";
echo "<li><strong>File exists:</strong> " . (file_exists($file_path) ? '✅ YES' : '❌ NO') . "</li>";

if (file_exists($file_path)) {
    echo "<li><strong>File size:</strong> " . filesize($file_path) . " bytes</li>";
    echo "<li><strong>File modified:</strong> " . date('Y-m-d H:i:s', filemtime($file_path)) . "</li>";
    echo "<li><strong>File created:</strong> " . date('Y-m-d H:i:s', filectime($file_path)) . "</li>";
    echo "<li><strong>File permissions:</strong> " . substr(sprintf('%o', fileperms($file_path)), -4) . "</li>";
    
    // Test view_file.php
    $test_url = "api/view_file.php?file=" . urlencode($test_file) . "&type=transfer_slip";
    echo "<li><strong>Test URL:</strong> <a href='$test_url' target='_blank'>$test_url</a></li>";
    
    echo "<li><strong>Image preview:</strong><br>";
    echo "<img src='$test_url' style='max-width: 300px; border: 1px solid #ccc;' alt='Current System' onerror=\"this.style.display='none'; this.nextSibling.style.display='block';\">";
    echo "<div style='display: none; color: red; border: 1px solid red; padding: 10px;'>❌ Failed to load from current system</div>";
    echo "</li>";
}
echo "</ul>";

// Check if V1 system exists locally
$v1_file_path = '../expenses_system-V1/uploads/transfer_slips/' . $test_file;
echo "<h3>V1 System Check (expenses_system-V1):</h3>";
echo "<ul>";
echo "<li><strong>V1 file path:</strong> $v1_file_path</li>";
echo "<li><strong>V1 file exists:</strong> " . (file_exists($v1_file_path) ? '✅ YES' : '❌ NO') . "</li>";

if (file_exists($v1_file_path)) {
    echo "<li><strong>V1 file size:</strong> " . filesize($v1_file_path) . " bytes</li>";
    echo "<li><strong>V1 file modified:</strong> " . date('Y-m-d H:i:s', filemtime($v1_file_path)) . "</li>";
    echo "<li><strong>V1 file created:</strong> " . date('Y-m-d H:i:s', filectime($v1_file_path)) . "</li>";
}
echo "</ul>";

// Check database for this file
echo "<h3>Database Check:</h3>";
try {
    require_once 'config/database.php';
    $database = new Database();
    $db = $database->getConnection();
    
    $stmt = $db->prepare("SELECT id, exno, transfer_slip_image, created_at, updated_at FROM expenses WHERE transfer_slip_image = ?");
    $stmt->execute([$test_file]);
    $expense = $stmt->fetch();
    
    if ($expense) {
        echo "<ul>";
        echo "<li><strong>Expense ID:</strong> {$expense['id']}</li>";
        echo "<li><strong>Expense No:</strong> {$expense['exno']}</li>";
        echo "<li><strong>Created at:</strong> {$expense['created_at']}</li>";
        echo "<li><strong>Updated at:</strong> {$expense['updated_at']}</li>";
        echo "<li><strong>View expense:</strong> <a href='expenses/view.php?id={$expense['id']}' target='_blank'>expenses/view.php?id={$expense['id']}</a></li>";
        echo "</ul>";
        
        // Compare timestamps
        $expense_time = strtotime($expense['created_at']);
        if (isset($timestamp)) {
            $time_diff = abs($expense_time - $timestamp);
            echo "<p><strong>Time difference between expense creation and filename:</strong> " . round($time_diff / 60, 1) . " minutes</p>";
        }
    } else {
        echo "<p>❌ No expense found with this image filename</p>";
    }
} catch (Exception $e) {
    echo "<p>❌ Database error: " . $e->getMessage() . "</p>";
}

// Look for similar files
echo "<h3>Similar Files in Current System:</h3>";
$all_files = glob('uploads/transfer_slips/transfer_*.{jpg,jpeg,png}', GLOB_BRACE);

if ($all_files) {
    // Filter files from similar time period
    $target_time = isset($timestamp) ? $timestamp : time();
    $time_range = 3600; // 1 hour
    
    $similar_files = [];
    foreach ($all_files as $file) {
        $file_time = filemtime($file);
        if (abs($file_time - $target_time) <= $time_range) {
            $similar_files[] = [
                'path' => $file,
                'filename' => basename($file),
                'time_diff' => abs($file_time - $target_time),
                'file_time' => $file_time
            ];
        }
    }
    
    if ($similar_files) {
        usort($similar_files, function($a, $b) {
            return $a['time_diff'] - $b['time_diff'];
        });
        
        echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
        echo "<tr><th>Filename</th><th>Time Difference</th><th>File Time</th><th>Test</th></tr>";
        
        foreach (array_slice($similar_files, 0, 5) as $file) {
            $time_diff_min = round($file['time_diff'] / 60, 1);
            $file_time_str = date('Y-m-d H:i:s', $file['file_time']);
            
            echo "<tr>";
            echo "<td>{$file['filename']}</td>";
            echo "<td>{$time_diff_min} min</td>";
            echo "<td>$file_time_str</td>";
            echo "<td><a href='api/view_file.php?file=" . urlencode($file['filename']) . "&type=transfer_slip' target='_blank'>Test</a></td>";
            echo "</tr>";
        }
        echo "</table>";
    } else {
        echo "<p>No files found within 1 hour of target time</p>";
    }
} else {
    echo "<p>No transfer slip files found</p>";
}

// Server timezone info
echo "<h3>Server Information:</h3>";
echo "<ul>";
echo "<li><strong>Server timezone:</strong> " . date_default_timezone_get() . "</li>";
echo "<li><strong>Server time:</strong> " . date('Y-m-d H:i:s') . "</li>";
echo "<li><strong>Server timestamp:</strong> " . time() . "</li>";
echo "<li><strong>PHP version:</strong> " . PHP_VERSION . "</li>";
echo "</ul>";

// Copy file from V1 if exists
if (isset($_GET['copy_from_v1']) && $_GET['copy_from_v1'] === 'yes') {
    if (file_exists($v1_file_path) && !file_exists($file_path)) {
        // Ensure directory exists
        $target_dir = dirname($file_path);
        if (!is_dir($target_dir)) {
            mkdir($target_dir, 0755, true);
        }
        
        if (copy($v1_file_path, $file_path)) {
            echo "<div style='background: #d4edda; border: 1px solid #c3e6cb; padding: 10px; margin: 10px 0; border-radius: 5px;'>";
            echo "<strong>✅ Success!</strong> Copied file from V1 system to current system.";
            echo "<br><a href='?file=" . urlencode($test_file) . "'>Refresh to test</a>";
            echo "</div>";
        } else {
            echo "<div style='background: #f8d7da; border: 1px solid #f5c6cb; padding: 10px; margin: 10px 0; border-radius: 5px;'>";
            echo "<strong>❌ Error!</strong> Failed to copy file from V1 system.";
            echo "</div>";
        }
    }
}

// Action buttons
echo "<h3>Actions:</h3>";
if (file_exists($v1_file_path) && !file_exists($file_path)) {
    echo "<a href='?file=" . urlencode($test_file) . "&copy_from_v1=yes' style='background: #007bff; color: white; padding: 10px 15px; text-decoration: none; border-radius: 5px;'>Copy from V1 System</a> ";
}

echo "<a href='production_debug.php?file=" . urlencode($test_file) . "&type=transfer_slip' style='background: #28a745; color: white; padding: 10px 15px; text-decoration: none; border-radius: 5px;'>Full Debug</a>";

// Test form
echo "<h3>Test Different File:</h3>";
echo "<form method='GET' style='background: #f9f9f9; padding: 15px; border: 1px solid #ddd;'>";
echo "<label>Filename: <input type='text' name='file' value='$test_file' style='width: 400px;'></label><br><br>";
echo "<input type='submit' value='Analyze File' style='padding: 10px 20px;'>";
echo "</form>";
?>
