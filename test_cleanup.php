<?php
/**
 * Test cleanup functionality with transaction fixes
 */

session_start();
require_once 'config/database.php';
require_once 'includes/TransactionHelper.php';

// Mock session for testing
$_SESSION['user_id'] = 1;
$_SESSION['role'] = 'administrator';

echo "<h1>🧪 Test Cleanup Functionality</h1>";

try {
    $database = new Database();
    $db = $database->getConnection();
    
    echo "<h2>1. Database Connection Test</h2>";
    echo "<p>✅ Database connected successfully</p>";
    
    echo "<h2>2. TransactionHelper Test</h2>";
    
    $transaction = createTransactionHelper($db);
    echo "<p>✅ TransactionHelper created</p>";
    
    // Test transaction start
    try {
        $transaction->beginTransaction();
        echo "<p>✅ Transaction started successfully</p>";
        
        // Test transaction status
        $status = $transaction->getStatus();
        echo "<p>📊 Transaction Status:</p>";
        echo "<ul>";
        echo "<li>Helper State: " . ($status['transaction_started'] ? 'Active' : 'Inactive') . "</li>";
        echo "<li>PDO State: " . $status['pdo_in_transaction'] . "</li>";
        echo "</ul>";
        
        // Test rollback
        $transaction->rollback();
        echo "<p>✅ Transaction rolled back successfully</p>";
        
    } catch (Exception $e) {
        echo "<p>❌ Transaction test failed: " . $e->getMessage() . "</p>";
    }
    
    echo "<h2>3. Legacy Function Test</h2>";
    
    // Test legacy functions
    $inTransaction = safeInTransaction($db);
    echo "<p>safeInTransaction(): " . ($inTransaction ? 'true' : 'false') . "</p>";
    
    $rollbackResult = safeRollback($db);
    echo "<p>safeRollback(): " . ($rollbackResult ? 'success' : 'failed') . "</p>";
    
    echo "<h2>4. Test Data Creation</h2>";
    
    // Create some test data for cleanup
    $test_data_created = false;
    try {
        $transaction->beginTransaction();
        
        // Insert test expense
        $stmt = $db->prepare("
            INSERT INTO expenses (exno, customer_id, item_id, transfer_amount, status, created_at) 
            VALUES (?, 1, 1, 1000.00, 'open', NOW())
        ");
        $test_exno = 'TEST_CLEANUP_' . date('YmdHis');
        $stmt->execute([$test_exno]);
        $test_expense_id = $db->lastInsertId();
        
        $transaction->commit();
        $test_data_created = true;
        
        echo "<p>✅ Test expense created: {$test_exno} (ID: {$test_expense_id})</p>";
        
    } catch (Exception $e) {
        if ($transaction->inTransaction()) {
            $transaction->rollback();
        }
        echo "<p>❌ Failed to create test data: " . $e->getMessage() . "</p>";
    }
    
    echo "<h2>5. Cleanup API Test</h2>";
    
    if ($test_data_created) {
        // Test cleanup API call
        $cleanup_url = "http://localhost:82/expenses_system/admin/api/cleanup_test_data.php";
        
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $cleanup_url);
        curl_setopt($ch, CURLOPT_POST, true);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_TIMEOUT, 30);
        curl_setopt($ch, CURLOPT_COOKIE, session_name() . '=' . session_id());
        
        $response = curl_exec($ch);
        $http_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        curl_close($ch);
        
        echo "<p><strong>HTTP Response Code:</strong> {$http_code}</p>";
        
        if ($http_code == 200) {
            $result = json_decode($response, true);
            if ($result && isset($result['success'])) {
                if ($result['success']) {
                    echo "<p>✅ <strong>Cleanup API Success!</strong></p>";
                    echo "<p>Message: " . htmlspecialchars($result['message']) . "</p>";
                    if (isset($result['deleted_count'])) {
                        echo "<p>Deleted Count: " . $result['deleted_count'] . "</p>";
                    }
                } else {
                    echo "<p>❌ <strong>Cleanup API Failed!</strong></p>";
                    echo "<p>Error: " . htmlspecialchars($result['error'] ?? 'Unknown error') . "</p>";
                }
            } else {
                echo "<p>❌ Invalid JSON response</p>";
                echo "<pre>" . htmlspecialchars($response) . "</pre>";
            }
        } else {
            echo "<p>❌ HTTP Error: {$http_code}</p>";
            echo "<pre>" . htmlspecialchars($response) . "</pre>";
        }
    } else {
        echo "<p>⚠️ Skipping cleanup test - no test data created</p>";
    }
    
    echo "<h2>6. Database State Check</h2>";
    
    // Check if test data was cleaned up
    if ($test_data_created && isset($test_exno)) {
        $stmt = $db->prepare("SELECT COUNT(*) FROM expenses WHERE exno = ?");
        $stmt->execute([$test_exno]);
        $count = $stmt->fetchColumn();
        
        if ($count == 0) {
            echo "<p>✅ Test data successfully cleaned up</p>";
        } else {
            echo "<p>⚠️ Test data still exists (count: {$count})</p>";
        }
    }
    
    // Check overall database state
    $stmt = $db->query("SELECT COUNT(*) FROM expenses WHERE exno LIKE 'TEST%'");
    $test_count = $stmt->fetchColumn();
    echo "<p>📊 Total test expenses in database: {$test_count}</p>";
    
    echo "<h2>✅ Test Complete</h2>";
    
    echo "<h3>Test Links:</h3>";
    echo "<ul>";
    echo "<li><a href='http://localhost:82/expenses_system/admin/cleanup.php' target='_blank'>🧹 Admin Cleanup Page</a></li>";
    echo "<li><a href='http://localhost:82/expenses_system/debug_batch_data.php' target='_blank'>🔍 Debug Batch Data</a></li>";
    echo "<li><a href='http://localhost:82/expenses_system/test_simple_api.php' target='_blank'>🧪 Simple API Test</a></li>";
    echo "</ul>";
    
} catch (Exception $e) {
    echo "<p>❌ <strong>Test Failed:</strong> " . $e->getMessage() . "</p>";
    echo "<pre>" . $e->getTraceAsString() . "</pre>";
}
?>
