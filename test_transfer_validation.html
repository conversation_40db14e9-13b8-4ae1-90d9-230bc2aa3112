<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Transfer Number Validation</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        /* Transfer number validation styling */
        #transfer_no.is-invalid {
            border-color: #dc3545;
            background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 12 12' width='12' height='12' fill='none' stroke='%23dc3545'%3e%3ccircle cx='6' cy='6' r='4.5'/%3e%3cpath d='m5.8 3.6h.4L6 6.5z'/%3e%3ccircle cx='6' cy='8.2' r='.6' fill='%23dc3545' stroke='none'/%3e%3c/svg%3e");
            background-repeat: no-repeat;
            background-position: right calc(0.375em + 0.1875rem) center;
            background-size: calc(0.75em + 0.375rem) calc(0.75em + 0.375rem);
        }
        
        #transfer_no.is-valid {
            border-color: #198754;
            background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 8 8'%3e%3cpath fill='%23198754' d='m2.3 6.73.94-.94 1.93 1.93 3.53-3.53.94.94L4.16 9.66z'/%3e%3c/svg%3e");
            background-repeat: no-repeat;
            background-position: right calc(0.375em + 0.1875rem) center;
            background-size: calc(0.75em + 0.375rem) calc(0.75em + 0.375rem);
        }
        
        .invalid-feedback {
            display: block;
            width: 100%;
            margin-top: 0.25rem;
            font-size: 0.875em;
            color: #dc3545;
        }
        
        .valid-feedback {
            display: block;
            width: 100%;
            margin-top: 0.25rem;
            font-size: 0.875em;
            color: #198754;
        }
        
        #transfer_no.transfer-number-checking {
            position: relative;
        }
        
        #transfer_no.transfer-number-checking::after {
            content: '';
            position: absolute;
            right: 10px;
            top: 50%;
            transform: translateY(-50%);
            width: 16px;
            height: 16px;
            border: 2px solid #f3f3f3;
            border-top: 2px solid #007bff;
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }
        
        @keyframes spin {
            0% { transform: translateY(-50%) rotate(0deg); }
            100% { transform: translateY(-50%) rotate(360deg); }
        }
    </style>
</head>
<body>
    <div class="container mt-4">
        <div class="row">
            <div class="col-md-8 mx-auto">
                <div class="card">
                    <div class="card-header">
                        <h4><i class="fas fa-test-tube me-2"></i>Test Transfer Number Validation</h4>
                    </div>
                    <div class="card-body">
                        <div class="alert alert-info">
                            <h6><i class="fas fa-info-circle me-2"></i>Test Instructions:</h6>
                            <ul class="mb-0">
                                <li><strong>Existing Transfer Numbers:</strong> Try "1111", "2222", or "3333" (should show error)</li>
                                <li><strong>New Transfer Numbers:</strong> Try "TEST123" or any new number (should show success)</li>
                                <li><strong>Empty Field:</strong> Leave field empty (should show required error)</li>
                            </ul>
                        </div>
                        
                        <form id="test-form">
                            <div class="mb-3">
                                <label for="transfer_no" class="form-label">Transfer Number เลขใบโอน <span class="text-danger">*</span></label>
                                <input type="text" class="form-control" id="transfer_no" name="transfer_no" 
                                       placeholder="กรอกเลขใบโอน" required>
                                <div class="invalid-feedback"></div>
                                <div class="valid-feedback"></div>
                                <div class="form-text">Try: 1111 (existing), TEST123 (new), or leave empty</div>
                            </div>
                            
                            <div class="mb-3">
                                <label for="transfer_amount" class="form-label">Transfer Amount</label>
                                <input type="number" class="form-control" id="transfer_amount" name="transfer_amount" 
                                       placeholder="0.00" step="0.01" min="0">
                            </div>
                            
                            <div class="d-flex justify-content-between">
                                <button type="button" class="btn btn-secondary" onclick="clearForm()">
                                    <i class="fas fa-undo me-1"></i>Clear Form
                                </button>
                                <button type="submit" class="btn btn-primary" id="submit-btn">
                                    <i class="fas fa-save me-1"></i>Test Submit
                                </button>
                            </div>
                        </form>
                        
                        <div id="validation-status" class="mt-4"></div>
                        
                        <div class="mt-4">
                            <h6>Quick Test Buttons:</h6>
                            <div class="btn-group" role="group">
                                <button type="button" class="btn btn-outline-danger btn-sm" onclick="testExisting()">
                                    Test Existing (1111)
                                </button>
                                <button type="button" class="btn btn-outline-success btn-sm" onclick="testNew()">
                                    Test New (TEST123)
                                </button>
                                <button type="button" class="btn btn-outline-warning btn-sm" onclick="testEmpty()">
                                    Test Empty
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        $(document).ready(function() {
            // Event listener for transfer number validation
            $('#transfer_no').on('input blur', function() {
                console.log('Transfer number input event triggered:', $(this).val());
                validateTransferNumber();
            });
            
            // Form submission
            $('#test-form').on('submit', function(e) {
                e.preventDefault();
                
                const hasInvalidTransferInput = $('#transfer_no.is-invalid').length > 0;
                const transferNumber = $('#transfer_no').val().trim();
                
                if (hasInvalidTransferInput || !transferNumber) {
                    $('#validation-status').html(`
                        <div class="alert alert-danger">
                            <i class="fas fa-exclamation-triangle me-2"></i>
                            <strong>Form validation failed!</strong><br>
                            Please fix transfer number errors before submitting.
                        </div>
                    `);
                } else {
                    $('#validation-status').html(`
                        <div class="alert alert-success">
                            <i class="fas fa-check-circle me-2"></i>
                            <strong>Form validation passed!</strong><br>
                            Transfer number "${transferNumber}" is valid and available.
                        </div>
                    `);
                }
            });
        });
        
        // Validate transfer number for duplicates
        function validateTransferNumber() {
            console.log('validateTransferNumber called');
            const transferInput = $('#transfer_no');
            const transferNumber = transferInput.val().trim();
            
            // Clear previous validation states
            transferInput.removeClass('is-invalid is-valid transfer-number-checking');
            transferInput.siblings('.invalid-feedback, .valid-feedback').text('');
            
            if (transferNumber) {
                // Check if transfer number exists in database
                checkTransferNumberInDatabase(transferNumber, transferInput);
            }
            
            updateSubmitButtonState();
        }
        
        // Check if transfer number exists in database
        function checkTransferNumberInDatabase(transferNumber, inputElement) {
            console.log('Checking transfer number in database:', transferNumber);
            
            // Add loading indicator
            inputElement.addClass('transfer-number-checking');
            
            // Clear previous timeout if exists
            const timeoutId = inputElement.data('timeout-id');
            if (timeoutId) {
                clearTimeout(timeoutId);
            }
            
            // Debounce the API call
            const newTimeoutId = setTimeout(function() {
                $.ajax({
                    url: 'api/check_transfer_number.php',
                    method: 'POST',
                    data: { transfer_no: transferNumber },
                    dataType: 'json',
                    success: function(response) {
                        inputElement.removeClass('transfer-number-checking');
                        console.log('Transfer API response for', transferNumber, ':', response);
                        
                        if (response.exists) {
                            inputElement.addClass('is-invalid').removeClass('is-valid');
                            inputElement.siblings('.invalid-feedback').text(`Transfer number already exists in expense: ${response.expense_no}`);
                            console.log('Transfer number exists:', transferNumber, 'in expense:', response.expense_no);
                        } else {
                            inputElement.removeClass('is-invalid').addClass('is-valid');
                            inputElement.siblings('.invalid-feedback').text('');
                            inputElement.siblings('.valid-feedback').text('Transfer number is available');
                            console.log('Transfer number available:', transferNumber);
                        }
                        updateSubmitButtonState();
                    },
                    error: function(xhr, status, error) {
                        inputElement.removeClass('transfer-number-checking');
                        console.error('Error checking transfer number in database:', error);
                        
                        // Show error but don't block submission
                        inputElement.removeClass('is-invalid is-valid');
                        inputElement.siblings('.invalid-feedback').text('Unable to verify transfer number. Please check manually.');
                        updateSubmitButtonState();
                    }
                });
            }, 500); // 500ms debounce
            
            inputElement.data('timeout-id', newTimeoutId);
        }
        
        // Update submit button state based on validation
        function updateSubmitButtonState() {
            const hasInvalidTransferInput = $('#transfer_no.is-invalid').length > 0;
            const submitButton = $('#submit-btn');
            
            if (hasInvalidTransferInput) {
                submitButton.prop('disabled', true);
                submitButton.html('<i class="fas fa-exclamation-triangle me-1"></i>Fix Transfer Number');
                submitButton.removeClass('btn-primary').addClass('btn-danger');
            } else {
                submitButton.prop('disabled', false);
                submitButton.html('<i class="fas fa-save me-1"></i>Test Submit');
                submitButton.removeClass('btn-danger').addClass('btn-primary');
            }
        }
        
        // Test functions
        function testExisting() {
            $('#transfer_no').val('1111').trigger('input');
        }
        
        function testNew() {
            $('#transfer_no').val('TEST123').trigger('input');
        }
        
        function testEmpty() {
            $('#transfer_no').val('').trigger('input');
        }
        
        function clearForm() {
            $('#test-form')[0].reset();
            $('#transfer_no').removeClass('is-invalid is-valid transfer-number-checking');
            $('.invalid-feedback, .valid-feedback').text('');
            $('#validation-status').empty();
            updateSubmitButtonState();
        }
    </script>
</body>
</html>
