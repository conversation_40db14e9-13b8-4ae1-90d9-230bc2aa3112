<?php
/**
 * Debug CSV Per Receipt Export
 * ทดสอบ export_csv_per_receipt.php เพื่อหาสาเหตุหน้าขาว
 * ลบไฟล์นี้หลังจากทดสอบเสร็จแล้ว
 */

// Enable error reporting
error_reporting(E_ALL);
ini_set('display_errors', 1);
ini_set('log_errors', 1);

echo "<h2>Debug CSV Per Receipt Export</h2>";
echo "<p>Testing export_csv_per_receipt.php step by step...</p>";

try {
    echo "<h3>1. Session Check</h3>";
    session_start();
    if (!isset($_SESSION['user_id'])) {
        echo "❌ <strong>ERROR:</strong> User not logged in<br>";
        echo "<a href='login.php'>Please login first</a><br>";
        exit();
    }
    echo "✅ User logged in: " . $_SESSION['user_id'] . " (Role: " . ($_SESSION['role'] ?? 'unknown') . ")<br>";

    echo "<h3>2. Database Connection</h3>";
    require_once 'config/database.php';
    $database = new Database();
    $db = $database->getConnection();
    echo "✅ Database connected successfully<br>";

    echo "<h3>3. Functions Include</h3>";
    if (file_exists('includes/functions.php')) {
        require_once 'includes/functions.php';
        echo "✅ Functions included successfully<br>";
    } else {
        echo "⚠️ Warning: functions.php not found (but may not be needed)<br>";
    }

    echo "<h3>4. Parameters Check</h3>";
    $search = $_GET['search'] ?? '';
    $status_filter = $_GET['status'] ?? '';
    $date_from = $_GET['date_from'] ?? '';
    $date_to = $_GET['date_to'] ?? '';
    
    echo "Search: " . ($search ?: 'none') . "<br>";
    echo "Status: " . ($status_filter ?: 'none') . "<br>";
    echo "Date From: " . ($date_from ?: 'none') . "<br>";
    echo "Date To: " . ($date_to ?: 'none') . "<br>";

    echo "<h3>5. User Role Check</h3>";
    $user_id = $_SESSION['user_id'];
    $user_role = $_SESSION['role'];
    echo "User ID: $user_id<br>";
    echo "User Role: $user_role<br>";

    echo "<h3>6. WHERE Conditions Build</h3>";
    $where_conditions = [];
    $params = [];

    // Role-based access
    if ($user_role === 'data_entry') {
        $where_conditions[] = 'e.created_by = ?';
        $params[] = $user_id;
        echo "✅ Data entry filter added<br>";
    }

    // Search filter
    if (!empty($search)) {
        $where_conditions[] = '(e.exno LIKE ? OR e.bookingno LIKE ? OR i.name LIKE ? OR c.name LIKE ? OR d.name LIKE ?)';
        $search_param = '%' . $search . '%';
        $params = array_merge($params, [$search_param, $search_param, $search_param, $search_param, $search_param]);
        echo "✅ Search filter added: $search<br>";
    }

    // Status filter
    if (!empty($status_filter)) {
        $where_conditions[] = 'e.status = ?';
        $params[] = $status_filter;
        echo "✅ Status filter added: $status_filter<br>";
    }

    // Date range filter
    if (!empty($date_from)) {
        $where_conditions[] = 'e.job_open_date >= ?';
        $params[] = $date_from;
        echo "✅ Date from filter added: $date_from<br>";
    }

    if (!empty($date_to)) {
        $where_conditions[] = 'e.job_open_date <= ?';
        $params[] = $date_to;
        echo "✅ Date to filter added: $date_to<br>";
    }

    $where_clause = !empty($where_conditions) ? 'WHERE ' . implode(' AND ', $where_conditions) : '';
    echo "WHERE clause: " . ($where_clause ?: 'none') . "<br>";
    echo "Parameters: " . implode(', ', $params) . "<br>";

    echo "<h3>7. Check transfer_amount Column</h3>";
    $stmt = $db->prepare("SHOW COLUMNS FROM expenses LIKE 'transfer_amount'");
    $stmt->execute();
    $transfer_amount_exists = $stmt->rowCount() > 0;
    echo "Transfer amount column exists: " . ($transfer_amount_exists ? 'Yes' : 'No') . "<br>";

    $transfer_amount_field = $transfer_amount_exists ? 'e.transfer_amount' : '0 as transfer_amount';
    echo "Transfer amount field: $transfer_amount_field<br>";

    echo "<h3>8. Test Query</h3>";
    $test_sql = "
        SELECT 
            e.id as expense_id,
            e.exno,
            e.job_open_date,
            e.bookingno,
            e.containerno,
            e.vehicle_plate,
            e.payment_account_no,
            e.additional_details,
            e.requester,
            e.receiver,
            e.payer,
            e.withdrawal_date,
            e.transfer_no,
            $transfer_amount_field,
            COALESCE(e.total_amount, 0) as total_amount,
            e.status,
            e.created_at,
            i.name as item_name,
            c.name as customer_name,
            d.name as driver_name,
            u.full_name as created_by_name,
            rn.receipt_number,
            rn.amount as receipt_amount,
            rn.description as receipt_description,
            ROW_NUMBER() OVER (PARTITION BY e.id ORDER BY rn.id) as receipt_row_num
        FROM expenses e
        LEFT JOIN items i ON e.item_id = i.id
        LEFT JOIN customers c ON e.customer_id = c.id
        LEFT JOIN drivers d ON e.driver_id = d.id
        LEFT JOIN users u ON e.created_by = u.id
        LEFT JOIN receipt_numbers rn ON e.id = rn.expense_id
        $where_clause
        ORDER BY e.created_at DESC, rn.id ASC
        LIMIT 5
    ";

    echo "<details><summary>Click to see SQL query</summary><pre>" . htmlspecialchars($test_sql) . "</pre></details>";

    $stmt = $db->prepare($test_sql);
    $stmt->execute($params);
    $results = $stmt->fetchAll();

    echo "Query executed successfully<br>";
    echo "Results count: " . count($results) . "<br>";

    if (count($results) > 0) {
        echo "<h3>9. Sample Data (First 3 rows)</h3>";
        echo "<table border='1' style='border-collapse: collapse; font-size: 12px;'>";
        echo "<tr>";
        echo "<th>Expense ID</th><th>Expense No</th><th>Receipt Number</th><th>Receipt Amount</th><th>Status</th>";
        echo "</tr>";
        
        $count = 0;
        foreach ($results as $row) {
            if ($count >= 3) break;
            echo "<tr>";
            echo "<td>" . htmlspecialchars($row['expense_id']) . "</td>";
            echo "<td>" . htmlspecialchars($row['exno']) . "</td>";
            echo "<td>" . htmlspecialchars($row['receipt_number'] ?? 'N/A') . "</td>";
            echo "<td>" . htmlspecialchars($row['receipt_amount'] ?? 'N/A') . "</td>";
            echo "<td>" . htmlspecialchars($row['status']) . "</td>";
            echo "</tr>";
            $count++;
        }
        echo "</table>";
    } else {
        echo "⚠️ No data found with current filters<br>";
    }

    echo "<h3>10. Test CSV Generation</h3>";
    echo "✅ All checks passed! CSV generation should work.<br>";
    
    echo "<h3>11. Test Links</h3>";
    $current_params = $_GET;
    $query_string = http_build_query($current_params);
    
    echo "<a href='api/export_csv_per_receipt.php?" . $query_string . "' target='_blank' class='btn btn-success'>
        🔗 Test CSV Export (Per Receipt)
    </a><br><br>";
    
    echo "<a href='api/export_csv.php?" . $query_string . "' target='_blank' class='btn btn-info'>
        🔗 Test CSV Export (Regular)
    </a><br><br>";

    echo "<h3>12. Possible Issues</h3>";
    echo "<ul>";
    echo "<li>✅ Session: OK</li>";
    echo "<li>✅ Database: OK</li>";
    echo "<li>✅ Query: OK</li>";
    echo "<li>⚠️ Check: Browser might be blocking CSV download</li>";
    echo "<li>⚠️ Check: Server might have output buffering issues</li>";
    echo "<li>⚠️ Check: PHP memory limit for large datasets</li>";
    echo "</ul>";

} catch (Exception $e) {
    echo "<h3>❌ ERROR FOUND:</h3>";
    echo "<div style='background: #ffebee; padding: 10px; border: 1px solid #f44336; border-radius: 4px;'>";
    echo "<strong>Error:</strong> " . htmlspecialchars($e->getMessage()) . "<br>";
    echo "<strong>File:</strong> " . htmlspecialchars($e->getFile()) . "<br>";
    echo "<strong>Line:</strong> " . $e->getLine() . "<br>";
    echo "</div>";
    
    echo "<h4>Stack Trace:</h4>";
    echo "<pre>" . htmlspecialchars($e->getTraceAsString()) . "</pre>";
}

echo "<hr>";
echo "<p><strong>หมายเหตุ:</strong> ลบไฟล์ debug_csv_per_receipt.php หลังจากทดสอบเสร็จแล้ว</p>";
echo "<a href='reports/'>← กลับไปหน้า Reports</a>";
?>

<style>
body { font-family: Arial, sans-serif; margin: 20px; }
h2, h3 { color: #333; }
.btn { padding: 8px 16px; margin: 5px; text-decoration: none; border-radius: 4px; display: inline-block; }
.btn-success { background: #28a745; color: white; }
.btn-info { background: #17a2b8; color: white; }
table { margin: 10px 0; }
th, td { padding: 5px; text-align: left; }
details { margin: 10px 0; }
</style>
