<?php
/**
 * Database Configuration
 */

class Database {
    private $host;
    private $db_name;
    private $username;
    private $password;
    private $conn;

    public function __construct() {
        // Detect environment based on server name
        $server_name = $_SERVER['SERVER_NAME'] ?? $_SERVER['HTTP_HOST'] ?? 'localhost';

        if (strpos($server_name, 'nkslgroup.com') !== false) {
            // Production environment
            $this->host = 'localhost';
            $this->db_name = 'nksl_expense';
            $this->username = 'nksl_expense';
            $this->password = '%Kpr@lgAbgp28Fx9';
        } else {
            // Local development environment
            $this->host = 'localhost';
            $this->db_name = 'expenses_system';
            $this->username = 'root';
            $this->password = 'root';
        }
    }

    public function getConnection() {
        $this->conn = null;
        
        try {
            $this->conn = new PDO(
                "mysql:host=" . $this->host . ";dbname=" . $this->db_name,
                $this->username,
                $this->password,
                array(
                    PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
                    PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
                    PDO::MYSQL_ATTR_INIT_COMMAND => "SET NAMES utf8"
                )
            );
        } catch(PDOException $exception) {
            echo "Connection error: " . $exception->getMessage();
        }
        
        return $this->conn;
    }
}
?>
