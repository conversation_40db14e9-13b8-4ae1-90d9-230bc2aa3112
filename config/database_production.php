<?php
/**
 * Production Database Configuration
 * For PHP 8.1.33 + MariaDB 5.5.68
 */

class Database {
    // Production database settings - UPDATE THESE VALUES
    private $host = 'localhost';
    private $db_name = 'expenses_system';
    private $username = 'nksl_expense';
    private $password = '%Kpr@lgAbgp28Fx9';
    private $port = 3306;
    private $conn;

    public function getConnection() {
        $this->conn = null;
        
        try {
            // MariaDB 5.5.68 compatible connection
            $dsn = "mysql:host=" . $this->host . ";port=" . $this->port . ";dbname=" . $this->db_name . ";charset=utf8";
            
            $this->conn = new PDO(
                $dsn,
                $this->username,
                $this->password,
                array(
                    PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
                    PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
                    PDO::MYSQL_ATTR_INIT_COMMAND => "SET NAMES utf8",
                    PDO::ATTR_EMULATE_PREPARES => false,
                    PDO::ATTR_STRINGIFY_FETCHES => false,
                    // MariaDB 5.5.68 specific settings
                    PDO::MYSQL_ATTR_USE_BUFFERED_QUERY => true,
                    PDO::ATTR_PERSISTENT => false
                )
            );
            
            // Set SQL mode for compatibility
            $this->conn->exec("SET sql_mode = 'TRADITIONAL'");
            
        } catch(PDOException $exception) {
            error_log("Database connection error: " . $exception->getMessage());
            throw new Exception("Database connection failed. Please check your configuration.");
        }
        
        return $this->conn;
    }
    
    /**
     * Test database connection and compatibility
     */
    public function testConnection() {
        try {
            $conn = $this->getConnection();
            
            // Test basic query
            $stmt = $conn->query("SELECT 1 as test");
            $result = $stmt->fetch();
            
            if ($result['test'] !== 1) {
                throw new Exception("Basic query test failed");
            }
            
            // Get database version
            $stmt = $conn->query("SELECT VERSION() as version");
            $version = $stmt->fetch()['version'];
            
            // Check if MariaDB
            $is_mariadb = stripos($version, 'mariadb') !== false;
            
            return [
                'success' => true,
                'version' => $version,
                'is_mariadb' => $is_mariadb,
                'message' => 'Database connection successful'
            ];
            
        } catch (Exception $e) {
            return [
                'success' => false,
                'error' => $e->getMessage()
            ];
        }
    }
    
    /**
     * Check if required tables exist
     */
    public function checkTables() {
        try {
            $conn = $this->getConnection();
            
            $required_tables = [
                'users', 'items', 'customers', 'drivers', 'expenses',
                'activity_logs', 'receipt_numbers', 'batch_operations',
                'batch_items', 'batch_documents', 'batch_performance_logs'
            ];
            
            $existing_tables = [];
            $missing_tables = [];
            
            foreach ($required_tables as $table) {
                $stmt = $conn->prepare("SHOW TABLES LIKE ?");
                $stmt->execute([$table]);
                
                if ($stmt->rowCount() > 0) {
                    $existing_tables[] = $table;
                } else {
                    $missing_tables[] = $table;
                }
            }
            
            return [
                'existing' => $existing_tables,
                'missing' => $missing_tables,
                'all_exist' => empty($missing_tables)
            ];
            
        } catch (Exception $e) {
            return [
                'error' => $e->getMessage()
            ];
        }
    }
    
    /**
     * Get database statistics
     */
    public function getStats() {
        try {
            $conn = $this->getConnection();
            
            $stats = [];
            
            // Count records in main tables
            $tables = ['users', 'expenses', 'items', 'customers', 'drivers', 'activity_logs'];
            
            foreach ($tables as $table) {
                try {
                    $stmt = $conn->query("SELECT COUNT(*) as count FROM {$table}");
                    $stats[$table] = $stmt->fetch()['count'];
                } catch (Exception $e) {
                    $stats[$table] = 'Error: ' . $e->getMessage();
                }
            }
            
            return $stats;
            
        } catch (Exception $e) {
            return ['error' => $e->getMessage()];
        }
    }
}

/**
 * Production-specific database helper functions
 */

/**
 * Safe JSON encode/decode for MariaDB 5.5.68
 */
function safe_json_encode($data) {
    $json = json_encode($data, JSON_UNESCAPED_UNICODE);
    if ($json === false) {
        throw new Exception('JSON encoding failed: ' . json_last_error_msg());
    }
    return $json;
}

function safe_json_decode($json, $assoc = true) {
    if (empty($json) || $json === 'null') {
        return $assoc ? [] : null;
    }
    
    $data = json_decode($json, $assoc);
    if (json_last_error() !== JSON_ERROR_NONE) {
        throw new Exception('JSON decoding failed: ' . json_last_error_msg());
    }
    
    return $data;
}

/**
 * Count JSON array elements without JSON_LENGTH function
 */
function count_json_array($json_string) {
    if (empty($json_string) || $json_string === '[]' || $json_string === 'null') {
        return 0;
    }
    
    try {
        $array = safe_json_decode($json_string);
        return is_array($array) ? count($array) : 0;
    } catch (Exception $e) {
        // Fallback: count commas + 1 for simple arrays
        if (strpos($json_string, ',') !== false) {
            return substr_count($json_string, ',') + 1;
        }
        return 1;
    }
}

/**
 * Production error logging
 */
function log_production_error($message, $context = []) {
    $log_entry = [
        'timestamp' => date('Y-m-d H:i:s'),
        'message' => $message,
        'context' => $context,
        'server' => $_SERVER['SERVER_NAME'] ?? 'unknown',
        'user_agent' => $_SERVER['HTTP_USER_AGENT'] ?? 'unknown'
    ];
    
    error_log('EXPENSES_SYSTEM: ' . json_encode($log_entry));
}
?>
