<?php
/**
 * Debug script to check batch data and file paths
 */

session_start();
require_once 'config/database.php';

// Mock session for testing
$_SESSION['user_id'] = 1;
$_SESSION['role'] = 'administrator';

$database = new Database();
$db = $database->getConnection();

echo "<h1>🔍 Debug Batch Data</h1>";

// Check batch_documents table structure and data
echo "<h2>1. Batch Documents Table</h2>";

try {
    $stmt = $db->query("SELECT * FROM batch_documents ORDER BY id DESC LIMIT 5");
    $batch_docs = $stmt->fetchAll();
    
    if (count($batch_docs) > 0) {
        echo "<p>✅ Found " . count($batch_docs) . " batch documents</p>";
        
        echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
        echo "<tr><th>ID</th><th>Batch ID</th><th>Type</th><th>File Path</th><th>Original Name</th><th>File Exists?</th></tr>";
        
        foreach ($batch_docs as $doc) {
            $file_exists = false;
            $full_path = '';
            
            // Check different possible paths
            $possible_paths = [
                $doc['file_path'], // As stored in database
                'uploads/' . $doc['file_path'], // With uploads/ prefix
                'uploads/batch_documents/' . $doc['file_path'], // With batch_documents/ prefix
                'uploads/bulk_operations/' . $doc['file_path'] // Old path
            ];

            $file_exists = false;
            $full_path = '';

            foreach ($possible_paths as $path) {
                if (file_exists($path)) {
                    $file_exists = true;
                    $full_path = $path;
                    break;
                }
            }

            if (!$file_exists) {
                $full_path = $doc['file_path'] . ' (NOT FOUND)';
            }
            
            echo "<tr>";
            echo "<td>{$doc['id']}</td>";
            echo "<td>{$doc['batch_id']}</td>";
            echo "<td>{$doc['document_type']}</td>";
            echo "<td style='font-size: 11px;'>{$doc['file_path']}</td>";
            echo "<td>{$doc['original_filename']}</td>";
            echo "<td>" . ($file_exists ? '✅' : '❌') . " ({$full_path})</td>";
            echo "</tr>";
        }
        echo "</table>";
        
        // Test first document
        if (count($batch_docs) > 0) {
            $test_doc = $batch_docs[0];
            echo "<h3>Testing First Document:</h3>";
            echo "<p><strong>File Path:</strong> {$test_doc['file_path']}</p>";
            echo "<p><strong>Original Name:</strong> {$test_doc['original_filename']}</p>";
            
            // Test different URL constructions
            $test_urls = [
                "api/view_file.php?file=" . urlencode($test_doc['file_path']) . "&type=batch_document",
                "api/view_file.php?file=" . urlencode(basename($test_doc['file_path'])) . "&type=batch_document"
            ];
            
            echo "<h4>Test URLs:</h4>";
            foreach ($test_urls as $i => $url) {
                echo "<p>" . ($i + 1) . ". <a href='http://localhost:82/expenses_system/{$url}' target='_blank'>{$url}</a></p>";
            }
        }
        
    } else {
        echo "<p>⚠️ No batch documents found</p>";
    }
    
} catch (Exception $e) {
    echo "<p>❌ Error: " . $e->getMessage() . "</p>";
}

// Check expenses with batch IDs
echo "<h2>2. Expenses with Batch Processing</h2>";

try {
    $stmt = $db->query("
        SELECT 
            e.id, e.exno, e.status,
            e.batch_verification_id, 
            e.batch_review_id,
            e.verification_slip_image,
            e.reviewer_slip_image,
            c.name as customer_name,
            i.name as item_name
        FROM expenses e
        LEFT JOIN customers c ON e.customer_id = c.id
        LEFT JOIN items i ON e.item_id = i.id
        WHERE e.batch_verification_id IS NOT NULL 
           OR e.batch_review_id IS NOT NULL
        ORDER BY e.id DESC
        LIMIT 10
    ");
    
    $batch_expenses = $stmt->fetchAll();
    
    if (count($batch_expenses) > 0) {
        echo "<p>✅ Found " . count($batch_expenses) . " expenses with batch processing</p>";
        
        echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
        echo "<tr><th>ID</th><th>Expense No</th><th>Item</th><th>Customer</th><th>Status</th><th>Batch Ver</th><th>Batch Rev</th><th>Individual Slips</th><th>View Link</th></tr>";
        
        foreach ($batch_expenses as $exp) {
            echo "<tr>";
            echo "<td>{$exp['id']}</td>";
            echo "<td>{$exp['exno']}</td>";
            echo "<td>{$exp['item_name']}</td>";
            echo "<td>{$exp['customer_name']}</td>";
            echo "<td>{$exp['status']}</td>";
            echo "<td style='font-size: 10px;'>" . ($exp['batch_verification_id'] ?: '-') . "</td>";
            echo "<td style='font-size: 10px;'>" . ($exp['batch_review_id'] ?: '-') . "</td>";
            echo "<td>";
            if ($exp['verification_slip_image']) echo "V ";
            if ($exp['reviewer_slip_image']) echo "R ";
            if (!$exp['verification_slip_image'] && !$exp['reviewer_slip_image']) echo "-";
            echo "</td>";
            echo "<td><a href='http://localhost:82/expenses_system/expenses/view.php?id={$exp['id']}' target='_blank'>View</a></td>";
            echo "</tr>";
        }
        echo "</table>";
        
    } else {
        echo "<p>⚠️ No expenses with batch processing found</p>";
    }
    
} catch (Exception $e) {
    echo "<p>❌ Error: " . $e->getMessage() . "</p>";
}

// Check batch operations
echo "<h2>3. Batch Operations</h2>";

try {
    $stmt = $db->query("
        SELECT 
            bo.*,
            COUNT(bd.id) as document_count
        FROM batch_operations bo
        LEFT JOIN batch_documents bd ON bo.batch_id = bd.batch_id
        GROUP BY bo.batch_id
        ORDER BY bo.created_at DESC
        LIMIT 5
    ");
    
    $batch_ops = $stmt->fetchAll();
    
    if (count($batch_ops) > 0) {
        echo "<p>✅ Found " . count($batch_ops) . " batch operations</p>";
        
        echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
        echo "<tr><th>Batch ID</th><th>Type</th><th>Status</th><th>Items</th><th>Documents</th><th>Created</th></tr>";
        
        foreach ($batch_ops as $op) {
            echo "<tr>";
            echo "<td style='font-size: 10px;'>{$op['batch_id']}</td>";
            echo "<td>{$op['operation_type']}</td>";
            echo "<td>{$op['status']}</td>";
            echo "<td>{$op['total_items']}</td>";
            echo "<td>{$op['document_count']}</td>";
            echo "<td>" . date('M d, H:i', strtotime($op['created_at'])) . "</td>";
            echo "</tr>";
        }
        echo "</table>";
        
    } else {
        echo "<p>⚠️ No batch operations found</p>";
    }
    
} catch (Exception $e) {
    echo "<p>❌ Error: " . $e->getMessage() . "</p>";
}

// File system check
echo "<h2>4. File System Check</h2>";

$upload_dirs = [
    'uploads/batch_documents/',
    'uploads/batch_documents/verification/',
    'uploads/batch_documents/review/',
    'uploads/bulk_operations/',
    'uploads/verification_slips/',
    'uploads/reviewer_slips/',
    'uploads/receipts/',
    'uploads/transfer_slips/'
];

foreach ($upload_dirs as $dir) {
    if (is_dir($dir)) {
        $files = scandir($dir);
        $file_count = count($files) - 2; // Remove . and ..
        echo "<p>✅ {$dir}: {$file_count} files</p>";
        
        if (($dir === 'uploads/batch_documents/' || $dir === 'uploads/bulk_operations/') && $file_count > 0) {
            echo "<ul>";
            foreach ($files as $file) {
                if ($file !== '.' && $file !== '..') {
                    $file_size = filesize($dir . $file);
                    echo "<li>{$file} (" . number_format($file_size) . " bytes)</li>";
                }
            }
            echo "</ul>";
        }
    } else {
        echo "<p>❌ {$dir}: Directory not found</p>";
    }
}

echo "<h2>✅ Debug Complete</h2>";
echo "<p><a href='http://localhost:82/expenses_system/test_batch_documents.php' target='_blank'>Run Full Test</a></p>";
?>
