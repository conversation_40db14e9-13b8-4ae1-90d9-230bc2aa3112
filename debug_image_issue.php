<?php
require_once 'config/database.php';

// Check expense data
$database = new Database();
$db = $database->getConnection();

$expense_id = 152;

echo "<h2>Debug Image Issue for Expense ID: $expense_id</h2>";

// Get expense data
$stmt = $db->prepare('SELECT id, exno, transfer_slip_image, transfer_amount, created_at FROM expenses WHERE id = ?');
$stmt->execute([$expense_id]);
$expense = $stmt->fetch();

if ($expense) {
    echo "<h3>Expense Data:</h3>";
    echo "<ul>";
    echo "<li>Expense ID: " . $expense['id'] . "</li>";
    echo "<li>Expense No: " . $expense['exno'] . "</li>";
    echo "<li>Transfer Slip Image: " . $expense['transfer_slip_image'] . "</li>";
    echo "<li>Transfer Amount: " . $expense['transfer_amount'] . "</li>";
    echo "<li>Created At: " . $expense['created_at'] . "</li>";
    echo "</ul>";
    
    // Check if file exists
    $filename = $expense['transfer_slip_image'];
    if ($filename) {
        $file_path = 'uploads/transfer_slips/' . $filename;
        echo "<h3>File Check:</h3>";
        echo "<ul>";
        echo "<li>Expected file path: " . $file_path . "</li>";
        echo "<li>File exists: " . (file_exists($file_path) ? 'YES' : 'NO') . "</li>";
        
        if (file_exists($file_path)) {
            echo "<li>File size: " . filesize($file_path) . " bytes</li>";
            echo "<li>File permissions: " . substr(sprintf('%o', fileperms($file_path)), -4) . "</li>";
            echo "<li>File readable: " . (is_readable($file_path) ? 'YES' : 'NO') . "</li>";
        } else {
            // Search for similar files
            echo "<li>Searching for similar files...</li>";
            $search_pattern = 'uploads/transfer_slips/*' . substr($filename, 0, 10) . '*';
            $similar_files = glob($search_pattern);
            if ($similar_files) {
                echo "<li>Similar files found:</li>";
                echo "<ul>";
                foreach ($similar_files as $file) {
                    echo "<li>" . $file . "</li>";
                }
                echo "</ul>";
            } else {
                echo "<li>No similar files found</li>";
            }
        }
        echo "</ul>";
        
        // Test the view_file.php URL
        echo "<h3>URL Test:</h3>";
        $test_url = "api/view_file.php?file=" . urlencode($filename) . "&type=transfer_slip";
        echo "<p>Test URL: <a href='$test_url' target='_blank'>$test_url</a></p>";
        
        // Check directory permissions
        echo "<h3>Directory Permissions:</h3>";
        $upload_dir = 'uploads/transfer_slips/';
        echo "<ul>";
        echo "<li>Directory exists: " . (is_dir($upload_dir) ? 'YES' : 'NO') . "</li>";
        echo "<li>Directory readable: " . (is_readable($upload_dir) ? 'YES' : 'NO') . "</li>";
        echo "<li>Directory permissions: " . substr(sprintf('%o', fileperms($upload_dir)), -4) . "</li>";
        echo "</ul>";
        
        // List recent files in directory
        echo "<h3>Recent Files in Directory:</h3>";
        $files = glob($upload_dir . '*');
        usort($files, function($a, $b) {
            return filemtime($b) - filemtime($a);
        });
        echo "<ul>";
        foreach (array_slice($files, 0, 10) as $file) {
            $basename = basename($file);
            echo "<li>$basename (modified: " . date('Y-m-d H:i:s', filemtime($file)) . ")</li>";
        }
        echo "</ul>";
    } else {
        echo "<p>No transfer slip image filename in database</p>";
    }
} else {
    echo "<p>Expense not found</p>";
}

// Check if there are any files with similar timestamp
echo "<h3>Files with Similar Timestamp:</h3>";
$timestamp_part = '1761305128';
$all_files = glob('uploads/*/' . '*' . $timestamp_part . '*');
if ($all_files) {
    echo "<ul>";
    foreach ($all_files as $file) {
        echo "<li>$file</li>";
    }
    echo "</ul>";
} else {
    echo "<p>No files found with timestamp $timestamp_part</p>";
}
?>
