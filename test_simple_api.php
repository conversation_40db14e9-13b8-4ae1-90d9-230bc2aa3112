<?php
session_start();
$_SESSION['user_id'] = 1;
$_SESSION['role'] = 'administrator';

// Test the exact file path you mentioned
$test_file = "batch_documents/review/BATCH_REV_20251019_175531_review_68f526245e645_1760896548.png";

echo "<h1>🧪 Simple API Test</h1>";

echo "<h2>File Path Test</h2>";
echo "<p><strong>Testing file:</strong> {$test_file}</p>";

// Check if file exists in different locations
$paths_to_check = [
    $test_file,
    "uploads/" . $test_file,
    "uploads/batch_documents/review/BATCH_REV_20251019_175531_review_68f526245e645_1760896548.png"
];

echo "<h3>File Existence Check:</h3>";
foreach ($paths_to_check as $path) {
    $exists = file_exists($path);
    $size = $exists ? filesize($path) : 0;
    echo "<p>" . ($exists ? '✅' : '❌') . " {$path}" . ($exists ? " (" . number_format($size) . " bytes)" : "") . "</p>";
}

// Test API URL
$api_url = "api/view_file.php?file=" . urlencode($test_file) . "&type=batch_document";
echo "<h3>API Test:</h3>";
echo "<p><a href='http://localhost:82/expenses_system/{$api_url}' target='_blank'>🔗 {$api_url}</a></p>";

// Test with different file parameter
$api_url2 = "api/view_file.php?file=" . urlencode("uploads/" . $test_file) . "&type=batch_document";
echo "<p><a href='http://localhost:82/expenses_system/{$api_url2}' target='_blank'>🔗 {$api_url2}</a></p>";

// Show what the API logic would do
echo "<h3>API Logic Simulation:</h3>";
$file = $test_file;
$upload_dir = '../uploads/';

$possible_paths = [
    '../' . $file, // If file already has uploads/ prefix
    $upload_dir . $file, // Direct path from database
    $upload_dir . 'batch_documents/' . $file, // With batch_documents/ prefix
    $upload_dir . 'bulk_operations/' . $file // Fallback to old path
];

echo "<p><strong>API will try these paths in order:</strong></p>";
echo "<ol>";
foreach ($possible_paths as $i => $path) {
    $exists = file_exists($path);
    echo "<li>" . ($exists ? '✅' : '❌') . " {$path}</li>";
    if ($exists) {
        echo "<p><strong>✅ API will use this path!</strong></p>";
        break;
    }
}
echo "</ol>";

// Test actual API call
echo "<h3>Actual API Call Test:</h3>";
$full_api_url = "http://localhost:82/expenses_system/api/view_file.php?file=" . urlencode($test_file) . "&type=batch_document";

$ch = curl_init();
curl_setopt($ch, CURLOPT_URL, $full_api_url);
curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
curl_setopt($ch, CURLOPT_HEADER, true);
curl_setopt($ch, CURLOPT_NOBODY, true); // HEAD request only
curl_setopt($ch, CURLOPT_FOLLOWLOCATION, true);
curl_setopt($ch, CURLOPT_TIMEOUT, 10);
curl_setopt($ch, CURLOPT_COOKIE, session_name() . '=' . session_id());

$response = curl_exec($ch);
$http_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
$content_type = curl_getinfo($ch, CURLINFO_CONTENT_TYPE);
curl_close($ch);

echo "<p><strong>HTTP Response Code:</strong> {$http_code}</p>";
echo "<p><strong>Content Type:</strong> {$content_type}</p>";

if ($http_code == 200) {
    echo "<p>✅ <strong>SUCCESS!</strong> File is accessible via API</p>";
    echo "<p><a href='{$full_api_url}' target='_blank'>🖼️ View Image</a></p>";
} else {
    echo "<p>❌ <strong>FAILED!</strong> File not accessible</p>";
    echo "<pre>" . htmlspecialchars($response) . "</pre>";
}

echo "<hr>";
echo "<p><a href='http://localhost:82/expenses_system/debug_batch_data.php' target='_blank'>🔍 Debug Batch Data</a></p>";
echo "<p><a href='http://localhost:82/expenses_system/test_api_view_file.php' target='_blank'>🧪 Full API Test</a></p>";
?>
