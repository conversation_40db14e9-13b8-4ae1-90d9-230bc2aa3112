<?php
session_start();
require_once 'config/database.php';

// Mock session for testing
$_SESSION['user_id'] = 1;
$_SESSION['username'] = 'test_user';
$_SESSION['role'] = 'admin';
$_SESSION['name'] = 'Test User';

$database = new Database();
$db = $database->getConnection();

$expense_id = 1;

echo "<h2>🧮 Test Receipt Deductions Display</h2>";

// Get expense data
$stmt = $db->prepare("SELECT * FROM expenses WHERE id = ?");
$stmt->execute([$expense_id]);
$expense = $stmt->fetch();

if (!$expense) {
    echo "❌ Expense not found<br>";
    exit;
}

echo "<h3>📄 Expense: " . htmlspecialchars($expense['exno']) . "</h3>";

// Get receipt numbers
$stmt = $db->prepare("SELECT * FROM receipt_numbers WHERE expense_id = ? ORDER BY id");
$stmt->execute([$expense_id]);
$receipt_numbers = $stmt->fetchAll();

echo "<h3>📋 Receipt Numbers: " . count($receipt_numbers) . "</h3>";

// Get receipt deductions
$receipt_deductions = [];
if (!empty($receipt_numbers)) {
    $receipt_ids = array_column($receipt_numbers, 'id');
    $placeholders = str_repeat('?,', count($receipt_ids) - 1) . '?';
    $stmt = $db->prepare("SELECT * FROM receipt_deductions WHERE receipt_number_id IN ($placeholders) ORDER BY receipt_number_id, id");
    $stmt->execute($receipt_ids);
    $deductions = $stmt->fetchAll();
    
    // Group deductions by receipt_number_id
    foreach ($deductions as $deduction) {
        $receipt_deductions[$deduction['receipt_number_id']][] = $deduction;
    }
}

echo "<h3>🧮 Receipt Deductions: " . count($receipt_deductions) . " receipts with deductions</h3>";

// Display the Receipt Deductions section (same as in view.php)
if (!empty($receipt_deductions)) {
    echo "<div style='border: 2px solid #007bff; padding: 20px; margin: 20px 0; background: #f8f9fa;'>";
    echo "<h4><i class='fas fa-calculator'></i> Receipt Deductions</h4>";
    
    $total_deduction_count = 0;
    foreach ($receipt_deductions as $deductions) {
        $total_deduction_count += count($deductions);
    }
    echo "<span style='background: #007bff; color: white; padding: 2px 8px; border-radius: 3px;'>$total_deduction_count รายการ</span>";
    
    foreach ($receipt_numbers as $receipt) {
        if (isset($receipt_deductions[$receipt['id']])) {
            echo "<div style='border-left: 4px solid #007bff; padding-left: 15px; margin: 15px 0;'>";
            echo "<h5 style='color: #007bff;'>📄 Receipt: " . htmlspecialchars($receipt['receipt_number']) . " <small>(" . count($receipt_deductions[$receipt['id']]) . " deductions)</small></h5>";
            
            echo "<div style='display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 15px;'>";
            foreach ($receipt_deductions[$receipt['id']] as $deduction) {
                echo "<div style='border: 1px solid #dee2e6; border-radius: 5px; padding: 15px; background: white;'>";
                
                $deduction_types = [
                    'tax_vat' => 'VAT Tax',
                    'tax_withholding' => 'Withholding Tax', 
                    'service_fee' => 'Service Fee',
                    'discount' => 'Discount',
                    'penalty' => 'Penalty',
                    'commission' => 'Commission',
                    'other' => 'Other'
                ];
                
                echo "<div style='display: flex; justify-content: space-between; align-items: center; margin-bottom: 10px;'>";
                echo "<h6 style='margin: 0; color: #333;'>" . ($deduction_types[$deduction['deduction_type']] ?? ucfirst($deduction['deduction_type'])) . "</h6>";
                echo "<span style='background: #dc3545; color: white; padding: 2px 8px; border-radius: 3px; font-weight: bold;'>-" . number_format($deduction['amount'], 2) . " ฿</span>";
                echo "</div>";
                
                if (!empty($deduction['description'])) {
                    echo "<p style='color: #6c757d; font-size: 0.9em; margin-bottom: 10px;'>" . htmlspecialchars($deduction['description']) . "</p>";
                }
                
                if (!empty($deduction['deduction_image'])) {
                    echo "<div style='margin-top: 10px;'>";
                    echo "<img src='../api/view_file.php?file=" . urlencode($deduction['deduction_image']) . "&type=deduction' style='max-width: 100px; max-height: 80px; border: 1px solid #ddd; border-radius: 3px; cursor: pointer;' alt='Deduction Image'>";
                    echo "<br><small style='color: #6c757d;'><i class='fas fa-image'></i> Click to view</small>";
                    echo "</div>";
                }
                
                echo "</div>";
            }
            echo "</div>";
            echo "</div>";
        }
    }
    echo "</div>";
} else {
    echo "<div style='border: 2px solid #dc3545; padding: 20px; margin: 20px 0; background: #fff5f5;'>";
    echo "<h4 style='color: #dc3545;'>❌ No Receipt Deductions Found</h4>";
    echo "<p>The Receipt Deductions section will not show because no deductions were found for this expense.</p>";
    echo "</div>";
}

echo "<h3>🔗 Links:</h3>";
echo "<p><a href='expenses/view.php?id=1' target='_blank'>View Original Page (requires login)</a></p>";
echo "<p><a href='debug_view_deductions.php' target='_blank'>Debug Deductions Logic</a></p>";
?>
