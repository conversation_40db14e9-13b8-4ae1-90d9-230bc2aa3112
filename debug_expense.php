<?php
require_once 'config/database.php';

$database = new Database();
$db = $database->getConnection();

// Get the latest expense
$stmt = $db->query("SELECT * FROM expenses ORDER BY id DESC LIMIT 1");
$expense = $stmt->fetch();

echo "<h2>Latest Expense Debug Info</h2>";

if ($expense) {
    echo "<h3>Expense ID: " . $expense['id'] . "</h3>";
    echo "<p><strong>Expense No:</strong> " . htmlspecialchars($expense['exno']) . "</p>";
    echo "<p><strong>Transfer Slip Image:</strong> " . htmlspecialchars($expense['transfer_slip_image']) . "</p>";
    echo "<p><strong>Receipt Images (JSON):</strong> " . htmlspecialchars($expense['receipt_images']) . "</p>";
    
    // Parse receipt images
    $receipt_images = json_decode($expense['receipt_images'], true);
    echo "<h4>Parsed Receipt Images:</h4>";
    if ($receipt_images) {
        echo "<pre>" . print_r($receipt_images, true) . "</pre>";
    } else {
        echo "<p>No receipt images or invalid JSON</p>";
    }
    
    // Check if files exist
    echo "<h4>File Existence Check:</h4>";
    if (!empty($expense['transfer_slip_image'])) {
        $transfer_path = 'uploads/transfer_slips/' . $expense['transfer_slip_image'];
        echo "<p><strong>Transfer Slip:</strong> " . $transfer_path . " - " . (file_exists($transfer_path) ? "EXISTS" : "NOT FOUND") . "</p>";
    }
    
    if ($receipt_images) {
        foreach ($receipt_images as $index => $receipt) {
            $receipt_path = 'uploads/receipts/' . $receipt['filename'];
            echo "<p><strong>Receipt " . ($index + 1) . ":</strong> " . $receipt_path . " - " . (file_exists($receipt_path) ? "EXISTS" : "NOT FOUND") . "</p>";
            echo "<p><strong>Receipt Number:</strong> " . htmlspecialchars($receipt['receipt_no'] ?? 'Not set') . "</p>";
        }
    }
    
    // Show all expense data
    echo "<h4>Full Expense Data:</h4>";
    echo "<pre>" . print_r($expense, true) . "</pre>";
    
} else {
    echo "<p>No expenses found in database</p>";
}

// Check items and customers
echo "<h3>Items in Database:</h3>";
$items = $db->query("SELECT * FROM items ORDER BY id DESC LIMIT 5")->fetchAll();
echo "<pre>" . print_r($items, true) . "</pre>";

echo "<h3>Customers in Database:</h3>";
$customers = $db->query("SELECT * FROM customers ORDER BY id DESC LIMIT 5")->fetchAll();
echo "<pre>" . print_r($customers, true) . "</pre>";
?>
