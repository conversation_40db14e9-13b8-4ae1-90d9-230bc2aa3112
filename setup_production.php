<?php
/**
 * Production Setup Script for PHP 8.1.33 + MariaDB 5.5.68
 * This script sets up the database with compatibility fixes
 */

// Prevent running in production if already set up
if (file_exists('.production_setup_complete')) {
    die('Production setup already completed. Delete .production_setup_complete file to run again.');
}

?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Production Setup - Expenses System</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
</head>
<body class="bg-light">
    <div class="container mt-5">
        <div class="row justify-content-center">
            <div class="col-md-8">
                <div class="card shadow">
                    <div class="card-header bg-primary text-white">
                        <h3><i class="fas fa-server me-2"></i>Production Setup</h3>
                        <p class="mb-0">PHP 8.1.33 + MariaDB 5.5.68 Compatibility</p>
                    </div>
                    <div class="card-body">
                        
<?php
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    try {
        echo "<div class='alert alert-info'><i class='fas fa-cog fa-spin me-2'></i>Setting up production environment...</div>";
        
        // Include database configuration
        require_once 'config/database.php';
        
        $database = new Database();
        $db = $database->getConnection();
        
        echo "<div class='alert alert-success'><i class='fas fa-check me-2'></i>Database connection successful</div>";
        
        // Get database version
        $stmt = $db->query("SELECT VERSION() as version");
        $version = $stmt->fetch()['version'];
        echo "<p><strong>Database Version:</strong> {$version}</p>";
        
        // Run compatibility-fixed schema files
        $schema_files = [
            'database/schema.sql',
            'database/add_receipt_numbers_table.sql',
            'database/add_transfer_amount.sql',
            'database/add_verification_fields.sql',
            'database/add_reject_return_workflow.sql',
            'database/batch_operations_schema_mariadb55.sql',
            'database/fix_production_issues.sql'
        ];
        
        foreach ($schema_files as $file) {
            if (file_exists($file)) {
                echo "<p><i class='fas fa-database me-2'></i>Executing {$file}...</p>";
                
                $sql = file_get_contents($file);
                
                // Split SQL file into individual statements
                $statements = array_filter(
                    array_map('trim', explode(';', $sql)),
                    function($stmt) {
                        return !empty($stmt) && !preg_match('/^\s*--/', $stmt);
                    }
                );
                
                foreach ($statements as $statement) {
                    if (trim($statement)) {
                        try {
                            $db->exec($statement);
                        } catch (PDOException $e) {
                            // Ignore "already exists" errors
                            if (strpos($e->getMessage(), 'already exists') === false && 
                                strpos($e->getMessage(), 'Duplicate') === false) {
                                echo "<div class='alert alert-warning'><small>Warning in {$file}: " . $e->getMessage() . "</small></div>";
                            }
                        }
                    }
                }
                
                echo "<p class='text-success'><i class='fas fa-check me-2'></i>{$file} completed</p>";
            } else {
                echo "<p class='text-warning'><i class='fas fa-exclamation-triangle me-2'></i>{$file} not found</p>";
            }
        }
        
        // Create upload directories
        $upload_dirs = [
            'uploads',
            'uploads/transfer_slips',
            'uploads/receipts',
            'uploads/verification_slips',
            'uploads/reviewer_slips',
            'uploads/bulk_operations'
        ];
        
        echo "<h5><i class='fas fa-folder me-2'></i>Creating upload directories...</h5>";
        foreach ($upload_dirs as $dir) {
            if (!is_dir($dir)) {
                if (mkdir($dir, 0755, true)) {
                    echo "<p class='text-success'><i class='fas fa-check me-2'></i>Created {$dir}</p>";
                } else {
                    echo "<p class='text-danger'><i class='fas fa-times me-2'></i>Failed to create {$dir}</p>";
                }
            } else {
                echo "<p class='text-info'><i class='fas fa-info me-2'></i>{$dir} already exists</p>";
            }
        }
        
        // Test critical functions
        echo "<h5><i class='fas fa-vial me-2'></i>Testing critical functions...</h5>";
        
        // Test JSON handling (without JSON functions)
        try {
            $test_json = json_encode(['test' => 'data']);
            $test_decode = json_decode($test_json, true);
            echo "<p class='text-success'><i class='fas fa-check me-2'></i>PHP JSON functions working</p>";
        } catch (Exception $e) {
            echo "<p class='text-danger'><i class='fas fa-times me-2'></i>PHP JSON functions failed: " . $e->getMessage() . "</p>";
        }
        
        // Test image functions
        if (extension_loaded('gd')) {
            echo "<p class='text-success'><i class='fas fa-check me-2'></i>GD extension available for image processing</p>";
        } else {
            echo "<p class='text-warning'><i class='fas fa-exclamation-triangle me-2'></i>GD extension not available</p>";
        }
        
        // Create production marker file
        file_put_contents('.production_setup_complete', date('Y-m-d H:i:s') . "\nPHP: " . PHP_VERSION . "\nDB: " . $version);
        
        echo "<div class='alert alert-success mt-4'>";
        echo "<h5><i class='fas fa-check-circle me-2'></i>Production Setup Complete!</h5>";
        echo "<p>Your expenses system is now ready for production use.</p>";
        echo "<p><strong>Next steps:</strong></p>";
        echo "<ul>";
        echo "<li>Delete this setup file (setup_production.php) for security</li>";
        echo "<li>Update config/database.php with production credentials</li>";
        echo "<li>Set proper file permissions (755 for directories, 644 for files)</li>";
        echo "<li>Configure SSL certificate</li>";
        echo "<li>Set up regular database backups</li>";
        echo "</ul>";
        echo "</div>";
        
        echo "<div class='text-center mt-4'>";
        echo "<a href='index.php' class='btn btn-primary btn-lg'>";
        echo "<i class='fas fa-sign-in-alt me-2'></i>Go to Login";
        echo "</a>";
        echo "</div>";
        
    } catch (Exception $e) {
        echo "<div class='alert alert-danger'>";
        echo "<h5><i class='fas fa-exclamation-triangle me-2'></i>Setup Failed</h5>";
        echo "<p>Error: " . $e->getMessage() . "</p>";
        echo "</div>";
    }
} else {
    // Show setup form
?>
                        <div class="alert alert-info">
                            <h5><i class="fas fa-info-circle me-2"></i>Production Environment</h5>
                            <p>This setup script is optimized for:</p>
                            <ul class="mb-0">
                                <li><strong>PHP:</strong> 8.1.33</li>
                                <li><strong>Database:</strong> MariaDB 5.5.68</li>
                                <li><strong>Compatibility:</strong> No JSON functions, TEXT columns for JSON data</li>
                            </ul>
                        </div>
                        
                        <div class="alert alert-warning">
                            <h6><i class="fas fa-exclamation-triangle me-2"></i>Before Running Setup:</h6>
                            <ol class="mb-0">
                                <li>Update <code>config/database.php</code> with production database credentials</li>
                                <li>Ensure database exists and user has CREATE/ALTER privileges</li>
                                <li>Verify web server has write permissions to uploads directory</li>
                                <li>Backup any existing data</li>
                            </ol>
                        </div>
                        
                        <form method="POST" class="text-center">
                            <button type="submit" class="btn btn-primary btn-lg">
                                <i class="fas fa-play me-2"></i>Start Production Setup
                            </button>
                        </form>
                        
                        <div class="mt-4">
                            <h6>What this setup will do:</h6>
                            <ul class="small">
                                <li>Create all required database tables</li>
                                <li>Apply MariaDB 5.5.68 compatibility fixes</li>
                                <li>Create upload directories with proper permissions</li>
                                <li>Test critical system functions</li>
                                <li>Insert default admin user (admin/admin123)</li>
                            </ul>
                        </div>
<?php
}
?>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
