<?php
session_start();
require_once 'config/database.php';
require_once 'includes/functions.php';

// Check if user is logged in
if (!isset($_SESSION['user_id'])) {
    header('Location: login.php');
    exit();
}

$user_id = $_SESSION['user_id'];
$user_role = $_SESSION['role'];

// Initialize database connection
$database = new Database();
$db = $database->getConnection();

// Get user information
$stmt = $db->prepare("SELECT * FROM users WHERE id = ?");
$stmt->execute([$user_id]);
$user = $stmt->fetch();

// Get current date ranges
$today = date('Y-m-d');
$this_month_start = date('Y-m-01');
$this_month_end = date('Y-m-t');
$last_month_start = date('Y-m-01', strtotime('-1 month'));
$last_month_end = date('Y-m-t', strtotime('-1 month'));

// Personal Statistics (for all users)
$personal_stats = [];

// Total expenses created by user
$stmt = $db->prepare("SELECT COUNT(*) as count, COALESCE(SUM(total_amount), 0) as total FROM expenses WHERE created_by = ?");
$stmt->execute([$user_id]);
$personal_stats['total_expenses'] = $stmt->fetch();

// This month expenses by user
$stmt = $db->prepare("SELECT COUNT(*) as count, COALESCE(SUM(total_amount), 0) as total FROM expenses WHERE created_by = ? AND DATE(created_at) BETWEEN ? AND ?");
$stmt->execute([$user_id, $this_month_start, $this_month_end]);
$personal_stats['this_month'] = $stmt->fetch();

// User's activity count
$stmt = $db->prepare("SELECT COUNT(*) as count FROM activity_logs WHERE user_id = ?");
$stmt->execute([$user_id]);
$personal_stats['activities'] = $stmt->fetch()['count'];

// Recent activities for user
$stmt = $db->prepare("
    SELECT al.*, e.exno 
    FROM activity_logs al 
    LEFT JOIN expenses e ON al.table_name = 'expenses' AND al.record_id = e.id
    WHERE al.user_id = ? 
    ORDER BY al.created_at DESC 
    LIMIT 5
");
$stmt->execute([$user_id]);
$recent_activities = $stmt->fetchAll();

// System Statistics (for administrators)
$system_stats = [];
if ($user_role === 'administrator') {
    // Total system expenses
    $stmt = $db->query("SELECT COUNT(*) as count, COALESCE(SUM(total_amount), 0) as total FROM expenses");
    $system_stats['total_expenses'] = $stmt->fetch();
    
    // This month system expenses
    $stmt = $db->prepare("SELECT COUNT(*) as count, COALESCE(SUM(total_amount), 0) as total FROM expenses WHERE DATE(created_at) BETWEEN ? AND ?");
    $stmt->execute([$this_month_start, $this_month_end]);
    $system_stats['this_month'] = $stmt->fetch();
    
    // Expenses by status
    $stmt = $db->query("SELECT status, COUNT(*) as count FROM expenses GROUP BY status");
    $status_breakdown = $stmt->fetchAll();
    $system_stats['status_breakdown'] = [];
    foreach ($status_breakdown as $status) {
        $system_stats['status_breakdown'][$status['status']] = $status['count'];
    }
    
    // Active users
    $stmt = $db->query("SELECT COUNT(*) as count FROM users WHERE is_active = 1");
    $system_stats['active_users'] = $stmt->fetch()['count'];
    
    // Users by role
    $stmt = $db->query("SELECT role, COUNT(*) as count FROM users WHERE is_active = 1 GROUP BY role");
    $role_breakdown = $stmt->fetchAll();
    $system_stats['role_breakdown'] = [];
    foreach ($role_breakdown as $role) {
        $system_stats['role_breakdown'][$role['role']] = $role['count'];
    }
    
    // Recent system activities
    $stmt = $db->prepare("
        SELECT al.*, u.full_name as user_name, e.exno 
        FROM activity_logs al 
        LEFT JOIN users u ON al.user_id = u.id
        LEFT JOIN expenses e ON al.table_name = 'expenses' AND al.record_id = e.id
        ORDER BY al.created_at DESC 
        LIMIT 10
    ");
    $stmt->execute();
    $system_activities = $stmt->fetchAll();
}

// Pending items for verification and reviewer roles
$pending_items = [];
if ($user_role === 'verification') {
    $stmt = $db->query("SELECT COUNT(*) as count FROM expenses WHERE status = 'open'");
    $pending_items['verification_pending'] = $stmt->fetch()['count'];
    
    // Get pending expenses for verification
    $stmt = $db->prepare("
        SELECT e.*, u.full_name as created_by_name 
        FROM expenses e 
        LEFT JOIN users u ON e.created_by = u.id 
        WHERE e.status = 'open' 
        ORDER BY e.created_at ASC 
        LIMIT 5
    ");
    $stmt->execute();
    $pending_items['verification_list'] = $stmt->fetchAll();
} elseif ($user_role === 'reviewer') {
    $stmt = $db->query("SELECT COUNT(*) as count FROM expenses WHERE status = 'pending'");
    $pending_items['review_pending'] = $stmt->fetch()['count'];
    
    // Get pending expenses for review
    $stmt = $db->prepare("
        SELECT e.*, u.full_name as created_by_name 
        FROM expenses e 
        LEFT JOIN users u ON e.created_by = u.id 
        WHERE e.status = 'pending' 
        ORDER BY e.created_at ASC 
        LIMIT 5
    ");
    $stmt->execute();
    $pending_items['review_list'] = $stmt->fetchAll();
}

// Performance metrics for verification and reviewer
$performance_metrics = [];
if ($user_role === 'verification' || $user_role === 'reviewer') {
    if ($user_role === 'verification') {
        // Verification performance
        $stmt = $db->prepare("SELECT COUNT(*) as count FROM expenses WHERE verification_by = ?");
        $stmt->execute([$user_id]);
        $performance_metrics['total_processed'] = $stmt->fetch()['count'];
        
        $stmt = $db->prepare("SELECT COUNT(*) as count FROM expenses WHERE verification_by = ? AND DATE(verification_date) BETWEEN ? AND ?");
        $stmt->execute([$user_id, $this_month_start, $this_month_end]);
        $performance_metrics['this_month_processed'] = $stmt->fetch()['count'];
    } elseif ($user_role === 'reviewer') {
        // Review performance
        $stmt = $db->prepare("SELECT COUNT(*) as count FROM expenses WHERE reviewer_by = ?");
        $stmt->execute([$user_id]);
        $performance_metrics['total_processed'] = $stmt->fetch()['count'];
        
        $stmt = $db->prepare("SELECT COUNT(*) as count FROM expenses WHERE reviewer_by = ? AND DATE(reviewer_date) BETWEEN ? AND ?");
        $stmt->execute([$user_id, $this_month_start, $this_month_end]);
        $performance_metrics['this_month_processed'] = $stmt->fetch()['count'];
    }
}
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Dashboard - Expense Management System</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="assets/css/style.css" rel="stylesheet">
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <style>
        .stat-card {
            transition: transform 0.2s;
        }
        .stat-card:hover {
            transform: translateY(-2px);
        }
        .activity-item {
            border-left: 3px solid #007bff;
            padding-left: 15px;
            margin-bottom: 15px;
        }
        .chart-container {
            position: relative;
            height: 300px;
        }
        .quick-action-btn {
            transition: all 0.2s;
        }
        .quick-action-btn:hover {
            transform: scale(1.05);
        }
    </style>
</head>
<body>
    <?php include 'includes/navbar.php'; ?>
    
    <div class="container-fluid mt-4">
        <!-- Page Header -->
        <div class="row mb-4">
            <div class="col">
                <h2><i class="fas fa-tachometer-alt me-2"></i>Dashboard</h2>
                <p class="text-muted">Welcome back, <?php echo htmlspecialchars($user['full_name']); ?>!</p>
            </div>
        </div>

        <!-- Personal Statistics (All Users) -->
        <div class="row mb-4">
            <div class="col-md-3">
                <div class="card stat-card h-100">
                    <div class="card-body text-center">
                        <i class="fas fa-file-invoice-dollar fa-2x text-primary mb-2"></i>
                        <h4 class="text-primary"><?php echo number_format($personal_stats['total_expenses']['count']); ?></h4>
                        <p class="text-muted mb-1">My Total Expenses</p>
                        <small class="text-success">
                            <?php echo number_format($personal_stats['total_expenses']['total'], 2); ?> บาท
                        </small>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card stat-card h-100">
                    <div class="card-body text-center">
                        <i class="fas fa-calendar-month fa-2x text-success mb-2"></i>
                        <h4 class="text-success"><?php echo number_format($personal_stats['this_month']['count']); ?></h4>
                        <p class="text-muted mb-1">This Month</p>
                        <small class="text-success">
                            <?php echo number_format($personal_stats['this_month']['total'], 2); ?> บาท
                        </small>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card stat-card h-100">
                    <div class="card-body text-center">
                        <i class="fas fa-history fa-2x text-info mb-2"></i>
                        <h4 class="text-info"><?php echo number_format($personal_stats['activities']); ?></h4>
                        <p class="text-muted mb-1">My Activities</p>
                        <small class="text-muted">Total actions performed</small>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card stat-card h-100">
                    <div class="card-body text-center">
                        <i class="fas fa-user-circle fa-2x text-warning mb-2"></i>
                        <h4 class="text-warning"><?php echo ucfirst($user_role); ?></h4>
                        <p class="text-muted mb-1">My Role</p>
                        <small class="text-muted">Current access level</small>
                    </div>
                </div>
            </div>
        </div>

        <!-- Administrator System Overview -->
        <?php if ($user_role === 'administrator'): ?>
        <div class="row mb-4">
            <div class="col-12">
                <h4><i class="fas fa-chart-line me-2"></i>System Overview</h4>
            </div>
        </div>
        <div class="row mb-4">
            <div class="col-md-3">
                <div class="card stat-card h-100 border-primary">
                    <div class="card-body text-center">
                        <i class="fas fa-building fa-2x text-primary mb-2"></i>
                        <h4 class="text-primary"><?php echo number_format($system_stats['total_expenses']['count']); ?></h4>
                        <p class="text-muted mb-1">Total System Expenses</p>
                        <small class="text-success">
                            <?php echo number_format($system_stats['total_expenses']['total'], 2); ?> บาท
                        </small>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card stat-card h-100 border-success">
                    <div class="card-body text-center">
                        <i class="fas fa-calendar-check fa-2x text-success mb-2"></i>
                        <h4 class="text-success"><?php echo number_format($system_stats['this_month']['count']); ?></h4>
                        <p class="text-muted mb-1">This Month (System)</p>
                        <small class="text-success">
                            <?php echo number_format($system_stats['this_month']['total'], 2); ?> บาท
                        </small>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card stat-card h-100 border-info">
                    <div class="card-body text-center">
                        <i class="fas fa-users fa-2x text-info mb-2"></i>
                        <h4 class="text-info"><?php echo number_format($system_stats['active_users']); ?></h4>
                        <p class="text-muted mb-1">Active Users</p>
                        <small class="text-muted">Currently active</small>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card stat-card h-100 border-warning">
                    <div class="card-body text-center">
                        <i class="fas fa-tasks fa-2x text-warning mb-2"></i>
                        <h4 class="text-warning">
                            <?php echo isset($system_stats['status_breakdown']['open']) ? $system_stats['status_breakdown']['open'] : 0; ?>
                        </h4>
                        <p class="text-muted mb-1">Pending Verification</p>
                        <small class="text-muted">Awaiting action</small>
                    </div>
                </div>
            </div>
        </div>

        <!-- Charts for Administrator -->
        <div class="row mb-4">
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h6 class="mb-0"><i class="fas fa-chart-pie me-1"></i>Expenses by Status</h6>
                    </div>
                    <div class="card-body">
                        <div class="chart-container">
                            <canvas id="statusChart"></canvas>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h6 class="mb-0"><i class="fas fa-chart-bar me-1"></i>Users by Role</h6>
                    </div>
                    <div class="card-body">
                        <div class="chart-container">
                            <canvas id="roleChart"></canvas>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <?php endif; ?>

        <!-- Pending Items for Verification/Reviewer -->
        <?php if ($user_role === 'verification' || $user_role === 'reviewer'): ?>
        <div class="row mb-4">
            <div class="col-12">
                <h4><i class="fas fa-clock me-2"></i>Pending Items</h4>
            </div>
        </div>
        <div class="row mb-4">
            <?php if ($user_role === 'verification'): ?>
            <div class="col-md-6">
                <div class="card border-warning">
                    <div class="card-header bg-warning">
                        <h6 class="mb-0 text-dark">
                            <i class="fas fa-exclamation-triangle me-1"></i>
                            Awaiting Verification (<?php echo $pending_items['verification_pending']; ?>)
                        </h6>
                    </div>
                    <div class="card-body">
                        <?php if (!empty($pending_items['verification_list'])): ?>
                            <?php foreach ($pending_items['verification_list'] as $expense): ?>
                            <div class="d-flex justify-content-between align-items-center mb-2 p-2 border rounded">
                                <div>
                                    <strong><?php echo htmlspecialchars($expense['exno']); ?></strong>
                                    <br>
                                    <small class="text-muted">
                                        By: <?php echo htmlspecialchars($expense['created_by_name']); ?> |
                                        <?php echo number_format($expense['total_amount'], 2); ?> บาท
                                    </small>
                                </div>
                                <a href="expenses/view.php?id=<?php echo $expense['id']; ?>" class="btn btn-sm btn-outline-primary">
                                    <i class="fas fa-eye me-1"></i>View
                                </a>
                            </div>
                            <?php endforeach; ?>
                            <?php if ($pending_items['verification_pending'] > 5): ?>
                            <div class="text-center mt-3">
                                <a href="expenses/list.php?status=open" class="btn btn-warning">
                                    View All (<?php echo $pending_items['verification_pending']; ?>)
                                </a>
                            </div>
                            <?php endif; ?>
                        <?php else: ?>
                            <p class="text-muted text-center">No items pending verification</p>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
            <?php elseif ($user_role === 'reviewer'): ?>
            <div class="col-md-6">
                <div class="card border-info">
                    <div class="card-header bg-info">
                        <h6 class="mb-0 text-white">
                            <i class="fas fa-clipboard-check me-1"></i>
                            Awaiting Review (<?php echo $pending_items['review_pending']; ?>)
                        </h6>
                    </div>
                    <div class="card-body">
                        <?php if (!empty($pending_items['review_list'])): ?>
                            <?php foreach ($pending_items['review_list'] as $expense): ?>
                            <div class="d-flex justify-content-between align-items-center mb-2 p-2 border rounded">
                                <div>
                                    <strong><?php echo htmlspecialchars($expense['exno']); ?></strong>
                                    <br>
                                    <small class="text-muted">
                                        By: <?php echo htmlspecialchars($expense['created_by_name']); ?> |
                                        <?php echo number_format($expense['total_amount'], 2); ?> บาท
                                    </small>
                                </div>
                                <a href="expenses/view.php?id=<?php echo $expense['id']; ?>" class="btn btn-sm btn-outline-primary">
                                    <i class="fas fa-eye me-1"></i>View
                                </a>
                            </div>
                            <?php endforeach; ?>
                            <?php if ($pending_items['review_pending'] > 5): ?>
                            <div class="text-center mt-3">
                                <a href="expenses/list.php?status=pending" class="btn btn-info">
                                    View All (<?php echo $pending_items['review_pending']; ?>)
                                </a>
                            </div>
                            <?php endif; ?>
                        <?php else: ?>
                            <p class="text-muted text-center">No items pending review</p>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
            <?php endif; ?>

            <!-- Performance Metrics -->
            <div class="col-md-6">
                <div class="card border-success">
                    <div class="card-header bg-success">
                        <h6 class="mb-0 text-white">
                            <i class="fas fa-chart-line me-1"></i>My Performance
                        </h6>
                    </div>
                    <div class="card-body">
                        <div class="row text-center">
                            <div class="col-6">
                                <h4 class="text-success"><?php echo number_format($performance_metrics['total_processed']); ?></h4>
                                <small class="text-muted">Total Processed</small>
                            </div>
                            <div class="col-6">
                                <h4 class="text-primary"><?php echo number_format($performance_metrics['this_month_processed']); ?></h4>
                                <small class="text-muted">This Month</small>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <?php endif; ?>

        <!-- Quick Actions (All Users) -->
        <div class="row mb-4">
            <div class="col-12">
                <h4><i class="fas fa-bolt me-2"></i>Quick Actions</h4>
            </div>
        </div>
        <div class="row mb-4">
            <div class="col-md-3">
                <a href="expenses/create.php" class="btn btn-primary btn-lg w-100 quick-action-btn">
                    <i class="fas fa-plus-circle fa-2x d-block mb-2"></i>
                    Create New Expense
                </a>
            </div>
            <div class="col-md-3">
                <a href="expenses/list.php" class="btn btn-info btn-lg w-100 quick-action-btn">
                    <i class="fas fa-list fa-2x d-block mb-2"></i>
                    View All Expenses
                </a>
            </div>
            <?php if ($user_role === 'verification'): ?>
            <div class="col-md-3">
                <a href="expenses/list.php?status=open" class="btn btn-warning btn-lg w-100 quick-action-btn">
                    <i class="fas fa-search fa-2x d-block mb-2"></i>
                    Verify Expenses
                </a>
            </div>
            <?php elseif ($user_role === 'reviewer'): ?>
            <div class="col-md-3">
                <a href="expenses/list.php?status=pending" class="btn btn-success btn-lg w-100 quick-action-btn">
                    <i class="fas fa-clipboard-check fa-2x d-block mb-2"></i>
                    Review Expenses
                </a>
            </div>
            <?php elseif ($user_role === 'administrator'): ?>
            <div class="col-md-3">
                <a href="admin/users.php" class="btn btn-danger btn-lg w-100 quick-action-btn">
                    <i class="fas fa-users-cog fa-2x d-block mb-2"></i>
                    Manage Users
                </a>
            </div>
            <?php else: ?>
            <div class="col-md-3">
                <a href="profile.php" class="btn btn-secondary btn-lg w-100 quick-action-btn">
                    <i class="fas fa-user fa-2x d-block mb-2"></i>
                    My Profile
                </a>
            </div>
            <?php endif; ?>
            <div class="col-md-3">
                <a href="reports/" class="btn btn-dark btn-lg w-100 quick-action-btn">
                    <i class="fas fa-chart-bar fa-2x d-block mb-2"></i>
                    View Reports
                </a>
            </div>
        </div>

        <!-- Recent Activities -->
        <div class="row">
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h6 class="mb-0">
                            <i class="fas fa-history me-1"></i>My Recent Activities
                        </h6>
                    </div>
                    <div class="card-body">
                        <?php if (!empty($recent_activities)): ?>
                            <?php foreach ($recent_activities as $activity): ?>
                            <div class="activity-item">
                                <div class="d-flex justify-content-between">
                                    <strong><?php echo htmlspecialchars($activity['description']); ?></strong>
                                    <small class="text-muted"><?php echo formatDateTime($activity['created_at']); ?></small>
                                </div>
                                <?php if (!empty($activity['exno'])): ?>
                                <small class="text-muted">Expense: <?php echo htmlspecialchars($activity['exno']); ?></small>
                                <?php endif; ?>
                            </div>
                            <?php endforeach; ?>
                            <div class="text-center mt-3">
                                <a href="reports/activity_logs.php" class="btn btn-outline-primary btn-sm">
                                    View All Activities
                                </a>
                            </div>
                        <?php else: ?>
                            <p class="text-muted text-center">No recent activities</p>
                        <?php endif; ?>
                    </div>
                </div>
            </div>

            <!-- System Activities (Admin Only) -->
            <?php if ($user_role === 'administrator'): ?>
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h6 class="mb-0">
                            <i class="fas fa-globe me-1"></i>Recent System Activities
                        </h6>
                    </div>
                    <div class="card-body">
                        <?php if (!empty($system_activities)): ?>
                            <?php foreach ($system_activities as $activity): ?>
                            <div class="activity-item">
                                <div class="d-flex justify-content-between">
                                    <strong><?php echo htmlspecialchars($activity['user_name']); ?></strong>
                                    <small class="text-muted"><?php echo formatDateTime($activity['created_at']); ?></small>
                                </div>
                                <div><?php echo htmlspecialchars($activity['description']); ?></div>
                                <?php if (!empty($activity['exno'])): ?>
                                <small class="text-muted">Expense: <?php echo htmlspecialchars($activity['exno']); ?></small>
                                <?php endif; ?>
                            </div>
                            <?php endforeach; ?>
                            <div class="text-center mt-3">
                                <a href="reports/activity_logs.php" class="btn btn-outline-primary btn-sm">
                                    View All System Activities
                                </a>
                            </div>
                        <?php else: ?>
                            <p class="text-muted text-center">No recent system activities</p>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
            <?php else: ?>
            <!-- Personal Stats Summary for Non-Admin -->
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h6 class="mb-0">
                            <i class="fas fa-chart-line me-1"></i>My Summary
                        </h6>
                    </div>
                    <div class="card-body">
                        <div class="row text-center">
                            <div class="col-12 mb-3">
                                <h5 class="text-primary">Personal Statistics</h5>
                            </div>
                            <div class="col-6">
                                <div class="border-end">
                                    <h4 class="text-success"><?php echo number_format($personal_stats['total_expenses']['count']); ?></h4>
                                    <small class="text-muted">Total Expenses</small>
                                </div>
                            </div>
                            <div class="col-6">
                                <h4 class="text-info"><?php echo number_format($personal_stats['this_month']['count']); ?></h4>
                                <small class="text-muted">This Month</small>
                            </div>
                        </div>
                        <hr>
                        <div class="text-center">
                            <p class="text-muted mb-2">Total Amount</p>
                            <h4 class="text-success"><?php echo number_format($personal_stats['total_expenses']['total'], 2); ?> บาท</h4>
                        </div>
                    </div>
                </div>
            </div>
            <?php endif; ?>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>

    <!-- Charts for Administrator -->
    <?php if ($user_role === 'administrator'): ?>
    <script>
        // Status Chart
        const statusCtx = document.getElementById('statusChart').getContext('2d');
        const statusChart = new Chart(statusCtx, {
            type: 'doughnut',
            data: {
                labels: ['Open', 'Pending', 'Success', 'Rejected', 'Returned'],
                datasets: [{
                    data: [
                        <?php echo isset($system_stats['status_breakdown']['open']) ? $system_stats['status_breakdown']['open'] : 0; ?>,
                        <?php echo isset($system_stats['status_breakdown']['pending']) ? $system_stats['status_breakdown']['pending'] : 0; ?>,
                        <?php echo isset($system_stats['status_breakdown']['success']) ? $system_stats['status_breakdown']['success'] : 0; ?>,
                        <?php echo isset($system_stats['status_breakdown']['rejected']) ? $system_stats['status_breakdown']['rejected'] : 0; ?>,
                        <?php echo isset($system_stats['status_breakdown']['returned']) ? $system_stats['status_breakdown']['returned'] : 0; ?>
                    ],
                    backgroundColor: [
                        '#ffc107',
                        '#17a2b8',
                        '#28a745',
                        '#dc3545',
                        '#6c757d'
                    ],
                    borderWidth: 2,
                    borderColor: '#fff'
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        position: 'bottom'
                    }
                }
            }
        });

        // Role Chart
        const roleCtx = document.getElementById('roleChart').getContext('2d');
        const roleChart = new Chart(roleCtx, {
            type: 'bar',
            data: {
                labels: ['Data Entry', 'Verification', 'Reviewer', 'Administrator'],
                datasets: [{
                    label: 'Number of Users',
                    data: [
                        <?php echo isset($system_stats['role_breakdown']['data_entry']) ? $system_stats['role_breakdown']['data_entry'] : 0; ?>,
                        <?php echo isset($system_stats['role_breakdown']['verification']) ? $system_stats['role_breakdown']['verification'] : 0; ?>,
                        <?php echo isset($system_stats['role_breakdown']['reviewer']) ? $system_stats['role_breakdown']['reviewer'] : 0; ?>,
                        <?php echo isset($system_stats['role_breakdown']['administrator']) ? $system_stats['role_breakdown']['administrator'] : 0; ?>
                    ],
                    backgroundColor: [
                        '#007bff',
                        '#ffc107',
                        '#28a745',
                        '#dc3545'
                    ],
                    borderColor: [
                        '#0056b3',
                        '#e0a800',
                        '#1e7e34',
                        '#c82333'
                    ],
                    borderWidth: 1
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                scales: {
                    y: {
                        beginAtZero: true,
                        ticks: {
                            stepSize: 1
                        }
                    }
                },
                plugins: {
                    legend: {
                        display: false
                    }
                }
            }
        });
    </script>
    <?php endif; ?>

    <script>
        // Auto-refresh dashboard every 5 minutes
        setTimeout(function() {
            location.reload();
        }, 300000); // 5 minutes

        // Add hover effects to stat cards
        document.querySelectorAll('.stat-card').forEach(card => {
            card.addEventListener('mouseenter', function() {
                this.style.boxShadow = '0 4px 8px rgba(0,0,0,0.1)';
            });

            card.addEventListener('mouseleave', function() {
                this.style.boxShadow = '';
            });
        });

        // Add click tracking for quick actions
        document.querySelectorAll('.quick-action-btn').forEach(btn => {
            btn.addEventListener('click', function() {
                // Could add analytics tracking here
                console.log('Quick action clicked:', this.textContent.trim());
            });
        });
    </script>
</body>
</html>
