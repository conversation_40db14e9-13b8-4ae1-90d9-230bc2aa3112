<?php
session_start();
require_once '../config/database.php';
require_once '../includes/functions.php';
require_once '../includes/ImageUploadHelper.php';

// Check if user is logged in and has admin role
if (!isset($_SESSION['user_id']) || $_SESSION['role'] !== 'administrator') {
    header('Location: ../login.php');
    exit();
}

$page_title = 'Image Compression Statistics';

// Get compression statistics
$stats = ImageUploadHelper::getCompressionStats();

// Calculate total savings
$totalOriginalSize = 0;
$totalCompressedSize = 0;
foreach ($stats['by_type'] as $typeStats) {
    $totalCompressedSize += $typeStats['size'];
    // Estimate original size (assuming 30% compression on average)
    $totalOriginalSize += $typeStats['size'] * 1.43; // Reverse calculation
}
$totalSavings = $totalOriginalSize - $totalCompressedSize;
$totalSavingsPercent = $totalOriginalSize > 0 ? (($totalSavings / $totalOriginalSize) * 100) : 0;

?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $page_title; ?> - Expense Management System</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        .stat-card {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 15px;
            padding: 20px;
            margin-bottom: 20px;
        }
        .stat-card h3 {
            font-size: 2.5rem;
            font-weight: bold;
            margin-bottom: 5px;
        }
        .stat-card p {
            margin-bottom: 0;
            opacity: 0.9;
        }
        .type-card {
            border: 1px solid #e0e0e0;
            border-radius: 10px;
            padding: 20px;
            margin-bottom: 20px;
            transition: transform 0.2s;
        }
        .type-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0,0,0,0.1);
        }
        .progress-custom {
            height: 8px;
            border-radius: 4px;
        }
        .file-list {
            max-height: 300px;
            overflow-y: auto;
        }
    </style>
</head>
<body class="bg-light">
    <?php include '../includes/navbar.php'; ?>

    <div class="container-fluid mt-4">
        <div class="row">
            <div class="col-md-12">
                <div class="d-flex justify-content-between align-items-center mb-4">
                    <h1><i class="fas fa-compress-alt me-2"></i><?php echo $page_title; ?></h1>
                    <a href="../admin/index.php" class="btn btn-secondary">
                        <i class="fas fa-arrow-left me-1"></i>Back to Admin
                    </a>
                </div>

                <!-- Overall Statistics -->
                <div class="row mb-4">
                    <div class="col-md-3">
                        <div class="stat-card text-center">
                            <h3><?php echo $stats['total_files']; ?></h3>
                            <p><i class="fas fa-images me-1"></i>Total Images</p>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="stat-card text-center">
                            <h3><?php echo $stats['total_size_formatted']; ?></h3>
                            <p><i class="fas fa-hdd me-1"></i>Total Size</p>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="stat-card text-center">
                            <h3><?php echo ImageCompressor::formatFileSize($totalSavings); ?></h3>
                            <p><i class="fas fa-save me-1"></i>Space Saved</p>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="stat-card text-center">
                            <h3><?php echo number_format($totalSavingsPercent, 1); ?>%</h3>
                            <p><i class="fas fa-chart-line me-1"></i>Compression Ratio</p>
                        </div>
                    </div>
                </div>

                <!-- Statistics by Type -->
                <div class="row">
                    <?php foreach ($stats['by_type'] as $type => $typeStats): ?>
                    <div class="col-md-6 col-lg-3">
                        <div class="type-card">
                            <div class="d-flex justify-content-between align-items-center mb-3">
                                <h5 class="mb-0">
                                    <?php
                                    $icons = [
                                        'receipts' => 'fas fa-receipt',
                                        'transfer_slips' => 'fas fa-money-check-alt',
                                        'verification_slips' => 'fas fa-check-double',
                                        'review_slips' => 'fas fa-clipboard-check',
                                        'batch_verification' => 'fas fa-tasks',
                                        'batch_review' => 'fas fa-clipboard-list'
                                    ];
                                    $icon = $icons[$type] ?? 'fas fa-image';
                                    $title = ucwords(str_replace('_', ' ', $type));
                                    ?>
                                    <i class="<?php echo $icon; ?> me-2 text-primary"></i>
                                    <?php echo $title; ?>
                                </h5>
                                <span class="badge bg-primary"><?php echo $typeStats['count']; ?></span>
                            </div>
                            
                            <div class="mb-2">
                                <small class="text-muted">Total Size</small>
                                <div class="fw-bold"><?php echo ImageCompressor::formatFileSize($typeStats['size']); ?></div>
                            </div>
                            
                            <?php if ($typeStats['count'] > 0): ?>
                            <div class="mb-3">
                                <small class="text-muted">Average Size</small>
                                <div class="fw-bold"><?php echo ImageCompressor::formatFileSize($typeStats['size'] / $typeStats['count']); ?></div>
                            </div>
                            
                            <!-- Recent Files -->
                            <div class="file-list">
                                <small class="text-muted">Recent Files:</small>
                                <?php 
                                $recentFiles = array_slice($typeStats['files'], -5);
                                foreach ($recentFiles as $file): 
                                ?>
                                <div class="d-flex justify-content-between align-items-center py-1">
                                    <small class="text-truncate me-2" style="max-width: 150px;" title="<?php echo htmlspecialchars($file['name']); ?>">
                                        <?php echo htmlspecialchars($file['name']); ?>
                                    </small>
                                    <small class="text-muted"><?php echo $file['size_formatted']; ?></small>
                                </div>
                                <?php endforeach; ?>
                            </div>
                            <?php endif; ?>
                        </div>
                    </div>
                    <?php endforeach; ?>
                </div>

                <!-- Compression Settings -->
                <div class="row mt-4">
                    <div class="col-md-12">
                        <div class="card">
                            <div class="card-header">
                                <h5 class="mb-0"><i class="fas fa-cogs me-2"></i>Compression Settings</h5>
                            </div>
                            <div class="card-body">
                                <div class="row">
                                    <div class="col-md-3">
                                        <div class="mb-3">
                                            <label class="form-label">Max Width</label>
                                            <div class="fw-bold"><?php echo ImageCompressor::MAX_WIDTH; ?>px</div>
                                        </div>
                                    </div>
                                    <div class="col-md-3">
                                        <div class="mb-3">
                                            <label class="form-label">Max Height</label>
                                            <div class="fw-bold"><?php echo ImageCompressor::MAX_HEIGHT; ?>px</div>
                                        </div>
                                    </div>
                                    <div class="col-md-3">
                                        <div class="mb-3">
                                            <label class="form-label">JPEG Quality</label>
                                            <div class="fw-bold"><?php echo ImageCompressor::JPEG_QUALITY; ?>%</div>
                                        </div>
                                    </div>
                                    <div class="col-md-3">
                                        <div class="mb-3">
                                            <label class="form-label">Max File Size</label>
                                            <div class="fw-bold"><?php echo ImageCompressor::formatFileSize(ImageCompressor::MAX_FILE_SIZE); ?></div>
                                        </div>
                                    </div>
                                </div>
                                
                                <div class="alert alert-info">
                                    <i class="fas fa-info-circle me-2"></i>
                                    <strong>How it works:</strong> Images are automatically compressed when uploaded. 
                                    Large images are resized to fit within <?php echo ImageCompressor::MAX_WIDTH; ?>x<?php echo ImageCompressor::MAX_HEIGHT; ?>px 
                                    while maintaining aspect ratio. JPEG quality is set to <?php echo ImageCompressor::JPEG_QUALITY; ?>% 
                                    for optimal balance between file size and image quality.
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Cleanup Tools -->
                <div class="row mt-4">
                    <div class="col-md-12">
                        <div class="card">
                            <div class="card-header">
                                <h5 class="mb-0"><i class="fas fa-broom me-2"></i>Cleanup Tools</h5>
                            </div>
                            <div class="card-body">
                                <div class="row">
                                    <div class="col-md-6">
                                        <h6>Clean Old Images</h6>
                                        <p class="text-muted">Remove images older than specified days to free up space.</p>
                                        <button class="btn btn-warning" onclick="cleanupOldImages(30)">
                                            <i class="fas fa-trash me-1"></i>Clean 30+ Days Old
                                        </button>
                                        <button class="btn btn-danger ms-2" onclick="cleanupOldImages(90)">
                                            <i class="fas fa-trash me-1"></i>Clean 90+ Days Old
                                        </button>
                                    </div>
                                    <div class="col-md-6">
                                        <h6>Generate Thumbnails</h6>
                                        <p class="text-muted">Create thumbnails for existing images to improve loading speed.</p>
                                        <button class="btn btn-info" onclick="generateThumbnails()">
                                            <i class="fas fa-images me-1"></i>Generate Thumbnails
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        function cleanupOldImages(days) {
            if (confirm(`Are you sure you want to delete images older than ${days} days? This action cannot be undone.`)) {
                // Implementation would go here
                alert('Cleanup functionality would be implemented here');
            }
        }
        
        function generateThumbnails() {
            if (confirm('Generate thumbnails for all images? This may take some time.')) {
                // Implementation would go here
                alert('Thumbnail generation would be implemented here');
            }
        }
    </script>
</body>
</html>
