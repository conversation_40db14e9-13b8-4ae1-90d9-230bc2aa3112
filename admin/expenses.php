<?php
session_start();
require_once '../config/database.php';
require_once '../includes/functions.php';

// Check if user is logged in and is admin
if (!isset($_SESSION['user_id']) || $_SESSION['role'] !== 'administrator') {
    header('Location: ../login.php');
    exit();
}

$database = new Database();
$db = $database->getConnection();

// Get statistics for dashboard cards
$stats = [];

// Total expenses
$stmt = $db->query("SELECT COUNT(*) as total FROM expenses");
$stats['total'] = $stmt->fetch()['total'];

// By status
$stmt = $db->query("SELECT status, COUNT(*) as count FROM expenses GROUP BY status");
while ($row = $stmt->fetch()) {
    $stats['status'][$row['status']] = $row['count'];
}

// Recent activity count
$stmt = $db->query("SELECT COUNT(*) as count FROM expenses WHERE created_at >= DATE_SUB(NOW(), INTERVAL 7 DAY)");
$stats['recent'] = $stmt->fetch()['count'];

// Test data count
$stmt = $db->query("SELECT COUNT(*) as count FROM expenses WHERE exno LIKE 'TEST%' OR exno LIKE 'DEMO%'");
$stats['test_data'] = $stmt->fetch()['count'];

$page_title = "Expense Management";
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $page_title; ?> - Admin Panel</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <link href="https://cdn.datatables.net/1.13.6/css/dataTables.bootstrap5.min.css" rel="stylesheet">
    <link href="https://cdn.datatables.net/buttons/2.4.1/css/buttons.bootstrap5.min.css" rel="stylesheet">
    <style>
        .expense-card {
            transition: transform 0.2s;
        }
        .expense-card:hover {
            transform: translateY(-2px);
        }
        .status-badge {
            font-size: 0.75rem;
        }
        .bulk-actions {
            background: #f8f9fa;
            border-radius: 8px;
            padding: 15px;
            margin-bottom: 20px;
            display: none;
        }
        .table-actions {
            white-space: nowrap;
        }
        .danger-zone {
            border: 2px solid #dc3545;
            border-radius: 8px;
            background: #fff5f5;
        }
    </style>
</head>
<body class="bg-light">
    <?php include 'includes/admin_nav.php'; ?>

    <div class="container-fluid mt-4">
        <!-- Page Header -->
        <div class="row mb-4">
            <div class="col-12">
                <h2><i class="fas fa-receipt me-2"></i><?php echo $page_title; ?></h2>
                <p class="text-muted">Manage all expense records, perform bulk operations, and maintain data integrity.</p>
            </div>
        </div>

        <!-- Statistics Cards -->
        <div class="row mb-4">
            <div class="col-md-3">
                <div class="card expense-card border-primary">
                    <div class="card-body text-center">
                        <i class="fas fa-receipt fa-2x text-primary mb-2"></i>
                        <h4 class="text-primary"><?php echo number_format($stats['total']); ?></h4>
                        <p class="text-muted mb-0">Total Expenses</p>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card expense-card border-success">
                    <div class="card-body text-center">
                        <i class="fas fa-check-circle fa-2x text-success mb-2"></i>
                        <h4 class="text-success"><?php echo number_format($stats['status']['success'] ?? 0); ?></h4>
                        <p class="text-muted mb-0">Approved</p>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card expense-card border-info">
                    <div class="card-body text-center">
                        <i class="fas fa-folder-open fa-2x text-info mb-2"></i>
                        <h4 class="text-info"><?php echo number_format($stats['status']['open'] ?? 0); ?></h4>
                        <p class="text-muted mb-0">Open</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Second Row of Statistics -->
        <div class="row mb-4">
            <div class="col-md-3">
                <div class="card expense-card border-warning">
                    <div class="card-body text-center">
                        <i class="fas fa-clock fa-2x text-warning mb-2"></i>
                        <h4 class="text-warning"><?php echo number_format($stats['status']['pending'] ?? 0); ?></h4>
                        <p class="text-muted mb-0">Pending</p>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card expense-card border-danger">
                    <div class="card-body text-center">
                        <i class="fas fa-times-circle fa-2x text-danger mb-2"></i>
                        <h4 class="text-danger"><?php echo number_format($stats['status']['rejected'] ?? 0); ?></h4>
                        <p class="text-muted mb-0">Rejected</p>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card expense-card border-secondary">
                    <div class="card-body text-center">
                        <i class="fas fa-undo fa-2x text-secondary mb-2"></i>
                        <h4 class="text-secondary"><?php echo number_format($stats['status']['returned'] ?? 0); ?></h4>
                        <p class="text-muted mb-0">Returned</p>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card expense-card border-info">
                    <div class="card-body text-center">
                        <i class="fas fa-calendar-week fa-2x text-info mb-2"></i>
                        <h4 class="text-info"><?php echo number_format($stats['recent']); ?></h4>
                        <p class="text-muted mb-0">This Week</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Action Buttons -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#advancedSearchModal">
                            <i class="fas fa-search me-1"></i>Advanced Search
                        </button>
                        <button type="button" class="btn btn-outline-secondary" onclick="refreshTable()">
                            <i class="fas fa-sync me-1"></i>Refresh
                        </button>
                    </div>
                    <div>
                        <button type="button" class="btn btn-success" onclick="exportData('csv')">
                            <i class="fas fa-download me-1"></i>Export CSV
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <!-- Bulk Actions Panel -->
        <div class="bulk-actions" id="bulkActionsPanel">
            <div class="row align-items-center">
                <div class="col-md-6">
                    <strong><span id="selectedCount">0</span> expenses selected</strong>
                </div>
                <div class="col-md-6 text-end">
                    <div class="btn-group me-2" role="group">
                        <button type="button" class="btn btn-info btn-sm" onclick="bulkAction('status', 'open')">
                            <i class="fas fa-folder-open me-1"></i>Open
                        </button>
                        <button type="button" class="btn btn-warning btn-sm" onclick="bulkAction('status', 'pending')">
                            <i class="fas fa-clock me-1"></i>Pending
                        </button>
                        <button type="button" class="btn btn-success btn-sm" onclick="bulkAction('status', 'success')">
                            <i class="fas fa-check-circle me-1"></i>Approved
                        </button>
                    </div>
                    <div class="btn-group me-2" role="group">
                        <button type="button" class="btn btn-outline-danger btn-sm" onclick="bulkAction('status', 'rejected')">
                            <i class="fas fa-times-circle me-1"></i>Rejected
                        </button>
                        <button type="button" class="btn btn-outline-secondary btn-sm" onclick="bulkAction('status', 'returned')">
                            <i class="fas fa-undo me-1"></i>Returned
                        </button>
                    </div>
                    <button type="button" class="btn btn-danger btn-sm" onclick="bulkAction('delete')">
                        <i class="fas fa-trash me-1"></i>Delete Selected
                    </button>
                </div>
            </div>
        </div>

        <!-- Expenses Table -->
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-list me-1"></i>All Expenses
                </h5>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-striped table-hover" id="expensesTable">
                        <thead>
                            <tr>
                                <th width="30">
                                    <input type="checkbox" id="selectAll" class="form-check-input">
                                </th>
                                <th>Expense No.</th>
                                <th>Job Date</th>
                                <th>Customer</th>
                                <th>Driver</th>
                                <th>Total Amount</th>
                                <th>Status</th>
                                <th>Created By</th>
                                <th>Created At</th>
                                <th width="120">Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            <!-- Data will be loaded via DataTables AJAX -->
                        </tbody>
                    </table>
                </div>
            </div>
        </div>

        <!-- Test Data Warning -->
        <?php if ($stats['test_data'] > 0): ?>
        <div class="row mt-4">
            <div class="col-12">
                <div class="card danger-zone">
                    <div class="card-header bg-danger text-white">
                        <h6 class="mb-0">
                            <i class="fas fa-exclamation-triangle me-1"></i>Test Data Detected
                        </h6>
                    </div>
                    <div class="card-body">
                        <p class="mb-2">
                            <strong><?php echo number_format($stats['test_data']); ?> test records</strong> found in the system.
                            These records have expense numbers starting with "TEST" or "DEMO".
                        </p>
                        <button type="button" class="btn btn-outline-danger" onclick="cleanupTestData()">
                            <i class="fas fa-broom me-1"></i>Clean Up Test Data
                        </button>
                        <button type="button" class="btn btn-outline-info" onclick="viewTestData()">
                            <i class="fas fa-eye me-1"></i>View Test Data
                        </button>
                    </div>
                </div>
            </div>
        </div>
        <?php endif; ?>
    </div>

    <!-- Advanced Search Modal -->
    <div class="modal fade" id="advancedSearchModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">Advanced Search</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <form id="advancedSearchForm">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">Expense Number</label>
                                    <input type="text" class="form-control" name="exno" placeholder="e.g., EXP001">
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">Status</label>
                                    <select class="form-select" name="status">
                                        <option value="">All Statuses</option>
                                        <option value="open">📂 Open</option>
                                        <option value="pending">⏰ Pending</option>
                                        <option value="success">✅ Approved</option>
                                        <option value="rejected">❌ Rejected</option>
                                        <option value="returned">↩️ Returned</option>
                                    </select>
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">Date From</label>
                                    <input type="date" class="form-control" name="date_from">
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">Date To</label>
                                    <input type="date" class="form-control" name="date_to">
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">Customer</label>
                                    <select class="form-select" name="customer_id">
                                        <option value="">All Customers</option>
                                        <!-- Will be populated via AJAX -->
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">Created By</label>
                                    <select class="form-select" name="created_by">
                                        <option value="">All Users</option>
                                        <!-- Will be populated via AJAX -->
                                    </select>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                    <button type="button" class="btn btn-outline-warning" onclick="clearSearch()">Clear</button>
                    <button type="button" class="btn btn-primary" onclick="applySearch()">Apply Search</button>
                </div>
            </div>
        </div>
    </div>

    <!-- Scripts -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://code.jquery.com/jquery-3.7.0.min.js"></script>
    <script src="https://cdn.datatables.net/1.13.6/js/jquery.dataTables.min.js"></script>
    <script src="https://cdn.datatables.net/1.13.6/js/dataTables.bootstrap5.min.js"></script>
    <script src="https://cdn.datatables.net/buttons/2.4.1/js/dataTables.buttons.min.js"></script>
    <script src="https://cdn.datatables.net/buttons/2.4.1/js/buttons.bootstrap5.min.js"></script>
    <script src="assets/js/admin-expenses.js"></script>
</body>
</html>
