<?php
session_start();
require_once '../../config/database.php';
require_once '../../includes/functions.php';

header('Content-Type: application/json');

// Check if user is logged in and is admin
if (!isset($_SESSION['user_id']) || $_SESSION['role'] !== 'administrator') {
    http_response_code(401);
    echo json_encode(['error' => 'Unauthorized']);
    exit();
}

try {
    $database = new Database();
    $db = $database->getConnection();
    
    // DataTables parameters
    $draw = intval($_POST['draw'] ?? 1);
    $start = intval($_POST['start'] ?? 0);
    $length = intval($_POST['length'] ?? 25);
    $search_value = $_POST['search']['value'] ?? '';
    $order_column = intval($_POST['order'][0]['column'] ?? 8);
    $order_dir = $_POST['order'][0]['dir'] ?? 'desc';
    
    // Search parameters from advanced search
    $search_params = $_POST['search_params'] ?? [];
    
    // Column mapping for ordering
    $columns = [
        0 => 'e.id',
        1 => 'e.exno',
        2 => 'e.job_open_date',
        3 => 'c.name',
        4 => 'd.name',
        5 => 'e.total_amount',
        6 => 'e.status',
        7 => 'u.full_name',
        8 => 'e.created_at'
    ];
    
    $order_by = $columns[$order_column] ?? 'e.created_at';
    
    // Base query
    $base_query = "
        FROM expenses e
        LEFT JOIN customers c ON e.customer_id = c.id
        LEFT JOIN drivers d ON e.driver_id = d.id
        LEFT JOIN users u ON e.created_by = u.id
    ";
    
    // Build WHERE clause
    $where_conditions = [];
    $params = [];
    
    // Global search
    if (!empty($search_value)) {
        $where_conditions[] = "(
            e.exno LIKE ? OR 
            c.name LIKE ? OR 
            d.name LIKE ? OR 
            u.full_name LIKE ? OR
            e.status LIKE ?
        )";
        $search_param = "%{$search_value}%";
        $params = array_merge($params, [$search_param, $search_param, $search_param, $search_param, $search_param]);
    }
    
    // Advanced search filters
    if (!empty($search_params['exno'])) {
        $where_conditions[] = "e.exno LIKE ?";
        $params[] = "%{$search_params['exno']}%";
    }
    
    if (!empty($search_params['status'])) {
        $where_conditions[] = "e.status = ?";
        $params[] = $search_params['status'];
    }
    
    if (!empty($search_params['customer_id'])) {
        $where_conditions[] = "e.customer_id = ?";
        $params[] = $search_params['customer_id'];
    }
    
    if (!empty($search_params['created_by'])) {
        $where_conditions[] = "e.created_by = ?";
        $params[] = $search_params['created_by'];
    }
    
    if (!empty($search_params['date_from'])) {
        $where_conditions[] = "e.job_open_date >= ?";
        $params[] = $search_params['date_from'];
    }
    
    if (!empty($search_params['date_to'])) {
        $where_conditions[] = "e.job_open_date <= ?";
        $params[] = $search_params['date_to'];
    }
    
    $where_clause = '';
    if (!empty($where_conditions)) {
        $where_clause = 'WHERE ' . implode(' AND ', $where_conditions);
    }
    
    // Count total records
    $count_query = "SELECT COUNT(*) as total " . $base_query . $where_clause;
    $stmt = $db->prepare($count_query);
    $stmt->execute($params);
    $total_records = $stmt->fetch()['total'];
    
    // Get filtered data
    $data_query = "
        SELECT 
            e.id,
            e.exno,
            e.job_open_date,
            e.withdrawal_date,
            e.status,
            e.total_amount,
            e.created_at,
            e.created_by,
            c.name as customer_name,
            d.name as driver_name,
            u.full_name as created_by_name
        " . $base_query . $where_clause . "
        ORDER BY {$order_by} {$order_dir}
        LIMIT {$start}, {$length}
    ";
    
    $stmt = $db->prepare($data_query);
    $stmt->execute($params);
    $expenses = $stmt->fetchAll();
    
    // Format data for DataTables
    $data = [];
    foreach ($expenses as $expense) {
        $data[] = [
            'id' => $expense['id'],
            'exno' => htmlspecialchars($expense['exno']),
            'job_open_date' => $expense['job_open_date'],
            'withdrawal_date' => $expense['withdrawal_date'],
            'customer_name' => htmlspecialchars($expense['customer_name'] ?? 'N/A'),
            'driver_name' => htmlspecialchars($expense['driver_name'] ?? 'N/A'),
            'total_amount' => $expense['total_amount'],
            'status' => $expense['status'],
            'created_by_name' => htmlspecialchars($expense['created_by_name'] ?? 'Unknown'),
            'created_at' => $expense['created_at']
        ];
    }
    
    // Return DataTables response
    echo json_encode([
        'draw' => $draw,
        'recordsTotal' => $total_records,
        'recordsFiltered' => $total_records,
        'data' => $data
    ]);
    
} catch (Exception $e) {
    error_log('DataTable error: ' . $e->getMessage());
    http_response_code(500);
    echo json_encode([
        'error' => 'Internal server error',
        'draw' => intval($_POST['draw'] ?? 1),
        'recordsTotal' => 0,
        'recordsFiltered' => 0,
        'data' => []
    ]);
}
?>
