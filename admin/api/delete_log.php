<?php
session_start();
require_once '../../config/database.php';
require_once '../../includes/functions.php';

header('Content-Type: application/json');

// Check if user is logged in and is admin
if (!isset($_SESSION['user_id']) || $_SESSION['role'] !== 'administrator') {
    http_response_code(401);
    echo json_encode(['success' => false, 'message' => 'Unauthorized']);
    exit();
}

try {
    $database = new Database();
    $db = $database->getConnection();
    
    // Get JSON input
    $input = json_decode(file_get_contents('php://input'), true);
    $ids = $input['ids'] ?? [];
    
    if (empty($ids)) {
        echo json_encode(['success' => false, 'message' => 'No log IDs provided']);
        exit();
    }
    
    // Validate IDs
    $ids = array_filter($ids, 'is_numeric');
    if (empty($ids)) {
        echo json_encode(['success' => false, 'message' => 'Invalid log IDs']);
        exit();
    }
    
    $placeholders = str_repeat('?,', count($ids) - 1) . '?';
    
    $db->beginTransaction();
    
    // Get log details before deletion for audit trail
    $stmt = $db->prepare("SELECT id, action_type, table_name, record_id, user_id FROM activity_logs WHERE id IN ($placeholders)");
    $stmt->execute($ids);
    $logs_to_delete = $stmt->fetchAll();
    
    // Delete the logs
    $stmt = $db->prepare("DELETE FROM activity_logs WHERE id IN ($placeholders)");
    $stmt->execute($ids);
    $deleted_count = $stmt->rowCount();
    
    // Log the deletion activity
    foreach ($logs_to_delete as $log) {
        logActivity($db, $_SESSION['user_id'], 'log_deleted', 'activity_logs', $log['id'], 
                   "Admin deleted activity log: {$log['action_type']} on {$log['table_name']} (Record ID: {$log['record_id']})");
    }
    
    $db->commit();
    
    echo json_encode([
        'success' => true,
        'message' => "$deleted_count log(s) deleted successfully",
        'deleted_count' => $deleted_count
    ]);
    
} catch (Exception $e) {
    if ($db->inTransaction()) {
        $db->rollback();
    }
    
    error_log('Delete log error: ' . $e->getMessage());
    echo json_encode([
        'success' => false,
        'message' => 'An error occurred while deleting logs: ' . $e->getMessage()
    ]);
}

function logActivity($db, $user_id, $action, $table_name, $record_id, $description) {
    try {
        $stmt = $db->prepare("
            INSERT INTO activity_logs (user_id, action_type, table_name, record_id, description, ip_address, created_at) 
            VALUES (?, ?, ?, ?, ?, ?, NOW())
        ");
        $stmt->execute([
            $user_id, 
            $action, 
            $table_name, 
            $record_id, 
            $description, 
            $_SERVER['REMOTE_ADDR'] ?? 'unknown'
        ]);
    } catch (Exception $e) {
        // Log activity error but don't fail the main operation
        error_log('Activity logging error: ' . $e->getMessage());
    }
}
?>
