<?php
session_start();
require_once '../../config/database.php';
require_once '../../includes/functions.php';

header('Content-Type: application/json');

// Check if user is logged in and is admin
if (!isset($_SESSION['user_id']) || $_SESSION['role'] !== 'administrator') {
    http_response_code(401);
    echo json_encode(['success' => false, 'message' => 'Unauthorized']);
    exit();
}

try {
    $database = new Database();
    $db = $database->getConnection();
    
    $action = $_POST['action'] ?? '';
    $ids = $_POST['ids'] ?? [];
    $value = $_POST['value'] ?? '';
    
    if (empty($action) || empty($ids)) {
        echo json_encode(['success' => false, 'message' => 'Missing required parameters']);
        exit();
    }
    
    // Validate IDs
    $ids = array_filter($ids, 'is_numeric');
    if (empty($ids)) {
        echo json_encode(['success' => false, 'message' => 'Invalid expense IDs']);
        exit();
    }
    
    $placeholders = str_repeat('?,', count($ids) - 1) . '?';
    $affected_rows = 0;
    
    $db->beginTransaction();
    
    switch ($action) {
        case 'delete':
            // First, delete related receipt numbers
            $stmt = $db->prepare("DELETE FROM receipt_numbers WHERE expense_id IN ($placeholders)");
            $stmt->execute($ids);
            
            // Then delete expenses
            $stmt = $db->prepare("DELETE FROM expenses WHERE id IN ($placeholders)");
            $stmt->execute($ids);
            $affected_rows = $stmt->rowCount();
            
            // Log the deletion
            foreach ($ids as $expense_id) {
                logActivity($db, $_SESSION['user_id'], 'expense_deleted', 'expenses', $expense_id, "Admin deleted expense ID: $expense_id", $_SERVER['REMOTE_ADDR'] ?? 'unknown', $_SERVER['HTTP_USER_AGENT'] ?? '');
            }
            
            $message = "$affected_rows expense(s) deleted successfully";
            break;
            
        case 'status':
            if (!in_array($value, ['open', 'pending', 'success', 'rejected', 'returned'])) {
                throw new Exception('Invalid status value');
            }
            
            $stmt = $db->prepare("UPDATE expenses SET status = ?, updated_at = NOW() WHERE id IN ($placeholders)");
            $params = array_merge([$value], $ids);
            $stmt->execute($params);
            $affected_rows = $stmt->rowCount();
            
            // Log the status change
            foreach ($ids as $expense_id) {
                logActivity($db, $_SESSION['user_id'], 'status_change', 'expenses', $expense_id, "Admin changed expense ID $expense_id status to: $value", $_SERVER['REMOTE_ADDR'] ?? 'unknown', $_SERVER['HTTP_USER_AGENT'] ?? '');
            }
            
            $message = "$affected_rows expense(s) status changed to '$value'";
            break;
            
        default:
            throw new Exception('Invalid action');
    }
    
    $db->commit();
    
    echo json_encode([
        'success' => true,
        'message' => $message,
        'affected_rows' => $affected_rows
    ]);
    
} catch (Exception $e) {
    if ($db->inTransaction()) {
        $db->rollback();
    }
    
    error_log('Bulk action error: ' . $e->getMessage());
    echo json_encode([
        'success' => false,
        'message' => $e->getMessage()
    ]);
}
?>
