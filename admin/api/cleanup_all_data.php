<?php
session_start();
require_once '../../config/database.php';
require_once '../../includes/functions.php';
require_once '../../includes/TransactionHelper.php';

header('Content-Type: application/json');

// Check if user is logged in and is admin
if (!isset($_SESSION['user_id']) || $_SESSION['role'] !== 'administrator') {
    http_response_code(401);
    echo json_encode(['success' => false, 'error' => 'Unauthorized']);
    exit();
}

try {
    $database = new Database();
    $db = $database->getConnection();
    
    // Get JSON input
    $input = json_decode(file_get_contents('php://input'), true);
    
    if (!$input) {
        throw new Exception('Invalid JSON input');
    }
    
    $tables = $input['tables'] ?? [];
    $preserve_admin = $input['preserve_admin'] ?? true;
    $reset_auto_increment = $input['reset_auto_increment'] ?? true;
    $create_backup = $input['create_backup'] ?? false;
    
    if (empty($tables)) {
        throw new Exception('No tables selected for cleanup');
    }
    
    // Define table cleanup order (respecting foreign key constraints)
    $cleanup_order = [
        'batch_performance_logs',
        'batch_documents',
        'batch_items',
        'batch_operations',
        'receipt_deductions',  // Must be deleted before receipt_numbers due to foreign key
        'receipt_numbers',
        'expenses',
        'activity_logs'
    ];
    
    // Filter selected tables in correct order
    $tables_to_cleanup = array_intersect($cleanup_order, $tables);
    
    $start_time = microtime(true);
    $total_deleted = 0;
    $tables_processed = 0;

    // Create backup if requested (before transaction)
    $backup_file = null;
    if ($create_backup) {
        $backup_timestamp = date('Y-m-d_H-i-s');
        $backup_dir = '../../backups';

        // Create backup directory if not exists
        if (!file_exists($backup_dir)) {
            mkdir($backup_dir, 0755, true);
        }

        $backup_file = "$backup_dir/cleanup_backup_$backup_timestamp.sql";
        $backup_success = false;

        try {
            // Create SQL dump for selected tables
            $sql_dump = "-- Cleanup Backup Created: " . date('Y-m-d H:i:s') . "\n\n";

            foreach ($tables_to_cleanup as $table) {
                // Get table structure
                $stmt = $db->prepare("SHOW CREATE TABLE $table");
                $stmt->execute();
                $create_table = $stmt->fetch();
                $sql_dump .= "-- Table: $table\n";
                $sql_dump .= $create_table['Create Table'] . ";\n\n";

                // Get table data
                $stmt = $db->prepare("SELECT * FROM $table");
                $stmt->execute();
                $rows = $stmt->fetchAll(PDO::FETCH_ASSOC);

                if (!empty($rows)) {
                    $sql_dump .= "-- Data for table: $table\n";
                    foreach ($rows as $row) {
                        $columns = array_keys($row);
                        $values = array_map(function($val) use ($db) {
                            return $val === null ? 'NULL' : $db->quote($val);
                        }, array_values($row));

                        $sql_dump .= "INSERT INTO $table (" . implode(', ', $columns) . ") VALUES (" . implode(', ', $values) . ");\n";
                    }
                    $sql_dump .= "\n";
                }
            }

            // Write backup file
            if (file_put_contents($backup_file, $sql_dump)) {
                $backup_success = true;
                logActivity($db, $_SESSION['user_id'], 'backup_created', null, null,
                           "Backup created: $backup_file",
                           $_SERVER['REMOTE_ADDR'] ?? 'unknown',
                           $_SERVER['HTTP_USER_AGENT'] ?? '');
            }
        } catch (Exception $e) {
            error_log("Backup creation failed: " . $e->getMessage());
        }

        if (!$backup_success) {
            throw new Exception("Failed to create backup file");
        }
    }

    // Start transaction after backup
    $transaction = createTransactionHelper($db);
    $transaction->beginTransaction();

    // Process each table
    foreach ($tables_to_cleanup as $table) {
        try {
            // Get count before deletion
            $stmt = $db->prepare("SELECT COUNT(*) as count FROM $table");
            $stmt->execute();
            $before_count = $stmt->fetch()['count'];
            
            if ($before_count == 0) {
                continue; // Skip empty tables
            }
            
            // Special handling for different tables
            switch ($table) {
                case 'activity_logs':
                    if ($preserve_admin) {
                        // Keep only current cleanup activity log
                        $stmt = $db->prepare("
                            DELETE FROM activity_logs
                            WHERE action_type != 'cleanup' OR created_at < NOW()
                        ");
                        $stmt->execute();
                    } else {
                        $stmt = $db->prepare("DELETE FROM activity_logs");
                        $stmt->execute();
                    }
                    break;

                case 'expenses':
                    // Always delete all expenses (test data cleanup)
                    $stmt = $db->prepare("DELETE FROM expenses");
                    $stmt->execute();
                    break;
                    
                case 'batch_operations':
                    // Delete all batch operations
                    $stmt = $db->prepare("DELETE FROM batch_operations");
                    $stmt->execute();
                    break;
                    
                case 'batch_items':
                    // Delete all batch items
                    $stmt = $db->prepare("DELETE FROM batch_items");
                    $stmt->execute();
                    break;
                    
                case 'batch_documents':
                    // Get file paths before deletion for cleanup
                    $stmt = $db->prepare("SELECT file_path FROM batch_documents");
                    $stmt->execute();
                    $files = $stmt->fetchAll();
                    
                    // Delete database records
                    $stmt = $db->prepare("DELETE FROM batch_documents");
                    $stmt->execute();
                    
                    // Clean up physical files
                    foreach ($files as $file) {
                        $file_path = '../../' . $file['file_path'];
                        if (file_exists($file_path)) {
                            unlink($file_path);
                        }
                    }
                    break;
                    
                case 'batch_performance_logs':
                    // Delete all performance logs
                    $stmt = $db->prepare("DELETE FROM batch_performance_logs");
                    $stmt->execute();
                    break;
                    
                case 'receipt_deductions':
                    // Get deduction image files before deletion for cleanup (if column exists)
                    try {
                        $stmt = $db->prepare("SELECT deduction_image FROM receipt_deductions WHERE deduction_image IS NOT NULL");
                        $stmt->execute();
                        $deduction_files = $stmt->fetchAll();

                        // Clean up physical deduction image files
                        foreach ($deduction_files as $file) {
                            $file_path = '../../uploads/deductions/' . $file['deduction_image'];
                            if (file_exists($file_path)) {
                                unlink($file_path);
                            }
                        }
                    } catch (Exception $e) {
                        // Column might not exist, continue with deletion
                    }

                    // Delete all receipt deductions
                    $stmt = $db->prepare("DELETE FROM receipt_deductions");
                    $stmt->execute();
                    break;

                case 'receipt_numbers':
                    // Delete all receipt numbers
                    $stmt = $db->prepare("DELETE FROM receipt_numbers");
                    $stmt->execute();
                    break;
                    
                default:
                    // Generic cleanup
                    $stmt = $db->prepare("DELETE FROM $table");
                    $stmt->execute();
                    break;
            }
            
            $deleted_count = $stmt->rowCount();
            $total_deleted += $deleted_count;
            $tables_processed++;
            
            // Reset AUTO_INCREMENT if requested
            if ($reset_auto_increment && $deleted_count > 0) {
                try {
                    $stmt = $db->prepare("ALTER TABLE $table AUTO_INCREMENT = 1");
                    $stmt->execute();
                } catch (Exception $e) {
                    // Some tables might not have AUTO_INCREMENT, ignore error
                }
            }
            
            // Log the cleanup activity
            logActivity($db, $_SESSION['user_id'], 'cleanup', $table, null, 
                       "Cleaned up table $table: deleted $deleted_count records", 
                       $_SERVER['REMOTE_ADDR'] ?? 'unknown', 
                       $_SERVER['HTTP_USER_AGENT'] ?? '');
            
        } catch (Exception $e) {
            // Log error but continue with other tables
            error_log("Error cleaning table $table: " . $e->getMessage());
            logActivity($db, $_SESSION['user_id'], 'cleanup_error', $table, null, 
                       "Error cleaning table $table: " . $e->getMessage(), 
                       $_SERVER['REMOTE_ADDR'] ?? 'unknown', 
                       $_SERVER['HTTP_USER_AGENT'] ?? '');
        }
    }

    $end_time = microtime(true);
    $duration = round($end_time - $start_time, 2) . ' seconds';

    // Log overall cleanup completion (before commit)
    logActivity($db, $_SESSION['user_id'], 'cleanup_completed', null, null,
               "Cleanup completed: $tables_processed tables, $total_deleted records deleted in $duration",
               $_SERVER['REMOTE_ADDR'] ?? 'unknown',
               $_SERVER['HTTP_USER_AGENT'] ?? '');

    // Commit transaction
    $transaction->commit();
    
    echo json_encode([
        'success' => true,
        'tables_processed' => $tables_processed,
        'total_deleted' => $total_deleted,
        'duration' => $duration,
        'message' => "Cleanup completed successfully"
    ]);
    
} catch (Exception $e) {
    if (isset($transaction) && $transaction->inTransaction()) {
        $transaction->rollback();
    }
    
    error_log('Cleanup error: ' . $e->getMessage());
    
    // Log error activity
    if (isset($db)) {
        try {
            logActivity($db, $_SESSION['user_id'], 'cleanup_failed', null, null, 
                       "Cleanup failed: " . $e->getMessage(), 
                       $_SERVER['REMOTE_ADDR'] ?? 'unknown', 
                       $_SERVER['HTTP_USER_AGENT'] ?? '');
        } catch (Exception $log_error) {
            error_log('Failed to log cleanup error: ' . $log_error->getMessage());
        }
    }
    
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'error' => $e->getMessage()
    ]);
}
?>
