<?php
session_start();
require_once '../../config/database.php';
require_once '../../includes/TransactionHelper.php';
require_once '../../includes/functions.php';

header('Content-Type: application/json');

// Check if user is logged in and is admin
if (!isset($_SESSION['user_id']) || $_SESSION['role'] !== 'administrator') {
    http_response_code(401);
    echo json_encode(['success' => false, 'message' => 'Unauthorized']);
    exit();
}

try {
    $database = new Database();
    $db = $database->getConnection();
    
    $transaction = createTransactionHelper($db);
    $transaction->beginTransaction();
    
    // Get test expense IDs first
    $stmt = $db->prepare("SELECT id, exno FROM expenses WHERE exno LIKE 'TEST%' OR exno LIKE 'DEMO%'");
    $stmt->execute();
    $test_expenses = $stmt->fetchAll();
    
    if (empty($test_expenses)) {
        echo json_encode([
            'success' => true,
            'message' => 'No test data found',
            'deleted_count' => 0
        ]);
        exit();
    }
    
    $test_ids = array_column($test_expenses, 'id');
    $placeholders = str_repeat('?,', count($test_ids) - 1) . '?';
    
    // Delete receipt numbers first
    $stmt = $db->prepare("DELETE FROM receipt_numbers WHERE expense_id IN ($placeholders)");
    $stmt->execute($test_ids);
    $receipt_count = $stmt->rowCount();
    
    // Delete expenses
    $stmt = $db->prepare("DELETE FROM expenses WHERE id IN ($placeholders)");
    $stmt->execute($test_ids);
    $expense_count = $stmt->rowCount();
    
    // Log the cleanup activity
    $expense_numbers = array_column($test_expenses, 'exno');
    $description = "Admin cleaned up test data: " . implode(', ', $expense_numbers);
    
    $stmt = $db->prepare("
        INSERT INTO user_activities (user_id, action, description, created_at) 
        VALUES (?, 'test_data_cleanup', ?, NOW())
    ");
    $stmt->execute([$_SESSION['user_id'], $description]);
    
    $transaction->commit();
    
    echo json_encode([
        'success' => true,
        'message' => "Test data cleanup completed",
        'deleted_count' => $expense_count,
        'receipt_count' => $receipt_count,
        'expense_numbers' => $expense_numbers
    ]);
    
} catch (Exception $e) {
    if (isset($transaction) && $transaction->inTransaction()) {
        $transaction->rollback();
    }
    
    error_log('Test data cleanup error: ' . $e->getMessage());
    echo json_encode([
        'success' => false,
        'message' => 'Cleanup failed: ' . $e->getMessage()
    ]);
}
?>
