<?php
session_start();
require_once '../../config/database.php';
require_once '../../includes/functions.php';

header('Content-Type: application/json');

// Check if user is logged in and is admin
if (!isset($_SESSION['user_id']) || $_SESSION['role'] !== 'administrator') {
    http_response_code(401);
    echo json_encode(['success' => false, 'error' => 'Unauthorized']);
    exit();
}

try {
    $database = new Database();
    $db = $database->getConnection();
    
    // Get JSON input
    $input = json_decode(file_get_contents('php://input'), true);
    
    if (!$input) {
        throw new Exception('Invalid JSON input');
    }
    
    $remove_table = $input['remove_table'] ?? false;
    $remove_columns = $input['remove_columns'] ?? false;
    $remove_views = $input['remove_views'] ?? false;
    $remove_procedures = $input['remove_procedures'] ?? false;
    
    if (!$remove_table && !$remove_columns && !$remove_views && !$remove_procedures) {
        throw new Exception('No cleanup options selected');
    }
    
    $messages = [];
    $start_time = microtime(true);
    
    // Start transaction
    $db->beginTransaction();
    
    try {
        // 1. Drop triggers first (they depend on procedures)
        if ($remove_procedures) {
            $triggers = [
                'tr_receipt_deductions_after_insert',
                'tr_receipt_deductions_after_update', 
                'tr_receipt_deductions_after_delete'
            ];
            
            foreach ($triggers as $trigger) {
                try {
                    $stmt = $db->prepare("DROP TRIGGER IF EXISTS $trigger");
                    $stmt->execute();
                    $messages[] = "Dropped trigger: $trigger";
                } catch (Exception $e) {
                    $messages[] = "Warning: Could not drop trigger $trigger - " . $e->getMessage();
                }
            }
        }
        
        // 2. Drop stored procedures
        if ($remove_procedures) {
            $procedures = [
                'AddReceiptDeduction',
                'UpdateReceiptCalculations'
            ];
            
            foreach ($procedures as $procedure) {
                try {
                    $stmt = $db->prepare("DROP PROCEDURE IF EXISTS $procedure");
                    $stmt->execute();
                    $messages[] = "Dropped procedure: $procedure";
                } catch (Exception $e) {
                    $messages[] = "Warning: Could not drop procedure $procedure - " . $e->getMessage();
                }
            }
        }
        
        // 3. Drop views
        if ($remove_views) {
            try {
                $stmt = $db->prepare("DROP VIEW IF EXISTS receipt_summary");
                $stmt->execute();
                $messages[] = "Dropped view: receipt_summary";
            } catch (Exception $e) {
                $messages[] = "Warning: Could not drop view receipt_summary - " . $e->getMessage();
            }
        }
        
        // 4. Drop receipt_deductions table
        if ($remove_table) {
            try {
                // First, clean up any deduction image files
                $stmt = $db->prepare("SELECT deduction_image FROM receipt_deductions WHERE deduction_image IS NOT NULL");
                $stmt->execute();
                $deduction_files = $stmt->fetchAll();
                
                foreach ($deduction_files as $file) {
                    $file_path = '../../uploads/deductions/' . $file['deduction_image'];
                    if (file_exists($file_path)) {
                        unlink($file_path);
                    }
                }
                
                $stmt = $db->prepare("DROP TABLE IF EXISTS receipt_deductions");
                $stmt->execute();
                $messages[] = "Dropped table: receipt_deductions";
                $messages[] = "Cleaned up deduction image files";
            } catch (Exception $e) {
                $messages[] = "Warning: Could not drop table receipt_deductions - " . $e->getMessage();
            }
        }
        
        // 5. Remove deduction columns from receipt_numbers table
        if ($remove_columns) {
            $columns_to_remove = [
                'gross_amount',
                'has_deductions', 
                'net_amount_calculated',
                'receipt_index'
            ];
            
            foreach ($columns_to_remove as $column) {
                try {
                    // Check if column exists first
                    $stmt = $db->prepare("
                        SELECT COUNT(*) as count 
                        FROM INFORMATION_SCHEMA.COLUMNS 
                        WHERE table_name = 'receipt_numbers' 
                        AND table_schema = DATABASE() 
                        AND column_name = ?
                    ");
                    $stmt->execute([$column]);
                    $exists = $stmt->fetch()['count'] > 0;
                    
                    if ($exists) {
                        $stmt = $db->prepare("ALTER TABLE receipt_numbers DROP COLUMN $column");
                        $stmt->execute();
                        $messages[] = "Removed column: receipt_numbers.$column";
                    } else {
                        $messages[] = "Column receipt_numbers.$column does not exist";
                    }
                } catch (Exception $e) {
                    $messages[] = "Warning: Could not remove column receipt_numbers.$column - " . $e->getMessage();
                }
            }
        }
        
        $end_time = microtime(true);
        $duration = round($end_time - $start_time, 2) . ' seconds';
        
        // Log the cleanup activity
        logActivity($db, $_SESSION['user_id'], 'structure_cleanup', null, null,
                   "Deduction structure cleanup completed: " . implode(', ', $messages),
                   $_SERVER['REMOTE_ADDR'] ?? 'unknown',
                   $_SERVER['HTTP_USER_AGENT'] ?? '');
        
        // Commit transaction
        $db->commit();
        
        echo json_encode([
            'success' => true,
            'message' => implode('<br>', $messages),
            'duration' => $duration
        ]);
        
    } catch (Exception $e) {
        $db->rollback();
        throw $e;
    }
    
} catch (Exception $e) {
    error_log('Deduction structure cleanup error: ' . $e->getMessage());
    
    // Log error activity
    if (isset($db)) {
        try {
            logActivity($db, $_SESSION['user_id'], 'structure_cleanup_failed', null, null, 
                       "Deduction structure cleanup failed: " . $e->getMessage(), 
                       $_SERVER['REMOTE_ADDR'] ?? 'unknown', 
                       $_SERVER['HTTP_USER_AGENT'] ?? '');
        } catch (Exception $log_error) {
            error_log('Failed to log structure cleanup error: ' . $log_error->getMessage());
        }
    }
    
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'error' => $e->getMessage()
    ]);
}
?>
