<?php
session_start();
require_once '../config/database.php';
require_once '../includes/functions.php';

// Check if user is logged in and is administrator
if (!isset($_SESSION['user_id']) || $_SESSION['role'] !== 'administrator') {
    header('Location: ../login.php');
    exit();
}

$database = new Database();
$db = $database->getConnection();

// Get user info
$stmt = $db->prepare("SELECT * FROM users WHERE id = ?");
$stmt->execute([$_SESSION['user_id']]);
$user = $stmt->fetch();

if (!$user || !$user['is_active']) {
    session_destroy();
    header('Location: ../login.php');
    exit();
}

$error = '';
$success = '';

// Handle form submissions
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    try {
        if (isset($_POST['action'])) {
            switch ($_POST['action']) {
                case 'add':
                    $name = trim($_POST['name']);
                    $contact_person = trim($_POST['contact_person']);
                    $phone = trim($_POST['phone']);
                    $email = trim($_POST['email']);
                    $address = trim($_POST['address']);

                    if (empty($name)) {
                        throw new Exception("Customer name is required.");
                    }

                    // Check if customer already exists
                    $stmt = $db->prepare("SELECT id FROM customers WHERE name = ?");
                    $stmt->execute([$name]);
                    if ($stmt->fetch()) {
                        throw new Exception("Customer with this name already exists.");
                    }

                    $stmt = $db->prepare("INSERT INTO customers (name, contact_person, phone, email, address, created_by) VALUES (?, ?, ?, ?, ?, ?)");
                    $stmt->execute([$name, $contact_person, $phone, $email, $address, $user['id']]);
                    
                    logActivity(
                        $db,
                        $user['id'],
                        'create',
                        'customers',
                        $db->lastInsertId(),
                        'Created new customer: ' . $name,
                        $_SERVER['REMOTE_ADDR'],
                        $_SERVER['HTTP_USER_AGENT']
                    );
                    
                    $success = "Customer added successfully!";
                    break;
                    
                case 'edit':
                    $id = $_POST['id'];
                    $name = trim($_POST['name']);
                    $contact_person = trim($_POST['contact_person']);
                    $phone = trim($_POST['phone']);
                    $email = trim($_POST['email']);
                    $address = trim($_POST['address']);
                    $is_active = isset($_POST['is_active']) ? 1 : 0;

                    if (empty($name)) {
                        throw new Exception("Customer name is required.");
                    }

                    // Check if another customer with same name exists
                    $stmt = $db->prepare("SELECT id FROM customers WHERE name = ? AND id != ?");
                    $stmt->execute([$name, $id]);
                    if ($stmt->fetch()) {
                        throw new Exception("Another customer with this name already exists.");
                    }

                    $stmt = $db->prepare("UPDATE customers SET name = ?, contact_person = ?, phone = ?, email = ?, address = ?, is_active = ? WHERE id = ?");
                    $stmt->execute([$name, $contact_person, $phone, $email, $address, $is_active, $id]);
                    
                    logActivity(
                        $db,
                        $user['id'],
                        'update',
                        'customers',
                        $id,
                        'Updated customer: ' . $name,
                        $_SERVER['REMOTE_ADDR'],
                        $_SERVER['HTTP_USER_AGENT']
                    );
                    
                    $success = "Customer updated successfully!";
                    break;
                    
                case 'delete':
                    $id = $_POST['id'];
                    
                    // Check if customer is used in any expenses
                    $stmt = $db->prepare("SELECT COUNT(*) as count FROM expenses WHERE customer_id = ?");
                    $stmt->execute([$id]);
                    $usage = $stmt->fetch();
                    
                    if ($usage['count'] > 0) {
                        throw new Exception("Cannot delete customer. It is used in " . $usage['count'] . " expense(s).");
                    }
                    
                    // Get customer name for logging
                    $stmt = $db->prepare("SELECT name FROM customers WHERE id = ?");
                    $stmt->execute([$id]);
                    $customer = $stmt->fetch();
                    
                    $stmt = $db->prepare("DELETE FROM customers WHERE id = ?");
                    $stmt->execute([$id]);
                    
                    logActivity(
                        $db,
                        $user['id'],
                        'delete',
                        'customers',
                        $id,
                        'Deleted customer: ' . ($customer['name'] ?? 'Unknown'),
                        $_SERVER['REMOTE_ADDR'],
                        $_SERVER['HTTP_USER_AGENT']
                    );
                    
                    $success = "Customer deleted successfully!";
                    break;
                    
                case 'import_csv':
                    if (!isset($_FILES['csv_file']) || $_FILES['csv_file']['error'] !== UPLOAD_ERR_OK) {
                        throw new Exception("Please select a valid CSV file.");
                    }
                    
                    $file = $_FILES['csv_file']['tmp_name'];
                    $handle = fopen($file, 'r');
                    
                    if (!$handle) {
                        throw new Exception("Cannot read CSV file.");
                    }
                    
                    $imported = 0;
                    $skipped = 0;
                    $row = 0;
                    
                    while (($data = fgetcsv($handle, 1000, ",")) !== FALSE) {
                        $row++;
                        
                        // Skip header row
                        if ($row === 1) {
                            continue;
                        }
                        
                        if (count($data) < 1 || empty(trim($data[0]))) {
                            $skipped++;
                            continue;
                        }
                        
                        $name = trim($data[0]);
                        $contact_person = isset($data[1]) ? trim($data[1]) : '';
                        $phone = isset($data[2]) ? trim($data[2]) : '';
                        $email = isset($data[3]) ? trim($data[3]) : '';
                        $address = isset($data[4]) ? trim($data[4]) : '';

                        // Check if customer already exists
                        $stmt = $db->prepare("SELECT id FROM customers WHERE name = ?");
                        $stmt->execute([$name]);
                        if ($stmt->fetch()) {
                            $skipped++;
                            continue;
                        }

                        // Insert new customer
                        $stmt = $db->prepare("INSERT INTO customers (name, contact_person, phone, email, address, created_by) VALUES (?, ?, ?, ?, ?, ?)");
                        $stmt->execute([$name, $contact_person, $phone, $email, $address, $user['id']]);
                        $imported++;
                    }
                    
                    fclose($handle);
                    
                    logActivity(
                        $db,
                        $user['id'],
                        'create',
                        'customers',
                        null,
                        "Imported $imported customers from CSV (skipped $skipped duplicates)",
                        $_SERVER['REMOTE_ADDR'],
                        $_SERVER['HTTP_USER_AGENT']
                    );
                    
                    $success = "CSV import completed! Imported: $imported, Skipped: $skipped";
                    break;
            }
        }
    } catch (Exception $e) {
        $error = $e->getMessage();
    }
}

// Get all customers
$search = $_GET['search'] ?? '';
$status_filter = $_GET['status'] ?? '';

$where_conditions = [];
$params = [];

if (!empty($search)) {
    $where_conditions[] = "(name LIKE ? OR contact_person LIKE ? OR phone LIKE ? OR email LIKE ?)";
    $params[] = "%$search%";
    $params[] = "%$search%";
    $params[] = "%$search%";
    $params[] = "%$search%";
}

if ($status_filter !== '') {
    $where_conditions[] = "is_active = ?";
    $params[] = $status_filter;
}

$where_clause = !empty($where_conditions) ? "WHERE " . implode(" AND ", $where_conditions) : "";

$stmt = $db->prepare("
    SELECT c.*, u.full_name as created_by_name,
           (SELECT COUNT(*) FROM expenses WHERE customer_id = c.id) as usage_count
    FROM customers c
    LEFT JOIN users u ON c.created_by = u.id
    $where_clause
    ORDER BY c.name ASC
");
$stmt->execute($params);
$customers = $stmt->fetchAll();
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Manage Customers - Admin Panel</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="../assets/css/style.css" rel="stylesheet">
</head>
<body>
    <?php include 'includes/admin_nav.php'; ?>

    <div class="container-fluid mt-4">
        <div class="row">
            <div class="col-12">
                <div class="d-flex justify-content-between align-items-center mb-4">
                    <h1><i class="fas fa-users me-2"></i>Manage Customers</h1>
                    <div class="d-flex gap-2">
                        <button type="button" class="btn btn-success" data-bs-toggle="modal" data-bs-target="#addCustomerModal">
                            <i class="fas fa-plus me-1"></i>Add Customer
                        </button>
                        <button type="button" class="btn btn-info" data-bs-toggle="modal" data-bs-target="#importCsvModal">
                            <i class="fas fa-file-csv me-1"></i>Import CSV
                        </button>
                    </div>
                </div>

                <?php if ($error): ?>
                    <div class="alert alert-danger alert-dismissible fade show" role="alert">
                        <i class="fas fa-exclamation-triangle me-2"></i>
                        <?php echo htmlspecialchars($error); ?>
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                <?php endif; ?>

                <?php if ($success): ?>
                    <div class="alert alert-success alert-dismissible fade show" role="alert">
                        <i class="fas fa-check-circle me-2"></i>
                        <?php echo htmlspecialchars($success); ?>
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                <?php endif; ?>

                <!-- Filters -->
                <div class="card mb-4">
                    <div class="card-body">
                        <form method="GET" class="row g-3">
                            <div class="col-md-6">
                                <label for="search" class="form-label">Search</label>
                                <input type="text" class="form-control" id="search" name="search"
                                       value="<?php echo htmlspecialchars($search); ?>"
                                       placeholder="Search by name, contact person, phone, or email...">
                            </div>
                            <div class="col-md-3">
                                <label for="status" class="form-label">Status</label>
                                <select class="form-select" id="status" name="status">
                                    <option value="">All Status</option>
                                    <option value="1" <?php echo $status_filter === '1' ? 'selected' : ''; ?>>Active</option>
                                    <option value="0" <?php echo $status_filter === '0' ? 'selected' : ''; ?>>Inactive</option>
                                </select>
                            </div>
                            <div class="col-md-3 d-flex align-items-end gap-2">
                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-search me-1"></i>Search
                                </button>
                                <a href="customers.php" class="btn btn-outline-secondary">
                                    <i class="fas fa-undo me-1"></i>Reset
                                </a>
                            </div>
                        </form>
                    </div>
                </div>

                <!-- Customers Table -->
                <div class="card">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <h5><i class="fas fa-list me-2"></i>Customers List (<?php echo count($customers); ?> customers)</h5>
                        <div class="d-flex gap-2">
                            <button type="button" class="btn btn-sm btn-outline-primary" onclick="exportCSV()">
                                <i class="fas fa-download me-1"></i>Export CSV
                            </button>
                        </div>
                    </div>
                    <div class="card-body">
                        <?php if (empty($customers)): ?>
                            <div class="text-center py-4">
                                <i class="fas fa-inbox fa-3x text-muted mb-3"></i>
                                <h5 class="text-muted">No customers found</h5>
                                <p class="text-muted">Add your first customer or adjust your search criteria.</p>
                            </div>
                        <?php else: ?>
                            <div class="table-responsive">
                                <table class="table table-hover">
                                    <thead>
                                        <tr>
                                            <th>ID</th>
                                            <th>Name</th>
                                            <th>Contact Person</th>
                                            <th>Phone</th>
                                            <th>Email</th>
                                            <th>Address</th>
                                            <th>Status</th>
                                            <th>Usage</th>
                                            <th>Created By</th>
                                            <th>Created Date</th>
                                            <th>Actions</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <?php foreach ($customers as $customer): ?>
                                            <tr>
                                                <td><?php echo $customer['id']; ?></td>
                                                <td><strong><?php echo htmlspecialchars($customer['name']); ?></strong></td>
                                                <td><?php echo htmlspecialchars($customer['contact_person'] ?? '-'); ?></td>
                                                <td><?php echo htmlspecialchars($customer['phone'] ?? '-'); ?></td>
                                                <td><?php echo htmlspecialchars($customer['email'] ?? '-'); ?></td>
                                                <td><?php echo htmlspecialchars($customer['address'] ?? '-'); ?></td>
                                                <td>
                                                    <?php if ($customer['is_active']): ?>
                                                        <span class="badge bg-success">Active</span>
                                                    <?php else: ?>
                                                        <span class="badge bg-secondary">Inactive</span>
                                                    <?php endif; ?>
                                                </td>
                                                <td>
                                                    <?php if ($customer['usage_count'] > 0): ?>
                                                        <span class="badge bg-info"><?php echo $customer['usage_count']; ?> expenses</span>
                                                    <?php else: ?>
                                                        <span class="text-muted">Not used</span>
                                                    <?php endif; ?>
                                                </td>
                                                <td><?php echo htmlspecialchars($customer['created_by_name'] ?: 'Unknown'); ?></td>
                                                <td><?php echo date('M j, Y', strtotime($customer['created_at'])); ?></td>
                                                <td>
                                                    <div class="btn-group btn-group-sm">
                                                        <button type="button" class="btn btn-outline-primary"
                                                                onclick="editCustomer(<?php echo htmlspecialchars(json_encode($customer)); ?>)">
                                                            <i class="fas fa-edit"></i>
                                                        </button>
                                                        <?php if ($customer['usage_count'] == 0): ?>
                                                            <button type="button" class="btn btn-outline-danger"
                                                                    onclick="deleteCustomer(<?php echo $customer['id']; ?>, '<?php echo htmlspecialchars($customer['name']); ?>')">
                                                                <i class="fas fa-trash"></i>
                                                            </button>
                                                        <?php else: ?>
                                                            <button type="button" class="btn btn-outline-secondary" disabled title="Cannot delete - customer is in use">
                                                                <i class="fas fa-lock"></i>
                                                            </button>
                                                        <?php endif; ?>
                                                    </div>
                                                </td>
                                            </tr>
                                        <?php endforeach; ?>
                                    </tbody>
                                </table>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Add Customer Modal -->
    <div class="modal fade" id="addCustomerModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">
                        <i class="fas fa-plus me-2"></i>Add New Customer
                    </h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <form method="POST">
                    <div class="modal-body">
                        <input type="hidden" name="action" value="add">

                        <div class="mb-3">
                            <label for="name" class="form-label">Customer Name <span class="text-danger">*</span></label>
                            <input type="text" class="form-control" id="name" name="name" required>
                        </div>

                        <div class="mb-3">
                            <label for="contact_person" class="form-label">Contact Person</label>
                            <input type="text" class="form-control" id="contact_person" name="contact_person">
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="phone" class="form-label">Phone</label>
                                    <input type="text" class="form-control" id="phone" name="phone">
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="email" class="form-label">Email</label>
                                    <input type="email" class="form-control" id="email" name="email">
                                </div>
                            </div>
                        </div>

                        <div class="mb-3">
                            <label for="address" class="form-label">Address</label>
                            <textarea class="form-control" id="address" name="address" rows="3"
                                      placeholder="Customer address..."></textarea>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                            <i class="fas fa-times me-1"></i>Cancel
                        </button>
                        <button type="submit" class="btn btn-success">
                            <i class="fas fa-plus me-1"></i>Add Customer
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- Edit Customer Modal -->
    <div class="modal fade" id="editCustomerModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">
                        <i class="fas fa-edit me-2"></i>Edit Customer
                    </h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <form method="POST">
                    <div class="modal-body">
                        <input type="hidden" name="action" value="edit">
                        <input type="hidden" id="edit_id" name="id">

                        <div class="mb-3">
                            <label for="edit_name" class="form-label">Customer Name <span class="text-danger">*</span></label>
                            <input type="text" class="form-control" id="edit_name" name="name" required>
                        </div>

                        <div class="mb-3">
                            <label for="edit_contact_person" class="form-label">Contact Person</label>
                            <input type="text" class="form-control" id="edit_contact_person" name="contact_person">
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="edit_phone" class="form-label">Phone</label>
                                    <input type="text" class="form-control" id="edit_phone" name="phone">
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="edit_email" class="form-label">Email</label>
                                    <input type="email" class="form-control" id="edit_email" name="email">
                                </div>
                            </div>
                        </div>

                        <div class="mb-3">
                            <label for="edit_address" class="form-label">Address</label>
                            <textarea class="form-control" id="edit_address" name="address" rows="3"
                                      placeholder="Customer address..."></textarea>
                        </div>

                        <div class="mb-3">
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="edit_is_active" name="is_active" checked>
                                <label class="form-check-label" for="edit_is_active">
                                    Active
                                </label>
                            </div>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                            <i class="fas fa-times me-1"></i>Cancel
                        </button>
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save me-1"></i>Update Customer
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- Import CSV Modal -->
    <div class="modal fade" id="importCsvModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">
                        <i class="fas fa-file-csv me-2"></i>Import Customers from CSV
                    </h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <form method="POST" enctype="multipart/form-data">
                    <div class="modal-body">
                        <input type="hidden" name="action" value="import_csv">

                        <div class="alert alert-info">
                            <i class="fas fa-info-circle me-2"></i>
                            <strong>CSV Format:</strong> Name, Contact Person, Phone, Email, Address<br>
                            <small>First row should be headers. Duplicate names will be skipped.</small>
                        </div>

                        <div class="mb-3">
                            <label for="csv_file" class="form-label">Select CSV File <span class="text-danger">*</span></label>
                            <input type="file" class="form-control" id="csv_file" name="csv_file" accept=".csv" required>
                        </div>

                        <div class="d-grid">
                            <button type="button" class="btn btn-outline-info btn-sm" onclick="downloadSampleCSV('customers')">
                                <i class="fas fa-download me-1"></i>Download Sample CSV
                            </button>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                            <i class="fas fa-times me-1"></i>Cancel
                        </button>
                        <button type="submit" class="btn btn-info">
                            <i class="fas fa-upload me-1"></i>Import CSV
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- Delete Customer Modal -->
    <div class="modal fade" id="deleteModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">
                        <i class="fas fa-trash me-2"></i>Delete Customer
                    </h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <form method="POST">
                    <div class="modal-body">
                        <input type="hidden" name="action" value="delete">
                        <input type="hidden" id="delete_id" name="id">

                        <div class="alert alert-warning">
                            <i class="fas fa-exclamation-triangle me-2"></i>
                            Are you sure you want to delete customer "<strong id="delete_name"></strong>"?
                            <br><small>This action cannot be undone.</small>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                            <i class="fas fa-times me-1"></i>Cancel
                        </button>
                        <button type="submit" class="btn btn-danger">
                            <i class="fas fa-trash me-1"></i>Delete Customer
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- Scripts -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script src="../assets/js/admin-master-data.js"></script>
    <script>
        // Export CSV function specific to customers
        function exportCSV() {
            const table = document.querySelector('.table');
            const rows = table.querySelectorAll('tr');
            let csv = [];

            // Headers
            const headers = ['ID', 'Name', 'Contact Person', 'Phone', 'Email', 'Address', 'Status', 'Usage Count', 'Created By', 'Created Date'];
            csv.push(headers.join(','));

            // Data rows
            for (let i = 1; i < rows.length; i++) {
                const row = rows[i];
                const cols = row.querySelectorAll('td');
                if (cols.length > 0) {
                    const rowData = [
                        cols[0].textContent.trim(), // ID
                        '"' + cols[1].textContent.trim().replace(/"/g, '""') + '"', // Name
                        '"' + cols[2].textContent.trim().replace(/"/g, '""') + '"', // Contact Person
                        '"' + cols[3].textContent.trim().replace(/"/g, '""') + '"', // Phone
                        '"' + cols[4].textContent.trim().replace(/"/g, '""') + '"', // Email
                        '"' + cols[5].textContent.trim().replace(/"/g, '""') + '"', // Address
                        cols[6].textContent.trim(), // Status
                        cols[7].textContent.trim(), // Usage
                        '"' + cols[8].textContent.trim().replace(/"/g, '""') + '"', // Created By
                        cols[9].textContent.trim() // Created Date
                    ];
                    csv.push(rowData.join(','));
                }
            }

            // Download
            const csvContent = csv.join('\n');
            const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
            const link = document.createElement('a');
            const url = URL.createObjectURL(blob);
            link.setAttribute('href', url);
            link.setAttribute('download', 'customers_' + new Date().toISOString().split('T')[0] + '.csv');
            link.style.visibility = 'hidden';
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);
        }
    </script>
</body>
</html>
