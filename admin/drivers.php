<?php
session_start();
require_once '../config/database.php';
require_once '../includes/functions.php';

// Check if user is logged in and is administrator
if (!isset($_SESSION['user_id']) || $_SESSION['role'] !== 'administrator') {
    header('Location: ../login.php');
    exit();
}

$database = new Database();
$db = $database->getConnection();

// Get user info
$stmt = $db->prepare("SELECT * FROM users WHERE id = ?");
$stmt->execute([$_SESSION['user_id']]);
$user = $stmt->fetch();

if (!$user || !$user['is_active']) {
    session_destroy();
    header('Location: ../login.php');
    exit();
}

$error = '';
$success = '';

// Handle form submissions
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    try {
        if (isset($_POST['action'])) {
            switch ($_POST['action']) {
                case 'add':
                    $name = trim($_POST['name']);
                    $license_number = trim($_POST['license_number']);
                    $phone = trim($_POST['phone']);
                    $vehicle_plate = trim($_POST['vehicle_plate']);
                    $payment_account_no = trim($_POST['payment_account_no']);

                    if (empty($name)) {
                        throw new Exception("Driver name is required.");
                    }

                    // Check if driver already exists
                    $stmt = $db->prepare("SELECT id FROM drivers WHERE name = ?");
                    $stmt->execute([$name]);
                    if ($stmt->fetch()) {
                        throw new Exception("Driver with this name already exists.");
                    }

                    $stmt = $db->prepare("INSERT INTO drivers (name, license_number, phone, vehicle_plate, payment_account_no, created_by) VALUES (?, ?, ?, ?, ?, ?)");
                    $stmt->execute([$name, $license_number, $phone, $vehicle_plate, $payment_account_no, $user['id']]);
                    
                    logActivity(
                        $db,
                        $user['id'],
                        'create',
                        'drivers',
                        $db->lastInsertId(),
                        'Created new driver: ' . $name,
                        $_SERVER['REMOTE_ADDR'],
                        $_SERVER['HTTP_USER_AGENT']
                    );
                    
                    $success = "Driver added successfully!";
                    break;
                    
                case 'edit':
                    $id = $_POST['id'];
                    $name = trim($_POST['name']);
                    $license_number = trim($_POST['license_number']);
                    $phone = trim($_POST['phone']);
                    $vehicle_plate = trim($_POST['vehicle_plate']);
                    $payment_account_no = trim($_POST['payment_account_no']);
                    $is_active = isset($_POST['is_active']) ? 1 : 0;

                    if (empty($name)) {
                        throw new Exception("Driver name is required.");
                    }

                    // Check if another driver with same name exists
                    $stmt = $db->prepare("SELECT id FROM drivers WHERE name = ? AND id != ?");
                    $stmt->execute([$name, $id]);
                    if ($stmt->fetch()) {
                        throw new Exception("Another driver with this name already exists.");
                    }

                    $stmt = $db->prepare("UPDATE drivers SET name = ?, license_number = ?, phone = ?, vehicle_plate = ?, payment_account_no = ?, is_active = ? WHERE id = ?");
                    $stmt->execute([$name, $license_number, $phone, $vehicle_plate, $payment_account_no, $is_active, $id]);
                    
                    logActivity(
                        $db,
                        $user['id'],
                        'update',
                        'drivers',
                        $id,
                        'Updated driver: ' . $name,
                        $_SERVER['REMOTE_ADDR'],
                        $_SERVER['HTTP_USER_AGENT']
                    );
                    
                    $success = "Driver updated successfully!";
                    break;
                    
                case 'delete':
                    $id = $_POST['id'];
                    
                    // Check if driver is used in any expenses
                    $stmt = $db->prepare("SELECT COUNT(*) as count FROM expenses WHERE driver_id = ?");
                    $stmt->execute([$id]);
                    $usage = $stmt->fetch();
                    
                    if ($usage['count'] > 0) {
                        throw new Exception("Cannot delete driver. It is used in " . $usage['count'] . " expense(s).");
                    }
                    
                    // Get driver name for logging
                    $stmt = $db->prepare("SELECT name FROM drivers WHERE id = ?");
                    $stmt->execute([$id]);
                    $driver = $stmt->fetch();
                    
                    $stmt = $db->prepare("DELETE FROM drivers WHERE id = ?");
                    $stmt->execute([$id]);
                    
                    logActivity(
                        $db,
                        $user['id'],
                        'delete',
                        'drivers',
                        $id,
                        'Deleted driver: ' . ($driver['name'] ?? 'Unknown'),
                        $_SERVER['REMOTE_ADDR'],
                        $_SERVER['HTTP_USER_AGENT']
                    );
                    
                    $success = "Driver deleted successfully!";
                    break;
                    
                case 'import_csv':
                    if (!isset($_FILES['csv_file']) || $_FILES['csv_file']['error'] !== UPLOAD_ERR_OK) {
                        throw new Exception("Please select a valid CSV file.");
                    }
                    
                    $file = $_FILES['csv_file']['tmp_name'];
                    $handle = fopen($file, 'r');
                    
                    if (!$handle) {
                        throw new Exception("Cannot read CSV file.");
                    }
                    
                    $imported = 0;
                    $skipped = 0;
                    $row = 0;
                    
                    while (($data = fgetcsv($handle, 1000, ",")) !== FALSE) {
                        $row++;
                        
                        // Skip header row
                        if ($row === 1) {
                            continue;
                        }
                        
                        if (count($data) < 1 || empty(trim($data[0]))) {
                            $skipped++;
                            continue;
                        }
                        
                        $name = trim($data[0]);
                        $license_number = isset($data[1]) ? trim($data[1]) : '';
                        $phone = isset($data[2]) ? trim($data[2]) : '';
                        $vehicle_plate = isset($data[3]) ? trim($data[3]) : '';
                        $payment_account_no = isset($data[4]) ? trim($data[4]) : '';

                        // Check if driver already exists
                        $stmt = $db->prepare("SELECT id FROM drivers WHERE name = ?");
                        $stmt->execute([$name]);
                        if ($stmt->fetch()) {
                            $skipped++;
                            continue;
                        }

                        // Insert new driver
                        $stmt = $db->prepare("INSERT INTO drivers (name, license_number, phone, vehicle_plate, payment_account_no, created_by) VALUES (?, ?, ?, ?, ?, ?)");
                        $stmt->execute([$name, $license_number, $phone, $vehicle_plate, $payment_account_no, $user['id']]);
                        $imported++;
                    }
                    
                    fclose($handle);
                    
                    logActivity(
                        $db,
                        $user['id'],
                        'create',
                        'drivers',
                        null,
                        "Imported $imported drivers from CSV (skipped $skipped duplicates)",
                        $_SERVER['REMOTE_ADDR'],
                        $_SERVER['HTTP_USER_AGENT']
                    );
                    
                    $success = "CSV import completed! Imported: $imported, Skipped: $skipped";
                    break;
            }
        }
    } catch (Exception $e) {
        $error = $e->getMessage();
    }
}

// Get all drivers
$search = $_GET['search'] ?? '';
$status_filter = $_GET['status'] ?? '';

$where_conditions = [];
$params = [];

if (!empty($search)) {
    $where_conditions[] = "(name LIKE ? OR license_number LIKE ? OR phone LIKE ? OR vehicle_plate LIKE ?)";
    $params[] = "%$search%";
    $params[] = "%$search%";
    $params[] = "%$search%";
    $params[] = "%$search%";
}

if ($status_filter !== '') {
    $where_conditions[] = "is_active = ?";
    $params[] = $status_filter;
}

$where_clause = !empty($where_conditions) ? "WHERE " . implode(" AND ", $where_conditions) : "";

$stmt = $db->prepare("
    SELECT d.*, u.full_name as created_by_name,
           (SELECT COUNT(*) FROM expenses WHERE driver_id = d.id) as usage_count
    FROM drivers d
    LEFT JOIN users u ON d.created_by = u.id
    $where_clause
    ORDER BY d.name ASC
");
$stmt->execute($params);
$drivers = $stmt->fetchAll();
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Manage Drivers - Admin Panel</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="../assets/css/style.css" rel="stylesheet">
</head>
<body>
    <?php include 'includes/admin_nav.php'; ?>

    <div class="container-fluid mt-4">
        <div class="row">
            <div class="col-12">
                <div class="d-flex justify-content-between align-items-center mb-4">
                    <h1><i class="fas fa-id-card me-2"></i>Manage Drivers</h1>
                    <div class="d-flex gap-2">
                        <button type="button" class="btn btn-success" data-bs-toggle="modal" data-bs-target="#addDriverModal">
                            <i class="fas fa-plus me-1"></i>Add Driver
                        </button>
                        <button type="button" class="btn btn-info" data-bs-toggle="modal" data-bs-target="#importCsvModal">
                            <i class="fas fa-file-csv me-1"></i>Import CSV
                        </button>
                    </div>
                </div>

                <?php if ($error): ?>
                    <div class="alert alert-danger alert-dismissible fade show" role="alert">
                        <i class="fas fa-exclamation-triangle me-2"></i>
                        <?php echo htmlspecialchars($error); ?>
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                <?php endif; ?>

                <?php if ($success): ?>
                    <div class="alert alert-success alert-dismissible fade show" role="alert">
                        <i class="fas fa-check-circle me-2"></i>
                        <?php echo htmlspecialchars($success); ?>
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                <?php endif; ?>

                <!-- Filters -->
                <div class="card mb-4">
                    <div class="card-body">
                        <form method="GET" class="row g-3">
                            <div class="col-md-6">
                                <label for="search" class="form-label">Search</label>
                                <input type="text" class="form-control" id="search" name="search"
                                       value="<?php echo htmlspecialchars($search); ?>"
                                       placeholder="Search by name, license, phone, or vehicle plate...">
                            </div>
                            <div class="col-md-3">
                                <label for="status" class="form-label">Status</label>
                                <select class="form-select" id="status" name="status">
                                    <option value="">All Status</option>
                                    <option value="1" <?php echo $status_filter === '1' ? 'selected' : ''; ?>>Active</option>
                                    <option value="0" <?php echo $status_filter === '0' ? 'selected' : ''; ?>>Inactive</option>
                                </select>
                            </div>
                            <div class="col-md-3 d-flex align-items-end gap-2">
                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-search me-1"></i>Search
                                </button>
                                <a href="drivers.php" class="btn btn-outline-secondary">
                                    <i class="fas fa-undo me-1"></i>Reset
                                </a>
                            </div>
                        </form>
                    </div>
                </div>

                <!-- Drivers Table -->
                <div class="card">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <h5><i class="fas fa-list me-2"></i>Drivers List (<?php echo count($drivers); ?> drivers)</h5>
                        <div class="d-flex gap-2">
                            <button type="button" class="btn btn-sm btn-outline-primary" onclick="exportCSV()">
                                <i class="fas fa-download me-1"></i>Export CSV
                            </button>
                        </div>
                    </div>
                    <div class="card-body">
                        <?php if (empty($drivers)): ?>
                            <div class="text-center py-4">
                                <i class="fas fa-inbox fa-3x text-muted mb-3"></i>
                                <h5 class="text-muted">No drivers found</h5>
                                <p class="text-muted">Add your first driver or adjust your search criteria.</p>
                            </div>
                        <?php else: ?>
                            <div class="table-responsive">
                                <table class="table table-hover">
                                    <thead>
                                        <tr>
                                            <th>ID</th>
                                            <th>Name</th>
                                            <th>License Number</th>
                                            <th>Phone</th>
                                            <th>Vehicle Plate</th>
                                            <th>Payment Account</th>
                                            <th>Status</th>
                                            <th>Usage</th>
                                            <th>Created By</th>
                                            <th>Created Date</th>
                                            <th>Actions</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <?php foreach ($drivers as $driver): ?>
                                            <tr>
                                                <td><?php echo $driver['id']; ?></td>
                                                <td><strong><?php echo htmlspecialchars($driver['name']); ?></strong></td>
                                                <td><?php echo htmlspecialchars($driver['license_number'] ?? '-'); ?></td>
                                                <td><?php echo htmlspecialchars($driver['phone'] ?? '-'); ?></td>
                                                <td><?php echo htmlspecialchars($driver['vehicle_plate'] ?? '-'); ?></td>
                                                <td><?php echo htmlspecialchars($driver['payment_account_no'] ?? '-'); ?></td>
                                                <td>
                                                    <?php if ($driver['is_active']): ?>
                                                        <span class="badge bg-success">Active</span>
                                                    <?php else: ?>
                                                        <span class="badge bg-secondary">Inactive</span>
                                                    <?php endif; ?>
                                                </td>
                                                <td>
                                                    <?php if ($driver['usage_count'] > 0): ?>
                                                        <span class="badge bg-info"><?php echo $driver['usage_count']; ?> expenses</span>
                                                    <?php else: ?>
                                                        <span class="text-muted">Not used</span>
                                                    <?php endif; ?>
                                                </td>
                                                <td><?php echo htmlspecialchars($driver['created_by_name'] ?: 'Unknown'); ?></td>
                                                <td><?php echo date('M j, Y', strtotime($driver['created_at'])); ?></td>
                                                <td>
                                                    <div class="btn-group btn-group-sm">
                                                        <button type="button" class="btn btn-outline-primary"
                                                                onclick="editDriver(<?php echo htmlspecialchars(json_encode($driver)); ?>)">
                                                            <i class="fas fa-edit"></i>
                                                        </button>
                                                        <?php if ($driver['usage_count'] == 0): ?>
                                                            <button type="button" class="btn btn-outline-danger"
                                                                    onclick="deleteDriver(<?php echo $driver['id']; ?>, '<?php echo htmlspecialchars($driver['name']); ?>')">
                                                                <i class="fas fa-trash"></i>
                                                            </button>
                                                        <?php else: ?>
                                                            <button type="button" class="btn btn-outline-secondary" disabled title="Cannot delete - driver is in use">
                                                                <i class="fas fa-lock"></i>
                                                            </button>
                                                        <?php endif; ?>
                                                    </div>
                                                </td>
                                            </tr>
                                        <?php endforeach; ?>
                                    </tbody>
                                </table>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Add Driver Modal -->
    <div class="modal fade" id="addDriverModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">
                        <i class="fas fa-plus me-2"></i>Add New Driver
                    </h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <form method="POST">
                    <div class="modal-body">
                        <input type="hidden" name="action" value="add">

                        <div class="mb-3">
                            <label for="name" class="form-label">Driver Name <span class="text-danger">*</span></label>
                            <input type="text" class="form-control" id="name" name="name" required>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="license_number" class="form-label">License Number</label>
                                    <input type="text" class="form-control" id="license_number" name="license_number">
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="phone" class="form-label">Phone</label>
                                    <input type="text" class="form-control" id="phone" name="phone">
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="vehicle_plate" class="form-label">Vehicle Plate</label>
                                    <input type="text" class="form-control" id="vehicle_plate" name="vehicle_plate">
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="payment_account_no" class="form-label">Payment Account No.</label>
                                    <input type="text" class="form-control" id="payment_account_no" name="payment_account_no">
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                            <i class="fas fa-times me-1"></i>Cancel
                        </button>
                        <button type="submit" class="btn btn-success">
                            <i class="fas fa-plus me-1"></i>Add Driver
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- Edit Driver Modal -->
    <div class="modal fade" id="editDriverModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">
                        <i class="fas fa-edit me-2"></i>Edit Driver
                    </h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <form method="POST">
                    <div class="modal-body">
                        <input type="hidden" name="action" value="edit">
                        <input type="hidden" id="edit_id" name="id">

                        <div class="mb-3">
                            <label for="edit_name" class="form-label">Driver Name <span class="text-danger">*</span></label>
                            <input type="text" class="form-control" id="edit_name" name="name" required>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="edit_license_number" class="form-label">License Number</label>
                                    <input type="text" class="form-control" id="edit_license_number" name="license_number">
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="edit_phone" class="form-label">Phone</label>
                                    <input type="text" class="form-control" id="edit_phone" name="phone">
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="edit_vehicle_plate" class="form-label">Vehicle Plate</label>
                                    <input type="text" class="form-control" id="edit_vehicle_plate" name="vehicle_plate">
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="edit_payment_account_no" class="form-label">Payment Account No.</label>
                                    <input type="text" class="form-control" id="edit_payment_account_no" name="payment_account_no">
                                </div>
                            </div>
                        </div>

                        <div class="mb-3">
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="edit_is_active" name="is_active" checked>
                                <label class="form-check-label" for="edit_is_active">
                                    Active
                                </label>
                            </div>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                            <i class="fas fa-times me-1"></i>Cancel
                        </button>
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save me-1"></i>Update Driver
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- Import CSV Modal -->
    <div class="modal fade" id="importCsvModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">
                        <i class="fas fa-file-csv me-2"></i>Import Drivers from CSV
                    </h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <form method="POST" enctype="multipart/form-data">
                    <div class="modal-body">
                        <input type="hidden" name="action" value="import_csv">

                        <div class="alert alert-info">
                            <i class="fas fa-info-circle me-2"></i>
                            <strong>CSV Format:</strong> Name, License Number, Phone, Vehicle Plate, Payment Account No.<br>
                            <small>First row should be headers. Duplicate names will be skipped.</small>
                        </div>

                        <div class="mb-3">
                            <label for="csv_file" class="form-label">Select CSV File <span class="text-danger">*</span></label>
                            <input type="file" class="form-control" id="csv_file" name="csv_file" accept=".csv" required>
                        </div>

                        <div class="d-grid">
                            <button type="button" class="btn btn-outline-info btn-sm" onclick="downloadSampleCSV('drivers')">
                                <i class="fas fa-download me-1"></i>Download Sample CSV
                            </button>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                            <i class="fas fa-times me-1"></i>Cancel
                        </button>
                        <button type="submit" class="btn btn-info">
                            <i class="fas fa-upload me-1"></i>Import CSV
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- Delete Driver Modal -->
    <div class="modal fade" id="deleteModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">
                        <i class="fas fa-trash me-2"></i>Delete Driver
                    </h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <form method="POST">
                    <div class="modal-body">
                        <input type="hidden" name="action" value="delete">
                        <input type="hidden" id="delete_id" name="id">

                        <div class="alert alert-warning">
                            <i class="fas fa-exclamation-triangle me-2"></i>
                            Are you sure you want to delete driver "<strong id="delete_name"></strong>"?
                            <br><small>This action cannot be undone.</small>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                            <i class="fas fa-times me-1"></i>Cancel
                        </button>
                        <button type="submit" class="btn btn-danger">
                            <i class="fas fa-trash me-1"></i>Delete Driver
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- Scripts -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script src="../assets/js/admin-master-data.js"></script>
    <script>
        // Export CSV function specific to drivers
        function exportCSV() {
            const table = document.querySelector('.table');
            const rows = table.querySelectorAll('tr');
            let csv = [];

            // Headers
            const headers = ['ID', 'Name', 'License Number', 'Phone', 'Vehicle Plate', 'Payment Account', 'Status', 'Usage Count', 'Created By', 'Created Date'];
            csv.push(headers.join(','));

            // Data rows
            for (let i = 1; i < rows.length; i++) {
                const row = rows[i];
                const cols = row.querySelectorAll('td');
                if (cols.length > 0) {
                    const rowData = [
                        cols[0].textContent.trim(), // ID
                        '"' + cols[1].textContent.trim().replace(/"/g, '""') + '"', // Name
                        '"' + cols[2].textContent.trim().replace(/"/g, '""') + '"', // License Number
                        '"' + cols[3].textContent.trim().replace(/"/g, '""') + '"', // Phone
                        '"' + cols[4].textContent.trim().replace(/"/g, '""') + '"', // Vehicle Plate
                        '"' + cols[5].textContent.trim().replace(/"/g, '""') + '"', // Payment Account
                        cols[6].textContent.trim(), // Status
                        cols[7].textContent.trim(), // Usage
                        '"' + cols[8].textContent.trim().replace(/"/g, '""') + '"', // Created By
                        cols[9].textContent.trim() // Created Date
                    ];
                    csv.push(rowData.join(','));
                }
            }

            // Download
            const csvContent = csv.join('\n');
            const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
            const link = document.createElement('a');
            const url = URL.createObjectURL(blob);
            link.setAttribute('href', url);
            link.setAttribute('download', 'drivers_' + new Date().toISOString().split('T')[0] + '.csv');
            link.style.visibility = 'hidden';
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);
        }
    </script>
</body>
</html>
