<?php
session_start();
require_once '../config/database.php';
require_once '../includes/functions.php';

// Check if user is logged in and is administrator
if (!isset($_SESSION['user_id']) || $_SESSION['role'] !== 'administrator') {
    header('Location: ../login.php');
    exit();
}

$database = new Database();
$db = $database->getConnection();

// Get user info
$stmt = $db->prepare("SELECT * FROM users WHERE id = ?");
$stmt->execute([$_SESSION['user_id']]);
$user = $stmt->fetch();

if (!$user || !$user['is_active']) {
    session_destroy();
    header('Location: ../login.php');
    exit();
}

$error = '';
$success = '';

// Handle form submissions
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    try {
        if (isset($_POST['action'])) {
            switch ($_POST['action']) {
                case 'add':
                    $name = trim($_POST['name']);
                    $description = trim($_POST['description']);
                    
                    if (empty($name)) {
                        throw new Exception("Item name is required.");
                    }
                    
                    // Check if item already exists
                    $stmt = $db->prepare("SELECT id FROM items WHERE name = ?");
                    $stmt->execute([$name]);
                    if ($stmt->fetch()) {
                        throw new Exception("Item with this name already exists.");
                    }
                    
                    $stmt = $db->prepare("INSERT INTO items (name, description, created_by) VALUES (?, ?, ?)");
                    $stmt->execute([$name, $description, $user['id']]);
                    
                    logActivity(
                        $db,
                        $user['id'],
                        'create',
                        'items',
                        $db->lastInsertId(),
                        'Created new item: ' . $name,
                        $_SERVER['REMOTE_ADDR'],
                        $_SERVER['HTTP_USER_AGENT']
                    );
                    
                    $success = "Item added successfully!";
                    break;
                    
                case 'edit':
                    $id = $_POST['id'];
                    $name = trim($_POST['name']);
                    $description = trim($_POST['description']);
                    $is_active = isset($_POST['is_active']) ? 1 : 0;
                    
                    if (empty($name)) {
                        throw new Exception("Item name is required.");
                    }
                    
                    // Check if another item with same name exists
                    $stmt = $db->prepare("SELECT id FROM items WHERE name = ? AND id != ?");
                    $stmt->execute([$name, $id]);
                    if ($stmt->fetch()) {
                        throw new Exception("Another item with this name already exists.");
                    }
                    
                    $stmt = $db->prepare("UPDATE items SET name = ?, description = ?, is_active = ? WHERE id = ?");
                    $stmt->execute([$name, $description, $is_active, $id]);
                    
                    logActivity(
                        $db,
                        $user['id'],
                        'update',
                        'items',
                        $id,
                        'Updated item: ' . $name,
                        $_SERVER['REMOTE_ADDR'],
                        $_SERVER['HTTP_USER_AGENT']
                    );
                    
                    $success = "Item updated successfully!";
                    break;
                    
                case 'delete':
                    $id = $_POST['id'];
                    
                    // Check if item is used in any expenses
                    $stmt = $db->prepare("SELECT COUNT(*) as count FROM expenses WHERE item_id = ?");
                    $stmt->execute([$id]);
                    $usage = $stmt->fetch();
                    
                    if ($usage['count'] > 0) {
                        throw new Exception("Cannot delete item. It is used in " . $usage['count'] . " expense(s).");
                    }
                    
                    // Get item name for logging
                    $stmt = $db->prepare("SELECT name FROM items WHERE id = ?");
                    $stmt->execute([$id]);
                    $item = $stmt->fetch();
                    
                    $stmt = $db->prepare("DELETE FROM items WHERE id = ?");
                    $stmt->execute([$id]);
                    
                    logActivity(
                        $db,
                        $user['id'],
                        'delete',
                        'items',
                        $id,
                        'Deleted item: ' . ($item['name'] ?? 'Unknown'),
                        $_SERVER['REMOTE_ADDR'],
                        $_SERVER['HTTP_USER_AGENT']
                    );
                    
                    $success = "Item deleted successfully!";
                    break;
                    
                case 'import_csv':
                    if (!isset($_FILES['csv_file']) || $_FILES['csv_file']['error'] !== UPLOAD_ERR_OK) {
                        throw new Exception("Please select a valid CSV file.");
                    }
                    
                    $file = $_FILES['csv_file']['tmp_name'];
                    $handle = fopen($file, 'r');
                    
                    if (!$handle) {
                        throw new Exception("Cannot read CSV file.");
                    }
                    
                    $imported = 0;
                    $skipped = 0;
                    $row = 0;
                    
                    while (($data = fgetcsv($handle, 1000, ",")) !== FALSE) {
                        $row++;
                        
                        // Skip header row
                        if ($row === 1) {
                            continue;
                        }
                        
                        if (count($data) < 1 || empty(trim($data[0]))) {
                            $skipped++;
                            continue;
                        }
                        
                        $name = trim($data[0]);
                        $description = isset($data[1]) ? trim($data[1]) : '';
                        
                        // Check if item already exists
                        $stmt = $db->prepare("SELECT id FROM items WHERE name = ?");
                        $stmt->execute([$name]);
                        if ($stmt->fetch()) {
                            $skipped++;
                            continue;
                        }
                        
                        // Insert new item
                        $stmt = $db->prepare("INSERT INTO items (name, description, created_by) VALUES (?, ?, ?)");
                        $stmt->execute([$name, $description, $user['id']]);
                        $imported++;
                    }
                    
                    fclose($handle);
                    
                    logActivity(
                        $db,
                        $user['id'],
                        'create',
                        'items',
                        null,
                        "Imported $imported items from CSV (skipped $skipped duplicates)",
                        $_SERVER['REMOTE_ADDR'],
                        $_SERVER['HTTP_USER_AGENT']
                    );
                    
                    $success = "CSV import completed! Imported: $imported, Skipped: $skipped";
                    break;
            }
        }
    } catch (Exception $e) {
        $error = $e->getMessage();
    }
}

// Get all items
$search = $_GET['search'] ?? '';
$status_filter = $_GET['status'] ?? '';

$where_conditions = [];
$params = [];

if (!empty($search)) {
    $where_conditions[] = "(name LIKE ? OR description LIKE ?)";
    $params[] = "%$search%";
    $params[] = "%$search%";
}

if ($status_filter !== '') {
    $where_conditions[] = "is_active = ?";
    $params[] = $status_filter;
}

$where_clause = !empty($where_conditions) ? "WHERE " . implode(" AND ", $where_conditions) : "";

$stmt = $db->prepare("
    SELECT i.*, u.full_name as created_by_name,
           (SELECT COUNT(*) FROM expenses WHERE item_id = i.id) as usage_count
    FROM items i
    LEFT JOIN users u ON i.created_by = u.id
    $where_clause
    ORDER BY i.name ASC
");
$stmt->execute($params);
$items = $stmt->fetchAll();
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Manage Items - Admin Panel</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="../assets/css/style.css" rel="stylesheet">
</head>
<body>
    <?php include 'includes/admin_nav.php'; ?>

    <div class="container-fluid mt-4">
        <div class="row">
            <div class="col-12">
                <div class="d-flex justify-content-between align-items-center mb-4">
                    <h1><i class="fas fa-boxes me-2"></i>Manage Items</h1>
                    <div class="d-flex gap-2">
                        <button type="button" class="btn btn-success" data-bs-toggle="modal" data-bs-target="#addItemModal">
                            <i class="fas fa-plus me-1"></i>Add Item
                        </button>
                        <button type="button" class="btn btn-info" data-bs-toggle="modal" data-bs-target="#importCsvModal">
                            <i class="fas fa-file-csv me-1"></i>Import CSV
                        </button>
                    </div>
                </div>

                <?php if ($error): ?>
                    <div class="alert alert-danger alert-dismissible fade show" role="alert">
                        <i class="fas fa-exclamation-triangle me-2"></i>
                        <?php echo htmlspecialchars($error); ?>
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                <?php endif; ?>

                <?php if ($success): ?>
                    <div class="alert alert-success alert-dismissible fade show" role="alert">
                        <i class="fas fa-check-circle me-2"></i>
                        <?php echo htmlspecialchars($success); ?>
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                <?php endif; ?>

                <!-- Filters -->
                <div class="card mb-4">
                    <div class="card-body">
                        <form method="GET" class="row g-3">
                            <div class="col-md-6">
                                <label for="search" class="form-label">Search</label>
                                <input type="text" class="form-control" id="search" name="search" 
                                       value="<?php echo htmlspecialchars($search); ?>" 
                                       placeholder="Search by name or description...">
                            </div>
                            <div class="col-md-3">
                                <label for="status" class="form-label">Status</label>
                                <select class="form-select" id="status" name="status">
                                    <option value="">All Status</option>
                                    <option value="1" <?php echo $status_filter === '1' ? 'selected' : ''; ?>>Active</option>
                                    <option value="0" <?php echo $status_filter === '0' ? 'selected' : ''; ?>>Inactive</option>
                                </select>
                            </div>
                            <div class="col-md-3 d-flex align-items-end gap-2">
                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-search me-1"></i>Search
                                </button>
                                <a href="items.php" class="btn btn-outline-secondary">
                                    <i class="fas fa-undo me-1"></i>Reset
                                </a>
                            </div>
                        </form>
                    </div>
                </div>

                <!-- Items Table -->
                <div class="card">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <h5><i class="fas fa-list me-2"></i>Items List (<?php echo count($items); ?> items)</h5>
                        <div class="d-flex gap-2">
                            <button type="button" class="btn btn-sm btn-outline-primary" onclick="exportCSV()">
                                <i class="fas fa-download me-1"></i>Export CSV
                            </button>
                        </div>
                    </div>
                    <div class="card-body">
                        <?php if (empty($items)): ?>
                            <div class="text-center py-4">
                                <i class="fas fa-inbox fa-3x text-muted mb-3"></i>
                                <h5 class="text-muted">No items found</h5>
                                <p class="text-muted">Add your first item or adjust your search criteria.</p>
                            </div>
                        <?php else: ?>
                            <div class="table-responsive">
                                <table class="table table-hover">
                                    <thead>
                                        <tr>
                                            <th>ID</th>
                                            <th>Name</th>
                                            <th>Description</th>
                                            <th>Status</th>
                                            <th>Usage</th>
                                            <th>Created By</th>
                                            <th>Created Date</th>
                                            <th>Actions</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <?php foreach ($items as $item): ?>
                                            <tr>
                                                <td><?php echo $item['id']; ?></td>
                                                <td><strong><?php echo htmlspecialchars($item['name']); ?></strong></td>
                                                <td><?php echo htmlspecialchars($item['description'] ?: '-'); ?></td>
                                                <td>
                                                    <?php if ($item['is_active']): ?>
                                                        <span class="badge bg-success">Active</span>
                                                    <?php else: ?>
                                                        <span class="badge bg-secondary">Inactive</span>
                                                    <?php endif; ?>
                                                </td>
                                                <td>
                                                    <?php if ($item['usage_count'] > 0): ?>
                                                        <span class="badge bg-info"><?php echo $item['usage_count']; ?> expenses</span>
                                                    <?php else: ?>
                                                        <span class="text-muted">Not used</span>
                                                    <?php endif; ?>
                                                </td>
                                                <td><?php echo htmlspecialchars($item['created_by_name'] ?: 'Unknown'); ?></td>
                                                <td><?php echo date('M j, Y', strtotime($item['created_at'])); ?></td>
                                                <td>
                                                    <div class="btn-group btn-group-sm">
                                                        <button type="button" class="btn btn-outline-primary"
                                                                onclick="editItem(<?php echo htmlspecialchars(json_encode($item)); ?>)">
                                                            <i class="fas fa-edit"></i>
                                                        </button>
                                                        <?php if ($item['usage_count'] == 0): ?>
                                                            <button type="button" class="btn btn-outline-danger"
                                                                    onclick="deleteItem(<?php echo $item['id']; ?>, '<?php echo htmlspecialchars($item['name']); ?>')">
                                                                <i class="fas fa-trash"></i>
                                                            </button>
                                                        <?php else: ?>
                                                            <button type="button" class="btn btn-outline-secondary" disabled title="Cannot delete - item is in use">
                                                                <i class="fas fa-lock"></i>
                                                            </button>
                                                        <?php endif; ?>
                                                    </div>
                                                </td>
                                            </tr>
                                        <?php endforeach; ?>
                                    </tbody>
                                </table>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Add Item Modal -->
    <div class="modal fade" id="addItemModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <form method="POST">
                    <input type="hidden" name="action" value="add">
                    <div class="modal-header">
                        <h5 class="modal-title"><i class="fas fa-plus me-2"></i>Add New Item</h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                    </div>
                    <div class="modal-body">
                        <div class="mb-3">
                            <label for="add_name" class="form-label">Item Name <span class="text-danger">*</span></label>
                            <input type="text" class="form-control" id="add_name" name="name" required>
                        </div>
                        <div class="mb-3">
                            <label for="add_description" class="form-label">Description</label>
                            <textarea class="form-control" id="add_description" name="description" rows="3"></textarea>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                        <button type="submit" class="btn btn-success">
                            <i class="fas fa-save me-1"></i>Add Item
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- Edit Item Modal -->
    <div class="modal fade" id="editItemModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <form method="POST">
                    <input type="hidden" name="action" value="edit">
                    <input type="hidden" name="id" id="edit_id">
                    <div class="modal-header">
                        <h5 class="modal-title"><i class="fas fa-edit me-2"></i>Edit Item</h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                    </div>
                    <div class="modal-body">
                        <div class="mb-3">
                            <label for="edit_name" class="form-label">Item Name <span class="text-danger">*</span></label>
                            <input type="text" class="form-control" id="edit_name" name="name" required>
                        </div>
                        <div class="mb-3">
                            <label for="edit_description" class="form-label">Description</label>
                            <textarea class="form-control" id="edit_description" name="description" rows="3"></textarea>
                        </div>
                        <div class="mb-3">
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="edit_is_active" name="is_active">
                                <label class="form-check-label" for="edit_is_active">
                                    Active
                                </label>
                            </div>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save me-1"></i>Update Item
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- Import CSV Modal -->
    <div class="modal fade" id="importCsvModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <form method="POST" enctype="multipart/form-data">
                    <input type="hidden" name="action" value="import_csv">
                    <div class="modal-header">
                        <h5 class="modal-title"><i class="fas fa-file-csv me-2"></i>Import Items from CSV</h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                    </div>
                    <div class="modal-body">
                        <div class="alert alert-info">
                            <h6><i class="fas fa-info-circle me-1"></i>CSV Format Requirements:</h6>
                            <ul class="mb-0">
                                <li>Column 1: Item Name (required)</li>
                                <li>Column 2: Description (optional)</li>
                                <li>First row should be headers</li>
                                <li>Duplicate names will be skipped</li>
                            </ul>
                        </div>
                        <div class="mb-3">
                            <label for="csv_file" class="form-label">CSV File <span class="text-danger">*</span></label>
                            <input type="file" class="form-control" id="csv_file" name="csv_file" accept=".csv" required>
                        </div>
                        <div class="mb-3">
                            <button type="button" class="btn btn-sm btn-outline-secondary" onclick="downloadSampleCSV()">
                                <i class="fas fa-download me-1"></i>Download Sample CSV
                            </button>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                        <button type="submit" class="btn btn-info">
                            <i class="fas fa-upload me-1"></i>Import CSV
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- Delete Confirmation Modal -->
    <div class="modal fade" id="deleteModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <form method="POST">
                    <input type="hidden" name="action" value="delete">
                    <input type="hidden" name="id" id="delete_id">
                    <div class="modal-header">
                        <h5 class="modal-title text-danger"><i class="fas fa-exclamation-triangle me-2"></i>Confirm Delete</h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                    </div>
                    <div class="modal-body">
                        <p>Are you sure you want to delete the item "<strong id="delete_name"></strong>"?</p>
                        <p class="text-danger"><small>This action cannot be undone.</small></p>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                        <button type="submit" class="btn btn-danger">
                            <i class="fas fa-trash me-1"></i>Delete Item
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        function editItem(item) {
            document.getElementById('edit_id').value = item.id;
            document.getElementById('edit_name').value = item.name;
            document.getElementById('edit_description').value = item.description || '';
            document.getElementById('edit_is_active').checked = item.is_active == 1;

            new bootstrap.Modal(document.getElementById('editItemModal')).show();
        }

        function deleteItem(id, name) {
            document.getElementById('delete_id').value = id;
            document.getElementById('delete_name').textContent = name;

            new bootstrap.Modal(document.getElementById('deleteModal')).show();
        }

        function exportCSV() {
            const table = document.querySelector('.table');
            const rows = table.querySelectorAll('tr');
            let csv = [];

            // Headers
            const headers = ['ID', 'Name', 'Description', 'Status', 'Usage Count', 'Created By', 'Created Date'];
            csv.push(headers.join(','));

            // Data rows
            for (let i = 1; i < rows.length; i++) {
                const row = rows[i];
                const cols = row.querySelectorAll('td');
                if (cols.length > 0) {
                    const rowData = [
                        cols[0].textContent.trim(), // ID
                        '"' + cols[1].textContent.trim().replace(/"/g, '""') + '"', // Name
                        '"' + cols[2].textContent.trim().replace(/"/g, '""') + '"', // Description
                        cols[3].textContent.trim(), // Status
                        cols[4].textContent.trim(), // Usage
                        '"' + cols[5].textContent.trim().replace(/"/g, '""') + '"', // Created By
                        cols[6].textContent.trim() // Created Date
                    ];
                    csv.push(rowData.join(','));
                }
            }

            // Download
            const csvContent = csv.join('\n');
            const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
            const link = document.createElement('a');
            const url = URL.createObjectURL(blob);
            link.setAttribute('href', url);
            link.setAttribute('download', 'items_' + new Date().toISOString().split('T')[0] + '.csv');
            link.style.visibility = 'hidden';
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);
        }

        function downloadSampleCSV() {
            const sampleData = [
                ['Name', 'Description'],
                ['Container Transport', 'Transportation of containers'],
                ['Fuel Cost', 'Fuel expenses for vehicles'],
                ['Maintenance', 'Vehicle maintenance costs']
            ];

            const csv = sampleData.map(row =>
                row.map(cell => '"' + cell.replace(/"/g, '""') + '"').join(',')
            ).join('\n');

            const blob = new Blob([csv], { type: 'text/csv;charset=utf-8;' });
            const link = document.createElement('a');
            const url = URL.createObjectURL(blob);
            link.setAttribute('href', url);
            link.setAttribute('download', 'items_sample.csv');
            link.style.visibility = 'hidden';
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);
        }

        // Clear form when modal is hidden
        document.getElementById('addItemModal').addEventListener('hidden.bs.modal', function () {
            this.querySelector('form').reset();
        });
    </script>
</body>
</html>
