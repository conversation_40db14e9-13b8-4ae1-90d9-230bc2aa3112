<?php
session_start();
require_once '../config/database.php';
require_once '../includes/functions.php';

// Check if user is logged in and is administrator
if (!isset($_SESSION['user_id']) || $_SESSION['role'] !== 'administrator') {
    header('Location: ../login.php');
    exit();
}

$database = new Database();
$db = $database->getConnection();

// Get filter parameters
$date_from = $_GET['date_from'] ?? date('Y-m-d', strtotime('-7 days'));
$date_to = $_GET['date_to'] ?? date('Y-m-d');
$action_filter = $_GET['action'] ?? '';
$user_filter = $_GET['user'] ?? '';
$page = max(1, (int)($_GET['page'] ?? 1));
$per_page = 50;

// Build query
$where_conditions = [];
$params = [];

// Date filters
if (!empty($date_from)) {
    $where_conditions[] = 'DATE(al.created_at) >= ?';
    $params[] = $date_from;
}

if (!empty($date_to)) {
    $where_conditions[] = 'DATE(al.created_at) <= ?';
    $params[] = $date_to;
}

// Action filter
if (!empty($action_filter)) {
    $where_conditions[] = 'al.action_type = ?';
    $params[] = $action_filter;
}

// User filter
if (!empty($user_filter)) {
    $where_conditions[] = 'al.user_id = ?';
    $params[] = $user_filter;
}

$where_clause = !empty($where_conditions) ? 'WHERE ' . implode(' AND ', $where_conditions) : '';

// Get total count for pagination
$count_stmt = $db->prepare("
    SELECT COUNT(*) as total
    FROM activity_logs al
    $where_clause
");
$count_stmt->execute($params);
$total_records = $count_stmt->fetch()['total'];

$pagination = paginate($total_records, $per_page, $page);

// Get activity logs
$stmt = $db->prepare("
    SELECT 
        al.*,
        u.full_name as user_name
    FROM activity_logs al
    LEFT JOIN users u ON al.user_id = u.id
    $where_clause
    ORDER BY al.created_at DESC
    LIMIT {$per_page} OFFSET {$pagination['offset']}
");

$stmt->execute($params);
$logs = $stmt->fetchAll();

// Get users for filter
$users_stmt = $db->query("SELECT id, full_name FROM users WHERE is_active = 1 ORDER BY full_name");
$users = $users_stmt->fetchAll();
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Activity Logs - Expenses Management System</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="../assets/css/style.css" rel="stylesheet">
</head>
<body>
    <?php include 'includes/admin_nav.php'; ?>

    <div class="container-fluid mt-4">
        <div class="row">
            <div class="col-12">
                <h1 class="mb-4"><i class="fas fa-history me-2"></i>Activity Logs</h1>

                <!-- Filters -->
                <div class="search-filter-bar">
                    <form method="GET" class="row g-3">
                        <div class="col-md-2">
                            <label for="date_from" class="form-label">Date From</label>
                            <input type="date" class="form-control" id="date_from" name="date_from" 
                                   value="<?php echo htmlspecialchars($date_from); ?>">
                        </div>
                        <div class="col-md-2">
                            <label for="date_to" class="form-label">Date To</label>
                            <input type="date" class="form-control" id="date_to" name="date_to" 
                                   value="<?php echo htmlspecialchars($date_to); ?>">
                        </div>
                        <div class="col-md-2">
                            <label for="action" class="form-label">Action</label>
                            <select class="form-select" id="action" name="action">
                                <option value="">All Actions</option>
                                <option value="create" <?php echo $action_filter === 'create' ? 'selected' : ''; ?>>Create</option>
                                <option value="update" <?php echo $action_filter === 'update' ? 'selected' : ''; ?>>Update</option>
                                <option value="delete" <?php echo $action_filter === 'delete' ? 'selected' : ''; ?>>Delete</option>
                                <option value="status_change" <?php echo $action_filter === 'status_change' ? 'selected' : ''; ?>>Status Change</option>
                                <option value="login" <?php echo $action_filter === 'login' ? 'selected' : ''; ?>>Login</option>
                                <option value="logout" <?php echo $action_filter === 'logout' ? 'selected' : ''; ?>>Logout</option>
                                <optgroup label="Batch Operations">
                                    <option value="batch_verification" <?php echo $action_filter === 'batch_verification' ? 'selected' : ''; ?>>Batch Verification</option>
                                    <option value="batch_review" <?php echo $action_filter === 'batch_review' ? 'selected' : ''; ?>>Batch Review</option>
                                    <option value="batch_cancel" <?php echo $action_filter === 'batch_cancel' ? 'selected' : ''; ?>>Batch Cancel</option>
                                    <option value="batch_retry" <?php echo $action_filter === 'batch_retry' ? 'selected' : ''; ?>>Batch Retry</option>
                                </optgroup>
                                <optgroup label="Administrative">
                                    <option value="admin_override" <?php echo $action_filter === 'admin_override' ? 'selected' : ''; ?>>Admin Override</option>
                                    <option value="reject" <?php echo $action_filter === 'reject' ? 'selected' : ''; ?>>Reject</option>
                                    <option value="return" <?php echo $action_filter === 'return' ? 'selected' : ''; ?>>Return</option>
                                </optgroup>
                            </select>
                        </div>
                        <div class="col-md-3">
                            <label for="user" class="form-label">User</label>
                            <select class="form-select" id="user" name="user">
                                <option value="">All Users</option>
                                <?php foreach ($users as $user): ?>
                                    <option value="<?php echo $user['id']; ?>" 
                                            <?php echo $user_filter == $user['id'] ? 'selected' : ''; ?>>
                                        <?php echo htmlspecialchars($user['full_name']); ?>
                                    </option>
                                <?php endforeach; ?>
                            </select>
                        </div>
                        <div class="col-md-2">
                            <label class="form-label">&nbsp;</label>
                            <div class="d-grid">
                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-search me-1"></i>Filter
                                </button>
                            </div>
                        </div>
                        <div class="col-md-1">
                            <label class="form-label">&nbsp;</label>
                            <div class="d-grid">
                                <a href="logs.php" class="btn btn-outline-secondary">
                                    <i class="fas fa-times"></i>
                                </a>
                            </div>
                        </div>
                    </form>
                </div>

                <!-- Results Info -->
                <div class="d-flex justify-content-between align-items-center mb-3">
                    <h5>Activity Logs (<?php echo number_format($total_records); ?> total records)</h5>
                    <div>
                        Showing <?php echo number_format($pagination['offset'] + 1); ?> - 
                        <?php echo number_format(min($pagination['offset'] + $per_page, $total_records)); ?> 
                        of <?php echo number_format($total_records); ?>
                    </div>
                </div>

                <!-- Bulk Actions Panel -->
                <div class="alert alert-info" id="bulkActionsPanel" style="display: none;">
                    <div class="row align-items-center">
                        <div class="col-md-6">
                            <strong><span id="selectedCount">0</span> logs selected</strong>
                        </div>
                        <div class="col-md-6 text-end">
                            <button type="button" class="btn btn-danger btn-sm" onclick="bulkDeleteLogs()">
                                <i class="fas fa-trash me-1"></i>Delete Selected
                            </button>
                        </div>
                    </div>
                </div>

                <!-- Activity Logs Table -->
                <div class="card">
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table table-striped table-hover">
                                <thead>
                                    <tr>
                                        <th width="30">
                                            <input type="checkbox" id="selectAll" class="form-check-input">
                                        </th>
                                        <th>Date/Time</th>
                                        <th>User</th>
                                        <th>Action</th>
                                        <th>Table</th>
                                        <th>Record ID</th>
                                        <th>Description</th>
                                        <th>IP Address</th>
                                        <th width="80">Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php if (empty($logs)): ?>
                                        <tr>
                                            <td colspan="9" class="text-center">No activity logs found for the selected criteria</td>
                                        </tr>
                                    <?php else: ?>
                                        <?php foreach ($logs as $log): ?>
                                            <tr>
                                                <td>
                                                    <input type="checkbox" class="form-check-input log-checkbox" value="<?php echo $log['id']; ?>">
                                                </td>
                                                <td>
                                                    <small><?php echo formatDateTime($log['created_at'], 'M d, Y H:i:s'); ?></small>
                                                </td>
                                                <td><?php echo htmlspecialchars($log['user_name'] ?: 'Unknown'); ?></td>
                                                <td>
                                                    <?php
                                                    $action_badges = [
                                                        'create' => 'bg-success',
                                                        'update' => 'bg-warning text-dark',
                                                        'delete' => 'bg-danger',
                                                        'status_change' => 'bg-info',
                                                        'login' => 'bg-primary',
                                                        'logout' => 'bg-secondary',
                                                        'batch_verification' => 'bg-primary',
                                                        'batch_review' => 'bg-success',
                                                        'batch_cancel' => 'bg-danger',
                                                        'batch_retry' => 'bg-warning text-dark',
                                                        'admin_override' => 'bg-dark',
                                                        'reject' => 'bg-danger',
                                                        'return' => 'bg-secondary'
                                                    ];
                                                    $badge_class = $action_badges[$log['action_type']] ?? 'bg-secondary';
                                                    ?>
                                                    <span class="badge <?php echo $badge_class; ?>">
                                                        <?php echo ucfirst(str_replace('_', ' ', $log['action_type'])); ?>
                                                    </span>
                                                </td>
                                                <td><?php echo htmlspecialchars($log['table_name'] ?: '-'); ?></td>
                                                <td><?php echo htmlspecialchars($log['record_id'] ?: '-'); ?></td>
                                                <td>
                                                    <div class="description-cell">
                                                        <?php echo htmlspecialchars($log['description']); ?>
                                                        <?php if (!empty($log['old_values']) || !empty($log['new_values'])): ?>
                                                            <button class="btn btn-sm btn-outline-info ms-2" 
                                                                    onclick="showDetails(<?php echo htmlspecialchars(json_encode([
                                                                        'old_values' => $log['old_values'],
                                                                        'new_values' => $log['new_values']
                                                                    ])); ?>)">
                                                                <i class="fas fa-info-circle"></i>
                                                            </button>
                                                        <?php endif; ?>
                                                    </div>
                                                </td>
                                                <td><small><?php echo htmlspecialchars($log['ip_address'] ?: '-'); ?></small></td>
                                                <td>
                                                    <button type="button" class="btn btn-sm btn-outline-danger"
                                                            onclick="deleteLog(<?php echo $log['id']; ?>)"
                                                            title="Delete Log">
                                                        <i class="fas fa-trash"></i>
                                                    </button>
                                                </td>
                                            </tr>
                                        <?php endforeach; ?>
                                    <?php endif; ?>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>

                <!-- Pagination -->
                <?php if ($pagination['total_pages'] > 1): ?>
                    <nav aria-label="Activity logs pagination" class="mt-4">
                        <ul class="pagination justify-content-center">
                            <?php if ($pagination['has_previous']): ?>
                                <li class="page-item">
                                    <a class="page-link" href="?<?php echo http_build_query(array_merge($_GET, ['page' => $pagination['current_page'] - 1])); ?>">
                                        <i class="fas fa-chevron-left"></i>
                                    </a>
                                </li>
                            <?php endif; ?>
                            
                            <?php
                            $start_page = max(1, $pagination['current_page'] - 2);
                            $end_page = min($pagination['total_pages'], $pagination['current_page'] + 2);
                            ?>
                            
                            <?php for ($i = $start_page; $i <= $end_page; $i++): ?>
                                <li class="page-item <?php echo $i === $pagination['current_page'] ? 'active' : ''; ?>">
                                    <a class="page-link" href="?<?php echo http_build_query(array_merge($_GET, ['page' => $i])); ?>">
                                        <?php echo $i; ?>
                                    </a>
                                </li>
                            <?php endfor; ?>
                            
                            <?php if ($pagination['has_next']): ?>
                                <li class="page-item">
                                    <a class="page-link" href="?<?php echo http_build_query(array_merge($_GET, ['page' => $pagination['current_page'] + 1])); ?>">
                                        <i class="fas fa-chevron-right"></i>
                                    </a>
                                </li>
                            <?php endif; ?>
                        </ul>
                    </nav>
                <?php endif; ?>
            </div>
        </div>
    </div>

    <!-- Details Modal -->
    <div class="modal fade" id="detailsModal" tabindex="-1" aria-hidden="true">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">Change Details</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <div id="details-content"></div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script>
        function showDetails(data) {
            let content = '';
            
            if (data.old_values) {
                try {
                    const oldValues = JSON.parse(data.old_values);
                    content += '<h6>Old Values:</h6>';
                    content += '<pre class="bg-light p-2 rounded">' + JSON.stringify(oldValues, null, 2) + '</pre>';
                } catch (e) {
                    content += '<h6>Old Values:</h6>';
                    content += '<pre class="bg-light p-2 rounded">' + data.old_values + '</pre>';
                }
            }
            
            if (data.new_values) {
                try {
                    const newValues = JSON.parse(data.new_values);
                    content += '<h6>New Values:</h6>';
                    content += '<pre class="bg-light p-2 rounded">' + JSON.stringify(newValues, null, 2) + '</pre>';
                } catch (e) {
                    content += '<h6>New Values:</h6>';
                    content += '<pre class="bg-light p-2 rounded">' + data.new_values + '</pre>';
                }
            }
            
            $('#details-content').html(content);
            $('#detailsModal').modal('show');
        }

        // Bulk actions functionality
        $(document).ready(function() {
            // Handle select all checkbox
            $('#selectAll').on('change', function() {
                const isChecked = $(this).is(':checked');
                $('.log-checkbox').prop('checked', isChecked);
                updateBulkActionsPanel();
            });

            // Handle individual checkboxes
            $(document).on('change', '.log-checkbox', function() {
                updateBulkActionsPanel();

                // Update select all checkbox
                const totalCheckboxes = $('.log-checkbox').length;
                const checkedCheckboxes = $('.log-checkbox:checked').length;

                if (checkedCheckboxes === 0) {
                    $('#selectAll').prop('indeterminate', false).prop('checked', false);
                } else if (checkedCheckboxes === totalCheckboxes) {
                    $('#selectAll').prop('indeterminate', false).prop('checked', true);
                } else {
                    $('#selectAll').prop('indeterminate', true);
                }
            });
        });

        function updateBulkActionsPanel() {
            const selectedCount = $('.log-checkbox:checked').length;
            $('#selectedCount').text(selectedCount);

            if (selectedCount > 0) {
                $('#bulkActionsPanel').show();
            } else {
                $('#bulkActionsPanel').hide();
            }
        }

        function deleteLog(id) {
            if (confirm('Are you sure you want to delete this log entry? This action cannot be undone.')) {
                $.ajax({
                    url: 'api/delete_log.php',
                    method: 'POST',
                    contentType: 'application/json',
                    data: JSON.stringify({ ids: [id] }),
                    success: function(data) {
                        if (data.success) {
                            showAlert('Log deleted successfully', 'success');
                            setTimeout(() => location.reload(), 1000);
                        } else {
                            showAlert(data.message || 'Delete failed', 'danger');
                        }
                    },
                    error: function() {
                        showAlert('An error occurred while deleting the log', 'danger');
                    }
                });
            }
        }

        function bulkDeleteLogs() {
            const selectedIds = $('.log-checkbox:checked').map(function() {
                return $(this).val();
            }).get();

            if (selectedIds.length === 0) {
                showAlert('Please select at least one log entry', 'warning');
                return;
            }

            if (confirm(`Are you sure you want to delete ${selectedIds.length} log entries? This action cannot be undone.`)) {
                $.ajax({
                    url: 'api/delete_log.php',
                    method: 'POST',
                    contentType: 'application/json',
                    data: JSON.stringify({ ids: selectedIds }),
                    success: function(data) {
                        if (data.success) {
                            showAlert(`${data.deleted_count} logs deleted successfully`, 'success');
                            setTimeout(() => location.reload(), 1000);
                        } else {
                            showAlert(data.message || 'Bulk delete failed', 'danger');
                        }
                    },
                    error: function() {
                        showAlert('An error occurred while deleting logs', 'danger');
                    }
                });
            }
        }

        function showAlert(message, type) {
            const alertHtml = `
                <div class="alert alert-${type} alert-dismissible fade show position-fixed"
                     style="top: 20px; right: 20px; z-index: 9999; min-width: 300px;" role="alert">
                    ${message}
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            `;

            $('body').append(alertHtml);

            // Auto dismiss after 5 seconds
            setTimeout(() => {
                $('.alert').alert('close');
            }, 5000);
        }
    </script>
</body>
</html>
