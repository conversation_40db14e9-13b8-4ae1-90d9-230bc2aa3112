<?php
session_start();
require_once '../config/database.php';
require_once '../includes/functions.php';

// Check if user is logged in and is administrator
if (!isset($_SESSION['user_id']) || $_SESSION['role'] !== 'administrator') {
    header('Location: ../login.php');
    exit();
}

$database = new Database();
$db = $database->getConnection();

// Get user info
$stmt = $db->prepare("SELECT * FROM users WHERE id = ?");
$stmt->execute([$_SESSION['user_id']]);
$user = $stmt->fetch();

if (!$user || !$user['is_active']) {
    session_destroy();
    header('Location: ../login.php');
    exit();
}

$error = '';
$success = '';

// Handle form submissions
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $action = $_POST['action'] ?? '';
    
    try {
        if ($action === 'add') {
            $username = trim($_POST['username'] ?? '');
            $email = trim($_POST['email'] ?? '');
            $password = $_POST['password'] ?? '';
            $full_name = trim($_POST['full_name'] ?? '');
            $role = $_POST['role'] ?? 'data_entry';
            $is_active = isset($_POST['is_active']) ? 1 : 0;
            
            // Validation
            if (empty($username) || empty($email) || empty($password) || empty($full_name)) {
                throw new Exception('All fields are required.');
            }
            
            if (strlen($password) < 6) {
                throw new Exception('Password must be at least 6 characters long.');
            }
            
            if (!filter_var($email, FILTER_VALIDATE_EMAIL)) {
                throw new Exception('Invalid email format.');
            }
            
            // Check if username or email already exists
            $stmt = $db->prepare("SELECT id FROM users WHERE username = ? OR email = ?");
            $stmt->execute([$username, $email]);
            if ($stmt->fetch()) {
                throw new Exception('Username or email already exists.');
            }
            
            // Hash password
            $password_hash = password_hash($password, PASSWORD_DEFAULT);
            
            // Insert new user
            $stmt = $db->prepare("
                INSERT INTO users (username, email, password_hash, full_name, role, is_active, created_at)
                VALUES (?, ?, ?, ?, ?, ?, NOW())
            ");
            $stmt->execute([$username, $email, $password_hash, $full_name, $role, $is_active]);
            
            $new_user_id = $db->lastInsertId();
            
            // Log activity
            logActivity(
                $db,
                $user['id'],
                'create',
                'users',
                $new_user_id,
                "Created new user: $username ($full_name) with role: $role",
                $_SERVER['REMOTE_ADDR'],
                $_SERVER['HTTP_USER_AGENT']
            );
            
            $success = 'User created successfully!';
            
        } elseif ($action === 'edit') {
            $user_id = (int)($_POST['user_id'] ?? 0);
            $username = trim($_POST['username'] ?? '');
            $email = trim($_POST['email'] ?? '');
            $full_name = trim($_POST['full_name'] ?? '');
            $role = $_POST['role'] ?? 'data_entry';
            $is_active = isset($_POST['is_active']) ? 1 : 0;
            $new_password = $_POST['new_password'] ?? '';
            
            // Validation
            if (empty($username) || empty($email) || empty($full_name)) {
                throw new Exception('Username, email, and full name are required.');
            }
            
            if (!filter_var($email, FILTER_VALIDATE_EMAIL)) {
                throw new Exception('Invalid email format.');
            }
            
            // Check if username or email already exists for other users
            $stmt = $db->prepare("SELECT id FROM users WHERE (username = ? OR email = ?) AND id != ?");
            $stmt->execute([$username, $email, $user_id]);
            if ($stmt->fetch()) {
                throw new Exception('Username or email already exists.');
            }
            
            // Get current user data for logging
            $stmt = $db->prepare("SELECT * FROM users WHERE id = ?");
            $stmt->execute([$user_id]);
            $old_user_data = $stmt->fetch();
            
            if (!$old_user_data) {
                throw new Exception('User not found.');
            }
            
            // Prepare update query
            if (!empty($new_password)) {
                if (strlen($new_password) < 6) {
                    throw new Exception('Password must be at least 6 characters long.');
                }
                $password_hash = password_hash($new_password, PASSWORD_DEFAULT);
                $stmt = $db->prepare("
                    UPDATE users 
                    SET username = ?, email = ?, password_hash = ?, full_name = ?, role = ?, is_active = ?, updated_at = NOW()
                    WHERE id = ?
                ");
                $stmt->execute([$username, $email, $password_hash, $full_name, $role, $is_active, $user_id]);
            } else {
                $stmt = $db->prepare("
                    UPDATE users 
                    SET username = ?, email = ?, full_name = ?, role = ?, is_active = ?, updated_at = NOW()
                    WHERE id = ?
                ");
                $stmt->execute([$username, $email, $full_name, $role, $is_active, $user_id]);
            }
            
            // Log activity
            $changes = [];
            if ($old_user_data['username'] !== $username) $changes[] = "username: {$old_user_data['username']} → $username";
            if ($old_user_data['email'] !== $email) $changes[] = "email: {$old_user_data['email']} → $email";
            if ($old_user_data['full_name'] !== $full_name) $changes[] = "full_name: {$old_user_data['full_name']} → $full_name";
            if ($old_user_data['role'] !== $role) $changes[] = "role: {$old_user_data['role']} → $role";
            if ($old_user_data['is_active'] != $is_active) $changes[] = "is_active: {$old_user_data['is_active']} → $is_active";
            if (!empty($new_password)) $changes[] = "password changed";
            
            logActivity(
                $db,
                $user['id'],
                'update',
                'users',
                $user_id,
                "Updated user: $username - " . implode(', ', $changes),
                $_SERVER['REMOTE_ADDR'],
                $_SERVER['HTTP_USER_AGENT']
            );
            
            $success = 'User updated successfully!';
            
        } elseif ($action === 'delete') {
            $user_id = (int)($_POST['user_id'] ?? 0);
            
            // Cannot delete self
            if ($user_id == $user['id']) {
                throw new Exception('You cannot delete your own account.');
            }
            
            // Get user data for logging
            $stmt = $db->prepare("SELECT * FROM users WHERE id = ?");
            $stmt->execute([$user_id]);
            $delete_user = $stmt->fetch();
            
            if (!$delete_user) {
                throw new Exception('User not found.');
            }
            
            // Check if user has created any records
            $stmt = $db->prepare("SELECT COUNT(*) as count FROM expenses WHERE created_by = ?");
            $stmt->execute([$user_id]);
            $expense_count = $stmt->fetch()['count'];
            
            if ($expense_count > 0) {
                // Deactivate instead of delete
                $stmt = $db->prepare("UPDATE users SET is_active = 0, updated_at = NOW() WHERE id = ?");
                $stmt->execute([$user_id]);
                
                logActivity(
                    $db,
                    $user['id'],
                    'update',
                    'users',
                    $user_id,
                    "Deactivated user: {$delete_user['username']} (has $expense_count expense records)",
                    $_SERVER['REMOTE_ADDR'],
                    $_SERVER['HTTP_USER_AGENT']
                );
                
                $success = 'User deactivated successfully (user has existing records).';
            } else {
                // Safe to delete
                $stmt = $db->prepare("DELETE FROM users WHERE id = ?");
                $stmt->execute([$user_id]);
                
                logActivity(
                    $db,
                    $user['id'],
                    'delete',
                    'users',
                    $user_id,
                    "Deleted user: {$delete_user['username']} ({$delete_user['full_name']})",
                    $_SERVER['REMOTE_ADDR'],
                    $_SERVER['HTTP_USER_AGENT']
                );
                
                $success = 'User deleted successfully!';
            }
        }
        
    } catch (Exception $e) {
        $error = $e->getMessage();
    }
}

// Get all users
$users = $db->query("
    SELECT u.*, 
           (SELECT COUNT(*) FROM expenses WHERE created_by = u.id) as expense_count,
           (SELECT COUNT(*) FROM activity_logs WHERE user_id = u.id) as activity_count
    FROM users u 
    ORDER BY u.created_at DESC
")->fetchAll();

// Role definitions for permissions display
$role_permissions = [
    'data_entry' => [
        'name' => 'Data Entry',
        'icon' => 'user',
        'color' => 'secondary',
        'description' => 'Basic user role for creating and managing personal expense records',
        'level' => 1,
        'permissions' => [
            '📝 Create new expense records',
            '✏️ Edit own expenses (Open status only)',
            '👁️ View own expense history',
            '📎 Upload receipt files and documents',
            '🔍 Search and filter own expenses',
            '📊 View personal expense statistics',
            '🖨️ Print own expense reports',
            '📱 Access mobile-friendly interface'
        ],
        'restrictions' => [
            '❌ Cannot view other users\' expenses',
            '❌ Cannot change expense status',
            '❌ Cannot delete expenses',
            '❌ Cannot access admin functions'
        ]
    ],
    'verification' => [
        'name' => 'Verification Officer',
        'icon' => 'user-check',
        'color' => 'info',
        'description' => 'First-level reviewer who verifies expense accuracy and completeness',
        'level' => 2,
        'permissions' => [
            '✅ All Data Entry permissions',
            '🔍 View all system expenses',
            '📋 Verify expense details and receipts',
            '🔄 Change status: Open → Pending',
            '🔄 Change status: Pending → Open (return for correction)',
            '✏️ Edit expenses in Open/Pending status',
            '💬 Add verification comments and notes',
            '📊 View verification dashboard and metrics',
            '🔔 Receive notifications for new expenses',
            '📈 Generate verification reports',
            '🏷️ Add verification tags and categories'
        ],
        'restrictions' => [
            '❌ Cannot approve expenses (final approval)',
            '❌ Cannot delete expenses',
            '❌ Cannot manage users or system settings',
            '❌ Cannot access financial summary reports'
        ]
    ],
    'reviewer' => [
        'name' => 'Reviewer/Approver',
        'icon' => 'user-edit',
        'color' => 'warning',
        'description' => 'Final approver with authority to approve or reject verified expenses',
        'level' => 3,
        'permissions' => [
            '✅ All Verification permissions',
            '✅ Final approval authority',
            '🔄 Change status: Pending → Approved',
            '🔄 Change status: Pending → Rejected',
            '🔄 Change status: Approved → Pending (reopen)',
            '🔄 Change status: Any → Returned (for correction)',
            '✏️ Edit expenses in any status',
            '💰 Access financial summary reports',
            '📊 View comprehensive analytics dashboard',
            '🔍 Advanced search and filtering options',
            '📧 Send approval/rejection notifications',
            '📈 Generate executive summary reports',
            '🏦 Input reviewer transfer numbers',
            '💼 Bulk approval operations'
        ],
        'restrictions' => [
            '❌ Cannot delete approved expenses',
            '❌ Cannot manage users or system settings',
            '❌ Cannot access admin panel functions'
        ]
    ],
    'report_viewer' => [
        'name' => 'Report Viewer',
        'icon' => 'chart-line',
        'color' => 'primary',
        'description' => 'Read-only access for viewing reports and analytics without modification rights',
        'level' => 2,
        'permissions' => [
            '📊 View all expense reports and analytics',
            '📈 Access dashboard with full statistics',
            '🔍 Advanced search and filtering',
            '📋 View all expenses (read-only)',
            '📁 Download and export reports (CSV, PDF)',
            '📊 View charts and graphs',
            '📅 Access historical data and trends',
            '🔔 View activity logs and audit trails',
            '📱 Mobile-optimized report viewing',
            '🖨️ Print reports and summaries',
            '📧 Schedule automated report emails',
            '🏷️ View expense categories and tags'
        ],
        'restrictions' => [
            '❌ Cannot create or edit expenses',
            '❌ Cannot change any expense status',
            '❌ Cannot delete any records',
            '❌ Cannot manage users or settings',
            '❌ Cannot access admin functions',
            '❌ Cannot upload files or documents'
        ]
    ],
    'administrator' => [
        'name' => 'System Administrator',
        'icon' => 'user-shield',
        'color' => 'danger',
        'description' => 'Full system access with complete administrative privileges and control',
        'level' => 5,
        'permissions' => [
            '🔧 Complete system administration',
            '👥 Manage all users and roles',
            '🛡️ Configure permissions and security',
            '📊 Access admin dashboard and analytics',
            '🗃️ Manage master data (Items, Customers, Drivers)',
            '💾 Database management and maintenance',
            '📋 View and manage activity logs',
            '🔄 Change any expense to any status',
            '✏️ Edit any expense record',
            '🗑️ Delete expenses and records',
            '📤 Data import/export and migration',
            '⚙️ System configuration and settings',
            '🔐 Security and backup management',
            '📊 Generate all types of reports',
            '🔔 System notifications and alerts',
            '🛠️ Troubleshooting and maintenance',
            '📈 Performance monitoring',
            '🏦 Bulk operations and data cleanup'
        ],
        'restrictions' => [
            '⚠️ Should follow change management procedures',
            '⚠️ Requires approval for major system changes',
            '⚠️ Must maintain audit trail for all actions'
        ]
    ]
];
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>User Management - Admin Panel</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="../assets/css/style.css" rel="stylesheet">
</head>
<body>
    <?php include 'includes/admin_nav.php'; ?>

    <div class="container-fluid mt-4">
        <!-- Page Header -->
        <div class="row mb-4">
            <div class="col">
                <h2><i class="fas fa-user-cog me-2"></i>User Management & Roles</h2>
                <p class="text-muted">Manage system users and their permissions</p>
            </div>
        </div>

        <!-- Alert Messages -->
        <?php if ($error): ?>
            <div class="alert alert-danger alert-dismissible fade show" role="alert">
                <i class="fas fa-exclamation-triangle me-2"></i><?php echo htmlspecialchars($error); ?>
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        <?php endif; ?>

        <?php if ($success): ?>
            <div class="alert alert-success alert-dismissible fade show" role="alert">
                <i class="fas fa-check-circle me-2"></i><?php echo htmlspecialchars($success); ?>
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        <?php endif; ?>

        <!-- Tabs -->
        <ul class="nav nav-tabs" id="userTabs" role="tablist">
            <li class="nav-item" role="presentation">
                <button class="nav-link active" id="users-tab" data-bs-toggle="tab" data-bs-target="#users" type="button" role="tab">
                    <i class="fas fa-users me-1"></i>Users
                </button>
            </li>
            <li class="nav-item" role="presentation">
                <button class="nav-link" id="roles-tab" data-bs-toggle="tab" data-bs-target="#roles" type="button" role="tab">
                    <i class="fas fa-shield-alt me-1"></i>Roles & Permissions
                </button>
            </li>
        </ul>

        <div class="tab-content" id="userTabsContent">
            <!-- Users Tab -->
            <div class="tab-pane fade show active" id="users" role="tabpanel">
                <div class="card">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <h5 class="mb-0"><i class="fas fa-users me-2"></i>System Users</h5>
                        <button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#addUserModal">
                            <i class="fas fa-plus me-1"></i>Add New User
                        </button>
                    </div>
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table table-striped table-hover">
                                <thead class="table-dark">
                                    <tr>
                                        <th>ID</th>
                                        <th>Username</th>
                                        <th>Full Name</th>
                                        <th>Email</th>
                                        <th>Role</th>
                                        <th>Status</th>
                                        <th>Expenses</th>
                                        <th>Activities</th>
                                        <th>Created</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($users as $u): ?>
                                        <tr>
                                            <td><?php echo $u['id']; ?></td>
                                            <td>
                                                <strong><?php echo htmlspecialchars($u['username']); ?></strong>
                                                <?php if ($u['id'] == $user['id']): ?>
                                                    <span class="badge bg-info ms-1">You</span>
                                                <?php endif; ?>
                                            </td>
                                            <td><?php echo htmlspecialchars($u['full_name']); ?></td>
                                            <td><?php echo htmlspecialchars($u['email']); ?></td>
                                            <td>
                                                <span class="badge bg-<?php
                                                    echo $u['role'] === 'administrator' ? 'danger' :
                                                        ($u['role'] === 'reviewer' ? 'warning' :
                                                        ($u['role'] === 'verification' ? 'info' :
                                                        ($u['role'] === 'report_viewer' ? 'primary' : 'secondary')));
                                                ?>">
                                                    <?php echo ucfirst(str_replace('_', ' ', $u['role'])); ?>
                                                </span>
                                            </td>
                                            <td>
                                                <?php if ($u['is_active']): ?>
                                                    <span class="badge bg-success">Active</span>
                                                <?php else: ?>
                                                    <span class="badge bg-danger">Inactive</span>
                                                <?php endif; ?>
                                            </td>
                                            <td>
                                                <span class="badge bg-primary"><?php echo $u['expense_count']; ?></span>
                                            </td>
                                            <td>
                                                <span class="badge bg-secondary"><?php echo $u['activity_count']; ?></span>
                                            </td>
                                            <td>
                                                <small class="text-muted">
                                                    <?php echo date('M j, Y', strtotime($u['created_at'])); ?>
                                                </small>
                                            </td>
                                            <td>
                                                <div class="btn-group btn-group-sm" role="group">
                                                    <button type="button" class="btn btn-outline-primary"
                                                            onclick="editUser(<?php echo htmlspecialchars(json_encode($u)); ?>)">
                                                        <i class="fas fa-edit"></i>
                                                    </button>
                                                    <?php if ($u['id'] != $user['id']): ?>
                                                        <button type="button" class="btn btn-outline-danger"
                                                                onclick="deleteUser(<?php echo $u['id']; ?>, '<?php echo htmlspecialchars($u['username']); ?>')">
                                                            <i class="fas fa-trash"></i>
                                                        </button>
                                                    <?php endif; ?>
                                                </div>
                                            </td>
                                        </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Roles & Permissions Tab -->
            <div class="tab-pane fade" id="roles" role="tabpanel">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0"><i class="fas fa-shield-alt me-2"></i>User Roles & Permissions</h5>
                        <p class="text-muted mb-0 mt-2">
                            <small>Comprehensive overview of system roles, their permissions, and access levels</small>
                        </p>
                    </div>
                    <div class="card-body">
                        <!-- Role Hierarchy Overview -->
                        <div class="alert alert-info mb-4">
                            <h6><i class="fas fa-info-circle me-2"></i>Role Hierarchy & Access Levels</h6>
                            <div class="row text-center">
                                <div class="col">
                                    <span class="badge bg-secondary fs-6">Level 1</span><br>
                                    <small>Data Entry</small>
                                </div>
                                <div class="col">
                                    <span class="badge bg-info fs-6">Level 2</span><br>
                                    <small>Verification</small>
                                </div>
                                <div class="col">
                                    <span class="badge bg-warning fs-6">Level 3</span><br>
                                    <small>Reviewer</small>
                                </div>
                                <div class="col">
                                    <span class="badge bg-primary fs-6">Level 2</span><br>
                                    <small>Report Viewer</small>
                                </div>
                                <div class="col">
                                    <span class="badge bg-danger fs-6">Level 5</span><br>
                                    <small>Administrator</small>
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <?php foreach ($role_permissions as $role_key => $role_info): ?>
                                <div class="col-md-6 col-xl-4 mb-4">
                                    <div class="card h-100 border-<?php echo $role_info['color']; ?> shadow-sm">
                                        <div class="card-header bg-<?php echo $role_info['color']; ?> text-white">
                                            <div class="d-flex justify-content-between align-items-center">
                                                <h6 class="mb-0">
                                                    <i class="fas fa-<?php echo $role_info['icon']; ?> me-2"></i>
                                                    <?php echo $role_info['name']; ?>
                                                </h6>
                                                <span class="badge bg-light text-dark">
                                                    Level <?php echo $role_info['level']; ?>
                                                </span>
                                            </div>
                                        </div>
                                        <div class="card-body">
                                            <p class="card-text text-muted small mb-3">
                                                <?php echo $role_info['description']; ?>
                                            </p>

                                            <h6 class="text-success">
                                                <i class="fas fa-check-circle me-1"></i>Permissions:
                                            </h6>
                                            <div class="permissions-list" style="max-height: 200px; overflow-y: auto;">
                                                <ul class="list-unstyled small">
                                                    <?php foreach ($role_info['permissions'] as $permission): ?>
                                                        <li class="mb-1">
                                                            <?php echo $permission; ?>
                                                        </li>
                                                    <?php endforeach; ?>
                                                </ul>
                                            </div>

                                            <?php if (isset($role_info['restrictions'])): ?>
                                                <h6 class="text-warning mt-3">
                                                    <i class="fas fa-exclamation-triangle me-1"></i>Restrictions:
                                                </h6>
                                                <div class="restrictions-list" style="max-height: 150px; overflow-y: auto;">
                                                    <ul class="list-unstyled small">
                                                        <?php foreach ($role_info['restrictions'] as $restriction): ?>
                                                            <li class="mb-1 text-muted">
                                                                <?php echo $restriction; ?>
                                                            </li>
                                                        <?php endforeach; ?>
                                                    </ul>
                                                </div>
                                            <?php endif; ?>
                                        </div>
                                        <div class="card-footer bg-light">
                                            <div class="row text-center">
                                                <div class="col">
                                                    <small class="text-muted">Active Users:</small><br>
                                                    <strong class="text-<?php echo $role_info['color']; ?>">
                                                        <?php
                                                            $count = array_filter($users, function($u) use ($role_key) {
                                                                return $u['role'] === $role_key && $u['is_active'];
                                                            });
                                                            echo count($count);
                                                        ?>
                                                    </strong>
                                                </div>
                                                <div class="col">
                                                    <small class="text-muted">Access Level:</small><br>
                                                    <span class="badge bg-<?php echo $role_info['color']; ?>">
                                                        <?php echo $role_info['level']; ?>
                                                    </span>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            <?php endforeach; ?>
                        </div>

                        <!-- Additional Information -->
                        <div class="row mt-4">
                            <div class="col-md-6">
                                <div class="card border-info">
                                    <div class="card-header bg-info text-white">
                                        <h6 class="mb-0"><i class="fas fa-lightbulb me-2"></i>Role Assignment Guidelines</h6>
                                    </div>
                                    <div class="card-body">
                                        <ul class="list-unstyled small">
                                            <li class="mb-2">
                                                <strong>Data Entry:</strong> For staff who create expense records
                                            </li>
                                            <li class="mb-2">
                                                <strong>Verification:</strong> For supervisors who verify accuracy
                                            </li>
                                            <li class="mb-2">
                                                <strong>Reviewer:</strong> For managers with approval authority
                                            </li>
                                            <li class="mb-2">
                                                <strong>Report Viewer:</strong> For stakeholders needing read-only access
                                            </li>
                                            <li class="mb-2">
                                                <strong>Administrator:</strong> For IT staff and system managers
                                            </li>
                                        </ul>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="card border-warning">
                                    <div class="card-header bg-warning text-dark">
                                        <h6 class="mb-0"><i class="fas fa-shield-alt me-2"></i>Security Best Practices</h6>
                                    </div>
                                    <div class="card-body">
                                        <ul class="list-unstyled small">
                                            <li class="mb-2">
                                                <i class="fas fa-check text-success me-1"></i>
                                                Assign minimum required permissions
                                            </li>
                                            <li class="mb-2">
                                                <i class="fas fa-check text-success me-1"></i>
                                                Regular review of user roles and access
                                            </li>
                                            <li class="mb-2">
                                                <i class="fas fa-check text-success me-1"></i>
                                                Deactivate users when no longer needed
                                            </li>
                                            <li class="mb-2">
                                                <i class="fas fa-check text-success me-1"></i>
                                                Monitor activity logs for unusual access
                                            </li>
                                            <li class="mb-2">
                                                <i class="fas fa-check text-success me-1"></i>
                                                Use strong passwords and regular updates
                                            </li>
                                        </ul>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Add User Modal -->
    <div class="modal fade" id="addUserModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">
                        <i class="fas fa-user-plus me-2"></i>Add New User
                    </h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <form method="POST">
                    <div class="modal-body">
                        <input type="hidden" name="action" value="add">

                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="username" class="form-label">Username *</label>
                                    <input type="text" class="form-control" id="username" name="username" required>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="email" class="form-label">Email *</label>
                                    <input type="email" class="form-control" id="email" name="email" required>
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="password" class="form-label">Password *</label>
                                    <input type="password" class="form-control" id="password" name="password" required minlength="6">
                                    <div class="form-text">Minimum 6 characters</div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="full_name" class="form-label">Full Name *</label>
                                    <input type="text" class="form-control" id="full_name" name="full_name" required>
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="role" class="form-label">Role *</label>
                                    <select class="form-select" id="role" name="role" required>
                                        <option value="data_entry">Data Entry</option>
                                        <option value="verification">Verification</option>
                                        <option value="reviewer">Reviewer</option>
                                        <option value="report_viewer">Report Viewer</option>
                                        <option value="administrator">Administrator</option>
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <div class="form-check mt-4">
                                        <input class="form-check-input" type="checkbox" id="is_active" name="is_active" checked>
                                        <label class="form-check-label" for="is_active">
                                            Active User
                                        </label>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save me-1"></i>Create User
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- Edit User Modal -->
    <div class="modal fade" id="editUserModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">
                        <i class="fas fa-user-edit me-2"></i>Edit User
                    </h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <form method="POST" id="editUserForm">
                    <div class="modal-body">
                        <input type="hidden" name="action" value="edit">
                        <input type="hidden" name="user_id" id="edit_user_id">

                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="edit_username" class="form-label">Username *</label>
                                    <input type="text" class="form-control" id="edit_username" name="username" required>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="edit_email" class="form-label">Email *</label>
                                    <input type="email" class="form-control" id="edit_email" name="email" required>
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="edit_full_name" class="form-label">Full Name *</label>
                                    <input type="text" class="form-control" id="edit_full_name" name="full_name" required>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="edit_role" class="form-label">Role *</label>
                                    <select class="form-select" id="edit_role" name="role" required>
                                        <option value="data_entry">Data Entry</option>
                                        <option value="verification">Verification</option>
                                        <option value="reviewer">Reviewer</option>
                                        <option value="report_viewer">Report Viewer</option>
                                        <option value="administrator">Administrator</option>
                                    </select>
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="new_password" class="form-label">New Password</label>
                                    <input type="password" class="form-control" id="new_password" name="new_password" minlength="6">
                                    <div class="form-text">Leave blank to keep current password</div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <div class="form-check mt-4">
                                        <input class="form-check-input" type="checkbox" id="edit_is_active" name="is_active">
                                        <label class="form-check-label" for="edit_is_active">
                                            Active User
                                        </label>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save me-1"></i>Update User
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- Delete User Modal -->
    <div class="modal fade" id="deleteUserModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title text-danger">
                        <i class="fas fa-exclamation-triangle me-2"></i>Delete User
                    </h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <form method="POST" id="deleteUserForm">
                    <div class="modal-body">
                        <input type="hidden" name="action" value="delete">
                        <input type="hidden" name="user_id" id="delete_user_id">

                        <div class="alert alert-warning">
                            <i class="fas fa-exclamation-triangle me-2"></i>
                            <strong>Warning!</strong> This action cannot be undone.
                        </div>

                        <p>Are you sure you want to delete the user <strong id="delete_username"></strong>?</p>

                        <div class="alert alert-info">
                            <i class="fas fa-info-circle me-2"></i>
                            <small>
                                <strong>Note:</strong> If the user has created expense records,
                                the account will be deactivated instead of deleted to maintain data integrity.
                            </small>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                        <button type="submit" class="btn btn-danger">
                            <i class="fas fa-trash me-1"></i>Delete User
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        function editUser(userData) {
            document.getElementById('edit_user_id').value = userData.id;
            document.getElementById('edit_username').value = userData.username;
            document.getElementById('edit_email').value = userData.email;
            document.getElementById('edit_full_name').value = userData.full_name;
            document.getElementById('edit_role').value = userData.role;
            document.getElementById('edit_is_active').checked = userData.is_active == 1;
            document.getElementById('new_password').value = '';

            new bootstrap.Modal(document.getElementById('editUserModal')).show();
        }

        function deleteUser(userId, username) {
            document.getElementById('delete_user_id').value = userId;
            document.getElementById('delete_username').textContent = username;

            new bootstrap.Modal(document.getElementById('deleteUserModal')).show();
        }

        // Clear form when add modal is closed
        document.getElementById('addUserModal').addEventListener('hidden.bs.modal', function () {
            this.querySelector('form').reset();
        });
    </script>
</body>
</html>
