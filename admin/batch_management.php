<?php
session_start();
require_once '../config/database.php';
require_once '../includes/functions.php';
require_once '../includes/BatchOperations.php';

// Check if user is logged in and is admin
if (!isset($_SESSION['user_id']) || $_SESSION['role'] !== 'administrator') {
    header('Location: ../login.php');
    exit();
}

$database = new Database();
$db = $database->getConnection();
$batchOps = new BatchOperations($db);

$user_id = $_SESSION['user_id'];
$user_role = $_SESSION['role'];

// Get filter parameters
$operation_type = $_GET['type'] ?? '';
$status_filter = $_GET['status'] ?? '';
$user_filter = $_GET['user'] ?? '';
$date_from = $_GET['date_from'] ?? date('Y-m-01');
$date_to = $_GET['date_to'] ?? date('Y-m-d');

// Build query conditions
$where_conditions = [];
$params = [];

if (!empty($operation_type)) {
    $where_conditions[] = 'bo.operation_type = ?';
    $params[] = $operation_type;
}

if (!empty($status_filter)) {
    $where_conditions[] = 'bo.status = ?';
    $params[] = $status_filter;
}

if (!empty($user_filter)) {
    $where_conditions[] = 'bo.user_id = ?';
    $params[] = $user_filter;
}

if (!empty($date_from)) {
    $where_conditions[] = 'DATE(bo.created_at) >= ?';
    $params[] = $date_from;
}

if (!empty($date_to)) {
    $where_conditions[] = 'DATE(bo.created_at) <= ?';
    $params[] = $date_to;
}

$where_clause = !empty($where_conditions) ? 'WHERE ' . implode(' AND ', $where_conditions) : '';

// Get batch operations
$stmt = $db->prepare("
    SELECT 
        bo.*,
        u.full_name as user_name,
        COUNT(bi.id) as total_items,
        SUM(CASE WHEN bi.status = 'completed' THEN 1 ELSE 0 END) as completed_items,
        SUM(CASE WHEN bi.status = 'failed' THEN 1 ELSE 0 END) as failed_items
    FROM batch_operations bo
    LEFT JOIN users u ON bo.user_id = u.id
    LEFT JOIN batch_items bi ON bo.batch_id = bi.batch_id
    $where_clause
    GROUP BY bo.id
    ORDER BY bo.created_at DESC
");

$stmt->execute($params);
$batch_operations = $stmt->fetchAll();

// Get summary statistics
$summary_stmt = $db->prepare("
    SELECT 
        COUNT(*) as total_batches,
        SUM(CASE WHEN operation_type = 'verification' THEN 1 ELSE 0 END) as verification_count,
        SUM(CASE WHEN operation_type = 'review' THEN 1 ELSE 0 END) as review_count,
        SUM(CASE WHEN status = 'pending' THEN 1 ELSE 0 END) as pending_count,
        SUM(CASE WHEN status = 'processing' THEN 1 ELSE 0 END) as processing_count,
        SUM(CASE WHEN status = 'completed' THEN 1 ELSE 0 END) as completed_count,
        SUM(CASE WHEN status = 'failed' THEN 1 ELSE 0 END) as failed_count
    FROM batch_operations bo
    $where_clause
");

$summary_stmt->execute($params);
$summary = $summary_stmt->fetch();

// Get users for filter
$users_stmt = $db->query("SELECT id, full_name FROM users WHERE is_active = 1 ORDER BY full_name");
$users = $users_stmt->fetchAll();
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Batch Management - Admin Panel</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="../assets/css/style.css" rel="stylesheet">
    <style>
        .batch-card {
            transition: transform 0.2s;
        }
        .batch-card:hover {
            transform: translateY(-2px);
        }
        .status-badge {
            font-size: 0.8em;
        }
        .progress-mini {
            height: 8px;
        }
    </style>
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container-fluid">
            <a class="navbar-brand" href="../dashboard.php">
                <i class="fas fa-receipt me-2"></i>Expenses System
            </a>
            
            <div class="navbar-nav ms-auto">
                <a class="nav-link" href="index.php">
                    <i class="fas fa-arrow-left me-1"></i>Back to Admin
                </a>
            </div>
        </div>
    </nav>

    <div class="container-fluid mt-4">
        <div class="row">
            <div class="col-12">
                <div class="d-flex justify-content-between align-items-center mb-4">
                    <h1><i class="fas fa-tasks me-2"></i>Batch Operations Management</h1>
                    <div class="d-flex gap-2">
                        <a href="../expenses/multi_verification.php" class="btn btn-primary">
                            <i class="fas fa-plus me-1"></i>New Verification Batch
                        </a>
                        <a href="../expenses/multi_review.php" class="btn btn-success">
                            <i class="fas fa-plus me-1"></i>New Review Batch
                        </a>
                    </div>
                </div>

                <!-- Filters -->
                <div class="search-filter-bar">
                    <form method="GET" class="row g-3">
                        <div class="col-md-2">
                            <label for="type" class="form-label">Operation Type</label>
                            <select class="form-select" id="type" name="type">
                                <option value="">All Types</option>
                                <option value="verification" <?php echo $operation_type === 'verification' ? 'selected' : ''; ?>>Verification</option>
                                <option value="review" <?php echo $operation_type === 'review' ? 'selected' : ''; ?>>Review</option>
                            </select>
                        </div>
                        <div class="col-md-2">
                            <label for="status" class="form-label">Status</label>
                            <select class="form-select" id="status" name="status">
                                <option value="">All Status</option>
                                <option value="pending" <?php echo $status_filter === 'pending' ? 'selected' : ''; ?>>Pending</option>
                                <option value="processing" <?php echo $status_filter === 'processing' ? 'selected' : ''; ?>>Processing</option>
                                <option value="completed" <?php echo $status_filter === 'completed' ? 'selected' : ''; ?>>Completed</option>
                                <option value="failed" <?php echo $status_filter === 'failed' ? 'selected' : ''; ?>>Failed</option>
                            </select>
                        </div>
                        <div class="col-md-2">
                            <label for="user" class="form-label">Created By</label>
                            <select class="form-select" id="user" name="user">
                                <option value="">All Users</option>
                                <?php foreach ($users as $user): ?>
                                    <option value="<?php echo $user['id']; ?>" 
                                            <?php echo $user_filter == $user['id'] ? 'selected' : ''; ?>>
                                        <?php echo htmlspecialchars($user['full_name']); ?>
                                    </option>
                                <?php endforeach; ?>
                            </select>
                        </div>
                        <div class="col-md-2">
                            <label for="date_from" class="form-label">Date From</label>
                            <input type="date" class="form-control" id="date_from" name="date_from" 
                                   value="<?php echo htmlspecialchars($date_from); ?>">
                        </div>
                        <div class="col-md-2">
                            <label for="date_to" class="form-label">Date To</label>
                            <input type="date" class="form-control" id="date_to" name="date_to" 
                                   value="<?php echo htmlspecialchars($date_to); ?>">
                        </div>
                        <div class="col-md-1">
                            <label class="form-label">&nbsp;</label>
                            <div class="d-grid">
                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-search"></i>
                                </button>
                            </div>
                        </div>
                        <div class="col-md-1">
                            <label class="form-label">&nbsp;</label>
                            <div class="d-grid">
                                <a href="batch_management.php" class="btn btn-outline-warning" title="Reset">
                                    <i class="fas fa-undo"></i>
                                </a>
                            </div>
                        </div>
                    </form>
                </div>

                <!-- Summary Statistics -->
                <div class="row mb-4">
                    <div class="col-md-2">
                        <div class="card bg-primary text-white">
                            <div class="card-body text-center">
                                <h4><?php echo $summary['total_batches']; ?></h4>
                                <small>Total Batches</small>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-2">
                        <div class="card bg-info text-white">
                            <div class="card-body text-center">
                                <h4><?php echo $summary['verification_count']; ?></h4>
                                <small>Verification</small>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-2">
                        <div class="card bg-success text-white">
                            <div class="card-body text-center">
                                <h4><?php echo $summary['review_count']; ?></h4>
                                <small>Review</small>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-2">
                        <div class="card bg-warning text-white">
                            <div class="card-body text-center">
                                <h4><?php echo $summary['pending_count']; ?></h4>
                                <small>Pending</small>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-2">
                        <div class="card bg-success text-white">
                            <div class="card-body text-center">
                                <h4><?php echo $summary['completed_count']; ?></h4>
                                <small>Completed</small>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-2">
                        <div class="card bg-danger text-white">
                            <div class="card-body text-center">
                                <h4><?php echo $summary['failed_count']; ?></h4>
                                <small>Failed</small>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Batch Operations List -->
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="fas fa-list me-2"></i>Batch Operations 
                            <span class="badge bg-secondary"><?php echo count($batch_operations); ?> records</span>
                        </h5>
                    </div>
                    <div class="card-body">
                        <?php if (empty($batch_operations)): ?>
                            <div class="text-center py-5">
                                <i class="fas fa-inbox fa-3x text-muted mb-3"></i>
                                <h5 class="text-muted">No Batch Operations Found</h5>
                                <p class="text-muted">No batch operations match your current filters.</p>
                            </div>
                        <?php else: ?>
                            <div class="table-responsive">
                                <table class="table table-hover">
                                    <thead class="table-dark">
                                        <tr>
                                            <th>Batch ID</th>
                                            <th>Type</th>
                                            <th>Created By</th>
                                            <th>Items</th>
                                            <th>Total Amount</th>
                                            <th>Progress</th>
                                            <th>Status</th>
                                            <th>Created</th>
                                            <th>Actions</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <?php foreach ($batch_operations as $batch): ?>
                                            <tr>
                                                <td>
                                                    <strong><?php echo htmlspecialchars($batch['batch_id']); ?></strong>
                                                </td>
                                                <td>
                                                    <span class="badge bg-<?php echo $batch['operation_type'] === 'verification' ? 'primary' : 'success'; ?>">
                                                        <i class="fas fa-<?php echo $batch['operation_type'] === 'verification' ? 'check-double' : 'clipboard-check'; ?> me-1"></i>
                                                        <?php echo ucfirst($batch['operation_type']); ?>
                                                    </span>
                                                </td>
                                                <td><?php echo htmlspecialchars($batch['user_name']); ?></td>
                                                <td>
                                                    <span class="fw-bold"><?php echo $batch['total_items']; ?></span> items
                                                    <?php if ($batch['total_items'] > 0): ?>
                                                        <div class="progress progress-mini mt-1">
                                                            <?php 
                                                            $progress = ($batch['completed_items'] / $batch['total_items']) * 100;
                                                            $failed_progress = ($batch['failed_items'] / $batch['total_items']) * 100;
                                                            ?>
                                                            <div class="progress-bar bg-success" style="width: <?php echo $progress; ?>%"></div>
                                                            <div class="progress-bar bg-danger" style="width: <?php echo $failed_progress; ?>%"></div>
                                                        </div>
                                                        <small class="text-muted">
                                                            <?php echo $batch['completed_items']; ?> completed, 
                                                            <?php echo $batch['failed_items']; ?> failed
                                                        </small>
                                                    <?php endif; ?>
                                                </td>
                                                <td class="text-end">
                                                    <strong><?php echo number_format($batch['total_amount'], 2); ?></strong>
                                                    <small class="text-muted d-block">บาท</small>
                                                </td>
                                                <td>
                                                    <?php if ($batch['total_items'] > 0): ?>
                                                        <?php $progress_percent = round(($batch['completed_items'] / $batch['total_items']) * 100); ?>
                                                        <div class="progress" style="height: 20px;">
                                                            <div class="progress-bar" style="width: <?php echo $progress_percent; ?>%">
                                                                <?php echo $progress_percent; ?>%
                                                            </div>
                                                        </div>
                                                    <?php else: ?>
                                                        <span class="text-muted">No items</span>
                                                    <?php endif; ?>
                                                </td>
                                                <td>
                                                    <?php
                                                    $status_colors = [
                                                        'pending' => 'warning',
                                                        'processing' => 'info',
                                                        'completed' => 'success',
                                                        'failed' => 'danger',
                                                        'cancelled' => 'secondary'
                                                    ];
                                                    $color = $status_colors[$batch['status']] ?? 'secondary';
                                                    ?>
                                                    <span class="badge bg-<?php echo $color; ?> status-badge">
                                                        <?php echo ucfirst($batch['status']); ?>
                                                    </span>
                                                </td>
                                                <td>
                                                    <div><?php echo date('M d, Y', strtotime($batch['created_at'])); ?></div>
                                                    <small class="text-muted"><?php echo date('H:i', strtotime($batch['created_at'])); ?></small>
                                                </td>
                                                <td>
                                                    <div class="btn-group btn-group-sm">
                                                        <a href="../expenses/batch_process.php?batch_id=<?php echo urlencode($batch['batch_id']); ?>&type=<?php echo $batch['operation_type']; ?>" 
                                                           class="btn btn-outline-primary" title="View Details">
                                                            <i class="fas fa-eye"></i>
                                                        </a>
                                                        <?php if ($batch['status'] === 'failed'): ?>
                                                            <button class="btn btn-outline-warning" 
                                                                    onclick="retryBatch('<?php echo $batch['batch_id']; ?>')" 
                                                                    title="Retry Batch">
                                                                <i class="fas fa-redo"></i>
                                                            </button>
                                                        <?php endif; ?>
                                                        <?php if (in_array($batch['status'], ['pending', 'failed'])): ?>
                                                            <button class="btn btn-outline-danger" 
                                                                    onclick="cancelBatch('<?php echo $batch['batch_id']; ?>')" 
                                                                    title="Cancel Batch">
                                                                <i class="fas fa-times"></i>
                                                            </button>
                                                        <?php endif; ?>
                                                    </div>
                                                </td>
                                            </tr>
                                        <?php endforeach; ?>
                                    </tbody>
                                </table>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script>
        function retryBatch(batchId) {
            if (confirm('Are you sure you want to retry this batch operation?\n\nThis will reset the batch status and allow reprocessing.')) {
                $.ajax({
                    url: '../api/batch_retry.php',
                    method: 'POST',
                    contentType: 'application/json',
                    data: JSON.stringify({
                        batch_id: batchId
                    }),
                    success: function(response) {
                        if (response.success) {
                            alert('Batch reset successfully! Redirecting to batch process page...');
                            // Redirect to batch process page
                            window.location.href = `../expenses/batch_process.php?batch_id=${batchId}&type=verification`;
                        } else {
                            alert('Error: ' + response.error);
                        }
                    },
                    error: function(xhr) {
                        const response = xhr.responseJSON || {};
                        alert('Error: ' + (response.error || 'Unknown error occurred'));
                    }
                });
            }
        }

        function cancelBatch(batchId) {
            if (confirm('Are you sure you want to cancel this batch operation?\n\nThis action cannot be undone.')) {
                $.ajax({
                    url: '../api/batch_cancel.php',
                    method: 'POST',
                    contentType: 'application/json',
                    data: JSON.stringify({
                        batch_id: batchId
                    }),
                    success: function(response) {
                        if (response.success) {
                            alert('Batch cancelled successfully!');
                            location.reload();
                        } else {
                            alert('Error: ' + response.error);
                        }
                    },
                    error: function(xhr) {
                        const response = xhr.responseJSON || {};
                        alert('Error: ' + (response.error || 'Unknown error occurred'));
                    }
                });
            }
        }
    </script>
</body>
</html>
