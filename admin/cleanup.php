<?php
session_start();
require_once '../config/database.php';
require_once '../includes/functions.php';

// Check if user is logged in and is admin
if (!isset($_SESSION['user_id']) || $_SESSION['role'] !== 'administrator') {
    header('Location: ../login.php');
    exit();
}

$database = new Database();
$db = $database->getConnection();

// Get table statistics
function getTableStats($db) {
    $tables = [
        'activity_logs' => 'Activity Logs',
        'batch_documents' => 'Batch Documents',
        'batch_items' => 'Batch Items',
        'batch_operations' => 'Batch Operations',
        'batch_performance_logs' => 'Performance Logs',
        'receipt_deductions' => 'Receipt Deductions',
        'expenses' => 'Expenses',
        'receipt_numbers' => 'Receipt Numbers'
    ];
    
    $stats = [];
    foreach ($tables as $table => $label) {
        try {
            $stmt = $db->prepare("SELECT COUNT(*) as count FROM $table");
            $stmt->execute();
            $result = $stmt->fetch();
            $stats[$table] = [
                'label' => $label,
                'count' => $result['count']
            ];
        } catch (Exception $e) {
            $stats[$table] = [
                'label' => $label,
                'count' => 0
            ];
        }
    }
    return $stats;
}

$table_stats = getTableStats($db);

// Get deduction structure status
function getDeductionStructureStatus($db) {
    $status = [];

    try {
        // Check if receipt_deductions table exists
        $stmt = $db->prepare("SHOW TABLES LIKE 'receipt_deductions'");
        $stmt->execute();
        $status['table_exists'] = $stmt->rowCount() > 0;

        // Check if deduction columns exist in receipt_numbers
        $columns = ['gross_amount', 'has_deductions', 'net_amount_calculated', 'receipt_index'];
        $status['columns_exist'] = [];
        foreach ($columns as $column) {
            $stmt = $db->prepare("
                SELECT COUNT(*) as count
                FROM INFORMATION_SCHEMA.COLUMNS
                WHERE table_name = 'receipt_numbers'
                AND table_schema = DATABASE()
                AND column_name = ?
            ");
            $stmt->execute([$column]);
            $status['columns_exist'][$column] = $stmt->fetch()['count'] > 0;
        }

        // Check if receipt_summary view exists
        $stmt = $db->prepare("SHOW TABLES LIKE 'receipt_summary'");
        $stmt->execute();
        $status['view_exists'] = $stmt->rowCount() > 0;

        // Check if procedures exist
        $procedures = ['AddReceiptDeduction', 'UpdateReceiptCalculations'];
        $status['procedures_exist'] = [];
        foreach ($procedures as $procedure) {
            $stmt = $db->prepare("SHOW PROCEDURE STATUS WHERE Name = ?");
            $stmt->execute([$procedure]);
            $status['procedures_exist'][$procedure] = $stmt->rowCount() > 0;
        }

    } catch (Exception $e) {
        $status['error'] = $e->getMessage();
    }

    return $status;
}

$deduction_status = getDeductionStructureStatus($db);
?>

<!DOCTYPE html>
<html lang="th">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Data Cleanup Tool - Admin</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        .cleanup-card {
            border: 2px solid #e9ecef;
            border-radius: 15px;
            transition: all 0.3s ease;
        }
        .cleanup-card:hover {
            border-color: #dc3545;
            box-shadow: 0 4px 15px rgba(220, 53, 69, 0.1);
        }
        .danger-zone {
            background: linear-gradient(135deg, #fff5f5 0%, #fed7d7 100%);
            border: 2px solid #fc8181;
            border-radius: 15px;
        }
        .stats-card {
            background: linear-gradient(135deg, #f7fafc 0%, #edf2f7 100%);
            border-radius: 10px;
            border: 1px solid #e2e8f0;
        }
        .table-row {
            transition: all 0.2s ease;
        }
        .table-row:hover {
            background-color: #f8f9fa;
        }
        .progress-section {
            display: none;
            background: #f8f9fa;
            border-radius: 10px;
            padding: 20px;
            margin-top: 20px;
        }
        .loading-overlay {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.7);
            z-index: 9999;
            justify-content: center;
            align-items: center;
        }
        .loading-spinner {
            text-align: center;
            color: white;
        }
    </style>
</head>
<body class="bg-light">
    <?php include 'includes/admin_nav.php'; ?>

    <div class="container-fluid py-4">
        <div class="row">
            <div class="col-12">
                <!-- Header -->
                <div class="d-flex justify-content-between align-items-center mb-4">
                    <div>
                        <h2><i class="fas fa-broom text-danger me-2"></i>Data Cleanup Tool</h2>
                        <p class="text-muted mb-0">Clean up test data and reset system tables</p>
                    </div>
                    <div>
                        <a href="index.php" class="btn btn-outline-secondary">
                            <i class="fas fa-arrow-left me-1"></i>Back to Dashboard
                        </a>
                    </div>
                </div>

                <!-- Alert Section -->
                <div id="alertContainer"></div>

                <div class="row">
                    <!-- Left Column: Table Statistics -->
                    <div class="col-md-6">
                        <div class="stats-card p-4 mb-4">
                            <h5><i class="fas fa-chart-bar text-primary me-2"></i>Current Data Statistics</h5>
                            <div class="table-responsive">
                                <table class="table table-sm">
                                    <thead>
                                        <tr>
                                            <th>Table</th>
                                            <th class="text-end">Records</th>
                                            <th class="text-center">Action</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <?php foreach ($table_stats as $table => $info): ?>
                                        <tr class="table-row">
                                            <td>
                                                <i class="fas fa-table text-muted me-2"></i>
                                                <?php echo $info['label']; ?>
                                            </td>
                                            <td class="text-end">
                                                <span class="badge bg-<?php echo $info['count'] > 0 ? 'primary' : 'secondary'; ?>">
                                                    <?php echo number_format($info['count']); ?>
                                                </span>
                                            </td>
                                            <td class="text-center">
                                                <input type="checkbox" class="form-check-input table-checkbox" 
                                                       value="<?php echo $table; ?>" 
                                                       <?php echo $info['count'] > 0 ? 'checked' : 'disabled'; ?>>
                                            </td>
                                        </tr>
                                        <?php endforeach; ?>
                                    </tbody>
                                </table>
                            </div>
                            
                            <div class="mt-3">
                                <button type="button" class="btn btn-sm btn-outline-primary" onclick="selectAll()">
                                    <i class="fas fa-check-square me-1"></i>Select All
                                </button>
                                <button type="button" class="btn btn-sm btn-outline-secondary" onclick="selectNone()">
                                    <i class="fas fa-square me-1"></i>Select None
                                </button>
                                <button type="button" class="btn btn-sm btn-info" onclick="refreshStats()">
                                    <i class="fas fa-sync me-1"></i>Refresh
                                </button>
                            </div>
                        </div>

                        <!-- Deduction Structure Status -->
                        <div class="stats-card p-4">
                            <h6><i class="fas fa-info-circle text-info me-2"></i>Deduction System Status</h6>
                            <div class="row">
                                <div class="col-6">
                                    <small class="text-muted">Table:</small><br>
                                    <span class="badge bg-<?php echo $deduction_status['table_exists'] ? 'success' : 'secondary'; ?>">
                                        receipt_deductions <?php echo $deduction_status['table_exists'] ? 'EXISTS' : 'NOT FOUND'; ?>
                                    </span>
                                </div>
                                <div class="col-6">
                                    <small class="text-muted">View:</small><br>
                                    <span class="badge bg-<?php echo $deduction_status['view_exists'] ? 'success' : 'secondary'; ?>">
                                        receipt_summary <?php echo $deduction_status['view_exists'] ? 'EXISTS' : 'NOT FOUND'; ?>
                                    </span>
                                </div>
                            </div>
                            <div class="row mt-2">
                                <div class="col-12">
                                    <small class="text-muted">Deduction Columns in receipt_numbers:</small><br>
                                    <?php if (isset($deduction_status['columns_exist'])): ?>
                                        <?php foreach ($deduction_status['columns_exist'] as $column => $exists): ?>
                                            <span class="badge bg-<?php echo $exists ? 'success' : 'secondary'; ?> me-1">
                                                <?php echo $column; ?>
                                            </span>
                                        <?php endforeach; ?>
                                    <?php endif; ?>
                                </div>
                            </div>
                            <div class="row mt-2">
                                <div class="col-12">
                                    <small class="text-muted">Procedures:</small><br>
                                    <?php if (isset($deduction_status['procedures_exist'])): ?>
                                        <?php foreach ($deduction_status['procedures_exist'] as $procedure => $exists): ?>
                                            <span class="badge bg-<?php echo $exists ? 'success' : 'secondary'; ?> me-1">
                                                <?php echo $procedure; ?>
                                            </span>
                                        <?php endforeach; ?>
                                    <?php endif; ?>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Right Column: Cleanup Actions -->
                    <div class="col-md-6">
                        <!-- Database Structure Cleanup -->
                        <div class="cleanup-card p-4 mb-4">
                            <h5><i class="fas fa-database text-warning me-2"></i>Database Structure Cleanup</h5>
                            <p class="text-warning mb-3">
                                <strong>Advanced:</strong> Remove deduction-related database objects (tables, views, procedures, triggers).
                            </p>

                            <div class="form-check mb-2">
                                <input class="form-check-input" type="checkbox" id="removeDeductionTable">
                                <label class="form-check-label" for="removeDeductionTable">
                                    <strong>Drop receipt_deductions table</strong>
                                    <small class="text-muted d-block">Permanently removes the deductions table and all its data</small>
                                </label>
                            </div>

                            <div class="form-check mb-2">
                                <input class="form-check-input" type="checkbox" id="removeDeductionColumns">
                                <label class="form-check-label" for="removeDeductionColumns">
                                    <strong>Remove deduction columns from receipt_numbers</strong>
                                    <small class="text-muted d-block">Removes: gross_amount, has_deductions, net_amount_calculated, receipt_index</small>
                                </label>
                            </div>

                            <div class="form-check mb-2">
                                <input class="form-check-input" type="checkbox" id="removeDeductionViews">
                                <label class="form-check-label" for="removeDeductionViews">
                                    <strong>Drop receipt_summary view</strong>
                                    <small class="text-muted d-block">Removes the view that aggregates receipt and deduction data</small>
                                </label>
                            </div>

                            <div class="form-check mb-3">
                                <input class="form-check-input" type="checkbox" id="removeDeductionProcedures">
                                <label class="form-check-label" for="removeDeductionProcedures">
                                    <strong>Drop deduction procedures and triggers</strong>
                                    <small class="text-muted d-block">Removes: AddReceiptDeduction, UpdateReceiptCalculations procedures and related triggers</small>
                                </label>
                            </div>

                            <button type="button" class="btn btn-warning btn-sm" onclick="cleanupDeductionStructure()" id="structureCleanupBtn">
                                <i class="fas fa-tools me-1"></i>Cleanup Database Structure
                            </button>
                        </div>

                        <div class="danger-zone p-4">
                            <h5><i class="fas fa-exclamation-triangle text-danger me-2"></i>Danger Zone</h5>
                            <p class="text-danger mb-3">
                                <strong>Warning:</strong> This action will permanently delete selected data.
                                This cannot be undone!
                            </p>

                            <!-- Cleanup Options -->
                            <div class="mb-3">
                                <h6>Cleanup Options:</h6>
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="preserveAdmin" checked>
                                    <label class="form-check-label" for="preserveAdmin">
                                        Preserve admin user account
                                    </label>
                                </div>
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="resetAutoIncrement" checked>
                                    <label class="form-check-label" for="resetAutoIncrement">
                                        Reset AUTO_INCREMENT counters
                                    </label>
                                </div>
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="createBackup">
                                    <label class="form-check-label" for="createBackup">
                                        Create backup before cleanup (recommended)
                                        <small class="text-muted d-block">Backup will be saved to: /backups/cleanup_backup_[timestamp].sql</small>
                                    </label>
                                </div>
                            </div>

                            <!-- Confirmation -->
                            <div class="mb-3">
                                <label for="confirmText" class="form-label">
                                    Type <strong>DELETE ALL DATA</strong> to confirm:
                                </label>
                                <input type="text" class="form-control" id="confirmText" 
                                       placeholder="Type confirmation text here">
                            </div>

                            <!-- Action Buttons -->
                            <div class="d-grid gap-2">
                                <button type="button" class="btn btn-danger btn-lg" onclick="startCleanup()" id="cleanupBtn" disabled>
                                    <i class="fas fa-trash me-2"></i>Start Cleanup Process
                                </button>
                                <small class="text-muted text-center">
                                    This process may take several minutes depending on data size
                                </small>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Progress Section -->
                <div class="progress-section" id="progressSection">
                    <h5><i class="fas fa-cog fa-spin me-2"></i>Cleanup in Progress...</h5>
                    <div class="progress mb-3">
                        <div class="progress-bar progress-bar-striped progress-bar-animated bg-danger"
                             role="progressbar" style="width: 0%" id="progressBar"></div>
                    </div>
                    <div id="progressText">Initializing cleanup process...</div>
                    <div class="mt-3">
                        <small class="text-muted">Please do not close this window during the cleanup process.</small>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Loading Overlay -->
    <div class="loading-overlay" id="loadingOverlay">
        <div class="loading-spinner">
            <div class="spinner-border text-light mb-3" role="status">
                <span class="visually-hidden">Loading...</span>
            </div>
            <div>Processing cleanup...</div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://code.jquery.com/jquery-3.7.0.min.js"></script>
    <script>
        // Enable/disable cleanup button based on confirmation text
        $('#confirmText').on('input', function() {
            const confirmText = $(this).val().trim();
            const isValid = confirmText === 'DELETE ALL DATA';
            $('#cleanupBtn').prop('disabled', !isValid);
        });

        // Select all checkboxes
        function selectAll() {
            $('.table-checkbox:not(:disabled)').prop('checked', true);
        }

        // Select none checkboxes  
        function selectNone() {
            $('.table-checkbox').prop('checked', false);
        }

        // Refresh statistics
        function refreshStats() {
            location.reload();
        }

        // Show alert
        function showAlert(message, type = 'info') {
            const alertHtml = `
                <div class="alert alert-${type} alert-dismissible fade show" role="alert">
                    ${message}
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            `;
            $('#alertContainer').html(alertHtml);
            
            // Auto-dismiss after 5 seconds
            setTimeout(() => {
                $('.alert').alert('close');
            }, 5000);
        }

        // Cleanup deduction database structure
        function cleanupDeductionStructure() {
            const options = {
                remove_table: $('#removeDeductionTable').is(':checked'),
                remove_columns: $('#removeDeductionColumns').is(':checked'),
                remove_views: $('#removeDeductionViews').is(':checked'),
                remove_procedures: $('#removeDeductionProcedures').is(':checked')
            };

            if (!options.remove_table && !options.remove_columns && !options.remove_views && !options.remove_procedures) {
                showAlert('Please select at least one structure cleanup option.', 'warning');
                return;
            }

            let confirmMessage = 'Are you sure you want to remove the selected database structures?\n\n';
            if (options.remove_table) confirmMessage += '- Drop receipt_deductions table\n';
            if (options.remove_columns) confirmMessage += '- Remove deduction columns from receipt_numbers\n';
            if (options.remove_views) confirmMessage += '- Drop receipt_summary view\n';
            if (options.remove_procedures) confirmMessage += '- Drop deduction procedures and triggers\n';
            confirmMessage += '\nThis action cannot be undone!';

            if (!confirm(confirmMessage)) {
                return;
            }

            $('#structureCleanupBtn').prop('disabled', true).html('<i class="fas fa-spinner fa-spin me-1"></i>Processing...');

            $.ajax({
                url: 'api/cleanup_deduction_structure.php',
                method: 'POST',
                contentType: 'application/json',
                data: JSON.stringify(options),
                success: function(response) {
                    $('#structureCleanupBtn').prop('disabled', false).html('<i class="fas fa-tools me-1"></i>Cleanup Database Structure');

                    if (response.success) {
                        showAlert(`
                            <strong>Database Structure Cleanup Completed!</strong><br>
                            ${response.message}
                        `, 'success');

                        // Reset checkboxes
                        $('#removeDeductionTable, #removeDeductionColumns, #removeDeductionViews, #removeDeductionProcedures').prop('checked', false);

                        // Refresh stats
                        setTimeout(() => {
                            refreshStats();
                        }, 1000);
                    } else {
                        showAlert('Structure cleanup failed: ' + response.error, 'danger');
                    }
                },
                error: function(xhr) {
                    $('#structureCleanupBtn').prop('disabled', false).html('<i class="fas fa-tools me-1"></i>Cleanup Database Structure');

                    const response = xhr.responseJSON || {};
                    showAlert('Error during structure cleanup: ' + (response.error || 'Unknown error'), 'danger');
                }
            });
        }

        // Start cleanup process
        function startCleanup() {
            const selectedTables = $('.table-checkbox:checked').map(function() {
                return $(this).val();
            }).get();

            if (selectedTables.length === 0) {
                showAlert('Please select at least one table to cleanup.', 'warning');
                return;
            }

            const confirmText = $('#confirmText').val().trim();
            if (confirmText !== 'DELETE ALL DATA') {
                showAlert('Please type the confirmation text correctly.', 'warning');
                return;
            }

            if (!confirm(`Are you sure you want to delete data from ${selectedTables.length} table(s)?\n\nThis action cannot be undone!`)) {
                return;
            }

            // Show progress section
            $('#progressSection').show();
            $('#loadingOverlay').show();

            // Prepare cleanup data
            const cleanupData = {
                tables: selectedTables,
                preserve_admin: $('#preserveAdmin').is(':checked'),
                reset_auto_increment: $('#resetAutoIncrement').is(':checked'),
                create_backup: $('#createBackup').is(':checked')
            };

            // Start cleanup process
            $.ajax({
                url: 'api/cleanup_all_data.php',
                method: 'POST',
                contentType: 'application/json',
                data: JSON.stringify(cleanupData),
                success: function(response) {
                    $('#loadingOverlay').hide();
                    
                    if (response.success) {
                        $('#progressBar').css('width', '100%');
                        $('#progressText').text('Cleanup completed successfully!');
                        
                        showAlert(`
                            <strong>Cleanup Completed!</strong><br>
                            Tables processed: ${response.tables_processed}<br>
                            Records deleted: ${response.total_deleted}<br>
                            Duration: ${response.duration}
                        `, 'success');
                        
                        // Reset form
                        $('#confirmText').val('');
                        $('#cleanupBtn').prop('disabled', true);
                        
                        // Refresh stats after 2 seconds
                        setTimeout(() => {
                            refreshStats();
                        }, 2000);
                        
                    } else {
                        $('#progressSection').hide();
                        showAlert('Cleanup failed: ' + response.error, 'danger');
                    }
                },
                error: function(xhr) {
                    $('#loadingOverlay').hide();
                    $('#progressSection').hide();
                    
                    const response = xhr.responseJSON || {};
                    showAlert('Error during cleanup: ' + (response.error || 'Unknown error'), 'danger');
                }
            });
        }
    </script>
</body>
</html>
