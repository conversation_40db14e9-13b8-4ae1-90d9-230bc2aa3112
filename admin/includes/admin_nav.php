<?php
// Get current page for active menu highlighting
$current_page = basename($_SERVER['PHP_SELF']);
$current_dir = basename(dirname($_SERVER['PHP_SELF']));

// Define menu items with their properties
$menu_items = [
    'dashboard' => [
        'icon' => 'fas fa-tachometer-alt',
        'text' => 'Dashboard',
        'url' => 'index.php',
        'active' => $current_page === 'index.php'
    ],
    'master_data' => [
        'icon' => 'fas fa-database',
        'text' => 'Master Data',
        'active' => in_array($current_page, ['items.php', 'customers.php', 'drivers.php']),
        'dropdown' => [
            ['icon' => 'fas fa-boxes', 'text' => 'Items', 'url' => 'items.php', 'active' => $current_page === 'items.php'],
            ['icon' => 'fas fa-users', 'text' => 'Customers', 'url' => 'customers.php', 'active' => $current_page === 'customers.php'],
            ['icon' => 'fas fa-id-card', 'text' => 'Drivers', 'url' => 'drivers.php', 'active' => $current_page === 'drivers.php']
        ]
    ],
    'expenses' => [
        'icon' => 'fas fa-receipt',
        'text' => 'Expenses',
        'active' => in_array($current_page, ['expenses.php', 'expense_bulk.php', 'migration.php', 'migration_batches.php']),
        'dropdown' => [
            ['icon' => 'fas fa-list', 'text' => 'Manage', 'url' => 'expenses.php', 'active' => $current_page === 'expenses.php'],
            ['icon' => 'fas fa-tasks', 'text' => 'Bulk Ops', 'url' => 'expense_bulk.php', 'active' => $current_page === 'expense_bulk.php'],
            'divider',
            ['icon' => 'fas fa-upload', 'text' => 'Migration', 'url' => 'migration.php', 'active' => $current_page === 'migration.php'],
            ['icon' => 'fas fa-history', 'text' => 'History', 'url' => 'migration_batches.php', 'active' => $current_page === 'migration_batches.php']
        ]
    ],
    'users' => [
        'icon' => 'fas fa-user-cog',
        'text' => 'Users',
        'url' => 'users.php',
        'active' => $current_page === 'users.php'
    ],
    'permissions' => [
        'icon' => 'fas fa-shield-alt',
        'text' => 'Permissions',
        'url' => 'permissions.php',
        'active' => $current_page === 'permissions.php'
    ],
    'logs' => [
        'icon' => 'fas fa-clipboard-list',
        'text' => 'Logs',
        'url' => 'logs.php',
        'active' => $current_page === 'logs.php'
    ],
    'system' => [
        'icon' => 'fas fa-cogs',
        'text' => '',
        'active' => in_array($current_page, ['cleanup.php', 'batch_management.php']),
        'dropdown' => [
            ['icon' => 'fas fa-layer-group', 'text' => 'Batch Management', 'url' => 'batch_management.php', 'active' => $current_page === 'batch_management.php'],
            'divider',
            ['icon' => 'fas fa-images', 'text' => 'Image Compression', 'url' => 'image_compression_stats.php', 'active' => $current_page === 'image_compression_stats.php'],
            'divider',
            ['icon' => 'fas fa-broom text-danger', 'text' => 'Data Cleanup', 'url' => 'cleanup.php', 'active' => $current_page === 'cleanup.php']
            
        ]
    ]
];
?>

<!-- Compact Admin Navigation -->
<nav class="navbar navbar-expand-lg navbar-dark bg-gradient-primary shadow-sm">
    <div class="container-fluid">
        <!-- Brand -->
        <a class="navbar-brand fw-bold" href="index.php">
            <i class="fas fa-cogs me-2"></i>
            <span class="d-none d-md-inline">Admin Panel</span>
            <span class="d-md-none">Admin</span>
        </a>

        <!-- Mobile Toggle -->
        <button class="navbar-toggler border-0" type="button" data-bs-toggle="collapse" data-bs-target="#adminNav">
            <span class="navbar-toggler-icon"></span>
        </button>

        <div class="collapse navbar-collapse" id="adminNav">
            <!-- Main Menu -->
            <ul class="navbar-nav me-auto">
                <?php foreach ($menu_items as $key => $item): ?>
                    <?php if (isset($item['dropdown'])): ?>
                        <!-- Dropdown Menu -->
                        <li class="nav-item dropdown">
                            <a class="nav-link dropdown-toggle px-3 <?php echo ($item['active'] ?? false) ? 'active' : ''; ?>"
                               href="#" id="<?php echo $key; ?>Dropdown"
                               role="button" data-bs-toggle="dropdown" aria-expanded="false">
                                <i class="<?php echo $item['icon']; ?> me-1"></i>
                                <span class="d-lg-inline d-xl-inline"><?php echo $item['text']; ?></span>
                            </a>
                            <ul class="dropdown-menu shadow border-0">
                                <?php foreach ($item['dropdown'] as $subitem): ?>
                                    <?php if ($subitem === 'divider'): ?>
                                        <li><hr class="dropdown-divider"></li>
                                    <?php else: ?>
                                        <li>
                                            <a class="dropdown-item py-2 <?php echo ($subitem['active'] ?? false) ? 'active' : ''; ?>"
                                               href="<?php echo $subitem['url']; ?>">
                                                <i class="<?php echo $subitem['icon']; ?> me-2 text-muted"></i>
                                                <?php echo $subitem['text']; ?>
                                            </a>
                                        </li>
                                    <?php endif; ?>
                                <?php endforeach; ?>
                            </ul>
                        </li>
                    <?php else: ?>
                        <!-- Single Menu Item -->
                        <li class="nav-item">
                            <a class="nav-link px-3 <?php echo ($item['active'] ?? false) ? 'active' : ''; ?>"
                               href="<?php echo $item['url']; ?>">
                                <i class="<?php echo $item['icon']; ?> me-1"></i>
                                <span class="d-lg-inline d-xl-inline"><?php echo $item['text']; ?></span>
                            </a>
                        </li>
                    <?php endif; ?>
                <?php endforeach; ?>
            </ul>

            <!-- Right Side Menu -->
            <ul class="navbar-nav">
                <!-- Quick Actions -->
                <li class="nav-item dropdown d-none d-lg-block">
                    <a class="nav-link dropdown-toggle px-2" href="#" id="quickActionsDropdown" 
                       role="button" data-bs-toggle="dropdown" aria-expanded="false">
                        <i class="fas fa-bolt"></i>
                    </a>
                    <ul class="dropdown-menu dropdown-menu-end shadow border-0">
                        <li><h6 class="dropdown-header">Quick Actions</h6></li>
                        <li><a class="dropdown-item" href="expenses.php">
                            <i class="fas fa-plus me-2 text-success"></i>Manage
                        </a></li>
                        <li><a class="dropdown-item" href="users.php">
                            <i class="fas fa-user-plus me-2 text-info"></i>Add User
                        </a></li>
                        <li><a class="dropdown-item" href="migration.php">
                            <i class="fas fa-upload me-2 text-warning"></i>Import Data
                        </a></li>
                    </ul>
                </li>

                <!-- Back to Main -->
                <li class="nav-item">
                    <a class="nav-link px-2" href="../dashboard.php" title="Back to Main Dashboard">
                        <i class="fas fa-arrow-left me-1"></i>
                        <span class="d-none d-md-inline">Main</span>
                    </a>
                </li>

                <!-- User Menu -->
                <li class="nav-item dropdown">
                    <a class="nav-link dropdown-toggle px-2" href="#" id="userDropdown" 
                       role="button" data-bs-toggle="dropdown" aria-expanded="false">
                        <i class="fas fa-user-circle me-1"></i>
                        <span class="d-none d-lg-inline"><?php echo htmlspecialchars($_SESSION['full_name'] ?? 'Admin'); ?></span>
                    </a>
                    <ul class="dropdown-menu dropdown-menu-end shadow border-0">
                        <li><h6 class="dropdown-header">
                            <i class="fas fa-user me-1"></i>
                            <?php echo htmlspecialchars($_SESSION['full_name'] ?? 'Administrator'); ?>
                        </h6></li>
                        <li><hr class="dropdown-divider"></li>
                        <li><a class="dropdown-item" href="../profile.php">
                            <i class="fas fa-user-edit me-2 text-primary"></i>Profile
                        </a></li>
                        <li><a class="dropdown-item" href="../settings.php">
                            <i class="fas fa-cog me-2 text-secondary"></i>Settings
                        </a></li>
                        <li><hr class="dropdown-divider"></li>
                        <li><a class="dropdown-item text-danger" href="../logout.php">
                            <i class="fas fa-sign-out-alt me-2"></i>Logout
                        </a></li>
                    </ul>
                </li>
            </ul>
        </div>
    </div>
</nav>

<!-- Custom CSS for Navigation -->
<style>
.bg-gradient-primary {
    background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
}

.navbar-nav .nav-link {
    transition: all 0.3s ease;
    border-radius: 6px;
    margin: 0 2px;
}

.navbar-nav .nav-link:hover {
    background-color: rgba(255, 255, 255, 0.1);
    transform: translateY(-1px);
}

.navbar-nav .nav-link.active {
    background-color: rgba(255, 255, 255, 0.2);
    font-weight: 600;
}

.dropdown-menu {
    border-radius: 10px;
    padding: 8px 0;
    margin-top: 8px;
}

.dropdown-item {
    border-radius: 6px;
    margin: 2px 8px;
    transition: all 0.2s ease;
}

.dropdown-item:hover {
    background-color: #f8f9fa;
    transform: translateX(4px);
}

.dropdown-header {
    font-size: 0.75rem;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    color: #6c757d;
    font-weight: 600;
}

.navbar-toggler:focus {
    box-shadow: none;
}

/* Responsive adjustments */
@media (max-width: 991.98px) {
    .navbar-nav .nav-link {
        padding: 8px 16px;
        margin: 2px 0;
        color: #ffffff !important; /* Force white text on mobile */
    }

    .navbar-nav .nav-link span {
        color: #ffffff !important; /* Force white text for spans */
        display: inline !important; /* Show text on mobile */
    }

    .navbar-nav .nav-link i {
        color: #ffffff !important; /* Force white icons */
    }

    .dropdown-menu {
        margin-top: 0;
        border-radius: 0;
        border: none;
        box-shadow: none;
        background-color: rgba(255, 255, 255, 0.95);
    }

    .dropdown-item {
        color: #333 !important; /* Dark text for dropdown items */
    }
}

@media (max-width: 1199.98px) and (min-width: 992px) {
    .navbar-nav .nav-link span {
        display: none;
    }

    .navbar-nav .nav-link {
        padding: 8px 12px;
    }
}

/* Additional mobile fixes */
@media (max-width: 991.98px) {
    .navbar-collapse {
        background-color: rgba(0, 123, 255, 0.95);
        border-radius: 8px;
        margin-top: 8px;
        padding: 8px;
    }

    .navbar-nav .nav-link:hover {
        background-color: rgba(255, 255, 255, 0.2);
        color: #ffffff !important;
    }

    .navbar-nav .nav-link.active {
        background-color: rgba(255, 255, 255, 0.3);
        color: #ffffff !important;
        font-weight: 600;
    }
}

/* Notification badge for future use */
.notification-badge {
    position: absolute;
    top: -2px;
    right: -2px;
    background: #dc3545;
    color: white;
    border-radius: 50%;
    width: 18px;
    height: 18px;
    font-size: 10px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: bold;
}
</style>
