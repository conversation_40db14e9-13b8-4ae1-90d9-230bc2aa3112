$(document).ready(function() {
    // Initialize DataTable
    const table = $('#expensesTable').DataTable({
        processing: true,
        serverSide: true,
        ajax: {
            url: 'api/expenses_datatable.php',
            type: 'POST',
            data: function(d) {
                // Add search parameters
                d.search_params = getSearchParams();
            }
        },
        columns: [
            {
                data: 'id',
                orderable: false,
                searchable: false,
                render: function(data, type, row) {
                    return `<input type="checkbox" class="form-check-input expense-checkbox" value="${data}">`;
                }
            },
            { data: 'exno' },
            { 
                data: 'job_open_date',
                render: function(data, type, row) {
                    return data ? new Date(data).toLocaleDateString() : '-';
                }
            },
            { data: 'customer_name' },
            { data: 'driver_name' },
            { 
                data: 'total_amount',
                render: function(data, type, row) {
                    return data ? parseFloat(data).toLocaleString('en-US', {
                        minimumFractionDigits: 2,
                        maximumFractionDigits: 2
                    }) + ' ฿' : '-';
                }
            },
            {
                data: 'status',
                render: function(data, type, row) {
                    const statusConfig = {
                        'open': { class: 'bg-info', icon: 'fas fa-folder-open', text: 'Open' },
                        'pending': { class: 'bg-warning text-dark', icon: 'fas fa-clock', text: 'Pending' },
                        'success': { class: 'bg-success', icon: 'fas fa-check-circle', text: 'Approved' },
                        'rejected': { class: 'bg-danger', icon: 'fas fa-times-circle', text: 'Rejected' },
                        'returned': { class: 'bg-secondary', icon: 'fas fa-undo', text: 'Returned' }
                    };
                    const config = statusConfig[data] || { class: 'bg-dark', icon: 'fas fa-question', text: data.charAt(0).toUpperCase() + data.slice(1) };
                    return `<span class="badge ${config.class} status-badge"><i class="${config.icon} me-1"></i>${config.text}</span>`;
                }
            },
            { data: 'created_by_name' },
            { 
                data: 'created_at',
                render: function(data, type, row) {
                    return new Date(data).toLocaleString();
                }
            },
            {
                data: 'id',
                orderable: false,
                searchable: false,
                render: function(data, type, row) {
                    return `
                        <div class="btn-group table-actions" role="group">
                            <a href="../expenses/view.php?id=${data}" class="btn btn-sm btn-outline-primary" title="View">
                                <i class="fas fa-eye"></i>
                            </a>
                            <a href="../expenses/edit.php?id=${data}" class="btn btn-sm btn-outline-warning" title="Edit">
                                <i class="fas fa-edit"></i>
                            </a>
                            <button type="button" class="btn btn-sm btn-outline-danger" onclick="deleteExpense(${data})" title="Delete">
                                <i class="fas fa-trash"></i>
                            </button>
                        </div>
                    `;
                }
            }
        ],
        order: [[8, 'desc']], // Order by created_at desc
        pageLength: 25,
        lengthMenu: [[10, 25, 50, 100, -1], [10, 25, 50, 100, "All"]],
        dom: '<"row"<"col-sm-12 col-md-6"l><"col-sm-12 col-md-6"f>>rtip',
        language: {
            processing: "Loading expenses...",
            emptyTable: "No expenses found",
            info: "Showing _START_ to _END_ of _TOTAL_ expenses",
            infoEmpty: "Showing 0 to 0 of 0 expenses",
            infoFiltered: "(filtered from _MAX_ total expenses)"
        }
    });

    // Handle select all checkbox
    $('#selectAll').on('change', function() {
        const isChecked = $(this).is(':checked');
        $('.expense-checkbox').prop('checked', isChecked);
        updateBulkActionsPanel();
    });

    // Handle individual checkboxes
    $(document).on('change', '.expense-checkbox', function() {
        updateBulkActionsPanel();
        
        // Update select all checkbox
        const totalCheckboxes = $('.expense-checkbox').length;
        const checkedCheckboxes = $('.expense-checkbox:checked').length;
        
        if (checkedCheckboxes === 0) {
            $('#selectAll').prop('indeterminate', false).prop('checked', false);
        } else if (checkedCheckboxes === totalCheckboxes) {
            $('#selectAll').prop('indeterminate', false).prop('checked', true);
        } else {
            $('#selectAll').prop('indeterminate', true);
        }
    });

    // Load dropdown options for advanced search
    loadSearchDropdowns();
});

function updateBulkActionsPanel() {
    const selectedCount = $('.expense-checkbox:checked').length;
    $('#selectedCount').text(selectedCount);
    
    if (selectedCount > 0) {
        $('#bulkActionsPanel').slideDown();
    } else {
        $('#bulkActionsPanel').slideUp();
    }
}

function getSearchParams() {
    const form = document.getElementById('advancedSearchForm');
    if (!form) return {};
    
    const formData = new FormData(form);
    const params = {};
    
    for (let [key, value] of formData.entries()) {
        if (value.trim() !== '') {
            params[key] = value;
        }
    }
    
    return params;
}

function applySearch() {
    $('#expensesTable').DataTable().ajax.reload();
    $('#advancedSearchModal').modal('hide');
    
    // Show search indicator
    const hasFilters = Object.keys(getSearchParams()).length > 0;
    if (hasFilters) {
        showAlert('Search filters applied', 'info');
    }
}

function clearSearch() {
    document.getElementById('advancedSearchForm').reset();
    $('#expensesTable').DataTable().ajax.reload();
    showAlert('Search filters cleared', 'info');
}

function refreshTable() {
    $('#expensesTable').DataTable().ajax.reload(null, false);
    showAlert('Table refreshed', 'success');
}

function bulkAction(action, value = null) {
    const selectedIds = $('.expense-checkbox:checked').map(function() {
        return $(this).val();
    }).get();
    
    if (selectedIds.length === 0) {
        showAlert('Please select at least one expense', 'warning');
        return;
    }
    
    let confirmMessage = '';
    let actionText = '';
    
    switch (action) {
        case 'delete':
            confirmMessage = `Are you sure you want to delete ${selectedIds.length} expense(s)? This action cannot be undone.`;
            actionText = 'Delete';
            break;
        case 'status':
            confirmMessage = `Are you sure you want to change status of ${selectedIds.length} expense(s) to "${value}"?`;
            actionText = 'Change Status';
            break;
    }
    
    if (confirm(confirmMessage)) {
        $.ajax({
            url: 'api/expense_bulk_action.php',
            method: 'POST',
            data: {
                action: action,
                value: value,
                ids: selectedIds
            },
            success: function(response) {
                if (response.success) {
                    showAlert(`${actionText} completed successfully`, 'success');
                    $('#expensesTable').DataTable().ajax.reload();
                    $('#selectAll').prop('checked', false);
                    updateBulkActionsPanel();
                } else {
                    showAlert(response.message || 'Operation failed', 'danger');
                }
            },
            error: function() {
                showAlert('An error occurred while processing the request', 'danger');
            }
        });
    }
}

function deleteExpense(id) {
    if (confirm('Are you sure you want to delete this expense? This action cannot be undone.')) {
        $.ajax({
            url: 'api/expense_bulk_action.php',
            method: 'POST',
            data: {
                action: 'delete',
                ids: [id]
            },
            success: function(response) {
                if (response.success) {
                    showAlert('Expense deleted successfully', 'success');
                    $('#expensesTable').DataTable().ajax.reload();
                } else {
                    showAlert(response.message || 'Delete failed', 'danger');
                }
            },
            error: function() {
                showAlert('An error occurred while deleting the expense', 'danger');
            }
        });
    }
}

function exportData(format) {
    const searchParams = getSearchParams();
    const queryString = new URLSearchParams(searchParams).toString();
    const url = `api/expense_export.php?format=${format}&${queryString}`;
    
    // Create temporary link and trigger download
    const link = document.createElement('a');
    link.href = url;
    link.download = `expenses_${format}_${new Date().toISOString().split('T')[0]}.${format}`;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    
    showAlert(`Export started (${format.toUpperCase()})`, 'info');
}

function cleanupTestData() {
    if (confirm('Are you sure you want to delete ALL test data? This will remove all expenses with numbers starting with "TEST" or "DEMO". This action cannot be undone.')) {
        $.ajax({
            url: 'api/cleanup_test_data.php',
            method: 'POST',
            success: function(response) {
                if (response.success) {
                    showAlert(`${response.deleted_count} test records deleted successfully`, 'success');
                    $('#expensesTable').DataTable().ajax.reload();
                    setTimeout(() => {
                        location.reload(); // Reload to update statistics
                    }, 2000);
                } else {
                    showAlert(response.message || 'Cleanup failed', 'danger');
                }
            },
            error: function() {
                showAlert('An error occurred during cleanup', 'danger');
            }
        });
    }
}

function viewTestData() {
    // Apply filter to show only test data
    $('#advancedSearchForm input[name="exno"]').val('TEST');
    applySearch();
}

function loadSearchDropdowns() {
    // Load customers
    $.get('api/get_customers.php', function(data) {
        const customerSelect = $('select[name="customer_id"]');
        data.forEach(customer => {
            customerSelect.append(`<option value="${customer.id}">${customer.name}</option>`);
        });
    });
    
    // Load users
    $.get('api/get_users.php', function(data) {
        const userSelect = $('select[name="created_by"]');
        data.forEach(user => {
            userSelect.append(`<option value="${user.id}">${user.full_name}</option>`);
        });
    });
}

function showAlert(message, type) {
    const alertHtml = `
        <div class="alert alert-${type} alert-dismissible fade show position-fixed" 
             style="top: 20px; right: 20px; z-index: 9999; min-width: 300px;" role="alert">
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    `;
    
    $('body').append(alertHtml);
    
    // Auto dismiss after 5 seconds
    setTimeout(() => {
        $('.alert').alert('close');
    }, 5000);
}
