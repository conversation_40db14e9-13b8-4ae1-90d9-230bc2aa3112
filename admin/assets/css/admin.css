/* Admin Panel Custom Styles */

/* Enhanced Navigation Styles */
.navbar {
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    border-bottom: 3px solid rgba(255,255,255,0.1);
}

.navbar-brand {
    font-weight: 700;
    font-size: 1.25rem;
    transition: all 0.3s ease;
}

.navbar-brand:hover {
    transform: scale(1.05);
}

/* Compact navigation for smaller screens */
@media (max-width: 1199.98px) {
    .navbar-nav .nav-link span {
        display: none;
    }

    .navbar-nav .nav-link {
        padding: 8px 10px;
        min-width: 40px;
        text-align: center;
    }

    .navbar-brand span {
        font-size: 0.9rem;
    }
}

@media (max-width: 991.98px) {
    .navbar-nav .nav-link span {
        display: inline;
    }

    .navbar-nav .nav-link {
        padding: 10px 16px;
        text-align: left;
    }
}

/* Mobile-first responsive design */
@media (max-width: 576px) {
    .container-fluid {
        padding-left: 10px;
        padding-right: 10px;
    }

    .card {
        margin-bottom: 15px;
    }

    .btn-group {
        flex-direction: column;
        width: 100%;
    }

    .btn-group .btn {
        border-radius: 6px !important;
        margin-bottom: 5px;
    }

    .table-responsive {
        font-size: 0.85rem;
    }

    .expense-card h4 {
        font-size: 1.5rem;
    }
}

/* Card Hover Effects */
.expense-card {
    transition: all 0.3s ease;
    border-radius: 10px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.expense-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 25px rgba(0,0,0,0.15);
}

/* Status Badges */
.status-badge {
    font-size: 0.75rem;
    font-weight: 600;
    padding: 0.375rem 0.75rem;
    border-radius: 20px;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

/* Bulk Actions Panel */
.bulk-actions {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    border: 1px solid #dee2e6;
    border-radius: 10px;
    padding: 20px;
    margin-bottom: 25px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.05);
    animation: slideDown 0.3s ease;
}

@keyframes slideDown {
    from {
        opacity: 0;
        transform: translateY(-10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Table Enhancements */
.table-responsive {
    border-radius: 10px;
    overflow: hidden;
    box-shadow: 0 0 20px rgba(0,0,0,0.1);
}

.table thead th {
    background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
    color: white;
    font-weight: 600;
    text-transform: uppercase;
    font-size: 0.85rem;
    letter-spacing: 0.5px;
    border: none;
    padding: 15px 12px;
}

.table tbody tr {
    transition: all 0.2s ease;
}

.table tbody tr:hover {
    background-color: #f8f9fa;
    transform: scale(1.01);
}

.table-actions {
    white-space: nowrap;
}

.table-actions .btn {
    margin: 0 2px;
    transition: all 0.2s ease;
}

.table-actions .btn:hover {
    transform: scale(1.1);
}

/* Danger Zone Styling */
.danger-zone {
    border: 2px solid #dc3545;
    border-radius: 12px;
    background: linear-gradient(135deg, #fff5f5 0%, #ffe6e6 100%);
    box-shadow: 0 4px 15px rgba(220, 53, 69, 0.1);
}

.danger-zone .card-header {
    background: linear-gradient(135deg, #dc3545 0%, #c82333 100%) !important;
    border-bottom: none;
    border-radius: 10px 10px 0 0 !important;
}

/* Advanced Search Modal */
.modal-content {
    border-radius: 15px;
    border: none;
    box-shadow: 0 10px 40px rgba(0,0,0,0.2);
}

.modal-header {
    background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
    color: white;
    border-radius: 15px 15px 0 0;
    border-bottom: none;
}

.modal-header .btn-close {
    filter: invert(1);
}

/* Form Enhancements */
.form-control, .form-select {
    border-radius: 8px;
    border: 2px solid #e9ecef;
    transition: all 0.3s ease;
}

.form-control:focus, .form-select:focus {
    border-color: #007bff;
    box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
    transform: translateY(-1px);
}

/* Button Enhancements */
.btn {
    border-radius: 8px;
    font-weight: 600;
    transition: all 0.3s ease;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    font-size: 0.85rem;
}

.btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(0,0,0,0.2);
}

.btn-lg {
    padding: 12px 30px;
    font-size: 1rem;
}

.btn-sm {
    padding: 6px 15px;
    font-size: 0.75rem;
}

/* Quick Action Buttons */
.quick-action-btn {
    border-radius: 12px;
    transition: all 0.3s ease;
    border: 2px solid transparent;
}

.quick-action-btn:hover {
    transform: translateY(-3px);
    border-color: rgba(255,255,255,0.3);
}

/* DataTables Customization */
.dataTables_wrapper .dataTables_length select,
.dataTables_wrapper .dataTables_filter input {
    border-radius: 6px;
    border: 1px solid #dee2e6;
    padding: 6px 12px;
}

.dataTables_wrapper .dataTables_paginate .paginate_button {
    border-radius: 6px !important;
    margin: 0 2px;
}

.dataTables_wrapper .dataTables_paginate .paginate_button.current {
    background: linear-gradient(135deg, #007bff 0%, #0056b3 100%) !important;
    border-color: #007bff !important;
}

/* Alert Enhancements */
.alert {
    border-radius: 10px;
    border: none;
    font-weight: 500;
    box-shadow: 0 4px 15px rgba(0,0,0,0.1);
}

.alert-success {
    background: linear-gradient(135deg, #d4edda 0%, #c3e6cb 100%);
    color: #155724;
}

.alert-danger {
    background: linear-gradient(135deg, #f8d7da 0%, #f5c6cb 100%);
    color: #721c24;
}

.alert-warning {
    background: linear-gradient(135deg, #fff3cd 0%, #ffeaa7 100%);
    color: #856404;
}

.alert-info {
    background: linear-gradient(135deg, #d1ecf1 0%, #bee5eb 100%);
    color: #0c5460;
}

/* Navigation Enhancements */
.navbar-brand {
    font-weight: 700;
    font-size: 1.5rem;
}

.nav-link {
    font-weight: 500;
    transition: all 0.3s ease;
}

.nav-link:hover {
    transform: translateY(-1px);
}

/* Card Header Enhancements */
.card-header {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    border-bottom: 2px solid #dee2e6;
    font-weight: 600;
}

/* Loading States */
.loading {
    opacity: 0.6;
    pointer-events: none;
}

.loading::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 20px;
    height: 20px;
    margin: -10px 0 0 -10px;
    border: 2px solid #007bff;
    border-radius: 50%;
    border-top-color: transparent;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    to {
        transform: rotate(360deg);
    }
}

/* Responsive Enhancements */
@media (max-width: 768px) {
    .expense-card {
        margin-bottom: 15px;
    }
    
    .table-actions {
        display: flex;
        flex-direction: column;
        gap: 5px;
    }
    
    .bulk-actions {
        text-align: center;
    }
    
    .bulk-actions .row {
        flex-direction: column;
        gap: 15px;
    }
}

/* Print Styles */
@media print {
    .btn, .bulk-actions, .modal, .navbar {
        display: none !important;
    }
    
    .table {
        font-size: 12px;
    }
    
    .card {
        border: 1px solid #000;
        box-shadow: none;
    }
}
