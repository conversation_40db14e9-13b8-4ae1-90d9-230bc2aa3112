<?php
session_start();
require_once '../config/database.php';
require_once '../includes/functions.php';

// Check if user is logged in and is administrator
if (!isset($_SESSION['user_id']) || $_SESSION['role'] !== 'administrator') {
    header('Location: ../login.php');
    exit();
}

$database = new Database();
$db = $database->getConnection();

// Get user info
$stmt = $db->prepare("SELECT * FROM users WHERE id = ?");
$stmt->execute([$_SESSION['user_id']]);
$user = $stmt->fetch();

if (!$user || !$user['is_active']) {
    session_destroy();
    header('Location: ../login.php');
    exit();
}

// Define detailed permissions matrix
$permissions_matrix = [
    'expenses' => [
        'name' => 'Expense Management',
        'permissions' => [
            'view_own' => 'View own expenses',
            'view_all' => 'View all expenses',
            'create' => 'Create new expenses',
            'edit_own' => 'Edit own expenses',
            'edit_all' => 'Edit all expenses',
            'delete' => 'Delete expenses',
            'change_status_to_pending' => 'Change status: Open → Pending',
            'change_status_to_success' => 'Change status: Pending → Success',
            'change_status_revert' => 'Revert status changes',
            'upload_files' => 'Upload files and receipts',
            'download_files' => 'Download files and receipts'
        ]
    ],
    'master_data' => [
        'name' => 'Master Data Management',
        'permissions' => [
            'view_items' => 'View items',
            'manage_items' => 'Add/Edit/Delete items',
            'view_customers' => 'View customers',
            'manage_customers' => 'Add/Edit/Delete customers',
            'view_drivers' => 'View drivers',
            'manage_drivers' => 'Add/Edit/Delete drivers',
            'import_export' => 'Import/Export CSV data'
        ]
    ],
    'users' => [
        'name' => 'User Management',
        'permissions' => [
            'view_users' => 'View user list',
            'create_users' => 'Create new users',
            'edit_users' => 'Edit user details',
            'delete_users' => 'Delete/Deactivate users',
            'manage_roles' => 'Assign user roles',
            'reset_passwords' => 'Reset user passwords'
        ]
    ],
    'reports' => [
        'name' => 'Reports & Analytics',
        'permissions' => [
            'view_reports' => 'View expense reports',
            'export_reports' => 'Export reports to CSV/PDF',
            'view_analytics' => 'View dashboard analytics',
            'view_activity_logs' => 'View system activity logs'
        ]
    ],
    'system' => [
        'name' => 'System Administration',
        'permissions' => [
            'admin_panel' => 'Access admin panel',
            'system_settings' => 'Modify system settings',
            'backup_restore' => 'Backup and restore data',
            'view_logs' => 'View system logs',
            'maintenance_mode' => 'Enable maintenance mode'
        ]
    ]
];

// Define role permissions mapping
$role_permissions = [
    'data_entry' => [
        'expenses' => ['view_own', 'create', 'edit_own', 'upload_files', 'download_files'],
        'master_data' => ['view_items', 'view_customers', 'view_drivers'],
        'users' => [],
        'reports' => [],
        'system' => []
    ],
    'verification' => [
        'expenses' => ['view_own', 'view_all', 'create', 'edit_own', 'edit_all', 'change_status_to_pending', 'upload_files', 'download_files'],
        'master_data' => ['view_items', 'view_customers', 'view_drivers'],
        'users' => ['view_users'],
        'reports' => ['view_reports'],
        'system' => []
    ],
    'reviewer' => [
        'expenses' => ['view_own', 'view_all', 'create', 'edit_own', 'edit_all', 'change_status_to_pending', 'change_status_to_success', 'change_status_revert', 'upload_files', 'download_files'],
        'master_data' => ['view_items', 'view_customers', 'view_drivers', 'manage_items', 'manage_customers', 'manage_drivers'],
        'users' => ['view_users'],
        'reports' => ['view_reports', 'export_reports', 'view_analytics'],
        'system' => []
    ],
    'report_viewer' => [
        'expenses' => ['view_all', 'download_files'],
        'master_data' => ['view_items', 'view_customers', 'view_drivers'],
        'users' => [],
        'reports' => ['view_reports', 'export_reports', 'view_analytics', 'view_activity_logs'],
        'system' => []
    ],
    'administrator' => [
        'expenses' => ['view_own', 'view_all', 'create', 'edit_own', 'edit_all', 'delete', 'change_status_to_pending', 'change_status_to_success', 'change_status_revert', 'upload_files', 'download_files'],
        'master_data' => ['view_items', 'manage_items', 'view_customers', 'manage_customers', 'view_drivers', 'manage_drivers', 'import_export'],
        'users' => ['view_users', 'create_users', 'edit_users', 'delete_users', 'manage_roles', 'reset_passwords'],
        'reports' => ['view_reports', 'export_reports', 'view_analytics', 'view_activity_logs'],
        'system' => ['admin_panel', 'system_settings', 'backup_restore', 'view_logs', 'maintenance_mode']
    ]
];

// Get user statistics by role
$user_stats = [];
foreach (['data_entry', 'verification', 'reviewer', 'report_viewer', 'administrator'] as $role) {
    $stmt = $db->prepare("SELECT COUNT(*) as count FROM users WHERE role = ? AND is_active = 1");
    $stmt->execute([$role]);
    $user_stats[$role] = $stmt->fetch()['count'];
}
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Role Permissions - Admin Panel</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="../assets/css/style.css" rel="stylesheet">
    <style>
        .permission-matrix {
            font-size: 0.9rem;
        }
        .permission-category {
            background: #f8f9fa;
            border-left: 4px solid #007bff;
        }
        .permission-item {
            border-bottom: 1px solid #e9ecef;
        }
        .permission-item:last-child {
            border-bottom: none;
        }
        .role-header {
            writing-mode: vertical-rl;
            text-orientation: mixed;
            min-width: 120px;
            font-weight: bold;
        }
        .permission-check {
            text-align: center;
            vertical-align: middle;
        }
        .permission-check i {
            font-size: 1.2rem;
        }
    </style>
</head>
<body>
    <?php include 'includes/admin_nav.php'; ?>

    <div class="container-fluid mt-4">
        <!-- Page Header -->
        <div class="row mb-4">
            <div class="col">
                <h2><i class="fas fa-shield-alt me-2"></i>Role Permissions Matrix</h2>
                <p class="text-muted">Detailed view of permissions for each user role</p>
            </div>
        </div>

        <!-- Role Statistics -->
        <div class="row mb-4">
            <div class="col-md-3">
                <div class="card border-secondary">
                    <div class="card-body text-center">
                        <i class="fas fa-edit fa-2x text-secondary mb-2"></i>
                        <h5>Data Entry</h5>
                        <h3 class="text-secondary"><?php echo $user_stats['data_entry']; ?></h3>
                        <small class="text-muted">Active Users</small>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card border-info">
                    <div class="card-body text-center">
                        <i class="fas fa-check-circle fa-2x text-info mb-2"></i>
                        <h5>Verification</h5>
                        <h3 class="text-info"><?php echo $user_stats['verification']; ?></h3>
                        <small class="text-muted">Active Users</small>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card border-warning">
                    <div class="card-body text-center">
                        <i class="fas fa-eye fa-2x text-warning mb-2"></i>
                        <h5>Reviewer</h5>
                        <h3 class="text-warning"><?php echo $user_stats['reviewer']; ?></h3>
                        <small class="text-muted">Active Users</small>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card border-danger">
                    <div class="card-body text-center">
                        <i class="fas fa-crown fa-2x text-danger mb-2"></i>
                        <h5>Administrator</h5>
                        <h3 class="text-danger"><?php echo $user_stats['administrator']; ?></h3>
                        <small class="text-muted">Active Users</small>
                    </div>
                </div>
            </div>
        </div>

        <!-- Permissions Matrix -->
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0"><i class="fas fa-table me-2"></i>Permissions Matrix</h5>
            </div>
            <div class="card-body p-0">
                <div class="table-responsive">
                    <table class="table table-bordered permission-matrix mb-0">
                        <thead class="table-dark">
                            <tr>
                                <th style="width: 300px;">Permission</th>
                                <th class="role-header text-center bg-secondary">Data Entry</th>
                                <th class="role-header text-center bg-info">Verification</th>
                                <th class="role-header text-center bg-warning">Reviewer</th>
                                <th class="role-header text-center bg-primary">Report Viewer</th>
                                <th class="role-header text-center bg-danger">Administrator</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($permissions_matrix as $category_key => $category): ?>
                                <tr class="permission-category">
                                    <td colspan="5" class="fw-bold py-2">
                                        <i class="fas fa-<?php
                                            echo $category_key === 'expenses' ? 'file-invoice-dollar' :
                                                ($category_key === 'master_data' ? 'database' :
                                                ($category_key === 'users' ? 'users' :
                                                ($category_key === 'reports' ? 'chart-bar' : 'cogs')));
                                        ?> me-2"></i>
                                        <?php echo $category['name']; ?>
                                    </td>
                                </tr>
                                <?php foreach ($category['permissions'] as $perm_key => $perm_name): ?>
                                    <tr class="permission-item">
                                        <td class="ps-4"><?php echo $perm_name; ?></td>
                                        <?php foreach (['data_entry', 'verification', 'reviewer', 'report_viewer', 'administrator'] as $role): ?>
                                            <td class="permission-check">
                                                <?php if (in_array($perm_key, $role_permissions[$role][$category_key] ?? [])): ?>
                                                    <i class="fas fa-check text-success"></i>
                                                <?php else: ?>
                                                    <i class="fas fa-times text-muted"></i>
                                                <?php endif; ?>
                                            </td>
                                        <?php endforeach; ?>
                                    </tr>
                                <?php endforeach; ?>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>

        <!-- Role Descriptions -->
        <div class="row mt-4">
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0"><i class="fas fa-info-circle me-2"></i>Role Descriptions</h5>
                    </div>
                    <div class="card-body">
                        <div class="mb-3">
                            <h6 class="text-secondary">
                                <i class="fas fa-edit me-1"></i>Data Entry
                            </h6>
                            <p class="small text-muted mb-2">
                                Basic users who can create and manage their own expense records.
                                They can upload files and view master data but cannot change expense status.
                            </p>
                        </div>

                        <div class="mb-3">
                            <h6 class="text-info">
                                <i class="fas fa-check-circle me-1"></i>Verification
                            </h6>
                            <p class="small text-muted mb-2">
                                Can review all expenses and change status from "Open" to "Pending".
                                They have broader view access and can edit expenses in open/pending status.
                            </p>
                        </div>

                        <div class="mb-3">
                            <h6 class="text-warning">
                                <i class="fas fa-eye me-1"></i>Reviewer
                            </h6>
                            <p class="small text-muted mb-2">
                                Senior role that can approve expenses by changing status from "Pending" to "Success".
                                Can manage master data and access reports and analytics.
                            </p>
                        </div>

                        <div class="mb-0">
                            <h6 class="text-danger">
                                <i class="fas fa-crown me-1"></i>Administrator
                            </h6>
                            <p class="small text-muted mb-0">
                                Full system access including user management, system settings,
                                and all data operations. Can delete records and access admin panel.
                            </p>
                        </div>
                    </div>
                </div>
            </div>

            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0"><i class="fas fa-route me-2"></i>Workflow Process</h5>
                    </div>
                    <div class="card-body">
                        <div class="d-flex align-items-center mb-3">
                            <div class="badge bg-secondary me-2">1</div>
                            <div>
                                <strong>Data Entry</strong> creates expense record
                                <br><small class="text-muted">Status: Open</small>
                            </div>
                        </div>

                        <div class="d-flex align-items-center mb-3">
                            <div class="badge bg-info me-2">2</div>
                            <div>
                                <strong>Verification</strong> reviews and validates
                                <br><small class="text-muted">Status: Open → Pending</small>
                            </div>
                        </div>

                        <div class="d-flex align-items-center mb-3">
                            <div class="badge bg-warning me-2">3</div>
                            <div>
                                <strong>Reviewer</strong> final approval
                                <br><small class="text-muted">Status: Pending → Success</small>
                            </div>
                        </div>

                        <div class="d-flex align-items-center">
                            <div class="badge bg-danger me-2">*</div>
                            <div>
                                <strong>Administrator</strong> can override any step
                                <br><small class="text-muted">Full control over all statuses</small>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Legend -->
        <div class="card mt-4">
            <div class="card-header">
                <h5 class="mb-0"><i class="fas fa-question-circle me-2"></i>Legend</h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <div class="d-flex align-items-center mb-2">
                            <i class="fas fa-check text-success me-2"></i>
                            <span>Permission granted</span>
                        </div>
                        <div class="d-flex align-items-center">
                            <i class="fas fa-times text-muted me-2"></i>
                            <span>Permission not granted</span>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <small class="text-muted">
                            <strong>Note:</strong> Higher-level roles inherit permissions from lower-level roles
                            in addition to their specific permissions.
                        </small>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
