<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Accessibility Fix Test - Image Viewer Modal</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        .test-section {
            margin: 2rem 0;
            padding: 1.5rem;
            border: 1px solid #dee2e6;
            border-radius: 0.5rem;
        }
        .accessibility-info {
            background-color: #e7f3ff;
            border: 1px solid #b3d9ff;
            border-radius: 0.5rem;
            padding: 1rem;
            margin: 1rem 0;
        }
        .test-image {
            width: 150px;
            height: 100px;
            object-fit: cover;
            border-radius: 0.5rem;
            cursor: pointer;
            transition: transform 0.2s;
        }
        .test-image:hover {
            transform: scale(1.05);
        }
    </style>
</head>
<body>
    <div class="container mt-4">
        <div class="row">
            <div class="col-12">
                <h1><i class="fas fa-universal-access me-2"></i>Accessibility Fix Test</h1>
                <p class="lead">Testing the fixed image viewer modal for accessibility compliance</p>
                
                <div class="accessibility-info">
                    <h5><i class="fas fa-info-circle me-2"></i>Accessibility Improvements Made:</h5>
                    <ul>
                        <li><strong>ARIA Labels:</strong> Added proper aria-label and aria-labelledby attributes</li>
                        <li><strong>Focus Management:</strong> Modal properly manages focus when shown/hidden</li>
                        <li><strong>Keyboard Navigation:</strong> Enhanced keyboard shortcuts with proper event handling</li>
                        <li><strong>Screen Reader Support:</strong> Added aria-hidden management and descriptive labels</li>
                        <li><strong>Cache Busting:</strong> Added version parameter to prevent caching issues</li>
                    </ul>
                </div>

                <div class="test-section">
                    <h3>Test Image Viewer Modal</h3>
                    <p>Click on any image below to test the accessibility-improved modal:</p>
                    
                    <div class="row">
                        <div class="col-md-3 mb-3">
                            <img src="https://picsum.photos/300/200?random=1" 
                                 class="test-image img-fluid image-thumbnail" 
                                 data-src="https://picsum.photos/800/600?random=1"
                                 alt="Test Image 1">
                            <p class="mt-2 small">Test Image 1</p>
                        </div>
                        <div class="col-md-3 mb-3">
                            <img src="https://picsum.photos/300/200?random=2" 
                                 class="test-image img-fluid image-thumbnail" 
                                 data-src="https://picsum.photos/800/600?random=2"
                                 alt="Test Image 2">
                            <p class="mt-2 small">Test Image 2</p>
                        </div>
                        <div class="col-md-3 mb-3">
                            <img src="https://picsum.photos/300/200?random=3" 
                                 class="test-image img-fluid image-thumbnail" 
                                 data-src="https://picsum.photos/800/600?random=3"
                                 alt="Test Image 3">
                            <p class="mt-2 small">Test Image 3</p>
                        </div>
                        <div class="col-md-3 mb-3">
                            <img src="https://picsum.photos/300/200?random=4" 
                                 class="test-image img-fluid image-thumbnail" 
                                 data-src="https://picsum.photos/800/600?random=4"
                                 alt="Test Image 4">
                            <p class="mt-2 small">Test Image 4</p>
                        </div>
                    </div>
                </div>

                <div class="test-section">
                    <h3>Keyboard Shortcuts</h3>
                    <p>When the modal is open, try these keyboard shortcuts:</p>
                    <div class="row">
                        <div class="col-md-6">
                            <ul>
                                <li><kbd>←</kbd> / <kbd>→</kbd> - Navigate between images</li>
                                <li><kbd>+</kbd> / <kbd>-</kbd> - Zoom in/out</li>
                                <li><kbd>0</kbd> - Reset zoom</li>
                            </ul>
                        </div>
                        <div class="col-md-6">
                            <ul>
                                <li><kbd>L</kbd> / <kbd>R</kbd> - Rotate left/right</li>
                                <li><kbd>D</kbd> - Download image</li>
                                <li><kbd>Esc</kbd> - Close modal</li>
                            </ul>
                        </div>
                    </div>
                </div>

                <div class="test-section">
                    <h3>Accessibility Testing</h3>
                    <p>To test accessibility compliance:</p>
                    <ol>
                        <li><strong>Screen Reader:</strong> Use a screen reader to navigate the modal</li>
                        <li><strong>Keyboard Only:</strong> Navigate using only the keyboard (Tab, Enter, Arrow keys)</li>
                        <li><strong>Focus Indicators:</strong> Check that focus is visible and properly managed</li>
                        <li><strong>ARIA Attributes:</strong> Inspect the DOM to verify proper ARIA attributes</li>
                    </ol>
                    
                    <div class="alert alert-success">
                        <h6><i class="fas fa-check-circle me-2"></i>Fixed Issues:</h6>
                        <ul class="mb-0">
                            <li>✅ Removed conflicting aria-hidden on focused elements</li>
                            <li>✅ Added proper modal event handlers for accessibility</li>
                            <li>✅ Enhanced keyboard navigation with preventDefault</li>
                            <li>✅ Added descriptive ARIA labels to all interactive elements</li>
                            <li>✅ Implemented proper focus management</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script src="assets/js/image-viewer.js"></script>
    
    <script>
        $(document).ready(function() {
            // Initialize Image Viewer
            window.imageViewer = new ImageViewer();
            
            console.log('Accessibility test page loaded. Image viewer initialized.');
            
            // Add some debugging for accessibility testing
            $('#imageViewerModal').on('show.bs.modal', function() {
                console.log('Modal showing - aria-hidden should be removed');
            });
            
            $('#imageViewerModal').on('shown.bs.modal', function() {
                console.log('Modal shown - focus should be managed');
                console.log('Current focus:', document.activeElement);
            });
            
            $('#imageViewerModal').on('hide.bs.modal', function() {
                console.log('Modal hiding');
            });
            
            $('#imageViewerModal').on('hidden.bs.modal', function() {
                console.log('Modal hidden - aria-hidden should be restored');
            });
        });
    </script>
</body>
</html>
