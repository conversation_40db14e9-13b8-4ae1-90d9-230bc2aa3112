<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Production Validation</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        /* Validation styling */
        .receipt-number-input.is-invalid,
        #transfer_no.is-invalid {
            border-color: #dc3545;
            background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 12 12' width='12' height='12' fill='none' stroke='%23dc3545'%3e%3ccircle cx='6' cy='6' r='4.5'/%3e%3cpath d='m5.8 3.6h.4L6 6.5z'/%3e%3ccircle cx='6' cy='8.2' r='.6' fill='%23dc3545' stroke='none'/%3e%3c/svg%3e");
            background-repeat: no-repeat;
            background-position: right calc(0.375em + 0.1875rem) center;
            background-size: calc(0.75em + 0.375rem) calc(0.75em + 0.375rem);
        }
        
        .receipt-number-input.is-valid,
        #transfer_no.is-valid {
            border-color: #198754;
            background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 8 8'%3e%3cpath fill='%23198754' d='m2.3 6.73.94-.94 1.93 1.93 3.53-3.53.94.94L4.16 9.66z'/%3e%3c/svg%3e");
            background-repeat: no-repeat;
            background-position: right calc(0.375em + 0.1875rem) center;
            background-size: calc(0.75em + 0.375rem) calc(0.75em + 0.375rem);
        }
        
        .invalid-feedback {
            display: block;
            width: 100%;
            margin-top: 0.25rem;
            font-size: 0.875em;
            color: #dc3545;
        }
        
        .valid-feedback {
            display: block;
            width: 100%;
            margin-top: 0.25rem;
            font-size: 0.875em;
            color: #198754;
        }
        
        .receipt-number-checking,
        #transfer_no.transfer-number-checking {
            position: relative;
        }
        
        .receipt-number-checking::after,
        #transfer_no.transfer-number-checking::after {
            content: '';
            position: absolute;
            right: 10px;
            top: 50%;
            transform: translateY(-50%);
            width: 16px;
            height: 16px;
            border: 2px solid #f3f3f3;
            border-top: 2px solid #007bff;
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }
        
        @keyframes spin {
            0% { transform: translateY(-50%) rotate(0deg); }
            100% { transform: translateY(-50%) rotate(360deg); }
        }
    </style>
</head>
<body>
    <div class="container mt-4">
        <div class="row">
            <div class="col-md-8 mx-auto">
                <div class="card">
                    <div class="card-header">
                        <h4><i class="fas fa-test-tube me-2"></i>Test Production Validation</h4>
                    </div>
                    <div class="card-body">
                        <div class="alert alert-info">
                            <h6><i class="fas fa-info-circle me-2"></i>Production Test Instructions:</h6>
                            <ul class="mb-0">
                                <li><strong>Transfer Numbers:</strong> Try existing numbers from production database</li>
                                <li><strong>Receipt Numbers:</strong> Try existing numbers from production database</li>
                                <li><strong>New Numbers:</strong> Try "TEST123" or any new number (should show success)</li>
                            </ul>
                        </div>
                        
                        <form id="test-form">
                            <div class="mb-3">
                                <label for="transfer_no" class="form-label">Transfer Number เลขใบโอน <span class="text-danger">*</span></label>
                                <input type="text" class="form-control" id="transfer_no" name="transfer_no" 
                                       placeholder="กรอกเลขใบโอน" required>
                                <div class="invalid-feedback"></div>
                                <div class="valid-feedback"></div>
                                <div class="form-text">Try existing numbers from production database</div>
                            </div>
                            
                            <div class="mb-3">
                                <label for="receipt_number" class="form-label">Receipt Number เลขใบเสร็จ <span class="text-danger">*</span></label>
                                <input type="text" class="form-control receipt-number-input" id="receipt_number" name="receipt_number" 
                                       placeholder="กรอกเลขใบเสร็จ" required>
                                <div class="invalid-feedback"></div>
                                <div class="valid-feedback"></div>
                                <div class="form-text">Try existing numbers from production database</div>
                            </div>
                            
                            <div class="d-flex justify-content-between">
                                <button type="button" class="btn btn-secondary" onclick="clearForm()">
                                    <i class="fas fa-undo me-1"></i>Clear Form
                                </button>
                                <button type="submit" class="btn btn-primary" id="submit-btn">
                                    <i class="fas fa-save me-1"></i>Test Submit
                                </button>
                            </div>
                        </form>
                        
                        <div id="validation-status" class="mt-4"></div>
                        
                        <div class="mt-4">
                            <h6>Quick Test Buttons:</h6>
                            <div class="btn-group mb-2" role="group">
                                <button type="button" class="btn btn-outline-success btn-sm" onclick="testNew()">
                                    Test New Numbers (TEST123)
                                </button>
                                <button type="button" class="btn btn-outline-warning btn-sm" onclick="testEmpty()">
                                    Test Empty
                                </button>
                            </div>
                        </div>
                        
                        <div class="mt-4">
                            <h6>Debug Log:</h6>
                            <div id="debug-log" class="border p-2" style="height: 200px; overflow-y: auto; background-color: #f8f9fa; font-family: monospace; font-size: 12px;"></div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Debug logging function
        function debugLog(message) {
            const timestamp = new Date().toLocaleTimeString();
            const logDiv = $('#debug-log');
            logDiv.append(`[${timestamp}] ${message}\n`);
            logDiv.scrollTop(logDiv[0].scrollHeight);
            console.log(message);
        }
        
        $(document).ready(function() {
            debugLog('Document ready - jQuery version: ' + $.fn.jquery);
            
            // Event listeners for validation
            $('#transfer_no').on('input blur', function() {
                debugLog('Transfer number input event triggered: ' + $(this).val());
                validateTransferNumber();
            });
            
            $('.receipt-number-input').on('input blur', function() {
                debugLog('Receipt number input event triggered: ' + $(this).val());
                validateReceiptNumber();
            });
            
            // Form submission
            $('#test-form').on('submit', function(e) {
                e.preventDefault();
                debugLog('Form submitted');
                
                const hasInvalidTransferInput = $('#transfer_no.is-invalid').length > 0;
                const hasInvalidReceiptInput = $('.receipt-number-input.is-invalid').length > 0;
                const transferNumber = $('#transfer_no').val().trim();
                const receiptNumber = $('#receipt_number').val().trim();
                
                if (hasInvalidTransferInput || hasInvalidReceiptInput || !transferNumber || !receiptNumber) {
                    $('#validation-status').html(`
                        <div class="alert alert-danger">
                            <i class="fas fa-exclamation-triangle me-2"></i>
                            <strong>Form validation failed!</strong><br>
                            Please fix validation errors before submitting.
                        </div>
                    `);
                    debugLog('Form validation failed');
                } else {
                    $('#validation-status').html(`
                        <div class="alert alert-success">
                            <i class="fas fa-check-circle me-2"></i>
                            <strong>Form validation passed!</strong><br>
                            All numbers are valid and available.
                        </div>
                    `);
                    debugLog('Form validation passed');
                }
            });
        });
        
        // Validate transfer number
        function validateTransferNumber() {
            debugLog('validateTransferNumber called');
            const transferInput = $('#transfer_no');
            const transferNumber = transferInput.val().trim();
            
            // Clear previous validation states
            transferInput.removeClass('is-invalid is-valid transfer-number-checking');
            transferInput.siblings('.invalid-feedback, .valid-feedback').text('');
            
            if (transferNumber) {
                // Check if transfer number exists in database
                checkTransferNumberInDatabase(transferNumber, transferInput);
            }
            
            updateSubmitButtonState();
        }
        
        // Validate receipt number
        function validateReceiptNumber() {
            debugLog('validateReceiptNumber called');
            const receiptInput = $('.receipt-number-input');
            const receiptNumber = receiptInput.val().trim();
            
            // Clear previous validation states
            receiptInput.removeClass('is-invalid is-valid receipt-number-checking');
            receiptInput.siblings('.invalid-feedback, .valid-feedback').text('');
            
            if (receiptNumber) {
                // Check if receipt number exists in database
                checkReceiptNumberInDatabase(receiptNumber, receiptInput);
            }
            
            updateSubmitButtonState();
        }
        
        // Check transfer number in database using absolute URL
        function checkTransferNumberInDatabase(transferNumber, inputElement) {
            debugLog('Checking transfer number in database: ' + transferNumber);
            
            // Add loading indicator
            inputElement.addClass('transfer-number-checking');
            
            // Use absolute URL for production
            const apiUrl = 'https://nkslgroup.com/expenses_system/api/check_transfer_number.php';
            debugLog('API URL: ' + apiUrl);
            
            $.ajax({
                url: apiUrl,
                method: 'POST',
                data: { transfer_no: transferNumber },
                dataType: 'json',
                success: function(response) {
                    inputElement.removeClass('transfer-number-checking');
                    debugLog('Transfer API response for ' + transferNumber + ': ' + JSON.stringify(response));
                    
                    if (response.exists) {
                        inputElement.addClass('is-invalid').removeClass('is-valid');
                        inputElement.siblings('.invalid-feedback').text(`Transfer number already exists in expense: ${response.expense_no}`);
                        debugLog('Transfer number exists: ' + transferNumber + ' in expense: ' + response.expense_no);
                    } else {
                        inputElement.removeClass('is-invalid').addClass('is-valid');
                        inputElement.siblings('.invalid-feedback').text('');
                        inputElement.siblings('.valid-feedback').text('Transfer number is available');
                        debugLog('Transfer number available: ' + transferNumber);
                    }
                    updateSubmitButtonState();
                },
                error: function(xhr, status, error) {
                    inputElement.removeClass('transfer-number-checking');
                    debugLog('Error checking transfer number: ' + error + ' (Status: ' + status + ')');
                    
                    // Show error but don't block submission
                    inputElement.removeClass('is-invalid is-valid');
                    inputElement.siblings('.invalid-feedback').text('Unable to verify transfer number. Please check manually.');
                    updateSubmitButtonState();
                }
            });
        }
        
        // Check receipt number in database using absolute URL
        function checkReceiptNumberInDatabase(receiptNumber, inputElement) {
            debugLog('Checking receipt number in database: ' + receiptNumber);
            
            // Add loading indicator
            inputElement.addClass('receipt-number-checking');
            
            // Use absolute URL for production
            const apiUrl = 'https://nkslgroup.com/expenses_system/api/check_receipt_number.php';
            debugLog('API URL: ' + apiUrl);
            
            $.ajax({
                url: apiUrl,
                method: 'POST',
                data: { receipt_number: receiptNumber },
                dataType: 'json',
                success: function(response) {
                    inputElement.removeClass('receipt-number-checking');
                    debugLog('Receipt API response for ' + receiptNumber + ': ' + JSON.stringify(response));
                    
                    if (response.exists) {
                        inputElement.addClass('is-invalid').removeClass('is-valid');
                        inputElement.siblings('.invalid-feedback').text(`Receipt number already exists in expense: ${response.expense_no}`);
                        debugLog('Receipt number exists: ' + receiptNumber + ' in expense: ' + response.expense_no);
                    } else {
                        inputElement.removeClass('is-invalid').addClass('is-valid');
                        inputElement.siblings('.invalid-feedback').text('');
                        inputElement.siblings('.valid-feedback').text('Receipt number is available');
                        debugLog('Receipt number available: ' + receiptNumber);
                    }
                    updateSubmitButtonState();
                },
                error: function(xhr, status, error) {
                    inputElement.removeClass('receipt-number-checking');
                    debugLog('Error checking receipt number: ' + error + ' (Status: ' + status + ')');
                    
                    // Show error but don't block submission
                    inputElement.removeClass('is-invalid is-valid');
                    inputElement.siblings('.invalid-feedback').text('Unable to verify receipt number. Please check manually.');
                    updateSubmitButtonState();
                }
            });
        }
        
        // Update submit button state
        function updateSubmitButtonState() {
            const hasInvalidReceiptInputs = $('.receipt-number-input.is-invalid').length > 0;
            const hasInvalidTransferInput = $('#transfer_no.is-invalid').length > 0;
            const hasInvalidInputs = hasInvalidReceiptInputs || hasInvalidTransferInput;
            const submitButton = $('#submit-btn');
            
            if (hasInvalidInputs) {
                submitButton.prop('disabled', true);
                if (hasInvalidReceiptInputs && hasInvalidTransferInput) {
                    submitButton.html('<i class="fas fa-exclamation-triangle me-1"></i>Fix Receipt & Transfer Numbers');
                } else if (hasInvalidReceiptInputs) {
                    submitButton.html('<i class="fas fa-exclamation-triangle me-1"></i>Fix Receipt Numbers');
                } else {
                    submitButton.html('<i class="fas fa-exclamation-triangle me-1"></i>Fix Transfer Number');
                }
                submitButton.removeClass('btn-primary').addClass('btn-danger');
                debugLog('Submit button disabled due to invalid inputs');
            } else {
                submitButton.prop('disabled', false);
                submitButton.html('<i class="fas fa-save me-1"></i>Test Submit');
                submitButton.removeClass('btn-danger').addClass('btn-primary');
                debugLog('Submit button enabled');
            }
        }
        
        // Test functions
        function testNew() {
            $('#transfer_no').val('TEST123').trigger('input');
            $('#receipt_number').val('TEST123').trigger('input');
            debugLog('Testing with new numbers: TEST123');
        }
        
        function testEmpty() {
            $('#transfer_no').val('').trigger('input');
            $('#receipt_number').val('').trigger('input');
            debugLog('Testing with empty values');
        }
        
        function clearForm() {
            $('#test-form')[0].reset();
            $('#transfer_no, .receipt-number-input').removeClass('is-invalid is-valid transfer-number-checking receipt-number-checking');
            $('.invalid-feedback, .valid-feedback').text('');
            $('#validation-status').empty();
            $('#debug-log').empty();
            updateSubmitButtonState();
            debugLog('Form cleared');
        }
    </script>
</body>
</html>
