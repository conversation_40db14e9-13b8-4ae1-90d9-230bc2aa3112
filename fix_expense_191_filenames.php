<?php
/**
 * Fix Expense 191 Filenames
 */
session_start();
require_once 'config/database.php';

// Check if user is logged in
if (!isset($_SESSION['user_id'])) {
    header('Location: login.php');
    exit();
}

try {
    $database = new Database();
    $db = $database->getConnection();
    
    $expense_id = 191;
    
    echo "<h2>🔧 Fix Expense ID: {$expense_id} Filenames</h2>";
    echo "<hr>";
    
    // Get expense data
    $stmt = $db->prepare("
        SELECT id, exno, transfer_slip_image, verification_slip_image, reviewer_slip_image
        FROM expenses 
        WHERE id = ?
    ");
    $stmt->execute([$expense_id]);
    $expense = $stmt->fetch();
    
    if ($expense) {
        echo "<h3>📋 Current Database Values:</h3>";
        echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
        echo "<tr><th>Field</th><th>Database Value</th><th>File Exists</th><th>Suggested Fix</th><th>Action</th></tr>";
        
        // Check transfer slip
        $db_filename = $expense['transfer_slip_image'];
        if ($db_filename) {
            $file_path = 'uploads/transfer_slips/' . $db_filename;
            $exists = file_exists($file_path);
            
            echo "<tr>";
            echo "<td>Transfer Slip</td>";
            echo "<td>{$db_filename}</td>";
            echo "<td style='color: " . ($exists ? 'green' : 'red') . "'>" . ($exists ? '✅ YES' : '❌ NO') . "</td>";
            
            if (!$exists) {
                // Try to find similar filename
                $pattern = str_replace(['6', '7', '8', '9'], '*', $db_filename);
                $similar_files = glob('uploads/transfer_slips/' . $pattern);
                
                if ($similar_files) {
                    $suggested_file = basename($similar_files[0]);
                    echo "<td style='color: orange;'>{$suggested_file}</td>";
                    echo "<td><a href='?fix_transfer={$suggested_file}'>Fix</a></td>";
                } else {
                    echo "<td>No similar file found</td>";
                    echo "<td>-</td>";
                }
            } else {
                echo "<td>✅ OK</td>";
                echo "<td>-</td>";
            }
            echo "</tr>";
        }
        
        echo "</table>";
        
        // Get receipt data
        echo "<h3>🧾 Receipt Files:</h3>";
        $stmt = $db->prepare("
            SELECT id, receipt_number, receipt_image
            FROM receipt_numbers 
            WHERE expense_id = ?
            ORDER BY id
        ");
        $stmt->execute([$expense_id]);
        $receipts = $stmt->fetchAll();
        
        if ($receipts) {
            echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
            echo "<tr><th>Receipt ID</th><th>Receipt Number</th><th>Database Value</th><th>File Exists</th><th>Suggested Fix</th><th>Action</th></tr>";
            
            foreach ($receipts as $receipt) {
                $db_filename = $receipt['receipt_image'];
                if ($db_filename) {
                    $file_path = 'uploads/receipts/' . $db_filename;
                    $exists = file_exists($file_path);
                    
                    echo "<tr>";
                    echo "<td>{$receipt['id']}</td>";
                    echo "<td>{$receipt['receipt_number']}</td>";
                    echo "<td>{$db_filename}</td>";
                    echo "<td style='color: " . ($exists ? 'green' : 'red') . "'>" . ($exists ? '✅ YES' : '❌ NO') . "</td>";
                    
                    if (!$exists) {
                        // Try to find similar filename
                        $pattern = str_replace(['6', '7', '8', '9'], '*', $db_filename);
                        $similar_files = glob('uploads/receipts/' . $pattern);
                        
                        if ($similar_files) {
                            $suggested_file = basename($similar_files[0]);
                            echo "<td style='color: orange;'>{$suggested_file}</td>";
                            echo "<td><a href='?fix_receipt={$receipt['id']}&new_file={$suggested_file}'>Fix</a></td>";
                        } else {
                            echo "<td>No similar file found</td>";
                            echo "<td>-</td>";
                        }
                    } else {
                        echo "<td>✅ OK</td>";
                        echo "<td>-</td>";
                    }
                    echo "</tr>";
                }
            }
            echo "</table>";
        }
        
        // Get deduction data
        echo "<h3>💰 Deduction Files:</h3>";
        $stmt = $db->prepare("
            SELECT rd.id, rd.deduction_type, rd.deduction_image, rn.receipt_number
            FROM receipt_deductions rd
            JOIN receipt_numbers rn ON rd.receipt_number_id = rn.id
            WHERE rn.expense_id = ?
            ORDER BY rd.id
        ");
        $stmt->execute([$expense_id]);
        $deductions = $stmt->fetchAll();
        
        if ($deductions) {
            echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
            echo "<tr><th>Deduction ID</th><th>Type</th><th>Receipt</th><th>Database Value</th><th>File Exists</th><th>Suggested Fix</th><th>Action</th></tr>";
            
            foreach ($deductions as $deduction) {
                $db_filename = $deduction['deduction_image'];
                if ($db_filename) {
                    $file_path = 'uploads/deductions/' . $db_filename;
                    $exists = file_exists($file_path);
                    
                    echo "<tr>";
                    echo "<td>{$deduction['id']}</td>";
                    echo "<td>{$deduction['deduction_type']}</td>";
                    echo "<td>{$deduction['receipt_number']}</td>";
                    echo "<td>{$db_filename}</td>";
                    echo "<td style='color: " . ($exists ? 'green' : 'red') . "'>" . ($exists ? '✅ YES' : '❌ NO') . "</td>";
                    
                    if (!$exists) {
                        // Try to find similar filename
                        $pattern = str_replace(['6', '7', '8', '9'], '*', $db_filename);
                        $similar_files = glob('uploads/deductions/' . $pattern);
                        
                        if ($similar_files) {
                            $suggested_file = basename($similar_files[0]);
                            echo "<td style='color: orange;'>{$suggested_file}</td>";
                            echo "<td><a href='?fix_deduction={$deduction['id']}&new_file={$suggested_file}'>Fix</a></td>";
                        } else {
                            echo "<td>No similar file found</td>";
                            echo "<td>-</td>";
                        }
                    } else {
                        echo "<td>✅ OK</td>";
                        echo "<td>-</td>";
                    }
                    echo "</tr>";
                }
            }
            echo "</table>";
        }
        
    } else {
        echo "<p>❌ Expense ID {$expense_id} not found</p>";
    }
    
    // Handle fix requests
    if (isset($_GET['fix_transfer'])) {
        $new_filename = $_GET['fix_transfer'];
        $stmt = $db->prepare("UPDATE expenses SET transfer_slip_image = ? WHERE id = ?");
        if ($stmt->execute([$new_filename, $expense_id])) {
            echo "<div style='background: #d4edda; border: 1px solid #c3e6cb; padding: 10px; margin: 10px 0; border-radius: 5px;'>";
            echo "<strong>✅ Success!</strong> Transfer slip filename updated to: {$new_filename}";
            echo "</div>";
        }
    }
    
    if (isset($_GET['fix_receipt']) && isset($_GET['new_file'])) {
        $receipt_id = (int)$_GET['fix_receipt'];
        $new_filename = $_GET['new_file'];
        $stmt = $db->prepare("UPDATE receipt_numbers SET receipt_image = ? WHERE id = ?");
        if ($stmt->execute([$new_filename, $receipt_id])) {
            echo "<div style='background: #d4edda; border: 1px solid #c3e6cb; padding: 10px; margin: 10px 0; border-radius: 5px;'>";
            echo "<strong>✅ Success!</strong> Receipt filename updated to: {$new_filename}";
            echo "</div>";
        }
    }
    
    if (isset($_GET['fix_deduction']) && isset($_GET['new_file'])) {
        $deduction_id = (int)$_GET['fix_deduction'];
        $new_filename = $_GET['new_file'];
        $stmt = $db->prepare("UPDATE receipt_deductions SET deduction_image = ? WHERE id = ?");
        if ($stmt->execute([$new_filename, $deduction_id])) {
            echo "<div style='background: #d4edda; border: 1px solid #c3e6cb; padding: 10px; margin: 10px 0; border-radius: 5px;'>";
            echo "<strong>✅ Success!</strong> Deduction filename updated to: {$new_filename}";
            echo "</div>";
        }
    }
    
} catch (Exception $e) {
    echo "<div style='background: #f8d7da; border: 1px solid #f5c6cb; padding: 10px; margin: 10px 0; border-radius: 5px;'>";
    echo "<strong>❌ Error:</strong> " . $e->getMessage();
    echo "</div>";
}

echo "<hr>";
echo "<p><a href='expenses/view.php?id={$expense_id}'>← Back to Expense View</a></p>";
echo "<p><a href='debug_expense_191.php'>🔍 Debug Expense 191</a></p>";
?>
