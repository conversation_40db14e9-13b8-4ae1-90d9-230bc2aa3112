<?php
session_start();
require_once 'config/database.php';

// Mock user session for testing
$_SESSION['user_id'] = 1;
$_SESSION['username'] = 'test_user';
$_SESSION['role'] = 'admin';

// Test data
$test_data = [
    'driver_id' => 1,
    'customer_id' => 1,
    'item_id' => 1,
    'job_open_date' => date('Y-m-d'),
    'transfer_amount' => 970.00, // Net amount after 3% tax deduction
    'transfer_number' => 'TEST-' . time(),
    'receipt_numbers' => ['REC-001'],
    'receipt_amounts' => [1000.00],
    'receipt_descriptions' => ['Test receipt with deduction'],
    'deductions_data' => json_encode([
        [
            'receipt_number' => 'REC-001',
            'type' => 'tax_withholding',
            'amount' => 30.00,
            'percentage' => 3.00,
            'description' => 'ภาษีหัก ณ ที่จ่าย 3%',
            'isPercentageBased' => true
        ]
    ])
];

echo "<h2>Testing Expense Creation with Deductions</h2>";
echo "<h3>Test Data:</h3>";
echo "<pre>" . print_r($test_data, true) . "</pre>";

try {
    // Use Database class
    $database = new Database();
    $db = $database->getConnection();
    
    // Simulate the expense creation process
    $db->beginTransaction();
    
    // 1. Insert expense
    // Generate sequence and exno
    $sequence = '001';
    $exno = 'TEST-' . date('Ymd') . '-' . time();

    $stmt = $db->prepare("
        INSERT INTO expenses (sequence, exno, driver_id, customer_id, item_id, job_open_date, withdrawal_date, transfer_amount, transfer_no, created_by)
        VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
    ");

    $stmt->execute([
        $sequence,
        $exno,
        $test_data['driver_id'],
        $test_data['customer_id'],
        $test_data['item_id'],
        $test_data['job_open_date'],
        $test_data['job_open_date'], // withdrawal_date = job_open_date
        $test_data['transfer_amount'],
        $test_data['transfer_number'],
        $_SESSION['user_id']
    ]);
    
    $expense_id = $db->lastInsertId();
    echo "<h3>✅ Expense Created: ID = $expense_id</h3>";
    
    // 2. Insert receipt
    $stmt = $db->prepare("
        INSERT INTO receipt_numbers (expense_id, receipt_number, amount, gross_amount, has_deductions, net_amount_calculated, description, created_by)
        VALUES (?, ?, ?, ?, ?, ?, ?, ?)
    ");
    
    $stmt->execute([
        $expense_id,
        $test_data['receipt_numbers'][0],
        $test_data['receipt_amounts'][0],
        $test_data['receipt_amounts'][0], // gross_amount
        0, // has_deductions = false initially
        $test_data['receipt_amounts'][0], // net_amount_calculated
        $test_data['receipt_descriptions'][0],
        $_SESSION['user_id']
    ]);
    
    $receipt_id = $db->lastInsertId();
    echo "<h3>✅ Receipt Created: ID = $receipt_id</h3>";
    
    // 3. Add deduction
    require_once 'includes/ReceiptDeductionManager.php';
    $deductionManager = new ReceiptDeductionManager($db);
    
    $deductions_data = json_decode($test_data['deductions_data'], true);
    foreach ($deductions_data as $deduction) {
        $deduction_data = [
            'deduction_type' => $deduction['type'],
            'amount' => $deduction['amount'],
            'percentage' => $deduction['percentage'],
            'description' => $deduction['description'],
            'is_percentage_based' => $deduction['isPercentageBased']
        ];
        
        $result = $deductionManager->addDeduction($receipt_id, $deduction_data, $_SESSION['user_id']);
        echo "<h3>✅ Deduction Added: ID = " . ($result['deduction_id'] ?? 'Unknown') . "</h3>";
    }

    if ($db->inTransaction()) {
        $db->commit();
    }
    
    // 4. Verify results
    echo "<h3>📊 Verification Results:</h3>";
    
    // Check receipt summary
    $stmt = $db->prepare("SELECT * FROM receipt_summary WHERE expense_id = ?");
    $stmt->execute([$expense_id]);
    $summary = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if ($summary) {
        echo "<h4>Receipt Summary:</h4>";
        echo "<ul>";
        echo "<li>Gross Amount: " . number_format($summary['gross_amount'], 2) . " บาท</li>";
        echo "<li>Total Deductions: " . number_format($summary['total_deductions'], 2) . " บาท</li>";
        echo "<li>Net Amount: " . number_format($summary['net_amount_calculated'], 2) . " บาท</li>";
        echo "<li>Has Deductions: " . ($summary['has_deductions'] ? 'Yes' : 'No') . "</li>";
        echo "</ul>";
        
        // Check if net amount matches transfer amount
        $net_amount = floatval($summary['net_amount_calculated']);
        $transfer_amount = floatval($test_data['transfer_amount']);
        
        if (abs($net_amount - $transfer_amount) < 0.01) {
            echo "<h4 style='color: green;'>✅ SUCCESS: Net amount matches transfer amount!</h4>";
        } else {
            echo "<h4 style='color: red;'>❌ ERROR: Net amount ($net_amount) does not match transfer amount ($transfer_amount)</h4>";
        }
    }
    
    // Check deductions
    $stmt = $db->prepare("SELECT * FROM receipt_deductions WHERE receipt_number_id = ?");
    $stmt->execute([$receipt_id]);
    $deductions = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    echo "<h4>Deductions:</h4>";
    foreach ($deductions as $deduction) {
        echo "<ul>";
        echo "<li>Type: " . $deduction['deduction_type'] . "</li>";
        echo "<li>Amount: " . number_format($deduction['amount'], 2) . " บาท</li>";
        echo "<li>Percentage: " . ($deduction['percentage'] ? $deduction['percentage'] . '%' : 'N/A') . "</li>";
        echo "<li>Description: " . $deduction['description'] . "</li>";
        echo "<li>Is Percentage Based: " . ($deduction['is_percentage_based'] ? 'Yes' : 'No') . "</li>";
        echo "</ul>";
    }
    
    echo "<h3 style='color: green;'>🎉 Test Completed Successfully!</h3>";
    echo "<p><a href='expenses/view.php?id=$expense_id'>View Created Expense</a></p>";
    
} catch (Exception $e) {
    if ($db->inTransaction()) {
        $db->rollback();
    }
    echo "<h3 style='color: red;'>❌ Error: " . $e->getMessage() . "</h3>";
    echo "<pre>" . $e->getTraceAsString() . "</pre>";
}
?>
