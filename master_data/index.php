<?php
session_start();
require_once '../config/database.php';
require_once '../includes/functions.php';

// Check if user is logged in
if (!isset($_SESSION['user_id'])) {
    header('Location: ../login.php');
    exit();
}

// Check if user has permission to view master data
$user_role = $_SESSION['role'];
$allowed_roles = ['data_entry', 'verification', 'reviewer', 'report_viewer', 'administrator'];

if (!in_array($user_role, $allowed_roles)) {
    header('Location: ../dashboard.php?error=access_denied');
    exit();
}

$database = new Database();
$db = $database->getConnection();

// Get user info
$stmt = $db->prepare("SELECT * FROM users WHERE id = ?");
$stmt->execute([$_SESSION['user_id']]);
$user = $stmt->fetch();

if (!$user || !$user['is_active']) {
    session_destroy();
    header('Location: ../login.php');
    exit();
}

// Get statistics
$stats = [];

// Items count
$stmt = $db->query("SELECT COUNT(*) as count FROM items WHERE is_active = 1");
$stats['items'] = $stmt->fetch()['count'];

// Customers count
$stmt = $db->query("SELECT COUNT(*) as count FROM customers WHERE is_active = 1");
$stats['customers'] = $stmt->fetch()['count'];

// Drivers count
$stmt = $db->query("SELECT COUNT(*) as count FROM drivers WHERE is_active = 1");
$stats['drivers'] = $stmt->fetch()['count'];

// Check if user can manage master data
$can_manage = in_array($user_role, ['administrator']);
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Master Data - Expenses System</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="../assets/css/style.css" rel="stylesheet">
    <style>
        .master-data-header {
            background: linear-gradient(135deg, #6f42c1 0%, #e83e8c 100%);
            color: white;
            padding: 3rem 0;
            margin-bottom: 2rem;
        }
        .data-card {
            transition: transform 0.3s, box-shadow 0.3s;
            border: none;
            border-radius: 15px;
            overflow: hidden;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
        }
        .data-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 8px 25px rgba(0,0,0,0.15);
        }
        .data-card .card-header {
            padding: 1.5rem;
            border: none;
        }
        .data-card .card-body {
            padding: 2rem;
        }
        .icon-large {
            font-size: 3rem;
            margin-bottom: 1rem;
        }
        .role-info {
            background: rgba(255,255,255,0.1);
            border-radius: 10px;
            padding: 1rem;
            backdrop-filter: blur(10px);
        }
        .access-level {
            font-size: 0.9rem;
            opacity: 0.9;
        }
    </style>
</head>
<body class="bg-light">
    <!-- Navigation -->
    <?php include '../includes/navbar.php'; ?>

    <!-- Header -->
    <div class="master-data-header">
        <div class="container">
            <div class="row align-items-center">
                <div class="col-md-8">
                    <h1 class="mb-3">
                        <i class="fas fa-database me-3"></i>Master Data Center
                    </h1>
                    <p class="mb-0 fs-5 opacity-75">
                        Centralized access to system master data
                    </p>
                </div>
                <div class="col-md-4">
                    <div class="role-info text-center">
                        <h6 class="mb-2">
                            <i class="fas fa-user-shield me-2"></i>Your Access Level
                        </h6>
                        <span class="badge bg-light text-dark fs-6 px-3 py-2">
                            <?php echo ucfirst($user_role); ?> Role
                        </span>
                        <div class="access-level mt-2">
                            <?php if ($can_manage): ?>
                                <i class="fas fa-edit me-1"></i>View & Manage
                            <?php else: ?>
                                <i class="fas fa-eye me-1"></i>View Only
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="container">
        <!-- Master Data Cards -->
        <div class="row">
            <!-- Items -->
            <div class="col-md-4 mb-4">
                <div class="card data-card h-100">
                    <div class="card-header bg-primary text-white text-center">
                        <i class="fas fa-boxes icon-large"></i>
                        <h4 class="mb-0">Items</h4>
                    </div>
                    <div class="card-body text-center">
                        <h2 class="text-primary mb-3"><?php echo number_format($stats['items']); ?></h2>
                        <p class="text-muted mb-4">
                            View all system items including products, services, and materials used in expenses.
                        </p>
                        <a href="items.php" class="btn btn-primary btn-lg">
                            <i class="fas fa-eye me-2"></i>View Items
                        </a>
                        <?php if ($can_manage): ?>
                            <a href="../admin/items.php" class="btn btn-outline-primary mt-2">
                                <i class="fas fa-cog me-2"></i>Manage
                            </a>
                        <?php endif; ?>
                    </div>
                </div>
            </div>

            <!-- Customers -->
            <div class="col-md-4 mb-4">
                <div class="card data-card h-100">
                    <div class="card-header bg-success text-white text-center">
                        <i class="fas fa-building icon-large"></i>
                        <h4 class="mb-0">Customers</h4>
                    </div>
                    <div class="card-body text-center">
                        <h2 class="text-success mb-3"><?php echo number_format($stats['customers']); ?></h2>
                        <p class="text-muted mb-4">
                            Browse customer information including contact details, addresses, and business relationships.
                        </p>
                        <a href="customers.php" class="btn btn-success btn-lg">
                            <i class="fas fa-eye me-2"></i>View Customers
                        </a>
                        <?php if ($can_manage): ?>
                            <a href="../admin/customers.php" class="btn btn-outline-success mt-2">
                                <i class="fas fa-cog me-2"></i>Manage
                            </a>
                        <?php endif; ?>
                    </div>
                </div>
            </div>

            <!-- Drivers -->
            <div class="col-md-4 mb-4">
                <div class="card data-card h-100">
                    <div class="card-header bg-warning text-dark text-center">
                        <i class="fas fa-truck icon-large"></i>
                        <h4 class="mb-0">Drivers</h4>
                    </div>
                    <div class="card-body text-center">
                        <h2 class="text-warning mb-3"><?php echo number_format($stats['drivers']); ?></h2>
                        <p class="text-muted mb-4">
                            Access driver profiles with license information, vehicle details, and payment accounts.
                        </p>
                        <a href="drivers.php" class="btn btn-warning btn-lg">
                            <i class="fas fa-eye me-2"></i>View Drivers
                        </a>
                        <?php if ($can_manage): ?>
                            <a href="../admin/drivers.php" class="btn btn-outline-warning mt-2">
                                <i class="fas fa-cog me-2"></i>Manage
                            </a>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>

        <!-- Quick Actions -->
        <div class="row mt-4">
            <div class="col-12">
                <div class="card border-0 shadow-sm">
                    <div class="card-header bg-light">
                        <h5 class="mb-0">
                            <i class="fas fa-bolt me-2"></i>Quick Actions
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-3 mb-3">
                                <a href="../expenses/create.php" class="btn btn-outline-primary w-100">
                                    <i class="fas fa-plus me-2"></i>Create Expense
                                </a>
                            </div>
                            <div class="col-md-3 mb-3">
                                <a href="../expenses/list.php" class="btn btn-outline-info w-100">
                                    <i class="fas fa-list me-2"></i>View Expenses
                                </a>
                            </div>
                            <div class="col-md-3 mb-3">
                                <a href="../reports/index.php" class="btn btn-outline-success w-100">
                                    <i class="fas fa-chart-bar me-2"></i>Reports
                                </a>
                            </div>
                            <div class="col-md-3 mb-3">
                                <a href="../dashboard.php" class="btn btn-outline-secondary w-100">
                                    <i class="fas fa-home me-2"></i>Dashboard
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Access Information -->
        <div class="alert alert-info mt-4">
            <div class="row align-items-center">
                <div class="col-md-8">
                    <h6 class="mb-1">
                        <i class="fas fa-info-circle me-2"></i>Master Data Access Information
                    </h6>
                    <p class="mb-0">
                        As a <strong><?php echo ucfirst($user_role); ?></strong>, you have 
                        <strong><?php echo $can_manage ? 'view and manage' : 'view-only'; ?></strong> access to master data. 
                        This information is essential for creating and managing expenses in the system.
                    </p>
                </div>
                <div class="col-md-4 text-md-end">
                    <div class="d-flex flex-column align-items-md-end">
                        <span class="badge bg-info mb-1">
                            <i class="fas fa-shield-alt me-1"></i><?php echo ucfirst($user_role); ?> Role
                        </span>
                        <small class="text-muted">
                            <?php if ($can_manage): ?>
                                <i class="fas fa-check text-success me-1"></i>Management Access
                            <?php else: ?>
                                <i class="fas fa-eye text-info me-1"></i>Read-Only Access
                            <?php endif; ?>
                        </small>
                    </div>
                </div>
            </div>
        </div>

        <!-- Role-specific Information -->
        <?php if ($user_role === 'data_entry'): ?>
            <div class="alert alert-secondary mt-3">
                <h6><i class="fas fa-user me-2"></i>Data Entry Role Information</h6>
                <p class="mb-0">You can view master data to help you create accurate expense records. Use this information to select the correct items, customers, and drivers for your expenses.</p>
            </div>
        <?php elseif ($user_role === 'verification'): ?>
            <div class="alert alert-info mt-3">
                <h6><i class="fas fa-user-check me-2"></i>Verification Officer Role Information</h6>
                <p class="mb-0">Use master data to verify the accuracy of expense records. Cross-reference submitted information with the official master data records.</p>
            </div>
        <?php elseif ($user_role === 'reviewer'): ?>
            <div class="alert alert-warning mt-3">
                <h6><i class="fas fa-user-tie me-2"></i>Reviewer Role Information</h6>
                <p class="mb-0">You have management access to master data. You can view and modify items, customers, and drivers through the admin panel to maintain data accuracy.</p>
            </div>
        <?php elseif ($user_role === 'report_viewer'): ?>
            <div class="alert alert-primary mt-3">
                <h6><i class="fas fa-chart-line me-2"></i>Report Viewer Role Information</h6>
                <p class="mb-0">Master data provides context for your reports and analytics. Use this information to understand the relationships between expenses and master data entities.</p>
            </div>
        <?php elseif ($user_role === 'administrator'): ?>
            <div class="alert alert-danger mt-3">
                <h6><i class="fas fa-user-shield me-2"></i>Administrator Role Information</h6>
                <p class="mb-0">You have full access to all master data. Use the admin panel to manage and maintain the integrity of items, customers, and drivers data.</p>
            </div>
        <?php endif; ?>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
