<?php
session_start();
require_once '../config/database.php';
require_once '../includes/functions.php';

// Check if user is logged in
if (!isset($_SESSION['user_id'])) {
    header('Location: ../login.php');
    exit();
}

// Check if user has permission to view master data
$user_role = $_SESSION['role'];
$allowed_roles = ['data_entry', 'verification', 'reviewer', 'report_viewer', 'administrator'];

if (!in_array($user_role, $allowed_roles)) {
    header('Location: ../dashboard.php?error=access_denied');
    exit();
}

$database = new Database();
$db = $database->getConnection();

// Get user info
$stmt = $db->prepare("SELECT * FROM users WHERE id = ?");
$stmt->execute([$_SESSION['user_id']]);
$user = $stmt->fetch();

if (!$user || !$user['is_active']) {
    session_destroy();
    header('Location: ../login.php');
    exit();
}

// Get search parameters
$search = $_GET['search'] ?? '';
$page = max(1, (int)($_GET['page'] ?? 1));
$per_page = 20;

// Build query
$where_conditions = ['d.is_active = 1'];
$params = [];

if (!empty($search)) {
    $where_conditions[] = '(d.name LIKE ? OR d.license_number LIKE ? OR d.phone LIKE ? OR d.vehicle_plate LIKE ?)';
    $params[] = '%' . $search . '%';
    $params[] = '%' . $search . '%';
    $params[] = '%' . $search . '%';
    $params[] = '%' . $search . '%';
}

$where_clause = implode(' AND ', $where_conditions);

// Get total count
$count_sql = "SELECT COUNT(*) as total FROM drivers d WHERE $where_clause";
$stmt = $db->prepare($count_sql);
$stmt->execute($params);
$total_drivers = $stmt->fetch()['total'];

// Calculate pagination
$total_pages = ceil($total_drivers / $per_page);
$offset = ($page - 1) * $per_page;

// Get drivers
$sql = "
    SELECT d.*, 
           u.full_name as created_by_name,
           d.created_at
    FROM drivers d
    LEFT JOIN users u ON d.created_by = u.id
    WHERE $where_clause
    ORDER BY d.name ASC
    LIMIT $per_page OFFSET $offset
";

$stmt = $db->prepare($sql);
$stmt->execute($params);
$drivers = $stmt->fetchAll();

// Check if user can manage drivers (reviewer and administrator only)
$can_manage = in_array($user_role, ['reviewer', 'administrator']);
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Drivers - Master Data</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="../assets/css/style.css" rel="stylesheet">
    <style>
        .master-data-header {
            background: linear-gradient(135deg, #fd7e14 0%, #ffc107 100%);
            color: white;
            padding: 2rem 0;
            margin-bottom: 2rem;
        }
        .driver-card {
            transition: transform 0.2s, box-shadow 0.2s;
            border: none;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .driver-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.15);
        }
        .search-box {
            background: white;
            border-radius: 10px;
            padding: 1.5rem;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 2rem;
        }
        .role-badge {
            font-size: 0.75rem;
            padding: 0.25rem 0.5rem;
        }
        .stats-card {
            background: linear-gradient(45deg, #f8f9fa, #e9ecef);
            border: none;
            border-radius: 10px;
        }
        .driver-info {
            font-size: 0.9rem;
        }
        .driver-info i {
            width: 16px;
            text-align: center;
        }
        .vehicle-plate {
            background: #343a40;
            color: white;
            padding: 0.25rem 0.5rem;
            border-radius: 4px;
            font-family: monospace;
            font-weight: bold;
        }
    </style>
</head>
<body class="bg-light">
    <!-- Navigation -->
    <?php include '../includes/navbar.php'; ?>

    <!-- Header -->
    <div class="master-data-header">
        <div class="container">
            <div class="row align-items-center">
                <div class="col-md-8">
                    <h1 class="mb-2">
                        <i class="fas fa-truck me-3"></i>Drivers Master Data
                    </h1>
                    <p class="mb-0 opacity-75">View and manage driver information</p>
                </div>
                <div class="col-md-4 text-md-end">
                    <span class="badge bg-light text-dark role-badge">
                        <i class="fas fa-user me-1"></i><?php echo ucfirst($user_role); ?> Access
                    </span>
                </div>
            </div>
        </div>
    </div>

    <div class="container">
        <!-- Statistics -->
        <div class="row mb-4">
            <div class="col-md-4">
                <div class="card stats-card">
                    <div class="card-body text-center">
                        <i class="fas fa-truck fa-2x text-warning mb-2"></i>
                        <h4 class="mb-1"><?php echo number_format($total_drivers); ?></h4>
                        <small class="text-muted">Total Drivers</small>
                    </div>
                </div>
            </div>
            <div class="col-md-4">
                <div class="card stats-card">
                    <div class="card-body text-center">
                        <i class="fas fa-eye fa-2x text-info mb-2"></i>
                        <h4 class="mb-1">View Only</h4>
                        <small class="text-muted">Your Access Level</small>
                    </div>
                </div>
            </div>
            <div class="col-md-4">
                <div class="card stats-card">
                    <div class="card-body text-center">
                        <i class="fas fa-search fa-2x text-danger mb-2"></i>
                        <h4 class="mb-1">Searchable</h4>
                        <small class="text-muted">Find Drivers Easily</small>
                    </div>
                </div>
            </div>
        </div>

        <!-- Search Box -->
        <div class="search-box">
            <form method="GET" class="row g-3">
                <div class="col-md-8">
                    <div class="input-group">
                        <span class="input-group-text">
                            <i class="fas fa-search"></i>
                        </span>
                        <input type="text" class="form-control" name="search" 
                               value="<?php echo htmlspecialchars($search); ?>" 
                               placeholder="Search drivers by name, license, phone, or vehicle plate...">
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="d-grid gap-2 d-md-flex">
                        <button type="submit" class="btn btn-warning">
                            <i class="fas fa-search me-1"></i>Search
                        </button>
                        <?php if (!empty($search)): ?>
                            <a href="drivers.php" class="btn btn-outline-secondary">
                                <i class="fas fa-times me-1"></i>Clear
                            </a>
                        <?php endif; ?>
                    </div>
                </div>
            </form>
        </div>

        <!-- Drivers Grid -->
        <?php if (empty($drivers)): ?>
            <div class="text-center py-5">
                <i class="fas fa-truck fa-4x text-muted mb-3"></i>
                <h4 class="text-muted">No Drivers Found</h4>
                <p class="text-muted">
                    <?php if (!empty($search)): ?>
                        No drivers match your search criteria. Try different keywords.
                    <?php else: ?>
                        No drivers are currently available in the system.
                    <?php endif; ?>
                </p>
            </div>
        <?php else: ?>
            <div class="row">
                <?php foreach ($drivers as $driver): ?>
                    <div class="col-md-6 col-lg-4 mb-4">
                        <div class="card driver-card h-100">
                            <div class="card-header bg-warning text-dark">
                                <h6 class="mb-0">
                                    <i class="fas fa-user-tie me-2"></i>
                                    <?php echo htmlspecialchars($driver['name']); ?>
                                </h6>
                            </div>
                            <div class="card-body">
                                <div class="driver-info">
                                    <?php if (!empty($driver['license_number'])): ?>
                                        <div class="mb-2">
                                            <i class="fas fa-id-card text-muted me-2"></i>
                                            <strong>License:</strong> <?php echo htmlspecialchars($driver['license_number']); ?>
                                        </div>
                                    <?php endif; ?>
                                    
                                    <?php if (!empty($driver['phone'])): ?>
                                        <div class="mb-2">
                                            <i class="fas fa-phone text-muted me-2"></i>
                                            <strong>Phone:</strong> 
                                            <a href="tel:<?php echo htmlspecialchars($driver['phone']); ?>" class="text-decoration-none">
                                                <?php echo htmlspecialchars($driver['phone']); ?>
                                            </a>
                                        </div>
                                    <?php endif; ?>
                                    
                                    <?php if (!empty($driver['vehicle_plate'])): ?>
                                        <div class="mb-2">
                                            <i class="fas fa-car text-muted me-2"></i>
                                            <strong>Vehicle:</strong><br>
                                            <span class="vehicle-plate"><?php echo htmlspecialchars($driver['vehicle_plate']); ?></span>
                                        </div>
                                    <?php endif; ?>
                                    
                                    <?php if (!empty($driver['payment_account_no'])): ?>
                                        <div class="mb-2">
                                            <i class="fas fa-credit-card text-muted me-2"></i>
                                            <strong>Account:</strong><br>
                                            <code class="text-dark"><?php echo htmlspecialchars($driver['payment_account_no']); ?></code>
                                        </div>
                                    <?php endif; ?>
                                </div>
                            </div>
                            <div class="card-footer bg-light">
                                <div class="row text-center">
                                    <div class="col-6">
                                        <small class="text-muted">Created by:</small><br>
                                        <strong><?php echo htmlspecialchars($driver['created_by_name'] ?? 'Unknown'); ?></strong>
                                    </div>
                                    <div class="col-6">
                                        <small class="text-muted">Date:</small><br>
                                        <strong><?php echo date('M j, Y', strtotime($driver['created_at'])); ?></strong>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                <?php endforeach; ?>
            </div>

            <!-- Pagination -->
            <?php if ($total_pages > 1): ?>
                <nav aria-label="Drivers pagination" class="mt-4">
                    <ul class="pagination justify-content-center">
                        <?php if ($page > 1): ?>
                            <li class="page-item">
                                <a class="page-link" href="?page=<?php echo $page - 1; ?><?php echo !empty($search) ? '&search=' . urlencode($search) : ''; ?>">
                                    <i class="fas fa-chevron-left"></i> Previous
                                </a>
                            </li>
                        <?php endif; ?>

                        <?php for ($i = max(1, $page - 2); $i <= min($total_pages, $page + 2); $i++): ?>
                            <li class="page-item <?php echo $i == $page ? 'active' : ''; ?>">
                                <a class="page-link" href="?page=<?php echo $i; ?><?php echo !empty($search) ? '&search=' . urlencode($search) : ''; ?>">
                                    <?php echo $i; ?>
                                </a>
                            </li>
                        <?php endfor; ?>

                        <?php if ($page < $total_pages): ?>
                            <li class="page-item">
                                <a class="page-link" href="?page=<?php echo $page + 1; ?><?php echo !empty($search) ? '&search=' . urlencode($search) : ''; ?>">
                                    Next <i class="fas fa-chevron-right"></i>
                                </a>
                            </li>
                        <?php endif; ?>
                    </ul>
                </nav>
            <?php endif; ?>
        <?php endif; ?>

        <!-- Access Information -->
        <div class="alert alert-warning mt-4">
            <div class="row align-items-center">
                <div class="col-md-8">
                    <h6 class="mb-1">
                        <i class="fas fa-info-circle me-2"></i>Access Information
                    </h6>
                    <p class="mb-0">
                        You have <strong>view-only</strong> access to drivers master data. 
                        <?php if ($can_manage): ?>
                            You can manage drivers through the <a href="../admin/drivers.php" class="alert-link">Admin Panel</a>.
                        <?php else: ?>
                            Contact your administrator to add or modify drivers.
                        <?php endif; ?>
                    </p>
                </div>
                <div class="col-md-4 text-md-end">
                    <span class="badge bg-warning text-dark">
                        <i class="fas fa-shield-alt me-1"></i><?php echo ucfirst($user_role); ?> Role
                    </span>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
