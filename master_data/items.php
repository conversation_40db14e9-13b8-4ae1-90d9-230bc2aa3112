<?php
session_start();
require_once '../config/database.php';
require_once '../includes/functions.php';

// Check if user is logged in
if (!isset($_SESSION['user_id'])) {
    header('Location: ../login.php');
    exit();
}

// Check if user has permission to view master data
$user_role = $_SESSION['role'];
$allowed_roles = ['data_entry', 'verification', 'reviewer', 'report_viewer', 'administrator'];

if (!in_array($user_role, $allowed_roles)) {
    header('Location: ../dashboard.php?error=access_denied');
    exit();
}

$database = new Database();
$db = $database->getConnection();

// Get user info
$stmt = $db->prepare("SELECT * FROM users WHERE id = ?");
$stmt->execute([$_SESSION['user_id']]);
$user = $stmt->fetch();

if (!$user || !$user['is_active']) {
    session_destroy();
    header('Location: ../login.php');
    exit();
}

// Get search parameters
$search = $_GET['search'] ?? '';
$page = max(1, (int)($_GET['page'] ?? 1));
$per_page = 20;

// Build query
$where_conditions = ['i.is_active = 1'];
$params = [];

if (!empty($search)) {
    $where_conditions[] = '(i.name LIKE ? OR i.description LIKE ?)';
    $params[] = '%' . $search . '%';
    $params[] = '%' . $search . '%';
}

$where_clause = implode(' AND ', $where_conditions);

// Get total count
$count_sql = "SELECT COUNT(*) as total FROM items i WHERE $where_clause";
$stmt = $db->prepare($count_sql);
$stmt->execute($params);
$total_items = $stmt->fetch()['total'];

// Calculate pagination
$total_pages = ceil($total_items / $per_page);
$offset = ($page - 1) * $per_page;

// Get items
$sql = "
    SELECT i.*, 
           u.full_name as created_by_name,
           i.created_at
    FROM items i
    LEFT JOIN users u ON i.created_by = u.id
    WHERE $where_clause
    ORDER BY i.name ASC
    LIMIT $per_page OFFSET $offset
";

$stmt = $db->prepare($sql);
$stmt->execute($params);
$items = $stmt->fetchAll();

// Check if user can manage items (reviewer and administrator only)
$can_manage = in_array($user_role, ['reviewer', 'administrator']);
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Items - Master Data</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="../assets/css/style.css" rel="stylesheet">
    <style>
        .master-data-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 2rem 0;
            margin-bottom: 2rem;
        }
        .item-card {
            transition: transform 0.2s, box-shadow 0.2s;
            border: none;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .item-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.15);
        }
        .search-box {
            background: white;
            border-radius: 10px;
            padding: 1.5rem;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 2rem;
        }
        .role-badge {
            font-size: 0.75rem;
            padding: 0.25rem 0.5rem;
        }
        .stats-card {
            background: linear-gradient(45deg, #f8f9fa, #e9ecef);
            border: none;
            border-radius: 10px;
        }
    </style>
</head>
<body class="bg-light">
    <!-- Navigation -->
    <?php include '../includes/navbar.php'; ?>

    <!-- Header -->
    <div class="master-data-header">
        <div class="container">
            <div class="row align-items-center">
                <div class="col-md-8">
                    <h1 class="mb-2">
                        <i class="fas fa-boxes me-3"></i>Items Master Data
                    </h1>
                    <p class="mb-0 opacity-75">View and manage system items</p>
                </div>
                <div class="col-md-4 text-md-end">
                    <span class="badge bg-light text-dark role-badge">
                        <i class="fas fa-user me-1"></i><?php echo ucfirst($user_role); ?> Access
                    </span>
                </div>
            </div>
        </div>
    </div>

    <div class="container">
        <!-- Statistics -->
        <div class="row mb-4">
            <div class="col-md-4">
                <div class="card stats-card">
                    <div class="card-body text-center">
                        <i class="fas fa-boxes fa-2x text-primary mb-2"></i>
                        <h4 class="mb-1"><?php echo number_format($total_items); ?></h4>
                        <small class="text-muted">Total Items</small>
                    </div>
                </div>
            </div>
            <div class="col-md-4">
                <div class="card stats-card">
                    <div class="card-body text-center">
                        <i class="fas fa-eye fa-2x text-info mb-2"></i>
                        <h4 class="mb-1">View Only</h4>
                        <small class="text-muted">Your Access Level</small>
                    </div>
                </div>
            </div>
            <div class="col-md-4">
                <div class="card stats-card">
                    <div class="card-body text-center">
                        <i class="fas fa-search fa-2x text-success mb-2"></i>
                        <h4 class="mb-1">Searchable</h4>
                        <small class="text-muted">Find Items Easily</small>
                    </div>
                </div>
            </div>
        </div>

        <!-- Search Box -->
        <div class="search-box">
            <form method="GET" class="row g-3">
                <div class="col-md-8">
                    <div class="input-group">
                        <span class="input-group-text">
                            <i class="fas fa-search"></i>
                        </span>
                        <input type="text" class="form-control" name="search" 
                               value="<?php echo htmlspecialchars($search); ?>" 
                               placeholder="Search items by name or description...">
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="d-grid gap-2 d-md-flex">
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-search me-1"></i>Search
                        </button>
                        <?php if (!empty($search)): ?>
                            <a href="items.php" class="btn btn-outline-secondary">
                                <i class="fas fa-times me-1"></i>Clear
                            </a>
                        <?php endif; ?>
                    </div>
                </div>
            </form>
        </div>

        <!-- Items Grid -->
        <?php if (empty($items)): ?>
            <div class="text-center py-5">
                <i class="fas fa-boxes fa-4x text-muted mb-3"></i>
                <h4 class="text-muted">No Items Found</h4>
                <p class="text-muted">
                    <?php if (!empty($search)): ?>
                        No items match your search criteria. Try different keywords.
                    <?php else: ?>
                        No items are currently available in the system.
                    <?php endif; ?>
                </p>
            </div>
        <?php else: ?>
            <div class="row">
                <?php foreach ($items as $item): ?>
                    <div class="col-md-6 col-lg-4 mb-4">
                        <div class="card item-card h-100">
                            <div class="card-header bg-primary text-white">
                                <h6 class="mb-0">
                                    <i class="fas fa-box me-2"></i>
                                    <?php echo htmlspecialchars($item['name']); ?>
                                </h6>
                            </div>
                            <div class="card-body">
                                <?php if (!empty($item['description'])): ?>
                                    <p class="card-text text-muted">
                                        <?php echo htmlspecialchars($item['description']); ?>
                                    </p>
                                <?php else: ?>
                                    <p class="card-text text-muted fst-italic">
                                        No description available
                                    </p>
                                <?php endif; ?>
                            </div>
                            <div class="card-footer bg-light">
                                <div class="row text-center">
                                    <div class="col-6">
                                        <small class="text-muted">Created by:</small><br>
                                        <strong><?php echo htmlspecialchars($item['created_by_name'] ?? 'Unknown'); ?></strong>
                                    </div>
                                    <div class="col-6">
                                        <small class="text-muted">Date:</small><br>
                                        <strong><?php echo date('M j, Y', strtotime($item['created_at'])); ?></strong>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                <?php endforeach; ?>
            </div>

            <!-- Pagination -->
            <?php if ($total_pages > 1): ?>
                <nav aria-label="Items pagination" class="mt-4">
                    <ul class="pagination justify-content-center">
                        <?php if ($page > 1): ?>
                            <li class="page-item">
                                <a class="page-link" href="?page=<?php echo $page - 1; ?><?php echo !empty($search) ? '&search=' . urlencode($search) : ''; ?>">
                                    <i class="fas fa-chevron-left"></i> Previous
                                </a>
                            </li>
                        <?php endif; ?>

                        <?php for ($i = max(1, $page - 2); $i <= min($total_pages, $page + 2); $i++): ?>
                            <li class="page-item <?php echo $i == $page ? 'active' : ''; ?>">
                                <a class="page-link" href="?page=<?php echo $i; ?><?php echo !empty($search) ? '&search=' . urlencode($search) : ''; ?>">
                                    <?php echo $i; ?>
                                </a>
                            </li>
                        <?php endfor; ?>

                        <?php if ($page < $total_pages): ?>
                            <li class="page-item">
                                <a class="page-link" href="?page=<?php echo $page + 1; ?><?php echo !empty($search) ? '&search=' . urlencode($search) : ''; ?>">
                                    Next <i class="fas fa-chevron-right"></i>
                                </a>
                            </li>
                        <?php endif; ?>
                    </ul>
                </nav>
            <?php endif; ?>
        <?php endif; ?>

        <!-- Access Information -->
        <div class="alert alert-info mt-4">
            <div class="row align-items-center">
                <div class="col-md-8">
                    <h6 class="mb-1">
                        <i class="fas fa-info-circle me-2"></i>Access Information
                    </h6>
                    <p class="mb-0">
                        You have <strong>view-only</strong> access to items master data. 
                        <?php if ($can_manage): ?>
                            You can manage items through the <a href="../admin/items.php" class="alert-link">Admin Panel</a>.
                        <?php else: ?>
                            Contact your administrator to add or modify items.
                        <?php endif; ?>
                    </p>
                </div>
                <div class="col-md-4 text-md-end">
                    <span class="badge bg-primary">
                        <i class="fas fa-shield-alt me-1"></i><?php echo ucfirst($user_role); ?> Role
                    </span>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
