<?php
/**
 * Compatibility Check for Production Server
 * PHP 8.1.33 + MariaDB 5.5.68
 */

echo "<h1>🔍 Production Server Compatibility Check</h1>";
echo "<p><strong>Target Environment:</strong> PHP 8.1.33 + MariaDB 5.5.68</p>";

// Check PHP Version
echo "<h2>📋 PHP Compatibility</h2>";
echo "<p>Current PHP Version: " . PHP_VERSION . "</p>";

if (version_compare(PHP_VERSION, '8.1.0', '>=')) {
    echo "<p>✅ PHP version is compatible with 8.1.33</p>";
} else {
    echo "<p>❌ PHP version may have compatibility issues</p>";
}

// Check required PHP extensions
$required_extensions = ['pdo', 'pdo_mysql', 'gd', 'json', 'mbstring', 'fileinfo'];
echo "<h3>Required PHP Extensions:</h3>";
foreach ($required_extensions as $ext) {
    if (extension_loaded($ext)) {
        echo "<p>✅ {$ext} - Available</p>";
    } else {
        echo "<p>❌ {$ext} - Missing</p>";
    }
}

// Check Database Connection and Version
echo "<h2>🗄️ Database Compatibility</h2>";

try {
    require_once 'config/database.php';
    $database = new Database();
    $db = $database->getConnection();
    
    // Get database version
    $stmt = $db->query("SELECT VERSION() as version");
    $version = $stmt->fetch()['version'];
    echo "<p>Database Version: {$version}</p>";
    
    // Check if it's MariaDB
    if (stripos($version, 'mariadb') !== false) {
        echo "<p>✅ MariaDB detected</p>";
        
        // Extract version number
        preg_match('/(\d+\.\d+\.\d+)/', $version, $matches);
        $db_version = $matches[1] ?? '0.0.0';
        
        if (version_compare($db_version, '5.5.0', '>=')) {
            echo "<p>✅ MariaDB version is compatible</p>";
        } else {
            echo "<p>❌ MariaDB version may be too old</p>";
        }
    } else {
        echo "<p>⚠️ MySQL detected (expected MariaDB)</p>";
    }
    
    // Test JSON functions compatibility
    echo "<h3>JSON Functions Test:</h3>";
    
    try {
        // Test basic JSON functions
        $stmt = $db->query("SELECT JSON_VALID('[]') as test1");
        $result = $stmt->fetch();
        echo "<p>✅ JSON_VALID function works</p>";
    } catch (Exception $e) {
        echo "<p>❌ JSON_VALID function not available: " . $e->getMessage() . "</p>";
    }
    
    try {
        // Test JSON_LENGTH function
        $stmt = $db->query("SELECT JSON_LENGTH('[]') as test2");
        $result = $stmt->fetch();
        echo "<p>✅ JSON_LENGTH function works</p>";
    } catch (Exception $e) {
        echo "<p>❌ JSON_LENGTH function not available: " . $e->getMessage() . "</p>";
        echo "<p>🔧 <strong>Fix Required:</strong> Replace JSON_LENGTH with alternative</p>";
    }
    
} catch (Exception $e) {
    echo "<p>❌ Database connection failed: " . $e->getMessage() . "</p>";
}

// Check file permissions
echo "<h2>📁 File System Check</h2>";

$directories = ['uploads', 'uploads/transfer_slips', 'uploads/receipts', 'uploads/verification_slips', 'uploads/reviewer_slips'];

foreach ($directories as $dir) {
    if (is_dir($dir)) {
        if (is_writable($dir)) {
            echo "<p>✅ {$dir} - Writable</p>";
        } else {
            echo "<p>❌ {$dir} - Not writable</p>";
        }
    } else {
        echo "<p>⚠️ {$dir} - Directory doesn't exist</p>";
    }
}

// Check PHP configuration
echo "<h2>⚙️ PHP Configuration</h2>";

$php_settings = [
    'upload_max_filesize' => '10M',
    'post_max_size' => '10M',
    'max_execution_time' => '300',
    'memory_limit' => '256M'
];

foreach ($php_settings as $setting => $recommended) {
    $current = ini_get($setting);
    echo "<p><strong>{$setting}:</strong> {$current} (recommended: {$recommended})</p>";
}

echo "<h2>🚨 Known Issues & Fixes</h2>";

echo "<h3>1. JSON_LENGTH Function (MariaDB 5.5.68)</h3>";
echo "<p>❌ <strong>Issue:</strong> JSON_LENGTH function not available in MariaDB 5.5.68</p>";
echo "<p>🔧 <strong>Fix:</strong> Replace with alternative logic in search/advanced.php</p>";

echo "<h3>2. JSON Column Type</h3>";
echo "<p>❌ <strong>Issue:</strong> JSON column type not available in MariaDB 5.5.68</p>";
echo "<p>🔧 <strong>Fix:</strong> Use TEXT columns for JSON data</p>";

echo "<h3>3. Stored Procedures with JSON</h3>";
echo "<p>❌ <strong>Issue:</strong> JSON functions in stored procedures not available</p>";
echo "<p>🔧 <strong>Fix:</strong> Remove or replace stored procedures in batch_operations_schema.sql</p>";

echo "<h2>📝 Action Items</h2>";
echo "<ol>";
echo "<li>Replace JSON_LENGTH in search/advanced.php</li>";
echo "<li>Remove JSON column types from schema</li>";
echo "<li>Remove stored procedures with JSON functions</li>";
echo "<li>Test all functionality after fixes</li>";
echo "</ol>";

echo "<p><strong>Status:</strong> ⚠️ Requires fixes before production deployment</p>";
?>
