<?php
require_once 'config/database.php';

$database = new Database();
$db = $database->getConnection();

// Generate new password hash for admin123
$password = 'admin123';
$new_hash = password_hash($password, PASSWORD_DEFAULT);

echo "<h2>Password Fix</h2>";
echo "<p><strong>Password:</strong> " . $password . "</p>";
echo "<p><strong>New Hash:</strong> " . $new_hash . "</p>";

// Update the admin user password
try {
    $stmt = $db->prepare("UPDATE users SET password_hash = ? WHERE username = 'admin'");
    $result = $stmt->execute([$new_hash]);
    
    if ($result) {
        echo "<p style='color: green;'>✅ Password updated successfully!</p>";
        
        // Test the new password
        $stmt = $db->prepare("SELECT password_hash FROM users WHERE username = 'admin'");
        $stmt->execute();
        $user = $stmt->fetch();
        
        if ($user && password_verify($password, $user['password_hash'])) {
            echo "<p style='color: green;'>✅ Password verification test: SUCCESS</p>";
            echo "<p><strong>You can now login with:</strong></p>";
            echo "<ul>";
            echo "<li>Username: admin</li>";
            echo "<li>Password: admin123</li>";
            echo "</ul>";
        } else {
            echo "<p style='color: red;'>❌ Password verification test: FAILED</p>";
        }
    } else {
        echo "<p style='color: red;'>❌ Failed to update password</p>";
    }
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ Error: " . $e->getMessage() . "</p>";
}

echo "<hr>";
echo "<p><a href='login.php'>Go to Login Page</a></p>";
echo "<p><strong>Note:</strong> Delete this file after use for security.</p>";
?>
