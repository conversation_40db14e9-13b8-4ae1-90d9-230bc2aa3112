<?php
session_start();
require_once 'config/database.php';
require_once 'includes/functions.php';

// Create database connection
$database = new Database();
$db = $database->getConnection();

// Check if user is logged in
if (!isset($_SESSION['user_id'])) {
    header('Location: login.php');
    exit;
}

$user_id = $_SESSION['user_id'];
$user_role = $_SESSION['role'];

// Only allow admin and verification/reviewer roles
if (!in_array($user_role, ['administrator', 'verification', 'reviewer'])) {
    header('Location: dashboard.php');
    exit;
}

?>
<!DOCTYPE html>
<html lang="th">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Batch Logging - Expense System</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
</head>
<body class="bg-light">
    <div class="container-fluid py-4">
        <div class="row">
            <div class="col-12">
                <div class="card shadow">
                    <div class="card-header bg-primary text-white">
                        <h4 class="mb-0">
                            <i class="fas fa-clipboard-list me-2"></i>
                            Test Batch Logging System
                        </h4>
                    </div>
                    <div class="card-body">
                        
                        <!-- Test Overview -->
                        <div class="alert alert-info">
                            <h5><i class="fas fa-info-circle me-2"></i>การทดสอบ Batch Logging</h5>
                            <p class="mb-0">
                                หน้านี้ใช้สำหรับทดสอบระบบ logging ของ batch verification และ batch review 
                                เพื่อให้แน่ใจว่าระบบจะเก็บ log การกระทำของแต่ละ expense ใน batch
                            </p>
                        </div>

                        <!-- Recent Batch Operations -->
                        <div class="row mb-4">
                            <div class="col-md-6">
                                <div class="card border-warning">
                                    <div class="card-header bg-warning text-dark">
                                        <h6 class="mb-0">
                                            <i class="fas fa-check-double me-2"></i>
                                            Recent Batch Verifications
                                        </h6>
                                    </div>
                                    <div class="card-body">
                                        <?php
                                        try {
                                            $stmt = $db->prepare("
                                                SELECT bo.batch_id, bo.created_at, bo.item_count, bo.total_amount, bo.status,
                                                       u.full_name, u.username
                                                FROM batch_operations bo
                                                LEFT JOIN users u ON bo.user_id = u.id
                                                WHERE bo.operation_type = 'verification'
                                                ORDER BY bo.created_at DESC
                                                LIMIT 5
                                            ");
                                            $stmt->execute();
                                            $verification_batches = $stmt->fetchAll();
                                            
                                            if (empty($verification_batches)) {
                                                echo '<p class="text-muted">No verification batches found</p>';
                                            } else {
                                                echo '<div class="table-responsive">';
                                                echo '<table class="table table-sm">';
                                                echo '<thead><tr><th>Batch ID</th><th>Items</th><th>Amount</th><th>Status</th><th>Date</th></tr></thead>';
                                                echo '<tbody>';
                                                foreach ($verification_batches as $batch) {
                                                    $status_class = $batch['status'] === 'completed' ? 'success' : 
                                                                   ($batch['status'] === 'failed' ? 'danger' : 'warning');
                                                    echo '<tr>';
                                                    echo '<td><small>' . htmlspecialchars($batch['batch_id']) . '</small></td>';
                                                    echo '<td>' . $batch['item_count'] . '</td>';
                                                    echo '<td>' . number_format($batch['total_amount'], 2) . '</td>';
                                                    echo '<td><span class="badge bg-' . $status_class . '">' . $batch['status'] . '</span></td>';
                                                    echo '<td><small>' . date('d/m/Y H:i', strtotime($batch['created_at'])) . '</small></td>';
                                                    echo '</tr>';
                                                }
                                                echo '</tbody></table>';
                                                echo '</div>';
                                            }
                                        } catch (Exception $e) {
                                            echo '<p class="text-danger">Error: ' . $e->getMessage() . '</p>';
                                        }
                                        ?>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="col-md-6">
                                <div class="card border-success">
                                    <div class="card-header bg-success text-white">
                                        <h6 class="mb-0">
                                            <i class="fas fa-clipboard-check me-2"></i>
                                            Recent Batch Reviews
                                        </h6>
                                    </div>
                                    <div class="card-body">
                                        <?php
                                        try {
                                            $stmt = $db->prepare("
                                                SELECT bo.batch_id, bo.created_at, bo.item_count, bo.total_amount, bo.status,
                                                       u.full_name, u.username
                                                FROM batch_operations bo
                                                LEFT JOIN users u ON bo.user_id = u.id
                                                WHERE bo.operation_type = 'review'
                                                ORDER BY bo.created_at DESC
                                                LIMIT 5
                                            ");
                                            $stmt->execute();
                                            $review_batches = $stmt->fetchAll();
                                            
                                            if (empty($review_batches)) {
                                                echo '<p class="text-muted">No review batches found</p>';
                                            } else {
                                                echo '<div class="table-responsive">';
                                                echo '<table class="table table-sm">';
                                                echo '<thead><tr><th>Batch ID</th><th>Items</th><th>Amount</th><th>Status</th><th>Date</th></tr></thead>';
                                                echo '<tbody>';
                                                foreach ($review_batches as $batch) {
                                                    $status_class = $batch['status'] === 'completed' ? 'success' : 
                                                                   ($batch['status'] === 'failed' ? 'danger' : 'warning');
                                                    echo '<tr>';
                                                    echo '<td><small>' . htmlspecialchars($batch['batch_id']) . '</small></td>';
                                                    echo '<td>' . $batch['item_count'] . '</td>';
                                                    echo '<td>' . number_format($batch['total_amount'], 2) . '</td>';
                                                    echo '<td><span class="badge bg-' . $status_class . '">' . $batch['status'] . '</span></td>';
                                                    echo '<td><small>' . date('d/m/Y H:i', strtotime($batch['created_at'])) . '</small></td>';
                                                    echo '</tr>';
                                                }
                                                echo '</tbody></table>';
                                                echo '</div>';
                                            }
                                        } catch (Exception $e) {
                                            echo '<p class="text-danger">Error: ' . $e->getMessage() . '</p>';
                                        }
                                        ?>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Activity Logs Analysis -->
                        <div class="card border-info mb-4">
                            <div class="card-header bg-info text-white">
                                <h6 class="mb-0">
                                    <i class="fas fa-chart-bar me-2"></i>
                                    Activity Logs Analysis
                                </h6>
                            </div>
                            <div class="card-body">
                                <?php
                                try {
                                    // Count batch-related activity logs
                                    $stmt = $db->prepare("
                                        SELECT 
                                            action_type,
                                            COUNT(*) as count,
                                            MAX(created_at) as latest_activity
                                        FROM activity_logs 
                                        WHERE action_type IN ('batch_verification', 'batch_review')
                                        GROUP BY action_type
                                        ORDER BY action_type
                                    ");
                                    $stmt->execute();
                                    $log_stats = $stmt->fetchAll();
                                    
                                    if (empty($log_stats)) {
                                        echo '<div class="alert alert-warning">';
                                        echo '<i class="fas fa-exclamation-triangle me-2"></i>';
                                        echo '<strong>ไม่พบ Activity Logs สำหรับ Batch Operations</strong><br>';
                                        echo 'อาจเป็นเพราะ:<br>';
                                        echo '• ยังไม่มีการทำ batch verification หรือ batch review<br>';
                                        echo '• ระบบ logging ยังไม่ทำงาน<br>';
                                        echo '• ข้อมูล logs ถูกลบไปแล้ว';
                                        echo '</div>';
                                    } else {
                                        echo '<div class="row">';
                                        foreach ($log_stats as $stat) {
                                            $icon = $stat['action_type'] === 'batch_verification' ? 'fa-check-double' : 'fa-clipboard-check';
                                            $color = $stat['action_type'] === 'batch_verification' ? 'warning' : 'success';
                                            
                                            echo '<div class="col-md-6">';
                                            echo '<div class="card border-' . $color . '">';
                                            echo '<div class="card-body text-center">';
                                            echo '<i class="fas ' . $icon . ' fa-2x text-' . $color . ' mb-2"></i>';
                                            echo '<h4 class="text-' . $color . '">' . number_format($stat['count']) . '</h4>';
                                            echo '<p class="mb-1">' . ucfirst(str_replace('_', ' ', $stat['action_type'])) . ' Logs</p>';
                                            echo '<small class="text-muted">Latest: ' . date('d/m/Y H:i', strtotime($stat['latest_activity'])) . '</small>';
                                            echo '</div>';
                                            echo '</div>';
                                            echo '</div>';
                                        }
                                        echo '</div>';
                                    }
                                } catch (Exception $e) {
                                    echo '<p class="text-danger">Error: ' . $e->getMessage() . '</p>';
                                }
                                ?>
                            </div>
                        </div>

                        <!-- Recent Activity Logs -->
                        <div class="card">
                            <div class="card-header">
                                <h6 class="mb-0">
                                    <i class="fas fa-history me-2"></i>
                                    Recent Batch Activity Logs (Last 10)
                                </h6>
                            </div>
                            <div class="card-body">
                                <?php
                                try {
                                    $stmt = $db->prepare("
                                        SELECT al.*, u.full_name, u.username
                                        FROM activity_logs al
                                        LEFT JOIN users u ON al.user_id = u.id
                                        WHERE al.action_type IN ('batch_verification', 'batch_review')
                                        ORDER BY al.created_at DESC
                                        LIMIT 10
                                    ");
                                    $stmt->execute();
                                    $recent_logs = $stmt->fetchAll();
                                    
                                    if (empty($recent_logs)) {
                                        echo '<div class="alert alert-info">';
                                        echo '<i class="fas fa-info-circle me-2"></i>';
                                        echo 'ไม่พบ activity logs สำหรับ batch operations ล่าสุด';
                                        echo '</div>';
                                    } else {
                                        echo '<div class="table-responsive">';
                                        echo '<table class="table table-striped">';
                                        echo '<thead>';
                                        echo '<tr>';
                                        echo '<th>Date/Time</th>';
                                        echo '<th>User</th>';
                                        echo '<th>Action</th>';
                                        echo '<th>Record ID</th>';
                                        echo '<th>Description</th>';
                                        echo '</tr>';
                                        echo '</thead>';
                                        echo '<tbody>';
                                        
                                        foreach ($recent_logs as $log) {
                                            $action_badge = $log['action_type'] === 'batch_verification' ? 
                                                           '<span class="badge bg-warning">Verification</span>' : 
                                                           '<span class="badge bg-success">Review</span>';
                                            
                                            echo '<tr>';
                                            echo '<td><small>' . date('d/m/Y H:i:s', strtotime($log['created_at'])) . '</small></td>';
                                            echo '<td>' . htmlspecialchars($log['full_name'] ?: $log['username']) . '</td>';
                                            echo '<td>' . $action_badge . '</td>';
                                            echo '<td>' . ($log['record_id'] ?: '-') . '</td>';
                                            echo '<td><small>' . htmlspecialchars($log['description']) . '</small></td>';
                                            echo '</tr>';
                                        }
                                        
                                        echo '</tbody>';
                                        echo '</table>';
                                        echo '</div>';
                                    }
                                } catch (Exception $e) {
                                    echo '<p class="text-danger">Error: ' . $e->getMessage() . '</p>';
                                }
                                ?>
                            </div>
                        </div>

                        <!-- Action Buttons -->
                        <div class="mt-4 text-center">
                            <a href="expenses/multi_verification.php" class="btn btn-warning me-2">
                                <i class="fas fa-check-double me-1"></i>Test Multi Verification
                            </a>
                            <a href="expenses/multi_review.php" class="btn btn-success me-2">
                                <i class="fas fa-clipboard-check me-1"></i>Test Multi Review
                            </a>
                            <a href="reports/activity_logs.php" class="btn btn-info me-2">
                                <i class="fas fa-list me-1"></i>View All Activity Logs
                            </a>
                            <a href="dashboard.php" class="btn btn-secondary">
                                <i class="fas fa-arrow-left me-1"></i>Back to Dashboard
                            </a>
                        </div>

                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
