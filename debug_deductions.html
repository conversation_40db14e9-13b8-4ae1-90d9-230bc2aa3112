<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Debug Deductions</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="assets/css/checkbox-deductions.css" rel="stylesheet">
</head>
<body>
    <div class="container mt-4">
        <h2>🐛 Debug Deductions System</h2>
        
        <!-- Manual Receipt Card -->
        <div class="card mb-3 receipt-card" data-receipt-index="0">
            <div class="card-header">
                <div class="row align-items-center">
                    <div class="col">
                        <h6 class="mb-0"><i class="fas fa-receipt"></i> Test Receipt 1</h6>
                    </div>
                    <div class="col-auto">
                        <div class="form-check">
                            <input class="form-check-input has-deductions-checkbox" 
                                   type="checkbox" id="hasDeductions0" data-receipt-index="0">
                            <label class="form-check-label" for="hasDeductions0">
                                <i class="fas fa-minus-circle text-warning"></i>
                                <small class="ms-1">มีรายการหัก</small>
                            </label>
                        </div>
                    </div>
                </div>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-4">
                        <label class="form-label">Receipt Number</label>
                        <input type="text" class="form-control receipt-number" value="REC001">
                    </div>
                    <div class="col-md-4">
                        <label class="form-label">Amount (บาท)</label>
                        <input type="number" class="form-control receipt-amount" value="1000" step="0.01">
                    </div>
                    <div class="col-md-4">
                        <label class="form-label">Description</label>
                        <input type="text" class="form-control" value="Test Receipt">
                    </div>
                </div>
                
                <!-- Deductions Section -->
                <div class="deductions-section d-none mt-3" id="deductionsSection0">
                    <div class="card border-warning">
                        <div class="card-header bg-warning bg-opacity-10">
                            <h6 class="mb-0">
                                <i class="fas fa-minus-circle text-warning"></i>
                                รายการหักสำหรับใบเสร็จนี้
                            </h6>
                        </div>
                        <div class="card-body">
                            <!-- Deductions List -->
                            <div class="deductions-list" id="deductionsList0">
                                <div class="no-deductions-message text-muted text-center py-2">
                                    <i class="fas fa-info-circle"></i> ยังไม่มีรายการหัก
                                </div>
                            </div>
                            
                            <!-- Add Deduction Button -->
                            <button type="button" class="btn btn-outline-warning btn-sm add-deduction-btn" 
                                    data-receipt-index="0">
                                <i class="fas fa-plus"></i> เพิ่มรายการหัก
                            </button>
                            
                            <!-- Net Amount Display -->
                            <div class="net-amount-display mt-2">
                                <small class="text-muted">ยอดสุทธิ: </small>
                                <span class="net-amount fw-bold text-success" id="netAmount0">1000.00 บาท</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Debug Info -->
        <div class="card">
            <div class="card-header">
                <h5>Debug Information</h5>
            </div>
            <div class="card-body">
                <button type="button" class="btn btn-info" onclick="showDebugInfo()">Show Debug Info</button>
                <button type="button" class="btn btn-warning" onclick="testAddDeduction()">Test Add Deduction</button>
                <pre id="debug-output" class="mt-3"></pre>
            </div>
        </div>
    </div>

    <!-- Deduction Modal -->
    <div class="modal fade" id="deductionModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">เพิ่มรายการหัก</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <form id="deduction-form">
                        <div class="row">
                            <div class="col-md-6">
                                <label class="form-label">ประเภทการหัก *</label>
                                <select class="form-control" id="deduction_type" required>
                                    <option value="">เลือกประเภท</option>
                                    <option value="tax_withholding">ภาษีหัก ณ ที่จ่าย</option>
                                    <option value="service_fee">ค่าธรรมเนียม</option>
                                </select>
                            </div>
                            <div class="col-md-6">
                                <label class="form-label">คำอธิบาย</label>
                                <input type="text" class="form-control" id="deduction_description">
                            </div>
                        </div>
                        
                        <div class="row mt-3">
                            <div class="col-md-12">
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="is_percentage_based">
                                    <label class="form-check-label" for="is_percentage_based">
                                        คำนวณจากเปอร์เซ็นต์
                                    </label>
                                </div>
                            </div>
                        </div>
                        
                        <div class="row mt-3">
                            <div class="col-md-6" id="amount-input">
                                <label class="form-label">จำนวนเงิน (บาท) *</label>
                                <input type="number" class="form-control" id="deduction_amount" 
                                       placeholder="0.00" step="0.01" min="0">
                            </div>
                            <div class="col-md-6 d-none" id="percentage-input">
                                <label class="form-label">เปอร์เซ็นต์ *</label>
                                <input type="number" class="form-control" id="deduction_percentage" 
                                       placeholder="0.00" step="0.01" min="0" max="100">
                            </div>
                        </div>
                        
                        <div class="row mt-3">
                            <div class="col-md-12">
                                <label class="form-label">แนบหลักฐาน *</label>
                                <input type="file" class="form-control" id="deduction_image" accept="image/*,.pdf">
                                <div id="image-upload-status" class="mt-2"></div>
                                <div id="deduction_image_preview" class="mt-2" style="display: none;">
                                    <img src="" alt="Preview" class="img-thumbnail" style="max-width: 200px;">
                                </div>
                            </div>
                        </div>
                        
                        <div id="deduction-form-errors" class="mt-3"></div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">ยกเลิก</button>
                    <button type="button" class="btn btn-primary" id="save-deduction-btn">บันทึก</button>
                </div>
            </div>
        </div>
    </div>

    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script src="assets/js/expense-form.js"></script>
    <script src="assets/js/per-receipt-deductions.js"></script>
    
    <script>
        // Initialize global variables
        window.receiptDeductions = {};
        window.receiptFiles = [];
        window.currentReceiptIndex = -1;
        window.currentDeductionIndex = -1;
        
        function showDebugInfo() {
            const debugInfo = {
                receiptDeductions: window.receiptDeductions,
                currentReceiptIndex: window.currentReceiptIndex,
                currentDeductionIndex: window.currentDeductionIndex,
                modalMode: $('#deduction-form').data('mode'),
                deductionsListExists: $('#deductionsList0').length > 0,
                checkboxChecked: $('#hasDeductions0').is(':checked')
            };
            
            $('#debug-output').text(JSON.stringify(debugInfo, null, 2));
        }
        
        function testAddDeduction() {
            // Simulate adding a deduction manually
            window.currentReceiptIndex = 0;
            window.receiptDeductions[0] = [{
                type: 'tax_withholding',
                typeName: 'ภาษีหัก ณ ที่จ่าย',
                amount: 50,
                percentage: 0,
                description: 'Test deduction',
                isPercentageBased: false,
                image: 'test.jpg',
                imageUrl: 'uploads/deductions/test.jpg'
            }];
            
            console.log('Manual deduction added');
            renderReceiptDeductionsList(0);
        }
        
        // Initialize checkbox handlers
        $(document).ready(function() {
            if (typeof initializeCheckboxHandlers === 'function') {
                initializeCheckboxHandlers();
            }
        });
    </script>
</body>
</html>
