<?php
session_start();
require_once 'config/database.php';

// Mock session for testing
$_SESSION['user_id'] = 1;
$_SESSION['username'] = 'test_user';
$_SESSION['role'] = 'admin';

$database = new Database();
$db = $database->getConnection();

echo "<h2>🛠️ Create Sample Deductions</h2>";

try {
    // Get all receipt numbers
    $stmt = $db->query("SELECT id, receipt_number, amount FROM receipt_numbers LIMIT 5");
    $receipts = $stmt->fetchAll();
    
    if (empty($receipts)) {
        echo "❌ No receipts found<br>";
        exit;
    }
    
    echo "<h3>📄 Found " . count($receipts) . " receipts</h3>";
    
    // Sample deductions data
    $sample_deductions = [
        [
            'type' => 'tax_vat',
            'amount' => 150.00,
            'description' => 'VAT 7% deduction'
        ],
        [
            'type' => 'tax_withholding',
            'amount' => 75.00,
            'description' => 'Withholding tax 3%'
        ],
        [
            'type' => 'service_fee',
            'amount' => 50.00,
            'description' => 'Service fee'
        ],
        [
            'type' => 'discount',
            'amount' => 25.00,
            'description' => 'Early payment discount'
        ]
    ];
    
    $db->beginTransaction();
    
    foreach ($receipts as $index => $receipt) {
        $receipt_id = $receipt['id'];
        
        // Check if deductions already exist
        $stmt = $db->prepare("SELECT COUNT(*) as count FROM receipt_deductions WHERE receipt_number_id = ?");
        $stmt->execute([$receipt_id]);
        $existing = $stmt->fetch();
        
        if ($existing['count'] == 0) {
            // Add 1-2 random deductions per receipt
            $num_deductions = rand(1, 2);
            $used_types = [];
            
            for ($i = 0; $i < $num_deductions; $i++) {
                // Pick a random deduction type that hasn't been used
                do {
                    $deduction = $sample_deductions[array_rand($sample_deductions)];
                } while (in_array($deduction['type'], $used_types));
                
                $used_types[] = $deduction['type'];
                
                // Insert deduction
                $stmt = $db->prepare("
                    INSERT INTO receipt_deductions 
                    (receipt_number_id, deduction_type, amount, description, created_by, created_at) 
                    VALUES (?, ?, ?, ?, ?, NOW())
                ");
                
                $stmt->execute([
                    $receipt_id,
                    $deduction['type'],
                    $deduction['amount'],
                    $deduction['description'],
                    1
                ]);
                
                echo "✅ Added " . $deduction['type'] . " deduction (฿" . number_format($deduction['amount'], 2) . ") to receipt: " . $receipt['receipt_number'] . "<br>";
            }
        } else {
            echo "⏭️ Receipt " . $receipt['receipt_number'] . " already has deductions<br>";
        }
    }
    
    $db->commit();
    
    echo "<h3>🎉 Sample deductions created successfully!</h3>";
    echo "<p><strong>💡 Now go back to <a href='expenses/view.php?id=1' target='_blank'>view.php?id=1</a> to see the Receipt Deductions section!</strong></p>";
    
    // Show summary
    echo "<h3>📊 Summary:</h3>";
    $stmt = $db->query("
        SELECT 
            rd.deduction_type,
            COUNT(*) as count,
            SUM(rd.amount) as total_amount
        FROM receipt_deductions rd
        GROUP BY rd.deduction_type
        ORDER BY rd.deduction_type
    ");
    $summary = $stmt->fetchAll();
    
    echo "<table border='1'>";
    echo "<tr><th>Deduction Type</th><th>Count</th><th>Total Amount</th></tr>";
    foreach ($summary as $row) {
        echo "<tr>";
        echo "<td>" . ucfirst(str_replace('_', ' ', $row['deduction_type'])) . "</td>";
        echo "<td>" . $row['count'] . "</td>";
        echo "<td>฿" . number_format($row['total_amount'], 2) . "</td>";
        echo "</tr>";
    }
    echo "</table>";
    
} catch (Exception $e) {
    $db->rollback();
    echo "❌ Error: " . $e->getMessage() . "<br>";
}
?>
