<?php
session_start();
require_once 'config/database.php';
require_once 'includes/functions.php';
require_once 'includes/BatchOperations.php';

// Create database connection
$database = new Database();
$db = $database->getConnection();

// Check if user is logged in
if (!isset($_SESSION['user_id'])) {
    header('Location: login.php');
    exit;
}

$user_id = $_SESSION['user_id'];
$user_role = $_SESSION['role'];

// Only allow verification role
if (!in_array($user_role, ['administrator', 'verification'])) {
    header('Location: dashboard.php');
    exit;
}

$message = '';
$message_type = '';

// Handle form submission
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['test_batch'])) {
    try {
        // Create a simple test log first
        logActivity(
            $db,
            $user_id,
            'test',
            'test_table',
            999,
            'Test log before batch operation',
            $_SERVER['REMOTE_ADDR'],
            $_SERVER['HTTP_USER_AGENT']
        );
        
        // Get some open expenses for testing
        $stmt = $db->prepare("
            SELECT id, exno, total_amount 
            FROM expenses 
            WHERE status = 'open' AND verification_by IS NULL 
            LIMIT 2
        ");
        $stmt->execute();
        $test_expenses = $stmt->fetchAll();
        
        if (empty($test_expenses)) {
            throw new Exception('No open expenses found for testing');
        }
        
        // Create BatchOperations instance
        $batchOps = new BatchOperations($db);

        // Prepare expense items for batch
        $expense_items = [];
        $total_amount = 0;

        foreach ($test_expenses as $expense) {
            $expense_items[] = [
                'expense_id' => $expense['id'],
                'amount' => $expense['total_amount']  // ใช้ 'amount' ตาม BatchOperations.php
            ];
            $total_amount += $expense['total_amount'];
        }

        // Create a test batch with correct parameters
        $batch_result = $batchOps->createBatch(
            'verification',
            $user_id,
            $expense_items,
            $total_amount,
            'Test batch from test_simple_batch.php'
        );
        
        if (!$batch_result['success']) {
            throw new Exception('Failed to create batch: ' . $batch_result['error']);
        }
        
        $batch_id = $batch_result['batch_id'];
        
        // Process the batch
        $process_result = $batchOps->processBatchVerification(
            $batch_id,
            'test_verification_slip.jpg',
            $user_id,
            'TEST_TRANSFER_' . time(),
            []
        );
        
        if (!$process_result['success']) {
            throw new Exception('Failed to process batch: ' . $process_result['error']);
        }
        
        // Log batch-level activity
        logActivity(
            $db,
            $user_id,
            'batch_verification',
            'expenses',
            null,
            "Test batch verification: {$batch_id}, Items: {$process_result['processed']}, Success: {$process_result['success_count']}",
            $_SERVER['REMOTE_ADDR'],
            $_SERVER['HTTP_USER_AGENT']
        );
        
        $message = "Test batch verification completed successfully! Batch ID: {$batch_id}, Processed: {$process_result['processed']} items";
        $message_type = 'success';
        
    } catch (Exception $e) {
        $message = 'Error: ' . $e->getMessage();
        $message_type = 'danger';
    }
}

?>
<!DOCTYPE html>
<html lang="th">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Simple Batch Verification</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
</head>
<body class="bg-light">
    <div class="container py-4">
        <div class="row">
            <div class="col-12">
                <div class="card">
                    <div class="card-header bg-warning text-dark">
                        <h4 class="mb-0">
                            <i class="fas fa-flask me-2"></i>
                            Test Simple Batch Verification
                        </h4>
                        <small>User: <?php echo htmlspecialchars($_SESSION['full_name']); ?> (ID: <?php echo $user_id; ?>)</small>
                    </div>
                    <div class="card-body">
                        
                        <?php if ($message): ?>
                        <div class="alert alert-<?php echo $message_type; ?> alert-dismissible fade show">
                            <i class="fas fa-<?php echo $message_type === 'success' ? 'check' : 'exclamation-triangle'; ?> me-2"></i>
                            <?php echo htmlspecialchars($message); ?>
                            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                        </div>
                        <?php endif; ?>
                        
                        <!-- Test Information -->
                        <div class="alert alert-info">
                            <h6><i class="fas fa-info-circle me-2"></i>การทดสอบ</h6>
                            <p class="mb-0">
                                หน้านี้จะทดสอบการทำ batch verification แบบง่าย ๆ และตรวจสอบว่า individual logging ทำงานหรือไม่
                            </p>
                        </div>
                        
                        <!-- Available Open Expenses -->
                        <div class="card mb-4">
                            <div class="card-header">
                                <h6 class="mb-0">
                                    <i class="fas fa-list me-2"></i>
                                    Available Open Expenses for Testing
                                </h6>
                            </div>
                            <div class="card-body">
                                <?php
                                try {
                                    $stmt = $db->prepare("
                                        SELECT id, exno, total_amount, created_at
                                        FROM expenses 
                                        WHERE status = 'open' AND verification_by IS NULL 
                                        ORDER BY created_at DESC
                                        LIMIT 5
                                    ");
                                    $stmt->execute();
                                    $open_expenses = $stmt->fetchAll();
                                    
                                    if (empty($open_expenses)) {
                                        echo '<div class="alert alert-warning">';
                                        echo '<i class="fas fa-exclamation-triangle me-2"></i>';
                                        echo 'No open expenses available for testing. Please create some expenses first.';
                                        echo '</div>';
                                    } else {
                                        echo '<div class="table-responsive">';
                                        echo '<table class="table table-striped table-sm">';
                                        echo '<thead>';
                                        echo '<tr><th>ID</th><th>Expense No</th><th>Amount</th><th>Created</th></tr>';
                                        echo '</thead>';
                                        echo '<tbody>';
                                        
                                        foreach ($open_expenses as $expense) {
                                            echo '<tr>';
                                            echo '<td>' . $expense['id'] . '</td>';
                                            echo '<td>' . htmlspecialchars($expense['exno']) . '</td>';
                                            echo '<td>' . number_format($expense['total_amount'], 2) . '</td>';
                                            echo '<td>' . date('d/m/Y H:i', strtotime($expense['created_at'])) . '</td>';
                                            echo '</tr>';
                                        }
                                        
                                        echo '</tbody>';
                                        echo '</table>';
                                        echo '</div>';
                                    }
                                } catch (Exception $e) {
                                    echo '<p class="text-danger">Error: ' . $e->getMessage() . '</p>';
                                }
                                ?>
                            </div>
                        </div>
                        
                        <!-- Test Form -->
                        <div class="card mb-4">
                            <div class="card-header">
                                <h6 class="mb-0">
                                    <i class="fas fa-play me-2"></i>
                                    Run Test
                                </h6>
                            </div>
                            <div class="card-body">
                                <form method="POST">
                                    <div class="mb-3">
                                        <p class="text-muted">
                                            การทดสอบนี้จะ:
                                        </p>
                                        <ul class="text-muted">
                                            <li>สร้าง test log entry ก่อน</li>
                                            <li>เลือก expenses ที่มีสถานะ 'open' สูงสุด 2 รายการ</li>
                                            <li>สร้าง batch verification</li>
                                            <li>ประมวลผล batch และเปลี่ยนสถานะเป็น 'pending'</li>
                                            <li>สร้าง individual logs สำหรับแต่ละ expense</li>
                                            <li>สร้าง batch-level log</li>
                                        </ul>
                                    </div>
                                    
                                    <button type="submit" name="test_batch" class="btn btn-warning">
                                        <i class="fas fa-flask me-1"></i>
                                        Run Test Batch Verification
                                    </button>
                                </form>
                            </div>
                        </div>
                        
                        <!-- Recent Activity Logs -->
                        <div class="card">
                            <div class="card-header">
                                <h6 class="mb-0">
                                    <i class="fas fa-history me-2"></i>
                                    Recent Activity Logs (My Activities)
                                </h6>
                            </div>
                            <div class="card-body">
                                <?php
                                try {
                                    $stmt = $db->prepare("
                                        SELECT * 
                                        FROM activity_logs 
                                        WHERE user_id = ? 
                                        ORDER BY created_at DESC 
                                        LIMIT 10
                                    ");
                                    $stmt->execute([$user_id]);
                                    $my_logs = $stmt->fetchAll();
                                    
                                    if (empty($my_logs)) {
                                        echo '<div class="alert alert-warning">';
                                        echo '<i class="fas fa-exclamation-triangle me-2"></i>';
                                        echo 'No activity logs found for your user ID: ' . $user_id;
                                        echo '</div>';
                                    } else {
                                        echo '<div class="table-responsive">';
                                        echo '<table class="table table-striped table-sm">';
                                        echo '<thead>';
                                        echo '<tr>';
                                        echo '<th>Date/Time</th>';
                                        echo '<th>Action</th>';
                                        echo '<th>Table</th>';
                                        echo '<th>Record ID</th>';
                                        echo '<th>Description</th>';
                                        echo '</tr>';
                                        echo '</thead>';
                                        echo '<tbody>';
                                        
                                        foreach ($my_logs as $log) {
                                            $action_class = '';
                                            switch ($log['action_type']) {
                                                case 'batch_verification':
                                                    $action_class = 'bg-warning';
                                                    break;
                                                case 'batch_review':
                                                    $action_class = 'bg-success';
                                                    break;
                                                case 'test':
                                                    $action_class = 'bg-info';
                                                    break;
                                                default:
                                                    $action_class = 'bg-secondary';
                                            }
                                            
                                            echo '<tr>';
                                            echo '<td><small>' . date('d/m/Y H:i:s', strtotime($log['created_at'])) . '</small></td>';
                                            echo '<td><span class="badge ' . $action_class . '">' . htmlspecialchars($log['action_type']) . '</span></td>';
                                            echo '<td>' . htmlspecialchars($log['table_name'] ?: '-') . '</td>';
                                            echo '<td>' . ($log['record_id'] ?: '-') . '</td>';
                                            echo '<td><small>' . htmlspecialchars(substr($log['description'], 0, 80)) . '</small></td>';
                                            echo '</tr>';
                                        }
                                        
                                        echo '</tbody>';
                                        echo '</table>';
                                        echo '</div>';
                                    }
                                } catch (Exception $e) {
                                    echo '<p class="text-danger">Error: ' . $e->getMessage() . '</p>';
                                }
                                ?>
                            </div>
                        </div>
                        
                        <!-- Navigation -->
                        <div class="mt-4 text-center">
                            <a href="debug_logs.php" class="btn btn-info me-2">
                                <i class="fas fa-bug me-1"></i>Debug Logs
                            </a>
                            <a href="dashboard.php" class="btn btn-secondary me-2">
                                <i class="fas fa-arrow-left me-1"></i>Back to Dashboard
                            </a>
                            <a href="expenses/multi_verification.php" class="btn btn-warning">
                                <i class="fas fa-check-double me-1"></i>Real Multi Verification
                            </a>
                        </div>
                        
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
