-- Cleanup Backup Created: 2025-10-19 18:42:42

-- Table: batch_performance_logs
CREATE TABLE `batch_performance_logs` (
  `id` int NOT NULL AUTO_INCREMENT,
  `batch_id` varchar(50) COLLATE utf8mb4_general_ci NOT NULL,
  `operation_step` varchar(100) COLLATE utf8mb4_general_ci NOT NULL COMMENT 'ขั้นตอนการทำงาน',
  `start_time` timestamp NOT NULL,
  `end_time` timestamp NULL DEFAULT NULL,
  `duration_ms` int DEFAULT NULL COMMENT 'ระยะเวลา (milliseconds)',
  `memory_usage_mb` decimal(8,2) DEFAULT NULL COMMENT 'การใช้ Memory (MB)',
  `items_processed` int DEFAULT '0',
  `success_count` int DEFAULT '0',
  `error_count` int DEFAULT '0',
  PRIMARY KEY (`id`),
  KEY `idx_batch_id` (`batch_id`),
  KEY `idx_operation_step` (`operation_step`),
  CONSTRAINT `batch_performance_logs_ibfk_1` <PERSON><PERSON><PERSON><PERSON><PERSON> KEY (`batch_id`) REFERENCES `batch_operations` (`batch_id`) ON DELETE CASCADE
) ENGINE=InnoDB AUTO_INCREMENT=19 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='Log การทำงานเพื่อ Performance Analysis';

-- Data for table: batch_performance_logs
INSERT INTO batch_performance_logs (id, batch_id, operation_step, start_time, end_time, duration_ms, memory_usage_mb, items_processed, success_count, error_count) VALUES ('1', 'BATCH_VER_********_163134', 'batch_creation', '2025-10-19 23:31:35', '2025-10-19 23:31:35', '5', '0.00', '3', '3', '0');
INSERT INTO batch_performance_logs (id, batch_id, operation_step, start_time, end_time, duration_ms, memory_usage_mb, items_processed, success_count, error_count) VALUES ('2', 'BATCH_VER_********_170946', 'batch_creation', '2025-10-20 00:09:47', '2025-10-20 00:09:47', '4', '0.00', '3', '3', '0');
INSERT INTO batch_performance_logs (id, batch_id, operation_step, start_time, end_time, duration_ms, memory_usage_mb, items_processed, success_count, error_count) VALUES ('3', 'BATCH_VER_********_172025', 'batch_creation', '2025-10-20 00:20:26', '2025-10-20 00:20:26', '3', '0.00', '1', '1', '0');
INSERT INTO batch_performance_logs (id, batch_id, operation_step, start_time, end_time, duration_ms, memory_usage_mb, items_processed, success_count, error_count) VALUES ('4', 'BATCH_VER_********_172544', 'batch_creation', '2025-10-20 00:25:44', '2025-10-20 00:25:44', '2', '0.00', '1', '1', '0');
INSERT INTO batch_performance_logs (id, batch_id, operation_step, start_time, end_time, duration_ms, memory_usage_mb, items_processed, success_count, error_count) VALUES ('5', 'BATCH_VER_********_173312', 'batch_creation', '2025-10-20 00:33:12', '2025-10-20 00:33:12', '3', '0.00', '1', '1', '0');
INSERT INTO batch_performance_logs (id, batch_id, operation_step, start_time, end_time, duration_ms, memory_usage_mb, items_processed, success_count, error_count) VALUES ('6', 'BATCH_VER_********_175325', 'batch_creation', '2025-10-20 00:53:26', '2025-10-20 00:53:26', '2', '0.00', '1', '1', '0');
INSERT INTO batch_performance_logs (id, batch_id, operation_step, start_time, end_time, duration_ms, memory_usage_mb, items_processed, success_count, error_count) VALUES ('7', 'BATCH_VER_********_175337', 'batch_creation', '2025-10-20 00:53:37', '2025-10-20 00:53:37', '1', '0.00', '3', '3', '0');
INSERT INTO batch_performance_logs (id, batch_id, operation_step, start_time, end_time, duration_ms, memory_usage_mb, items_processed, success_count, error_count) VALUES ('8', 'BATCH_VER_********_175337', 'data_retrieval', '2025-10-20 00:53:58', '2025-10-20 00:53:58', '0', '0.00', '3', '3', '0');
INSERT INTO batch_performance_logs (id, batch_id, operation_step, start_time, end_time, duration_ms, memory_usage_mb, items_processed, success_count, error_count) VALUES ('9', 'BATCH_VER_********_175337', 'database_update', '2025-10-20 00:53:58', '2025-10-20 00:53:58', '1', '0.00', '3', '3', '0');
INSERT INTO batch_performance_logs (id, batch_id, operation_step, start_time, end_time, duration_ms, memory_usage_mb, items_processed, success_count, error_count) VALUES ('10', 'BATCH_VER_********_175337', 'batch_verification', '2025-10-20 00:53:58', '2025-10-20 00:53:58', '2', '0.00', '3', '3', '0');
INSERT INTO batch_performance_logs (id, batch_id, operation_step, start_time, end_time, duration_ms, memory_usage_mb, items_processed, success_count, error_count) VALUES ('11', 'BATCH_REV_********_175439', 'batch_creation', '2025-10-20 00:54:39', '2025-10-20 00:54:39', '2', '0.00', '2', '2', '0');
INSERT INTO batch_performance_logs (id, batch_id, operation_step, start_time, end_time, duration_ms, memory_usage_mb, items_processed, success_count, error_count) VALUES ('12', 'BATCH_REV_********_175439', 'data_retrieval_review', '2025-10-20 00:55:19', '2025-10-20 00:55:19', '1', '0.00', '2', '2', '0');
INSERT INTO batch_performance_logs (id, batch_id, operation_step, start_time, end_time, duration_ms, memory_usage_mb, items_processed, success_count, error_count) VALUES ('13', 'BATCH_REV_********_175439', 'database_update_review', '2025-10-20 00:55:19', '2025-10-20 00:55:19', '2', '0.00', '2', '2', '0');
INSERT INTO batch_performance_logs (id, batch_id, operation_step, start_time, end_time, duration_ms, memory_usage_mb, items_processed, success_count, error_count) VALUES ('14', 'BATCH_REV_********_175439', 'batch_review', '2025-10-20 00:55:19', '2025-10-20 00:55:19', '5', '0.00', '2', '2', '0');
INSERT INTO batch_performance_logs (id, batch_id, operation_step, start_time, end_time, duration_ms, memory_usage_mb, items_processed, success_count, error_count) VALUES ('15', 'BATCH_REV_********_175531', 'batch_creation', '2025-10-20 00:55:32', '2025-10-20 00:55:32', '3', '0.00', '2', '2', '0');
INSERT INTO batch_performance_logs (id, batch_id, operation_step, start_time, end_time, duration_ms, memory_usage_mb, items_processed, success_count, error_count) VALUES ('16', 'BATCH_REV_********_175531', 'data_retrieval_review', '2025-10-20 00:55:49', '2025-10-20 00:55:49', '0', '0.00', '2', '2', '0');
INSERT INTO batch_performance_logs (id, batch_id, operation_step, start_time, end_time, duration_ms, memory_usage_mb, items_processed, success_count, error_count) VALUES ('17', 'BATCH_REV_********_175531', 'database_update_review', '2025-10-20 00:55:49', '2025-10-20 00:55:49', '1', '0.00', '2', '2', '0');
INSERT INTO batch_performance_logs (id, batch_id, operation_step, start_time, end_time, duration_ms, memory_usage_mb, items_processed, success_count, error_count) VALUES ('18', 'BATCH_REV_********_175531', 'batch_review', '2025-10-20 00:55:49', '2025-10-20 00:55:49', '2', '0.00', '2', '2', '0');

-- Table: batch_documents
CREATE TABLE `batch_documents` (
  `id` int NOT NULL AUTO_INCREMENT,
  `batch_id` varchar(50) COLLATE utf8mb4_general_ci NOT NULL,
  `document_type` enum('verification_slip','review_slip') COLLATE utf8mb4_general_ci NOT NULL,
  `file_path` varchar(500) COLLATE utf8mb4_general_ci NOT NULL COMMENT 'Path ของไฟล์',
  `original_filename` varchar(255) COLLATE utf8mb4_general_ci NOT NULL COMMENT 'ชื่อไฟล์ต้นฉบับ',
  `file_size` int NOT NULL COMMENT 'ขนาดไฟล์ (bytes)',
  `mime_type` varchar(100) COLLATE utf8mb4_general_ci NOT NULL COMMENT 'ประเภทไฟล์',
  `uploaded_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `idx_batch_id` (`batch_id`),
  KEY `idx_document_type` (`document_type`),
  CONSTRAINT `batch_documents_ibfk_1` FOREIGN KEY (`batch_id`) REFERENCES `batch_operations` (`batch_id`) ON DELETE CASCADE
) ENGINE=InnoDB AUTO_INCREMENT=4 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='เอกสารที่แนบกับ Batch';

-- Data for table: batch_documents
INSERT INTO batch_documents (id, batch_id, document_type, file_path, original_filename, file_size, mime_type, uploaded_at) VALUES ('1', 'BATCH_VER_********_175337', 'verification_slip', 'batch_documents/verification/BATCH_VER_********_175337_verification_68f525b60452f_1760896438.png', 'Screenshot 2568-10-19 at 17.18.43.png', '83756', 'image/png', '2025-10-20 00:53:58');
INSERT INTO batch_documents (id, batch_id, document_type, file_path, original_filename, file_size, mime_type, uploaded_at) VALUES ('2', 'BATCH_REV_********_175439', 'review_slip', 'batch_documents/review/BATCH_REV_********_175439_review_68f526066e0c2_1760896518.png', 'Screenshot 2568-10-19 at 20.51.54.png', '147591', 'image/png', '2025-10-20 00:55:18');
INSERT INTO batch_documents (id, batch_id, document_type, file_path, original_filename, file_size, mime_type, uploaded_at) VALUES ('3', 'BATCH_REV_********_175531', 'review_slip', 'batch_documents/review/BATCH_REV_********_175531_review_68f526245e645_1760896548.png', 'Screenshot 2568-10-19 at 17.18.43.png', '83756', 'image/png', '2025-10-20 00:55:48');

-- Table: batch_items
CREATE TABLE `batch_items` (
  `id` int NOT NULL AUTO_INCREMENT,
  `batch_id` varchar(50) COLLATE utf8mb4_general_ci NOT NULL,
  `expense_id` int NOT NULL,
  `individual_amount` decimal(12,2) NOT NULL COMMENT 'จำนวนเงินของรายการนี้',
  `status` enum('pending','processing','completed','failed') COLLATE utf8mb4_general_ci DEFAULT 'pending',
  `error_message` text COLLATE utf8mb4_general_ci COMMENT 'ข้อความ Error หากมี',
  `processed_at` timestamp NULL DEFAULT NULL COMMENT 'เวลาที่ประมวลผลรายการนี้',
  PRIMARY KEY (`id`),
  UNIQUE KEY `unique_batch_expense` (`batch_id`,`expense_id`),
  KEY `idx_batch_id` (`batch_id`),
  KEY `idx_expense_id` (`expense_id`),
  KEY `idx_status` (`status`),
  CONSTRAINT `batch_items_ibfk_1` FOREIGN KEY (`batch_id`) REFERENCES `batch_operations` (`batch_id`) ON DELETE CASCADE,
  CONSTRAINT `batch_items_ibfk_2` FOREIGN KEY (`expense_id`) REFERENCES `expenses` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB AUTO_INCREMENT=18 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='รายการ Expenses ใน Batch';

-- Data for table: batch_items
INSERT INTO batch_items (id, batch_id, expense_id, individual_amount, status, error_message, processed_at) VALUES ('1', 'BATCH_VER_********_163134', '2', '2500.00', 'pending', NULL, NULL);
INSERT INTO batch_items (id, batch_id, expense_id, individual_amount, status, error_message, processed_at) VALUES ('2', 'BATCH_VER_********_163134', '3', '4500.00', 'pending', NULL, NULL);
INSERT INTO batch_items (id, batch_id, expense_id, individual_amount, status, error_message, processed_at) VALUES ('3', 'BATCH_VER_********_163134', '4', '10000.00', 'pending', NULL, NULL);
INSERT INTO batch_items (id, batch_id, expense_id, individual_amount, status, error_message, processed_at) VALUES ('4', 'BATCH_VER_********_170946', '2', '2500.00', 'pending', NULL, NULL);
INSERT INTO batch_items (id, batch_id, expense_id, individual_amount, status, error_message, processed_at) VALUES ('5', 'BATCH_VER_********_170946', '3', '4500.00', 'pending', NULL, NULL);
INSERT INTO batch_items (id, batch_id, expense_id, individual_amount, status, error_message, processed_at) VALUES ('6', 'BATCH_VER_********_170946', '4', '10000.00', 'pending', NULL, NULL);
INSERT INTO batch_items (id, batch_id, expense_id, individual_amount, status, error_message, processed_at) VALUES ('7', 'BATCH_VER_********_172025', '2', '2500.00', 'pending', NULL, NULL);
INSERT INTO batch_items (id, batch_id, expense_id, individual_amount, status, error_message, processed_at) VALUES ('8', 'BATCH_VER_********_172544', '4', '10000.00', 'pending', NULL, NULL);
INSERT INTO batch_items (id, batch_id, expense_id, individual_amount, status, error_message, processed_at) VALUES ('9', 'BATCH_VER_********_173312', '2', '2500.00', 'pending', NULL, NULL);
INSERT INTO batch_items (id, batch_id, expense_id, individual_amount, status, error_message, processed_at) VALUES ('10', 'BATCH_VER_********_175325', '2', '2500.00', 'pending', NULL, NULL);
INSERT INTO batch_items (id, batch_id, expense_id, individual_amount, status, error_message, processed_at) VALUES ('11', 'BATCH_VER_********_175337', '2', '2500.00', 'completed', NULL, '2025-10-20 00:53:58');
INSERT INTO batch_items (id, batch_id, expense_id, individual_amount, status, error_message, processed_at) VALUES ('12', 'BATCH_VER_********_175337', '3', '4500.00', 'completed', NULL, '2025-10-20 00:53:58');
INSERT INTO batch_items (id, batch_id, expense_id, individual_amount, status, error_message, processed_at) VALUES ('13', 'BATCH_VER_********_175337', '4', '10000.00', 'completed', NULL, '2025-10-20 00:53:58');
INSERT INTO batch_items (id, batch_id, expense_id, individual_amount, status, error_message, processed_at) VALUES ('14', 'BATCH_REV_********_175439', '3', '4500.00', 'completed', NULL, '2025-10-20 00:55:18');
INSERT INTO batch_items (id, batch_id, expense_id, individual_amount, status, error_message, processed_at) VALUES ('15', 'BATCH_REV_********_175439', '4', '10000.00', 'completed', NULL, '2025-10-20 00:55:18');
INSERT INTO batch_items (id, batch_id, expense_id, individual_amount, status, error_message, processed_at) VALUES ('16', 'BATCH_REV_********_175531', '1', '2000.00', 'completed', NULL, '2025-10-20 00:55:48');
INSERT INTO batch_items (id, batch_id, expense_id, individual_amount, status, error_message, processed_at) VALUES ('17', 'BATCH_REV_********_175531', '2', '2500.00', 'completed', NULL, '2025-10-20 00:55:48');

-- Table: batch_operations
CREATE TABLE `batch_operations` (
  `id` int NOT NULL AUTO_INCREMENT,
  `batch_id` varchar(50) COLLATE utf8mb4_general_ci NOT NULL COMMENT 'รหัส Batch เช่น BATCH_VER_20251017_001',
  `operation_type` enum('verification','review') COLLATE utf8mb4_general_ci NOT NULL COMMENT 'ประเภทการดำเนินการ',
  `user_id` int NOT NULL COMMENT 'ผู้ดำเนินการ',
  `total_amount` decimal(12,2) NOT NULL COMMENT 'ยอดรวมทั้งหมด',
  `item_count` int NOT NULL COMMENT 'จำนวนรายการ',
  `status` enum('pending','processing','completed','failed','cancelled') COLLATE utf8mb4_general_ci DEFAULT 'pending',
  `notes` text COLLATE utf8mb4_general_ci COMMENT 'หมายเหตุเพิ่มเติม',
  `transfer_number` varchar(100) COLLATE utf8mb4_general_ci DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `started_at` timestamp NULL DEFAULT NULL COMMENT 'เวลาเริ่มประมวลผล',
  `completed_at` timestamp NULL DEFAULT NULL COMMENT 'เวลาเสร็จสิ้น',
  PRIMARY KEY (`id`),
  UNIQUE KEY `batch_id` (`batch_id`),
  KEY `idx_batch_id` (`batch_id`),
  KEY `idx_user_operation` (`user_id`,`operation_type`),
  KEY `idx_status` (`status`),
  KEY `idx_created_at` (`created_at`),
  CONSTRAINT `batch_operations_ibfk_1` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB AUTO_INCREMENT=10 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='ตารางหลักสำหรับ Batch Operations';

-- Data for table: batch_operations
INSERT INTO batch_operations (id, batch_id, operation_type, user_id, total_amount, item_count, status, notes, transfer_number, created_at, started_at, completed_at) VALUES ('1', 'BATCH_VER_********_163134', 'verification', '3', '17000.00', '3', 'pending', 'Batch verification for 3 expenses', NULL, '2025-10-19 23:31:34', NULL, NULL);
INSERT INTO batch_operations (id, batch_id, operation_type, user_id, total_amount, item_count, status, notes, transfer_number, created_at, started_at, completed_at) VALUES ('2', 'BATCH_VER_********_170946', 'verification', '3', '17000.00', '3', 'pending', 'Batch verification for 3 expenses', NULL, '2025-10-20 00:09:46', NULL, NULL);
INSERT INTO batch_operations (id, batch_id, operation_type, user_id, total_amount, item_count, status, notes, transfer_number, created_at, started_at, completed_at) VALUES ('3', 'BATCH_VER_********_172025', 'verification', '3', '2500.00', '1', 'pending', 'Batch verification for 1 expenses', NULL, '2025-10-20 00:20:25', NULL, NULL);
INSERT INTO batch_operations (id, batch_id, operation_type, user_id, total_amount, item_count, status, notes, transfer_number, created_at, started_at, completed_at) VALUES ('4', 'BATCH_VER_********_172544', 'verification', '3', '10000.00', '1', 'pending', 'Batch verification for 1 expenses', NULL, '2025-10-20 00:25:44', NULL, NULL);
INSERT INTO batch_operations (id, batch_id, operation_type, user_id, total_amount, item_count, status, notes, transfer_number, created_at, started_at, completed_at) VALUES ('5', 'BATCH_VER_********_173312', 'verification', '3', '2500.00', '1', 'pending', 'Batch verification for 1 expenses', NULL, '2025-10-20 00:33:12', NULL, NULL);
INSERT INTO batch_operations (id, batch_id, operation_type, user_id, total_amount, item_count, status, notes, transfer_number, created_at, started_at, completed_at) VALUES ('6', 'BATCH_VER_********_175325', 'verification', '3', '2500.00', '1', 'pending', 'Batch verification for 1 expenses', NULL, '2025-10-20 00:53:25', NULL, NULL);
INSERT INTO batch_operations (id, batch_id, operation_type, user_id, total_amount, item_count, status, notes, transfer_number, created_at, started_at, completed_at) VALUES ('7', 'BATCH_VER_********_175337', 'verification', '3', '17000.00', '3', 'completed', 'Batch verification for 3 expenses', '123', '2025-10-20 00:53:37', '2025-10-20 00:53:58', '2025-10-20 00:53:58');
INSERT INTO batch_operations (id, batch_id, operation_type, user_id, total_amount, item_count, status, notes, transfer_number, created_at, started_at, completed_at) VALUES ('8', 'BATCH_REV_********_175439', 'review', '4', '14500.00', '2', 'completed', 'Batch review for 2 verified expenses', '12345', '2025-10-20 00:54:39', '2025-10-20 00:55:18', '2025-10-20 00:55:18');
INSERT INTO batch_operations (id, batch_id, operation_type, user_id, total_amount, item_count, status, notes, transfer_number, created_at, started_at, completed_at) VALUES ('9', 'BATCH_REV_********_175531', 'review', '4', '4500.00', '2', 'completed', 'Batch review for 2 verified expenses', '4000', '2025-10-20 00:55:31', '2025-10-20 00:55:48', '2025-10-20 00:55:48');

-- Table: receipt_numbers
CREATE TABLE `receipt_numbers` (
  `id` int NOT NULL AUTO_INCREMENT,
  `expense_id` int NOT NULL,
  `receipt_number` varchar(100) COLLATE utf8mb4_general_ci NOT NULL,
  `amount` decimal(15,2) DEFAULT '0.00',
  `description` text COLLATE utf8mb4_general_ci,
  `receipt_image` varchar(255) COLLATE utf8mb4_general_ci DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `created_by` int DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `created_by` (`created_by`),
  KEY `idx_expense_id` (`expense_id`),
  KEY `idx_receipt_number` (`receipt_number`),
  CONSTRAINT `receipt_numbers_ibfk_1` FOREIGN KEY (`expense_id`) REFERENCES `expenses` (`id`) ON DELETE CASCADE,
  CONSTRAINT `receipt_numbers_ibfk_2` FOREIGN KEY (`created_by`) REFERENCES `users` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=11 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- Data for table: receipt_numbers
INSERT INTO receipt_numbers (id, expense_id, receipt_number, amount, description, receipt_image, created_at, created_by) VALUES ('1', '1', '111', '1300.00', 'ค่าน้ำ ลาน NK', 'receipt_111_68f50f76a50da_1760890742.png', '2025-10-19 23:19:03', '5');
INSERT INTO receipt_numbers (id, expense_id, receipt_number, amount, description, receipt_image, created_at, created_by) VALUES ('2', '1', '222', '700.00', 'ค่า OT', 'receipt_222_68f50f77372ba_1760890743.png', '2025-10-19 23:19:03', '5');
INSERT INTO receipt_numbers (id, expense_id, receipt_number, amount, description, receipt_image, created_at, created_by) VALUES ('3', '2', '111', '1000.00', 'ค่าน้ำมัน', 'receipt_111_68f50fe35d3cb_1760890851.png', '2025-10-19 23:20:51', '5');
INSERT INTO receipt_numbers (id, expense_id, receipt_number, amount, description, receipt_image, created_at, created_by) VALUES ('4', '2', '222', '750.00', 'ค่าน้ำมัน', 'receipt_222_68f50fe386190_1760890851.png', '2025-10-19 23:20:51', '5');
INSERT INTO receipt_numbers (id, expense_id, receipt_number, amount, description, receipt_image, created_at, created_by) VALUES ('5', '2', '333', '750.00', 'ค่าน้ำมัน', 'receipt_333_68f50fe3aad72_1760890851.png', '2025-10-19 23:20:51', '5');
INSERT INTO receipt_numbers (id, expense_id, receipt_number, amount, description, receipt_image, created_at, created_by) VALUES ('6', '3', '111', '1000.00', 'ค่าใช้จ่ายอื่นๆ', 'receipt_111_68f5117441350_1760891252.png', '2025-10-19 23:27:33', '5');
INSERT INTO receipt_numbers (id, expense_id, receipt_number, amount, description, receipt_image, created_at, created_by) VALUES ('7', '3', '222', '800.00', 'ค่าใช้จ่ายอื่นๆ', 'receipt_222_68f51174664b3_1760891252.png', '2025-10-19 23:27:33', '5');
INSERT INTO receipt_numbers (id, expense_id, receipt_number, amount, description, receipt_image, created_at, created_by) VALUES ('8', '3', '333', '1200.00', 'ค่าใช้จ่ายอื่นๆ', 'receipt_333_68f51174756a7_1760891252.png', '2025-10-19 23:27:33', '5');
INSERT INTO receipt_numbers (id, expense_id, receipt_number, amount, description, receipt_image, created_at, created_by) VALUES ('9', '3', '444', '1500.00', 'จ่ายชอร์', 'receipt_444_68f511754c81a_1760891253.png', '2025-10-19 23:27:33', '5');
INSERT INTO receipt_numbers (id, expense_id, receipt_number, amount, description, receipt_image, created_at, created_by) VALUES ('10', '4', '55555', '10000.00', 'สำรองจ่ายค่าขนส่งล่วงหน้า', 'receipt_55555_68f511f2b4eca_1760891378.png', '2025-10-19 23:29:39', '5');

-- Table: expenses
CREATE TABLE `expenses` (
  `id` int NOT NULL AUTO_INCREMENT,
  `sequence` varchar(3) COLLATE utf8mb4_general_ci NOT NULL,
  `exno` varchar(15) COLLATE utf8mb4_general_ci NOT NULL,
  `bookingno` varchar(100) COLLATE utf8mb4_general_ci DEFAULT NULL,
  `job_open_date` date NOT NULL,
  `item_id` int DEFAULT NULL,
  `customer_id` int DEFAULT NULL,
  `containerno` varchar(255) COLLATE utf8mb4_general_ci DEFAULT NULL,
  `driver_id` int DEFAULT NULL,
  `vehicle_plate` varchar(20) COLLATE utf8mb4_general_ci DEFAULT NULL,
  `payment_account_no` varchar(50) COLLATE utf8mb4_general_ci DEFAULT NULL,
  `additional_details` varchar(255) COLLATE utf8mb4_general_ci DEFAULT NULL,
  `requester` varchar(100) COLLATE utf8mb4_general_ci DEFAULT NULL,
  `receiver` varchar(100) COLLATE utf8mb4_general_ci DEFAULT NULL,
  `payer` varchar(100) COLLATE utf8mb4_general_ci DEFAULT NULL,
  `withdrawal_date` date NOT NULL,
  `transfer_no` varchar(100) COLLATE utf8mb4_general_ci DEFAULT NULL,
  `transfer_amount` decimal(15,2) DEFAULT '0.00',
  `total_amount` decimal(15,2) DEFAULT '0.00',
  `transfer_slip_image` varchar(255) COLLATE utf8mb4_general_ci DEFAULT NULL,
  `receipt_images` text COLLATE utf8mb4_general_ci,
  `verification_slip_image` varchar(255) COLLATE utf8mb4_general_ci DEFAULT NULL,
  `verification_amount` decimal(15,2) DEFAULT NULL,
  `verification_transfer_no` varchar(100) COLLATE utf8mb4_general_ci DEFAULT NULL,
  `verification_date` datetime DEFAULT NULL,
  `verification_by` int DEFAULT NULL,
  `batch_verification_id` varchar(50) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT 'รหัส Batch Verification',
  `reviewer_slip_image` varchar(255) COLLATE utf8mb4_general_ci DEFAULT NULL,
  `reviewer_amount` decimal(15,2) DEFAULT NULL,
  `reviewer_transfer_no` varchar(100) COLLATE utf8mb4_general_ci DEFAULT NULL,
  `reviewer_date` datetime DEFAULT NULL,
  `reviewer_by` int DEFAULT NULL,
  `batch_review_id` varchar(50) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT 'รหัส Batch Review',
  `rejection_reason` text COLLATE utf8mb4_general_ci,
  `rejection_date` datetime DEFAULT NULL,
  `rejected_by` int DEFAULT NULL,
  `return_reason` text COLLATE utf8mb4_general_ci,
  `return_date` datetime DEFAULT NULL,
  `returned_by` int DEFAULT NULL,
  `workflow_history` json DEFAULT NULL,
  `status` enum('open','pending','success','rejected','returned') COLLATE utf8mb4_general_ci NOT NULL DEFAULT 'open',
  `is_batch_processed` tinyint(1) DEFAULT '0' COMMENT 'ประมวลผลแบบ Batch หรือไม่',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `created_by` int NOT NULL,
  `batch_notes` text COLLATE utf8mb4_general_ci COMMENT 'หมายเหตุจาก Batch Processing',
  `bulk_operation_batch_id` varchar(100) COLLATE utf8mb4_general_ci DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `exno` (`exno`),
  KEY `item_id` (`item_id`),
  KEY `customer_id` (`customer_id`),
  KEY `driver_id` (`driver_id`),
  KEY `idx_exno` (`exno`),
  KEY `idx_job_open_date` (`job_open_date`),
  KEY `idx_withdrawal_date` (`withdrawal_date`),
  KEY `idx_status` (`status`),
  KEY `idx_created_by` (`created_by`),
  KEY `idx_expenses_status_rejection` (`status`,`rejection_date`),
  KEY `idx_expenses_status_return` (`status`,`return_date`),
  KEY `idx_expenses_rejected_by` (`rejected_by`),
  KEY `idx_expenses_returned_by` (`returned_by`),
  KEY `idx_batch_verification` (`batch_verification_id`),
  KEY `idx_batch_review` (`batch_review_id`),
  KEY `idx_batch_processed` (`is_batch_processed`),
  KEY `idx_status_batch` (`status`,`is_batch_processed`),
  CONSTRAINT `expenses_ibfk_1` FOREIGN KEY (`item_id`) REFERENCES `items` (`id`),
  CONSTRAINT `expenses_ibfk_2` FOREIGN KEY (`customer_id`) REFERENCES `customers` (`id`),
  CONSTRAINT `expenses_ibfk_3` FOREIGN KEY (`driver_id`) REFERENCES `drivers` (`id`),
  CONSTRAINT `expenses_ibfk_4` FOREIGN KEY (`created_by`) REFERENCES `users` (`id`),
  CONSTRAINT `fk_batch_review` FOREIGN KEY (`batch_review_id`) REFERENCES `batch_operations` (`batch_id`) ON DELETE SET NULL,
  CONSTRAINT `fk_batch_verification` FOREIGN KEY (`batch_verification_id`) REFERENCES `batch_operations` (`batch_id`) ON DELETE SET NULL,
  CONSTRAINT `fk_expenses_rejected_by` FOREIGN KEY (`rejected_by`) REFERENCES `users` (`id`) ON DELETE SET NULL,
  CONSTRAINT `fk_expenses_returned_by` FOREIGN KEY (`returned_by`) REFERENCES `users` (`id`) ON DELETE SET NULL
) ENGINE=InnoDB AUTO_INCREMENT=5 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- Data for table: expenses
INSERT INTO expenses (id, sequence, exno, bookingno, job_open_date, item_id, customer_id, containerno, driver_id, vehicle_plate, payment_account_no, additional_details, requester, receiver, payer, withdrawal_date, transfer_no, transfer_amount, total_amount, transfer_slip_image, receipt_images, verification_slip_image, verification_amount, verification_transfer_no, verification_date, verification_by, batch_verification_id, reviewer_slip_image, reviewer_amount, reviewer_transfer_no, reviewer_date, reviewer_by, batch_review_id, rejection_reason, rejection_date, rejected_by, return_reason, return_date, returned_by, workflow_history, status, is_batch_processed, created_at, updated_at, created_by, batch_notes, bulk_operation_batch_id) VALUES ('1', '001', '********-001', 'ทดสอบ', '2025-10-18', '30', '91', 'ทดสอบ', '32', '71-1962', '', '', 'คำไพ', 'คำไพ', 'deaw', '2025-10-18', '1111111', '2000.00', '2000.00', 'transfer_68f50f7642f9c_1760890742.png', '[{\"filename\":\"receipt_111_68f50f76a50da_1760890742.png\",\"receipt_no\":\"111\"},{\"filename\":\"receipt_222_68f50f77372ba_1760890743.png\",\"receipt_no\":\"222\"}]', 'verification_68f5123af1a0e_1760891450.png', '2000.00', '123456', '2025-10-19 23:30:51', '3', NULL, 'batch_documents/review/BATCH_REV_********_175531_review_68f526245e645_1760896548.png', '2000.00', '4000', '2025-10-20 00:55:48', '4', 'BATCH_REV_********_175531', NULL, NULL, NULL, NULL, NULL, NULL, NULL, 'success', '0', '2025-10-19 23:19:03', '2025-10-20 00:55:48', '5', 'Processed via batch review', NULL);
INSERT INTO expenses (id, sequence, exno, bookingno, job_open_date, item_id, customer_id, containerno, driver_id, vehicle_plate, payment_account_no, additional_details, requester, receiver, payer, withdrawal_date, transfer_no, transfer_amount, total_amount, transfer_slip_image, receipt_images, verification_slip_image, verification_amount, verification_transfer_no, verification_date, verification_by, batch_verification_id, reviewer_slip_image, reviewer_amount, reviewer_transfer_no, reviewer_date, reviewer_by, batch_review_id, rejection_reason, rejection_date, rejected_by, return_reason, return_date, returned_by, workflow_history, status, is_batch_processed, created_at, updated_at, created_by, batch_notes, bulk_operation_batch_id) VALUES ('2', '002', '********-002', '', '2025-10-19', '24', NULL, '', '31', '70-2811', '4-1221-7187-0', '', 'จำเนียน', 'จำเนียน', 'deaw', '2025-10-19', '222222', '2500.00', '2500.00', 'transfer_68f50fe332ea5_1760890851.png', '[{\"filename\":\"receipt_111_68f50fe35d3cb_1760890851.png\",\"receipt_no\":\"111\"},{\"filename\":\"receipt_222_68f50fe386190_1760890851.png\",\"receipt_no\":\"222\"},{\"filename\":\"receipt_333_68f50fe3aad72_1760890851.png\",\"receipt_no\":\"333\"}]', 'batch_documents/verification/BATCH_VER_********_175337_verification_68f525b60452f_1760896438.png', '2500.00', '123', '2025-10-20 00:53:58', '3', 'BATCH_VER_********_175337', 'batch_documents/review/BATCH_REV_********_175531_review_68f526245e645_1760896548.png', '2500.00', '4000', '2025-10-20 00:55:48', '4', 'BATCH_REV_********_175531', NULL, NULL, NULL, NULL, NULL, NULL, NULL, 'success', '1', '2025-10-19 23:20:51', '2025-10-20 00:55:48', '5', 'Processed via batch review', NULL);
INSERT INTO expenses (id, sequence, exno, bookingno, job_open_date, item_id, customer_id, containerno, driver_id, vehicle_plate, payment_account_no, additional_details, requester, receiver, payer, withdrawal_date, transfer_no, transfer_amount, total_amount, transfer_slip_image, receipt_images, verification_slip_image, verification_amount, verification_transfer_no, verification_date, verification_by, batch_verification_id, reviewer_slip_image, reviewer_amount, reviewer_transfer_no, reviewer_date, reviewer_by, batch_review_id, rejection_reason, rejection_date, rejected_by, return_reason, return_date, returned_by, workflow_history, status, is_batch_processed, created_at, updated_at, created_by, batch_notes, bulk_operation_batch_id) VALUES ('3', '003', '********-003', 'BL123456789', '2025-10-19', '40', '24', 'CO1232456789', '8', '68-9280', '**********-Kbank', '', 'วชิกร ตุ้มทอง', 'วชิกร ตุ้มทอง', 'deaw', '2025-10-19', '333333', '4500.00', '4500.00', 'transfer_68f511741764b_1760891252.png', '[{\"filename\":\"receipt_111_68f5117441350_1760891252.png\",\"receipt_no\":\"111\"},{\"filename\":\"receipt_222_68f51174664b3_1760891252.png\",\"receipt_no\":\"222\"},{\"filename\":\"receipt_333_68f51174756a7_1760891252.png\",\"receipt_no\":\"333\"},{\"filename\":\"receipt_444_68f511754c81a_1760891253.png\",\"receipt_no\":\"444\"}]', 'batch_documents/verification/BATCH_VER_********_175337_verification_68f525b60452f_1760896438.png', '4500.00', '123', '2025-10-20 00:53:58', '3', 'BATCH_VER_********_175337', 'batch_documents/review/BATCH_REV_********_175439_review_68f526066e0c2_1760896518.png', '4500.00', '12345', '2025-10-20 00:55:18', '4', 'BATCH_REV_********_175439', NULL, NULL, NULL, NULL, NULL, NULL, NULL, 'success', '1', '2025-10-19 23:27:33', '2025-10-20 00:55:18', '5', 'Processed via batch review', NULL);
INSERT INTO expenses (id, sequence, exno, bookingno, job_open_date, item_id, customer_id, containerno, driver_id, vehicle_plate, payment_account_no, additional_details, requester, receiver, payer, withdrawal_date, transfer_no, transfer_amount, total_amount, transfer_slip_image, receipt_images, verification_slip_image, verification_amount, verification_transfer_no, verification_date, verification_by, batch_verification_id, reviewer_slip_image, reviewer_amount, reviewer_transfer_no, reviewer_date, reviewer_by, batch_review_id, rejection_reason, rejection_date, rejected_by, return_reason, return_date, returned_by, workflow_history, status, is_batch_processed, created_at, updated_at, created_by, batch_notes, bulk_operation_batch_id) VALUES ('4', '004', '********-004', 'BL987654321', '2025-10-19', '29', '59', 'CO987654321', '5', '75-0792', '**********-TTB', '', 'เสน่ห์', 'เสน่ห์', 'deaw', '2025-10-19', '444444', '10000.00', '10000.00', 'transfer_68f511f266993_1760891378.png', '[{\"filename\":\"receipt_55555_68f511f2b4eca_1760891378.png\",\"receipt_no\":\"55555\"}]', 'batch_documents/verification/BATCH_VER_********_175337_verification_68f525b60452f_1760896438.png', '10000.00', '123', '2025-10-20 00:53:58', '3', 'BATCH_VER_********_175337', 'batch_documents/review/BATCH_REV_********_175439_review_68f526066e0c2_1760896518.png', '10000.00', '12345', '2025-10-20 00:55:18', '4', 'BATCH_REV_********_175439', NULL, NULL, NULL, NULL, NULL, NULL, NULL, 'success', '1', '2025-10-19 23:29:39', '2025-10-20 00:55:18', '5', 'Processed via batch review', NULL);

-- Table: activity_logs
CREATE TABLE `activity_logs` (
  `id` int NOT NULL AUTO_INCREMENT,
  `user_id` int NOT NULL,
  `action_type` enum('create','update','delete','status_change','login','logout') COLLATE utf8mb4_general_ci NOT NULL,
  `table_name` varchar(50) COLLATE utf8mb4_general_ci DEFAULT NULL,
  `record_id` int DEFAULT NULL,
  `old_values` text COLLATE utf8mb4_general_ci,
  `new_values` text COLLATE utf8mb4_general_ci,
  `description` text COLLATE utf8mb4_general_ci,
  `ip_address` varchar(45) COLLATE utf8mb4_general_ci DEFAULT NULL,
  `user_agent` text COLLATE utf8mb4_general_ci,
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_action_type` (`action_type`),
  KEY `idx_created_at` (`created_at`),
  CONSTRAINT `activity_logs_ibfk_1` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=20 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- Data for table: activity_logs
INSERT INTO activity_logs (id, user_id, action_type, table_name, record_id, old_values, new_values, description, ip_address, user_agent, created_at) VALUES ('1', '1', 'logout', NULL, NULL, NULL, NULL, 'User logged out', '::1', 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/26.0.1 Safari/605.1.15', '2025-10-19 23:17:08');
INSERT INTO activity_logs (id, user_id, action_type, table_name, record_id, old_values, new_values, description, ip_address, user_agent, created_at) VALUES ('2', '5', 'login', NULL, NULL, NULL, NULL, 'User logged in', '::1', 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/26.0.1 Safari/605.1.15', '2025-10-19 23:17:18');
INSERT INTO activity_logs (id, user_id, action_type, table_name, record_id, old_values, new_values, description, ip_address, user_agent, created_at) VALUES ('3', '5', 'create', 'expenses', '1', NULL, NULL, 'Created new expense: ********-001 with 2 receipts, total amount: 2,000.00', '::1', 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/26.0.1 Safari/605.1.15', '2025-10-19 23:19:03');
INSERT INTO activity_logs (id, user_id, action_type, table_name, record_id, old_values, new_values, description, ip_address, user_agent, created_at) VALUES ('4', '5', 'create', 'expenses', '2', NULL, NULL, 'Created new expense: ********-002 with 3 receipts, total amount: 2,500.00', '::1', 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/26.0.1 Safari/605.1.15', '2025-10-19 23:20:51');
INSERT INTO activity_logs (id, user_id, action_type, table_name, record_id, old_values, new_values, description, ip_address, user_agent, created_at) VALUES ('5', '5', 'create', 'expenses', '3', NULL, NULL, 'Created new expense: ********-003 with 4 receipts, total amount: 4,500.00', '::1', 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/26.0.1 Safari/605.1.15', '2025-10-19 23:27:33');
INSERT INTO activity_logs (id, user_id, action_type, table_name, record_id, old_values, new_values, description, ip_address, user_agent, created_at) VALUES ('6', '5', 'create', 'expenses', '4', NULL, NULL, 'Created new expense: ********-004 with 1 receipts, total amount: 10,000.00', '::1', 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/26.0.1 Safari/605.1.15', '2025-10-19 23:29:39');
INSERT INTO activity_logs (id, user_id, action_type, table_name, record_id, old_values, new_values, description, ip_address, user_agent, created_at) VALUES ('7', '5', 'logout', NULL, NULL, NULL, NULL, 'User logged out', '::1', 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/26.0.1 Safari/605.1.15', '2025-10-19 23:29:47');
INSERT INTO activity_logs (id, user_id, action_type, table_name, record_id, old_values, new_values, description, ip_address, user_agent, created_at) VALUES ('8', '3', 'login', NULL, NULL, NULL, NULL, 'User logged in', '::1', 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/26.0.1 Safari/605.1.15', '2025-10-19 23:30:02');
INSERT INTO activity_logs (id, user_id, action_type, table_name, record_id, old_values, new_values, description, ip_address, user_agent, created_at) VALUES ('9', '3', 'update', 'expenses', '1', NULL, NULL, 'Submitted verification: Amount 2,000.00 บาท, Slip uploaded', '::1', 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/26.0.1 Safari/605.1.15', '2025-10-19 23:30:51');
INSERT INTO activity_logs (id, user_id, action_type, table_name, record_id, old_values, new_values, description, ip_address, user_agent, created_at) VALUES ('10', '3', 'status_change', 'expenses', '1', '{\"status\":\"open\"}', '{\"status\":\"pending\",\"comment\":\"\",\"admin_override\":false}', 'Changed status from \'open\' to \'pending\'', '::1', 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/26.0.1 Safari/605.1.15', '2025-10-19 23:31:02');
INSERT INTO activity_logs (id, user_id, action_type, table_name, record_id, old_values, new_values, description, ip_address, user_agent, created_at) VALUES ('11', '1', 'login', NULL, NULL, NULL, NULL, 'User logged in', '::1', 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-10-19 23:37:14');
INSERT INTO activity_logs (id, user_id, action_type, table_name, record_id, old_values, new_values, description, ip_address, user_agent, created_at) VALUES ('12', '3', 'logout', NULL, NULL, NULL, NULL, 'User logged out', '::1', 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/26.0.1 Safari/605.1.15', '2025-10-20 00:54:20');
INSERT INTO activity_logs (id, user_id, action_type, table_name, record_id, old_values, new_values, description, ip_address, user_agent, created_at) VALUES ('13', '4', 'login', NULL, NULL, NULL, NULL, 'User logged in', '::1', 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/26.0.1 Safari/605.1.15', '2025-10-20 00:54:28');
INSERT INTO activity_logs (id, user_id, action_type, table_name, record_id, old_values, new_values, description, ip_address, user_agent, created_at) VALUES ('14', '1', 'logout', NULL, NULL, NULL, NULL, 'User logged out', '::1', 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/26.0.1 Safari/605.1.15', '2025-10-20 01:33:00');
INSERT INTO activity_logs (id, user_id, action_type, table_name, record_id, old_values, new_values, description, ip_address, user_agent, created_at) VALUES ('15', '3', 'login', NULL, NULL, NULL, NULL, 'User logged in', '::1', 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/26.0.1 Safari/605.1.15', '2025-10-20 01:33:09');
INSERT INTO activity_logs (id, user_id, action_type, table_name, record_id, old_values, new_values, description, ip_address, user_agent, created_at) VALUES ('16', '1', 'logout', NULL, NULL, NULL, NULL, 'User logged out', '::1', 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/26.0.1 Safari/605.1.15', '2025-10-20 01:39:30');
INSERT INTO activity_logs (id, user_id, action_type, table_name, record_id, old_values, new_values, description, ip_address, user_agent, created_at) VALUES ('17', '3', 'login', NULL, NULL, NULL, NULL, 'User logged in', '::1', 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/26.0.1 Safari/605.1.15', '2025-10-20 01:39:39');
INSERT INTO activity_logs (id, user_id, action_type, table_name, record_id, old_values, new_values, description, ip_address, user_agent, created_at) VALUES ('18', '3', 'logout', NULL, NULL, NULL, NULL, 'User logged out', '::1', 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/26.0.1 Safari/605.1.15', '2025-10-20 01:41:36');
INSERT INTO activity_logs (id, user_id, action_type, table_name, record_id, old_values, new_values, description, ip_address, user_agent, created_at) VALUES ('19', '1', 'login', NULL, NULL, NULL, NULL, 'User logged in', '::1', 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/26.0.1 Safari/605.1.15', '2025-10-20 01:41:43');

