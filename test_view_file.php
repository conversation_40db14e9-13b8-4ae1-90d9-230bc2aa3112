<?php
session_start();

// Simulate logged in user for testing
if (!isset($_SESSION['user_id'])) {
    $_SESSION['user_id'] = 1;
    $_SESSION['role'] = 'admin';
    $_SESSION['username'] = 'test_user';
}

echo "<h2>Test View File System</h2>";

// Test parameters
$test_file = $_GET['file'] ?? 'transfer_68fb62282cbe1_1761305128.jpg';
$test_type = $_GET['type'] ?? 'transfer_slip';

echo "<h3>Testing File: $test_file</h3>";
echo "<h3>Type: $test_type</h3>";

// Check file path logic
$upload_dir = 'uploads/';
$file_path = $upload_dir . 'transfer_slips/' . $test_file;

echo "<h3>File Path Analysis:</h3>";
echo "<ul>";
echo "<li><strong>Upload directory:</strong> $upload_dir</li>";
echo "<li><strong>Full file path:</strong> $file_path</li>";
echo "<li><strong>File exists:</strong> " . (file_exists($file_path) ? '✅ YES' : '❌ NO') . "</li>";

if (file_exists($file_path)) {
    echo "<li><strong>File size:</strong> " . filesize($file_path) . " bytes</li>";
    echo "<li><strong>File readable:</strong> " . (is_readable($file_path) ? '✅ YES' : '❌ NO') . "</li>";
    echo "<li><strong>File permissions:</strong> " . substr(sprintf('%o', fileperms($file_path)), -4) . "</li>";
    echo "<li><strong>MIME type:</strong> " . mime_content_type($file_path) . "</li>";
} else {
    echo "<li><strong>Directory exists:</strong> " . (is_dir(dirname($file_path)) ? '✅ YES' : '❌ NO') . "</li>";
    echo "<li><strong>Directory readable:</strong> " . (is_readable(dirname($file_path)) ? '✅ YES' : '❌ NO') . "</li>";
}
echo "</ul>";

// Test different files
echo "<h3>Test Available Files:</h3>";
$transfer_files = glob('uploads/transfer_slips/*');
if ($transfer_files) {
    echo "<ul>";
    foreach (array_slice($transfer_files, 0, 10) as $file) {
        $filename = basename($file);
        $size = filesize($file);
        $modified = date('Y-m-d H:i:s', filemtime($file));
        
        echo "<li>";
        echo "<strong>$filename</strong> ($size bytes, modified: $modified)";
        echo " - <a href='api/view_file.php?file=" . urlencode($filename) . "&type=transfer_slip' target='_blank'>Test View</a>";
        echo " - <a href='?file=" . urlencode($filename) . "&type=transfer_slip'>Debug This</a>";
        echo "</li>";
    }
    echo "</ul>";
} else {
    echo "<p>No files found in uploads/transfer_slips/</p>";
}

// Test the actual view_file.php logic
echo "<h3>Simulate view_file.php Logic:</h3>";

$file = basename($test_file);
if (!preg_match('/^[a-zA-Z0-9_\-\.]+$/', $file)) {
    echo "<p style='color: red;'>❌ Filename validation failed</p>";
} else {
    echo "<p style='color: green;'>✅ Filename validation passed</p>";
}

$upload_dir = '../uploads/';
$file_path = $upload_dir . 'transfer_slips/' . $file;

echo "<p><strong>Calculated path:</strong> $file_path</p>";
echo "<p><strong>File exists:</strong> " . (file_exists($file_path) ? '✅ YES' : '❌ NO') . "</p>";

if (file_exists($file_path)) {
    $file_info = pathinfo($file_path);
    $file_extension = strtolower($file_info['extension']);
    
    $content_types = [
        'jpg' => 'image/jpeg',
        'jpeg' => 'image/jpeg',
        'png' => 'image/png',
        'gif' => 'image/gif',
        'pdf' => 'application/pdf'
    ];
    
    $content_type = $content_types[$file_extension] ?? 'application/octet-stream';
    
    echo "<p><strong>Content type:</strong> $content_type</p>";
    echo "<p><strong>File extension:</strong> $file_extension</p>";
    
    // Try to display the image
    echo "<h4>Image Preview:</h4>";
    $test_url = "api/view_file.php?file=" . urlencode($file) . "&type=transfer_slip";
    echo "<img src='$test_url' style='max-width: 300px; border: 1px solid #ccc;' alt='Test Image' onerror=\"this.style.display='none'; this.nextSibling.style.display='block';\">";
    echo "<div style='display: none; color: red; border: 1px solid red; padding: 10px;'>❌ Failed to load image</div>";
    
    echo "<p><a href='$test_url' target='_blank'>Open in new tab</a></p>";
}

// Check session
echo "<h3>Session Information:</h3>";
echo "<ul>";
echo "<li><strong>User ID:</strong> " . ($_SESSION['user_id'] ?? 'Not set') . "</li>";
echo "<li><strong>Role:</strong> " . ($_SESSION['role'] ?? 'Not set') . "</li>";
echo "<li><strong>Username:</strong> " . ($_SESSION['username'] ?? 'Not set') . "</li>";
echo "</ul>";

// Check directory permissions
echo "<h3>Directory Permissions:</h3>";
$dirs_to_check = [
    'uploads/',
    'uploads/transfer_slips/',
    'uploads/receipts/',
    'api/'
];

foreach ($dirs_to_check as $dir) {
    echo "<p><strong>$dir:</strong> ";
    if (is_dir($dir)) {
        echo "✅ EXISTS";
        echo " | Readable: " . (is_readable($dir) ? '✅' : '❌');
        echo " | Writable: " . (is_writable($dir) ? '✅' : '❌');
        echo " | Permissions: " . substr(sprintf('%o', fileperms($dir)), -4);
    } else {
        echo "❌ NOT FOUND";
    }
    echo "</p>";
}

// Test form to try different files
echo "<h3>Test Different Files:</h3>";
echo "<form method='GET'>";
echo "<label>File: <input type='text' name='file' value='$test_file' style='width: 300px;'></label><br><br>";
echo "<label>Type: ";
echo "<select name='type'>";
$types = ['transfer_slip', 'receipt', 'verification_slip', 'reviewer_slip'];
foreach ($types as $type) {
    $selected = ($type === $test_type) ? 'selected' : '';
    echo "<option value='$type' $selected>$type</option>";
}
echo "</select>";
echo "</label><br><br>";
echo "<input type='submit' value='Test File'>";
echo "</form>";
?>
