<?php
session_start();
require_once 'config/database.php';
require_once 'includes/functions.php';

// Check if user is logged in
if (!isset($_SESSION['user_id'])) {
    header('Location: login.php');
    exit();
}

$database = new Database();
$db = $database->getConnection();

// Get user info
$stmt = $db->prepare("SELECT * FROM users WHERE id = ?");
$stmt->execute([$_SESSION['user_id']]);
$user = $stmt->fetch();

if (!$user || !$user['is_active']) {
    session_destroy();
    header('Location: login.php');
    exit();
}

$success = '';
$error = '';

// Handle form submission
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    try {
        $action = $_POST['action'] ?? '';
        
        if ($action === 'update_profile') {
            // Update profile information
            $full_name = trim($_POST['full_name'] ?? '');
            $email = trim($_POST['email'] ?? '');
            
            // Validation
            if (empty($full_name) || empty($email)) {
                throw new Exception('Full name and email are required.');
            }
            
            if (!filter_var($email, FILTER_VALIDATE_EMAIL)) {
                throw new Exception('Invalid email format.');
            }
            
            // Check if email already exists for other users
            $stmt = $db->prepare("SELECT id FROM users WHERE email = ? AND id != ?");
            $stmt->execute([$email, $user['id']]);
            if ($stmt->fetch()) {
                throw new Exception('Email already exists for another user.');
            }
            
            // Update user profile
            $stmt = $db->prepare("UPDATE users SET full_name = ?, email = ?, updated_at = NOW() WHERE id = ?");
            $stmt->execute([$full_name, $email, $user['id']]);
            
            // Log activity
            logActivity(
                $db,
                $user['id'],
                'update',
                'users',
                $user['id'],
                "Updated profile information",
                $_SERVER['REMOTE_ADDR'],
                $_SERVER['HTTP_USER_AGENT']
            );
            
            // Update session and user data
            $user['full_name'] = $full_name;
            $user['email'] = $email;
            $_SESSION['full_name'] = $full_name;
            
            $success = 'Profile updated successfully!';
        }

    } catch (Exception $e) {
        $error = $e->getMessage();
    }
}

// Get user statistics
$stmt = $db->prepare("SELECT COUNT(*) as count FROM expenses WHERE created_by = ?");
$stmt->execute([$user['id']]);
$expense_count = $stmt->fetch()['count'];

$stmt = $db->prepare("SELECT COUNT(*) as count FROM activity_logs WHERE user_id = ?");
$stmt->execute([$user['id']]);
$activity_count = $stmt->fetch()['count'];

// Get recent activities
$stmt = $db->prepare("
    SELECT action_type, table_name, description, created_at 
    FROM activity_logs 
    WHERE user_id = ? 
    ORDER BY created_at DESC 
    LIMIT 10
");
$stmt->execute([$user['id']]);
$recent_activities = $stmt->fetchAll();

// Role information
$role_info = [
    'data_entry' => [
        'name' => 'Data Entry',
        'description' => 'Can create and edit own expense records',
        'color' => 'secondary'
    ],
    'verification' => [
        'name' => 'Verification',
        'description' => 'Can review and change status from open to pending',
        'color' => 'info'
    ],
    'reviewer' => [
        'name' => 'Reviewer',
        'description' => 'Can approve expenses and manage master data',
        'color' => 'warning'
    ],
    'report_viewer' => [
        'name' => 'Report Viewer',
        'description' => 'Read-only access to reports and data viewing',
        'color' => 'primary'
    ],
    'administrator' => [
        'name' => 'Administrator',
        'description' => 'Full system access and user management',
        'color' => 'danger'
    ]
];
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>User Profile - Expense Management System</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="assets/css/style.css" rel="stylesheet">
</head>
<body>
    <!-- Navigation -->
    <!-- Navigation -->
    <?php include 'includes/navbar.php'; ?>

    <div class="container-fluid mt-4">
        <!-- Page Header -->
        <div class="row mb-4">
            <div class="col">
                <h2><i class="fas fa-user-circle me-2"></i>User Profile</h2>
                <p class="text-muted">Manage your account information and settings</p>
            </div>
        </div>

        <!-- Success/Error Messages -->
        <?php if ($success): ?>
            <div class="alert alert-success alert-dismissible fade show" role="alert">
                <i class="fas fa-check-circle me-2"></i><?php echo $success; ?>
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        <?php endif; ?>

        <?php if ($error): ?>
            <div class="alert alert-danger alert-dismissible fade show" role="alert">
                <i class="fas fa-exclamation-circle me-2"></i><?php echo $error; ?>
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        <?php endif; ?>

        <div class="row">
            <!-- Profile Information -->
            <div class="col-md-8">
                <!-- Profile Details Card -->
                <div class="card mb-4">
                    <div class="card-header">
                        <h5 class="mb-0"><i class="fas fa-user me-2"></i>Profile Information</h5>
                    </div>
                    <div class="card-body">
                        <form method="POST">
                            <input type="hidden" name="action" value="update_profile">
                            
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="username" class="form-label">Username</label>
                                        <input type="text" class="form-control" id="username"
                                               value="<?php echo htmlspecialchars($user['username'] ?? ''); ?>" readonly>
                                        <div class="form-text">Username cannot be changed</div>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="role" class="form-label">Role</label>
                                        <div class="input-group">
                                            <input type="text" class="form-control" id="role"
                                                   value="<?php echo htmlspecialchars($role_info[$user['role']]['name'] ?? ''); ?>" readonly>
                                            <span class="input-group-text">
                                                <span class="badge bg-<?php echo $role_info[$user['role']]['color']; ?>">
                                                    <?php echo strtoupper($user['role']); ?>
                                                </span>
                                            </span>
                                        </div>
                                        <div class="form-text"><?php echo htmlspecialchars($role_info[$user['role']]['description'] ?? ''); ?></div>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="full_name" class="form-label">Full Name <span class="text-danger">*</span></label>
                                        <input type="text" class="form-control" id="full_name" name="full_name"
                                               value="<?php echo htmlspecialchars($user['full_name'] ?? ''); ?>" required>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="email" class="form-label">Email <span class="text-danger">*</span></label>
                                        <input type="email" class="form-control" id="email" name="email"
                                               value="<?php echo htmlspecialchars($user['email'] ?? ''); ?>" required>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="created_at" class="form-label">Member Since</label>
                                        <input type="text" class="form-control" id="created_at" 
                                               value="<?php echo date('M d, Y', strtotime($user['created_at'])); ?>" readonly>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="updated_at" class="form-label">Last Updated</label>
                                        <input type="text" class="form-control" id="updated_at" 
                                               value="<?php echo $user['updated_at'] ? date('M d, Y H:i', strtotime($user['updated_at'])) : 'Never'; ?>" readonly>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="d-flex justify-content-end">
                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-save me-1"></i>Update Profile
                                </button>
                            </div>
                        </form>
                    </div>
                </div>

                <!-- Password & Security Card -->
                <div class="card mb-4">
                    <div class="card-header">
                        <h5 class="mb-0"><i class="fas fa-shield-alt me-2"></i>Password & Security</h5>
                    </div>
                    <div class="card-body">
                        <div class="d-flex justify-content-between align-items-center mb-3">
                            <div>
                                <h6 class="mb-1">Password</h6>
                                <p class="text-muted mb-0">
                                    Last updated: <?php echo date('M d, Y', strtotime($user['updated_at'] ?? $user['created_at'])); ?>
                                </p>
                            </div>
                            <div>
                                <a href="profile/change_password.php" class="btn btn-warning">
                                    <i class="fas fa-key me-2"></i>Change Password
                                </a>
                            </div>
                        </div>

                        <div class="row text-center">
                            <div class="col-md-4">
                                <div class="mb-2">
                                    <i class="fas fa-shield-alt fa-2x text-success"></i>
                                </div>
                                <h6 class="mb-1">Secure</h6>
                                <small class="text-muted">Password Protected</small>
                            </div>
                            <div class="col-md-4">
                                <div class="mb-2">
                                    <i class="fas fa-clock fa-2x text-info"></i>
                                </div>
                                <h6 class="mb-1">Session</h6>
                                <small class="text-muted">Auto Logout</small>
                            </div>
                            <div class="col-md-4">
                                <div class="mb-2">
                                    <i class="fas fa-history fa-2x text-warning"></i>
                                </div>
                                <h6 class="mb-1">Activity</h6>
                                <small class="text-muted">Logged & Tracked</small>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Sidebar -->
            <div class="col-md-4">
                <!-- User Statistics -->
                <div class="card mb-4">
                    <div class="card-header">
                        <h5 class="mb-0"><i class="fas fa-chart-pie me-2"></i>Your Statistics</h5>
                    </div>
                    <div class="card-body">
                        <div class="row text-center">
                            <div class="col-6">
                                <div class="border-end">
                                    <h3 class="text-primary mb-1"><?php echo $expense_count; ?></h3>
                                    <small class="text-muted">Expenses Created</small>
                                </div>
                            </div>
                            <div class="col-6">
                                <h3 class="text-success mb-1"><?php echo $activity_count; ?></h3>
                                <small class="text-muted">Total Activities</small>
                            </div>
                        </div>

                        <hr>

                        <div class="d-flex justify-content-between align-items-center mb-2">
                            <span class="text-muted">Account Status:</span>
                            <span class="badge bg-success">Active</span>
                        </div>

                        <div class="d-flex justify-content-between align-items-center">
                            <span class="text-muted">Role Level:</span>
                            <span class="badge bg-<?php echo $role_info[$user['role']]['color']; ?>">
                                <?php echo $role_info[$user['role']]['name']; ?>
                            </span>
                        </div>
                    </div>
                </div>

                <!-- Recent Activities -->
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0"><i class="fas fa-history me-2"></i>Recent Activities</h5>
                    </div>
                    <div class="card-body">
                        <?php if (empty($recent_activities)): ?>
                            <p class="text-muted text-center mb-0">No recent activities</p>
                        <?php else: ?>
                            <div class="activity-list">
                                <?php foreach ($recent_activities as $activity): ?>
                                    <div class="d-flex align-items-start mb-3">
                                        <div class="flex-shrink-0 me-2">
                                            <span class="badge bg-<?php
                                                echo $activity['action_type'] === 'create' ? 'success' :
                                                    ($activity['action_type'] === 'update' ? 'warning' :
                                                    ($activity['action_type'] === 'delete' ? 'danger' : 'info'));
                                            ?> rounded-pill">
                                                <i class="fas fa-<?php
                                                    echo $activity['action_type'] === 'create' ? 'plus' :
                                                        ($activity['action_type'] === 'update' ? 'edit' :
                                                        ($activity['action_type'] === 'delete' ? 'trash' : 'info'));
                                                ?>"></i>
                                            </span>
                                        </div>
                                        <div class="flex-grow-1">
                                            <div class="small">
                                                <strong><?php echo ucfirst(str_replace('_', ' ', $activity['action_type'])); ?></strong>
                                                in <?php echo htmlspecialchars($activity['table_name'] ?? 'Unknown'); ?>
                                            </div>
                                            <div class="text-muted small">
                                                <?php echo htmlspecialchars($activity['description'] ?? 'No description available'); ?>
                                            </div>
                                            <div class="text-muted small">
                                                <?php echo date('M d, H:i', strtotime($activity['created_at'])); ?>
                                            </div>
                                        </div>
                                    </div>
                                <?php endforeach; ?>
                            </div>

                            <div class="text-center mt-3">
                                <a href="reports/activity_logs.php" class="btn btn-sm btn-outline-primary">
                                    <i class="fas fa-list me-1"></i>View All Activities
                                </a>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Toggle password visibility
        function togglePassword(fieldId) {
            const field = document.getElementById(fieldId);
            const icon = document.getElementById(fieldId + '_icon');

            if (field.type === 'password') {
                field.type = 'text';
                icon.classList.remove('fa-eye');
                icon.classList.add('fa-eye-slash');
            } else {
                field.type = 'password';
                icon.classList.remove('fa-eye-slash');
                icon.classList.add('fa-eye');
            }
        }

        // Reset password form
        function resetPasswordForm() {
            document.getElementById('passwordForm').reset();

            // Reset all password field types to password
            ['current_password', 'new_password', 'confirm_password'].forEach(fieldId => {
                const field = document.getElementById(fieldId);
                const icon = document.getElementById(fieldId + '_icon');

                field.type = 'password';
                icon.classList.remove('fa-eye-slash');
                icon.classList.add('fa-eye');
            });
        }

        // Password confirmation validation
        document.getElementById('confirm_password').addEventListener('input', function() {
            const newPassword = document.getElementById('new_password').value;
            const confirmPassword = this.value;

            if (confirmPassword && newPassword !== confirmPassword) {
                this.setCustomValidity('Passwords do not match');
                this.classList.add('is-invalid');
            } else {
                this.setCustomValidity('');
                this.classList.remove('is-invalid');
            }
        });

        // New password validation
        document.getElementById('new_password').addEventListener('input', function() {
            const confirmPassword = document.getElementById('confirm_password');

            if (confirmPassword.value && this.value !== confirmPassword.value) {
                confirmPassword.setCustomValidity('Passwords do not match');
                confirmPassword.classList.add('is-invalid');
            } else {
                confirmPassword.setCustomValidity('');
                confirmPassword.classList.remove('is-invalid');
            }
        });
    </script>
</body>
</html>
