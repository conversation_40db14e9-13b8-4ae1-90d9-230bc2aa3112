<?php
/**
 * Debug script for testing deductions and image display
 * ทดสอบการทำงานของ deductions และการแสดงรูปภาพ
 */
session_start();
require_once 'config/database.php';

// Check if user is logged in
if (!isset($_SESSION['user_id'])) {
    header('Location: index.php');
    exit();
}

try {
    $database = new Database();
    $db = $database->getConnection();
    
    echo "<h2>🔍 Deductions & Images Debug Report</h2>";
    echo "<hr>";
    
    // Test 1: Check recent expenses with deductions
    echo "<h3>📊 Recent Expenses with Deductions:</h3>";
    $stmt = $db->prepare("
        SELECT 
            e.id, e.exno, e.job_open_date,
            rn.id as receipt_id, rn.receipt_number, rn.receipt_image, rn.amount, rn.has_deductions,
            COUNT(rd.id) as deduction_count,
            SUM(rd.amount) as total_deductions
        FROM expenses e
        JOIN receipt_numbers rn ON e.id = rn.expense_id
        LEFT JOIN receipt_deductions rd ON rn.id = rd.receipt_number_id
        WHERE e.created_at >= DATE_SUB(NOW(), INTERVAL 7 DAY)
        GROUP BY e.id, rn.id
        ORDER BY e.created_at DESC
        LIMIT 10
    ");
    $stmt->execute();
    $recent_expenses = $stmt->fetchAll();
    
    if ($recent_expenses) {
        echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
        echo "<tr style='background-color: #f0f0f0;'>";
        echo "<th>Expense ID</th><th>EX No</th><th>Receipt ID</th><th>Receipt No</th>";
        echo "<th>Amount</th><th>Has Deductions</th><th>Deduction Count</th><th>Total Deductions</th>";
        echo "<th>Receipt Image</th><th>Image Status</th>";
        echo "</tr>";
        
        foreach ($recent_expenses as $expense) {
            echo "<tr>";
            echo "<td>" . $expense['id'] . "</td>";
            echo "<td>" . $expense['exno'] . "</td>";
            echo "<td>" . $expense['receipt_id'] . "</td>";
            echo "<td>" . $expense['receipt_number'] . "</td>";
            echo "<td>" . number_format($expense['amount'], 2) . "</td>";
            echo "<td>" . ($expense['has_deductions'] ? '✅ Yes' : '❌ No') . "</td>";
            echo "<td>" . $expense['deduction_count'] . "</td>";
            echo "<td>" . number_format($expense['total_deductions'] ?? 0, 2) . "</td>";
            echo "<td>" . ($expense['receipt_image'] ? basename($expense['receipt_image']) : 'No image') . "</td>";
            
            // Check if image file exists
            $image_status = "❌ No image";
            if ($expense['receipt_image']) {
                $image_path = 'uploads/receipts/' . $expense['receipt_image'];
                if (file_exists($image_path)) {
                    $image_status = "✅ Exists (" . round(filesize($image_path)/1024, 1) . " KB)";
                } else {
                    $image_status = "❌ Missing";
                }
            }
            echo "<td>" . $image_status . "</td>";
            echo "</tr>";
        }
        echo "</table>";
    } else {
        echo "❌ No recent expenses found<br>";
    }
    
    echo "<hr>";
    
    // Test 2: Check deduction details for latest expense
    echo "<h3>🔍 Deduction Details for Latest Expense:</h3>";
    if (!empty($recent_expenses)) {
        $latest_expense = $recent_expenses[0];
        $stmt = $db->prepare("
            SELECT 
                rd.*,
                rn.receipt_number,
                u.full_name as created_by_name
            FROM receipt_deductions rd
            JOIN receipt_numbers rn ON rd.receipt_number_id = rn.id
            LEFT JOIN users u ON rd.created_by = u.id
            WHERE rn.expense_id = ?
            ORDER BY rd.created_at DESC
        ");
        $stmt->execute([$latest_expense['id']]);
        $deductions = $stmt->fetchAll();
        
        if ($deductions) {
            echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
            echo "<tr style='background-color: #f0f0f0;'>";
            echo "<th>ID</th><th>Receipt No</th><th>Type</th><th>Amount</th>";
            echo "<th>Description</th><th>Image</th><th>Created By</th><th>Created At</th>";
            echo "</tr>";
            
            foreach ($deductions as $deduction) {
                echo "<tr>";
                echo "<td>" . $deduction['id'] . "</td>";
                echo "<td>" . $deduction['receipt_number'] . "</td>";
                echo "<td>" . $deduction['deduction_type'] . "</td>";
                echo "<td>" . number_format($deduction['amount'], 2) . "</td>";
                echo "<td>" . ($deduction['description'] ?: 'N/A') . "</td>";
                
                // Check deduction image
                $deduction_image_status = "❌ No image";
                if ($deduction['deduction_image']) {
                    $deduction_image_path = 'uploads/deductions/' . $deduction['deduction_image'];
                    if (file_exists($deduction_image_path)) {
                        $deduction_image_status = "✅ " . basename($deduction['deduction_image']);
                    } else {
                        $deduction_image_status = "❌ Missing: " . basename($deduction['deduction_image']);
                    }
                }
                echo "<td>" . $deduction_image_status . "</td>";
                echo "<td>" . ($deduction['created_by_name'] ?: 'Unknown') . "</td>";
                echo "<td>" . $deduction['created_at'] . "</td>";
                echo "</tr>";
            }
            echo "</table>";
        } else {
            echo "❌ No deductions found for latest expense<br>";
        }
    }
    
    echo "<hr>";
    
    // Test 3: Check image access
    echo "<h3>🖼️ Image Access Test:</h3>";
    if (!empty($recent_expenses)) {
        $test_expense = $recent_expenses[0];
        if ($test_expense['receipt_image']) {
            $image_url = "api/view_file.php?file=" . urlencode($test_expense['receipt_image']) . "&type=receipt";
            echo "<p>Testing image access for: " . $test_expense['receipt_image'] . "</p>";
            echo "<p>URL: <a href='{$image_url}' target='_blank'>{$image_url}</a></p>";
            echo "<p>Direct file path: uploads/receipts/" . $test_expense['receipt_image'] . "</p>";
            
            // Test if we can read the file
            $image_path = 'uploads/receipts/' . $test_expense['receipt_image'];
            if (file_exists($image_path)) {
                echo "<p>✅ File exists and is readable</p>";
                echo "<p>File size: " . round(filesize($image_path)/1024, 1) . " KB</p>";
                echo "<p>File permissions: " . substr(sprintf('%o', fileperms($image_path)), -4) . "</p>";
            } else {
                echo "<p>❌ File does not exist or is not readable</p>";
            }
        }
    }
    
    echo "<hr>";
    echo "<p><strong>Debug completed at:</strong> " . date('Y-m-d H:i:s') . "</p>";
    echo "<p><a href='expenses/list.php'>← Back to Expenses List</a></p>";
    
} catch (Exception $e) {
    echo "<div style='color: red;'>";
    echo "<h3>❌ Error occurred:</h3>";
    echo "<p>" . htmlspecialchars($e->getMessage()) . "</p>";
    echo "</div>";
}
?>
