<?php
session_start();

// Simulate logged in user for testing
if (!isset($_SESSION['user_id'])) {
    $_SESSION['user_id'] = 1;
    $_SESSION['role'] = 'admin';
    $_SESSION['username'] = 'test_user';
}

echo "<h2>Production Debug Tool</h2>";
echo "<p><strong>Server:</strong> " . $_SERVER['HTTP_HOST'] . "</p>";
echo "<p><strong>Current Time:</strong> " . date('Y-m-d H:i:s') . "</p>";

// Test specific file
$test_file = $_GET['file'] ?? 'transfer_68fb62282cbe1_1761305128.jpg';
$test_type = $_GET['type'] ?? 'transfer_slip';

echo "<h3>Testing File: $test_file</h3>";

// Check file paths
$upload_dir = 'uploads/';
$file_path = $upload_dir . 'transfer_slips/' . $test_file;

echo "<h3>File System Check:</h3>";
echo "<ul>";
echo "<li><strong>Upload directory:</strong> $upload_dir</li>";
echo "<li><strong>Full file path:</strong> $file_path</li>";
echo "<li><strong>File exists:</strong> " . (file_exists($file_path) ? '✅ YES' : '❌ NO') . "</li>";

if (file_exists($file_path)) {
    echo "<li><strong>File size:</strong> " . filesize($file_path) . " bytes</li>";
    echo "<li><strong>File readable:</strong> " . (is_readable($file_path) ? '✅ YES' : '❌ NO') . "</li>";
    echo "<li><strong>File permissions:</strong> " . substr(sprintf('%o', fileperms($file_path)), -4) . "</li>";
    echo "<li><strong>File owner:</strong> " . posix_getpwuid(fileowner($file_path))['name'] . "</li>";
    echo "<li><strong>File group:</strong> " . posix_getgrgid(filegroup($file_path))['name'] . "</li>";
    echo "<li><strong>MIME type:</strong> " . mime_content_type($file_path) . "</li>";
    echo "<li><strong>Last modified:</strong> " . date('Y-m-d H:i:s', filemtime($file_path)) . "</li>";
} else {
    // Check directory
    $dir_path = dirname($file_path);
    echo "<li><strong>Directory exists:</strong> " . (is_dir($dir_path) ? '✅ YES' : '❌ NO') . "</li>";
    echo "<li><strong>Directory readable:</strong> " . (is_readable($dir_path) ? '✅ YES' : '❌ NO') . "</li>";
    echo "<li><strong>Directory permissions:</strong> " . substr(sprintf('%o', fileperms($dir_path)), -4) . "</li>";
}
echo "</ul>";

// Test view_file.php directly
echo "<h3>Direct File Access Test:</h3>";
$test_url = "api/view_file.php?file=" . urlencode($test_file) . "&type=$test_type";
echo "<p><strong>Test URL:</strong> <a href='$test_url' target='_blank'>$test_url</a></p>";

if (file_exists($file_path)) {
    echo "<h4>Image Preview:</h4>";
    echo "<img src='$test_url' style='max-width: 400px; border: 1px solid #ccc;' alt='Test Image' onerror=\"this.style.display='none'; this.nextSibling.style.display='block';\">";
    echo "<div style='display: none; color: red; border: 1px solid red; padding: 10px; margin: 10px 0;'>❌ Failed to load image via view_file.php</div>";
}

// Check PHP errors
echo "<h3>PHP Configuration:</h3>";
echo "<ul>";
echo "<li><strong>PHP Version:</strong> " . PHP_VERSION . "</li>";
echo "<li><strong>Error Reporting:</strong> " . error_reporting() . "</li>";
echo "<li><strong>Display Errors:</strong> " . (ini_get('display_errors') ? 'ON' : 'OFF') . "</li>";
echo "<li><strong>File Uploads:</strong> " . (ini_get('file_uploads') ? 'ON' : 'OFF') . "</li>";
echo "<li><strong>Upload Max Size:</strong> " . ini_get('upload_max_filesize') . "</li>";
echo "<li><strong>Post Max Size:</strong> " . ini_get('post_max_size') . "</li>";
echo "<li><strong>Memory Limit:</strong> " . ini_get('memory_limit') . "</li>";
echo "</ul>";

// Check session
echo "<h3>Session Information:</h3>";
echo "<ul>";
echo "<li><strong>Session ID:</strong> " . session_id() . "</li>";
echo "<li><strong>User ID:</strong> " . ($_SESSION['user_id'] ?? 'Not set') . "</li>";
echo "<li><strong>Role:</strong> " . ($_SESSION['role'] ?? 'Not set') . "</li>";
echo "</ul>";

// Check database connection
echo "<h3>Database Connection Test:</h3>";
try {
    require_once 'config/database.php';
    $database = new Database();
    $db = $database->getConnection();
    
    if ($db) {
        echo "<p>✅ Database connection successful</p>";
        
        // Check expense 187
        $stmt = $db->prepare('SELECT id, exno, transfer_slip_image FROM expenses WHERE id = 187');
        $stmt->execute();
        $expense = $stmt->fetch();
        
        if ($expense) {
            echo "<p><strong>Expense 187:</strong></p>";
            echo "<ul>";
            echo "<li>ID: {$expense['id']}</li>";
            echo "<li>Expense No: {$expense['exno']}</li>";
            echo "<li>Transfer Slip Image: {$expense['transfer_slip_image']}</li>";
            echo "</ul>";
        } else {
            echo "<p>❌ Expense 187 not found</p>";
        }
    } else {
        echo "<p>❌ Database connection failed</p>";
    }
} catch (Exception $e) {
    echo "<p>❌ Database error: " . $e->getMessage() . "</p>";
}

// List files in transfer_slips directory
echo "<h3>Files in transfer_slips Directory:</h3>";
$transfer_dir = 'uploads/transfer_slips/';
if (is_dir($transfer_dir)) {
    $files = scandir($transfer_dir);
    $image_files = array_filter($files, function($file) {
        return preg_match('/\.(jpg|jpeg|png|gif)$/i', $file);
    });
    
    echo "<p><strong>Total image files:</strong> " . count($image_files) . "</p>";
    
    if ($image_files) {
        echo "<h4>Recent files (last 10):</h4>";
        $file_info = [];
        foreach ($image_files as $file) {
            $full_path = $transfer_dir . $file;
            $file_info[] = [
                'name' => $file,
                'size' => filesize($full_path),
                'modified' => filemtime($full_path)
            ];
        }
        
        // Sort by modification time (newest first)
        usort($file_info, function($a, $b) {
            return $b['modified'] - $a['modified'];
        });
        
        echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
        echo "<tr><th>Filename</th><th>Size</th><th>Modified</th><th>Test</th></tr>";
        
        foreach (array_slice($file_info, 0, 10) as $info) {
            $size_kb = round($info['size'] / 1024, 1);
            $modified = date('Y-m-d H:i:s', $info['modified']);
            
            echo "<tr>";
            echo "<td>{$info['name']}</td>";
            echo "<td>{$size_kb} KB</td>";
            echo "<td>$modified</td>";
            echo "<td><a href='?file=" . urlencode($info['name']) . "&type=transfer_slip'>Test</a></td>";
            echo "</tr>";
        }
        echo "</table>";
    }
} else {
    echo "<p>❌ Directory not found: $transfer_dir</p>";
}

// Check .htaccess files
echo "<h3>.htaccess Files:</h3>";
$htaccess_locations = [
    '.',
    'uploads/',
    'uploads/transfer_slips/',
    'api/'
];

foreach ($htaccess_locations as $location) {
    $htaccess_path = $location . '.htaccess';
    echo "<h4>$htaccess_path</h4>";
    if (file_exists($htaccess_path)) {
        echo "<pre style='background: #f5f5f5; padding: 10px; border: 1px solid #ddd;'>" . htmlspecialchars(file_get_contents($htaccess_path)) . "</pre>";
    } else {
        echo "<p>File does not exist</p>";
    }
}

// Test form
echo "<h3>Test Different Files:</h3>";
echo "<form method='GET' style='background: #f9f9f9; padding: 15px; border: 1px solid #ddd;'>";
echo "<label>File: <input type='text' name='file' value='$test_file' style='width: 400px;'></label><br><br>";
echo "<label>Type: ";
echo "<select name='type'>";
$types = ['transfer_slip', 'receipt', 'verification_slip', 'reviewer_slip'];
foreach ($types as $type) {
    $selected = ($type === $test_type) ? 'selected' : '';
    echo "<option value='$type' $selected>$type</option>";
}
echo "</select>";
echo "</label><br><br>";
echo "<input type='submit' value='Test File' style='padding: 10px 20px;'>";
echo "</form>";
?>
