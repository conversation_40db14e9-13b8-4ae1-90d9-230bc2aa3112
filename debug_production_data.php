<?php
session_start();
require_once 'config/database.php';

// Check if user is logged in
if (!isset($_SESSION['user_id'])) {
    header('Location: login.php');
    exit;
}

try {
    $database = new Database();
    $db = $database->getConnection();
    
    echo "<h2>Production Database Debug</h2>";
    
    // Show database connection info
    echo "<h3>Database Connection Info:</h3>";
    echo "<p>Server: " . ($_SERVER['SERVER_NAME'] ?? 'Unknown') . "</p>";
    
    // Check transfer numbers
    echo "<h3>Transfer Numbers in Database:</h3>";
    $stmt = $db->prepare("SELECT transfer_no, exno, id FROM expenses WHERE transfer_no != '' AND transfer_no IS NOT NULL ORDER BY id DESC LIMIT 10");
    $stmt->execute();
    $transfers = $stmt->fetchAll();
    
    if ($transfers) {
        echo "<table border='1'>";
        echo "<tr><th>ID</th><th>Transfer No</th><th>Expense No</th></tr>";
        foreach ($transfers as $transfer) {
            echo "<tr>";
            echo "<td>" . htmlspecialchars($transfer['id']) . "</td>";
            echo "<td>" . htmlspecialchars($transfer['transfer_no']) . "</td>";
            echo "<td>" . htmlspecialchars($transfer['exno']) . "</td>";
            echo "</tr>";
        }
        echo "</table>";
    } else {
        echo "<p>No transfer numbers found in database</p>";
    }
    
    // Check receipt numbers
    echo "<h3>Receipt Numbers in Database:</h3>";
    $stmt = $db->prepare("
        SELECT rn.receipt_number, e.exno, rn.expense_id 
        FROM receipt_numbers rn 
        INNER JOIN expenses e ON rn.expense_id = e.id 
        ORDER BY rn.id DESC 
        LIMIT 10
    ");
    $stmt->execute();
    $receipts = $stmt->fetchAll();
    
    if ($receipts) {
        echo "<table border='1'>";
        echo "<tr><th>Receipt Number</th><th>Expense No</th><th>Expense ID</th></tr>";
        foreach ($receipts as $receipt) {
            echo "<tr>";
            echo "<td>" . htmlspecialchars($receipt['receipt_number']) . "</td>";
            echo "<td>" . htmlspecialchars($receipt['exno']) . "</td>";
            echo "<td>" . htmlspecialchars($receipt['expense_id']) . "</td>";
            echo "</tr>";
        }
        echo "</table>";
    } else {
        echo "<p>No receipt numbers found in database</p>";
    }
    
    // Test API calls
    echo "<h3>Test API Calls:</h3>";
    
    // Test with existing transfer number if any
    if (!empty($transfers)) {
        $test_transfer = $transfers[0]['transfer_no'];
        echo "<h4>Testing existing transfer number: " . htmlspecialchars($test_transfer) . "</h4>";
        
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, "https://nkslgroup.com/expenses_system/api/check_transfer_number.php");
        curl_setopt($ch, CURLOPT_POST, 1);
        curl_setopt($ch, CURLOPT_POSTFIELDS, "transfer_no=" . urlencode($test_transfer));
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
        $response = curl_exec($ch);
        curl_close($ch);
        
        echo "<pre>" . htmlspecialchars($response) . "</pre>";
    }
    
    // Test with existing receipt number if any
    if (!empty($receipts)) {
        $test_receipt = $receipts[0]['receipt_number'];
        echo "<h4>Testing existing receipt number: " . htmlspecialchars($test_receipt) . "</h4>";
        
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, "https://nkslgroup.com/expenses_system/api/check_receipt_number.php");
        curl_setopt($ch, CURLOPT_POST, 1);
        curl_setopt($ch, CURLOPT_POSTFIELDS, "receipt_number=" . urlencode($test_receipt));
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
        $response = curl_exec($ch);
        curl_close($ch);
        
        echo "<pre>" . htmlspecialchars($response) . "</pre>";
    }
    
    // Test with new numbers
    echo "<h4>Testing new transfer number: TEST123</h4>";
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, "https://nkslgroup.com/expenses_system/api/check_transfer_number.php");
    curl_setopt($ch, CURLOPT_POST, 1);
    curl_setopt($ch, CURLOPT_POSTFIELDS, "transfer_no=TEST123");
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
    $response = curl_exec($ch);
    curl_close($ch);
    
    echo "<pre>" . htmlspecialchars($response) . "</pre>";
    
    echo "<h4>Testing new receipt number: TEST123</h4>";
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, "https://nkslgroup.com/expenses_system/api/check_receipt_number.php");
    curl_setopt($ch, CURLOPT_POST, 1);
    curl_setopt($ch, CURLOPT_POSTFIELDS, "receipt_number=TEST123");
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
    $response = curl_exec($ch);
    curl_close($ch);
    
    echo "<pre>" . htmlspecialchars($response) . "</pre>";
    
} catch (Exception $e) {
    echo "<p style='color: red;'>Error: " . htmlspecialchars($e->getMessage()) . "</p>";
}
?>
