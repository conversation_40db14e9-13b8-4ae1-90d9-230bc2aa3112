<?php
echo "<h2>Find Correct Upload Path</h2>";

echo "<h3>Current Directory Information:</h3>";
echo "<ul>";
echo "<li><strong>__FILE__:</strong> " . __FILE__ . "</li>";
echo "<li><strong>__DIR__:</strong> " . __DIR__ . "</li>";
echo "<li><strong>dirname(__DIR__):</strong> " . dirname(__DIR__) . "</li>";
echo "<li><strong>getcwd():</strong> " . getcwd() . "</li>";
echo "<li><strong>\$_SERVER['DOCUMENT_ROOT']:</strong> " . $_SERVER['DOCUMENT_ROOT'] . "</li>";
echo "<li><strong>\$_SERVER['SCRIPT_FILENAME']:</strong> " . $_SERVER['SCRIPT_FILENAME'] . "</li>";
echo "</ul>";

echo "<h3>Testing Different Upload Paths:</h3>";

$possible_paths = [
    dirname(__DIR__) . '/uploads/',
    $_SERVER['DOCUMENT_ROOT'] . '/uploads/',
    $_SERVER['DOCUMENT_ROOT'] . '/expenses_system/uploads/',
    '/var/www/vhosts/nkslgroup.com/httpdocs/expenses_system/uploads/',
    '/var/www/vhosts/nkslgroup.com/httpdocs/uploads/',
    '../uploads/',
    './uploads/',
    'uploads/'
];

echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
echo "<tr><th>Path</th><th>Exists</th><th>Readable</th><th>Files Count</th></tr>";

$working_paths = [];

foreach ($possible_paths as $path) {
    $exists = is_dir($path);
    $readable = is_readable($path);
    $files_count = 0;
    
    if ($exists && $readable) {
        $transfer_slips_path = $path . 'transfer_slips/';
        if (is_dir($transfer_slips_path)) {
            $files = glob($transfer_slips_path . '*.{jpg,jpeg,png}', GLOB_BRACE);
            $files_count = count($files);
            if ($files_count > 0) {
                $working_paths[] = $path;
            }
        }
    }
    
    echo "<tr>";
    echo "<td>$path</td>";
    echo "<td style='color: " . ($exists ? 'green' : 'red') . "'>" . ($exists ? '✅ YES' : '❌ NO') . "</td>";
    echo "<td style='color: " . ($readable ? 'green' : 'red') . "'>" . ($readable ? '✅ YES' : '❌ NO') . "</td>";
    echo "<td>$files_count</td>";
    echo "</tr>";
}
echo "</table>";

if ($working_paths) {
    echo "<h3>✅ Working Paths Found:</h3>";
    foreach ($working_paths as $path) {
        echo "<p><strong>$path</strong></p>";
        
        // Test specific file
        $test_file = 'transfer_68fcafb1efdee_1761390513.jpg';
        $test_path = $path . 'transfer_slips/' . $test_file;
        
        echo "<ul>";
        echo "<li>Test file path: $test_path</li>";
        echo "<li>Test file exists: " . (file_exists($test_path) ? '✅ YES' : '❌ NO') . "</li>";
        
        if (file_exists($test_path)) {
            echo "<li>File size: " . filesize($test_path) . " bytes</li>";
            echo "<li>File readable: " . (is_readable($test_path) ? '✅ YES' : '❌ NO') . "</li>";
        }
        echo "</ul>";
        
        // Show some files in directory
        $transfer_slips_path = $path . 'transfer_slips/';
        if (is_dir($transfer_slips_path)) {
            $files = array_slice(glob($transfer_slips_path . '*.{jpg,jpeg,png}', GLOB_BRACE), 0, 5);
            if ($files) {
                echo "<p>Sample files:</p>";
                echo "<ul>";
                foreach ($files as $file) {
                    echo "<li>" . basename($file) . "</li>";
                }
                echo "</ul>";
            }
        }
        echo "<hr>";
    }
} else {
    echo "<h3>❌ No Working Paths Found</h3>";
    echo "<p>Let's check if uploads directory exists anywhere:</p>";
    
    // Search for uploads directory
    $search_paths = [
        '/var/www/vhosts/nkslgroup.com/httpdocs/',
        '/var/www/vhosts/nkslgroup.com/',
        dirname(__DIR__),
        $_SERVER['DOCUMENT_ROOT']
    ];
    
    foreach ($search_paths as $search_path) {
        if (is_dir($search_path)) {
            echo "<h4>Searching in: $search_path</h4>";
            $dirs = glob($search_path . '*/uploads', GLOB_ONLYDIR);
            if ($dirs) {
                echo "<ul>";
                foreach ($dirs as $dir) {
                    echo "<li>$dir</li>";
                }
                echo "</ul>";
            } else {
                echo "<p>No uploads directories found</p>";
            }
        }
    }
}

// Generate the correct code for view_file.php
if ($working_paths) {
    $best_path = $working_paths[0];
    echo "<h3>🛠️ Fix for api/view_file.php:</h3>";
    echo "<div style='background: #f8f9fa; border: 1px solid #dee2e6; padding: 15px; border-radius: 5px;'>";
    echo "<p><strong>Replace line 40 in api/view_file.php:</strong></p>";
    echo "<p style='background: #f8d7da; padding: 10px; border-radius: 3px;'>";
    echo "<code>❌ \$upload_dir = dirname(__DIR__) . '/uploads/';</code>";
    echo "</p>";
    echo "<p style='background: #d4edda; padding: 10px; border-radius: 3px;'>";
    echo "<code>✅ \$upload_dir = '$best_path';</code>";
    echo "</p>";
    echo "</div>";
}

// Test current view_file.php
echo "<h3>Test Current view_file.php:</h3>";
echo "<p><a href='api/view_file.php?file=transfer_68fcafb1efdee_1761390513.jpg&type=transfer_slip' target='_blank'>Test Current view_file.php</a></p>";
?>
