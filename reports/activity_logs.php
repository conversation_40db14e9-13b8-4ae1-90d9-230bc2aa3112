<?php
session_start();
require_once '../config/database.php';
require_once '../includes/functions.php';
require_once '../includes/report_access_control.php';

// Check access
enforceReportAccess();

$database = new Database();
$db = $database->getConnection();

// Get user info
$stmt = $db->prepare("SELECT * FROM users WHERE id = ?");
$stmt->execute([$_SESSION['user_id']]);
$user = $stmt->fetch();

if (!$user || !$user['is_active']) {
    session_destroy();
    header('Location: ../login.php');
    exit();
}

// Pagination settings
$page = isset($_GET['page']) ? (int)$_GET['page'] : 1;
$limit = 20;
$offset = ($page - 1) * $limit;

// Filter settings
$user_filter = $_GET['user_filter'] ?? '';
$action_filter = $_GET['action_filter'] ?? '';
$table_filter = $_GET['table_filter'] ?? '';
$date_from = $_GET['date_from'] ?? '';
$date_to = $_GET['date_to'] ?? '';

// Build WHERE clause
$where_conditions = [];
$params = [];

// User filter - only show own activities for non-admin users
if ($user['role'] !== 'administrator') {
    $where_conditions[] = "al.user_id = ?";
    $params[] = $user['id'];
} elseif ($user_filter) {
    $where_conditions[] = "al.user_id = ?";
    $params[] = $user_filter;
}

if ($action_filter) {
    $where_conditions[] = "al.action_type = ?";
    $params[] = $action_filter;
}

if ($table_filter) {
    $where_conditions[] = "al.table_name = ?";
    $params[] = $table_filter;
}

if ($date_from) {
    $where_conditions[] = "DATE(al.created_at) >= ?";
    $params[] = $date_from;
}

if ($date_to) {
    $where_conditions[] = "DATE(al.created_at) <= ?";
    $params[] = $date_to;
}

$where_clause = !empty($where_conditions) ? 'WHERE ' . implode(' AND ', $where_conditions) : '';

// Get total count
$count_sql = "
    SELECT COUNT(*) as total 
    FROM activity_logs al 
    LEFT JOIN users u ON al.user_id = u.id 
    $where_clause
";
$stmt = $db->prepare($count_sql);
$stmt->execute($params);
$total_records = $stmt->fetch()['total'];
$total_pages = ceil($total_records / $limit);

// Get activity logs
$sql = "
    SELECT 
        al.*,
        u.username,
        u.full_name
    FROM activity_logs al
    LEFT JOIN users u ON al.user_id = u.id
    $where_clause
    ORDER BY al.created_at DESC
    LIMIT $limit OFFSET $offset
";

$stmt = $db->prepare($sql);
$stmt->execute($params);
$activities = $stmt->fetchAll();

// Get filter options (only for admin)
$users = [];
$actions = [];
$tables = [];

if ($user['role'] === 'administrator') {
    // Get all users
    $stmt = $db->query("SELECT id, username, full_name FROM users WHERE is_active = 1 ORDER BY full_name");
    $users = $stmt->fetchAll();
    
    // Get distinct actions
    $stmt = $db->query("SELECT DISTINCT action_type FROM activity_logs ORDER BY action_type");
    $actions = $stmt->fetchAll(PDO::FETCH_COLUMN);
    
    // Get distinct tables
    $stmt = $db->query("SELECT DISTINCT table_name FROM activity_logs ORDER BY table_name");
    $tables = $stmt->fetchAll(PDO::FETCH_COLUMN);
}
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <?php if ($user['role'] === 'administrator'): ?>
        <title>Activity Logs (All Users) - Expense Management System</title>
    <?php else: ?>
        <title>My Activity Logs - Expense Management System</title>
    <?php endif; ?>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="../assets/css/style.css" rel="stylesheet">
</head>
<body>
    <!-- Navigation -->
    <?php include '../includes/navbar.php'; ?>


    <div class="container-fluid mt-4">
        <!-- Page Header -->
        <div class="row mb-4">
            <div class="col">
                <?php if ($user['role'] === 'administrator'): ?>
                    <h2><i class="fas fa-history me-2"></i>Activity Logs (All Users)</h2>
                    <p class="text-muted">
                        System-wide activity tracking and audit trail for all users
                    </p>
                <?php else: ?>
                    <h2><i class="fas fa-history me-2"></i>My Activity Logs</h2>
                    <p class="text-muted">
                        Your personal activity history and actions
                    </p>
                <?php endif; ?>
            </div>
        </div>

        <!-- Filters -->
        <?php if ($user['role'] === 'administrator'): ?>
        <div class="card mb-4">
            <div class="card-header">
                <h5 class="mb-0"><i class="fas fa-filter me-2"></i>Filters</h5>
            </div>
            <div class="card-body">
                <form method="GET" class="row g-3">
                    <div class="col-md-3">
                        <label for="user_filter" class="form-label">User</label>
                        <select class="form-select" id="user_filter" name="user_filter">
                            <option value="">All Users</option>
                            <?php foreach ($users as $u): ?>
                                <option value="<?php echo $u['id']; ?>" <?php echo $user_filter == $u['id'] ? 'selected' : ''; ?>>
                                    <?php echo htmlspecialchars($u['full_name'] . ' (' . $u['username'] . ')'); ?>
                                </option>
                            <?php endforeach; ?>
                        </select>
                    </div>
                    
                    <div class="col-md-2">
                        <label for="action_filter" class="form-label">Action</label>
                        <select class="form-select" id="action_filter" name="action_filter">
                            <option value="">All Actions</option>
                            <?php foreach ($actions as $action): ?>
                                <option value="<?php echo $action; ?>" <?php echo $action_filter === $action ? 'selected' : ''; ?>>
                                    <?php echo ucfirst(str_replace('_', ' ', $action)); ?>
                                </option>
                            <?php endforeach; ?>
                        </select>
                    </div>
                    
                    <div class="col-md-2">
                        <label for="table_filter" class="form-label">Table</label>
                        <select class="form-select" id="table_filter" name="table_filter">
                            <option value="">All Tables</option>
                            <?php foreach ($tables as $table): ?>
                                <option value="<?php echo $table; ?>" <?php echo $table_filter === $table ? 'selected' : ''; ?>>
                                    <?php echo ucfirst($table); ?>
                                </option>
                            <?php endforeach; ?>
                        </select>
                    </div>
                    
                    <div class="col-md-2">
                        <label for="date_from" class="form-label">From Date</label>
                        <input type="date" class="form-control" id="date_from" name="date_from" value="<?php echo $date_from; ?>">
                    </div>
                    
                    <div class="col-md-2">
                        <label for="date_to" class="form-label">To Date</label>
                        <input type="date" class="form-control" id="date_to" name="date_to" value="<?php echo $date_to; ?>">
                    </div>
                    
                    <div class="col-md-1">
                        <label class="form-label">&nbsp;</label>
                        <div class="d-grid">
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-search"></i>
                            </button>
                        </div>
                    </div>
                </form>
            </div>
        </div>
        <?php endif; ?>

        <!-- Activity Logs Table -->
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="mb-0">
                    <?php if ($user['role'] === 'administrator'): ?>
                        <i class="fas fa-list me-2"></i>Activity History (All Users)
                    <?php else: ?>
                        <i class="fas fa-list me-2"></i>My Activity History
                    <?php endif; ?>
                    <span class="badge bg-secondary"><?php echo number_format($total_records); ?> records</span>
                </h5>
                
                <div>
                    <a href="index.php" class="btn btn-outline-secondary btn-sm">
                        <i class="fas fa-arrow-left me-1"></i>Back to Reports
                    </a>
                </div>
            </div>
            <div class="card-body p-0">
                <?php if (empty($activities)): ?>
                    <div class="text-center py-5">
                        <i class="fas fa-history fa-3x text-muted mb-3"></i>
                        <?php if ($user['role'] === 'administrator'): ?>
                            <h5 class="text-muted">No Activity Found</h5>
                            <p class="text-muted">No activities match your current filters.</p>
                        <?php else: ?>
                            <h5 class="text-muted">No Activity Found</h5>
                            <p class="text-muted">You haven't performed any actions that match the current filters.</p>
                        <?php endif; ?>
                    </div>
                <?php else: ?>
                    <div class="table-responsive">
                        <table class="table table-hover mb-0">
                            <thead class="table-dark">
                                <tr>
                                    <th>Date & Time</th>
                                    <?php if ($user['role'] === 'administrator'): ?>
                                    <th>User</th>
                                    <?php endif; ?>
                                    <th>Action</th>
                                    <th>Table</th>
                                    <th>Description</th>
                                    <th>IP Address</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($activities as $activity): ?>
                                    <tr>
                                        <td>
                                            <div class="fw-bold"><?php echo date('M d, Y', strtotime($activity['created_at'])); ?></div>
                                            <small class="text-muted"><?php echo date('H:i:s', strtotime($activity['created_at'])); ?></small>
                                        </td>

                                        <?php if ($user['role'] === 'administrator'): ?>
                                        <td>
                                            <div class="fw-bold"><?php echo htmlspecialchars($activity['full_name'] ?? 'Unknown'); ?></div>
                                            <small class="text-muted"><?php echo htmlspecialchars($activity['username'] ?? 'N/A'); ?></small>
                                        </td>
                                        <?php endif; ?>

                                        <td>
                                            <span class="badge bg-<?php
                                                echo $activity['action_type'] === 'create' ? 'success' :
                                                    ($activity['action_type'] === 'update' ? 'warning' :
                                                    ($activity['action_type'] === 'delete' ? 'danger' : 'info'));
                                            ?>">
                                                <i class="fas fa-<?php
                                                    echo $activity['action_type'] === 'create' ? 'plus' :
                                                        ($activity['action_type'] === 'update' ? 'edit' :
                                                        ($activity['action_type'] === 'delete' ? 'trash' : 'info'));
                                                ?> me-1"></i>
                                                <?php echo ucfirst(str_replace('_', ' ', $activity['action_type'])); ?>
                                            </span>
                                        </td>

                                        <td>
                                            <span class="badge bg-secondary">
                                                <?php echo htmlspecialchars($activity['table_name'] ?? 'N/A'); ?>
                                            </span>
                                        </td>

                                        <td>
                                            <div class="text-truncate" style="max-width: 300px;" title="<?php echo htmlspecialchars($activity['description'] ?? 'N/A'); ?>">
                                                <?php echo htmlspecialchars($activity['description'] ?? 'N/A'); ?>
                                            </div>
                                        </td>

                                        <td>
                                            <small class="text-muted font-monospace">
                                                <?php echo htmlspecialchars($activity['ip_address'] ?? 'N/A'); ?>
                                            </small>
                                        </td>
                                    </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>
                <?php endif; ?>
            </div>

            <!-- Pagination -->
            <?php if ($total_pages > 1): ?>
            <div class="card-footer">
                <nav aria-label="Activity logs pagination">
                    <ul class="pagination pagination-sm mb-0 justify-content-center">
                        <!-- Previous Page -->
                        <?php if ($page > 1): ?>
                            <li class="page-item">
                                <a class="page-link" href="?<?php echo http_build_query(array_merge($_GET, ['page' => $page - 1])); ?>">
                                    <i class="fas fa-chevron-left"></i>
                                </a>
                            </li>
                        <?php else: ?>
                            <li class="page-item disabled">
                                <span class="page-link"><i class="fas fa-chevron-left"></i></span>
                            </li>
                        <?php endif; ?>

                        <!-- Page Numbers -->
                        <?php
                        $start_page = max(1, $page - 2);
                        $end_page = min($total_pages, $page + 2);

                        if ($start_page > 1): ?>
                            <li class="page-item">
                                <a class="page-link" href="?<?php echo http_build_query(array_merge($_GET, ['page' => 1])); ?>">1</a>
                            </li>
                            <?php if ($start_page > 2): ?>
                                <li class="page-item disabled"><span class="page-link">...</span></li>
                            <?php endif; ?>
                        <?php endif; ?>

                        <?php for ($i = $start_page; $i <= $end_page; $i++): ?>
                            <li class="page-item <?php echo $i === $page ? 'active' : ''; ?>">
                                <a class="page-link" href="?<?php echo http_build_query(array_merge($_GET, ['page' => $i])); ?>">
                                    <?php echo $i; ?>
                                </a>
                            </li>
                        <?php endfor; ?>

                        <?php if ($end_page < $total_pages): ?>
                            <?php if ($end_page < $total_pages - 1): ?>
                                <li class="page-item disabled"><span class="page-link">...</span></li>
                            <?php endif; ?>
                            <li class="page-item">
                                <a class="page-link" href="?<?php echo http_build_query(array_merge($_GET, ['page' => $total_pages])); ?>">
                                    <?php echo $total_pages; ?>
                                </a>
                            </li>
                        <?php endif; ?>

                        <!-- Next Page -->
                        <?php if ($page < $total_pages): ?>
                            <li class="page-item">
                                <a class="page-link" href="?<?php echo http_build_query(array_merge($_GET, ['page' => $page + 1])); ?>">
                                    <i class="fas fa-chevron-right"></i>
                                </a>
                            </li>
                        <?php else: ?>
                            <li class="page-item disabled">
                                <span class="page-link"><i class="fas fa-chevron-right"></i></span>
                            </li>
                        <?php endif; ?>
                    </ul>
                </nav>

                <div class="text-center mt-2">
                    <small class="text-muted">
                        Showing <?php echo number_format($offset + 1); ?> to <?php echo number_format(min($offset + $limit, $total_records)); ?>
                        of <?php echo number_format($total_records); ?> records
                        (Page <?php echo $page; ?> of <?php echo $total_pages; ?>)
                    </small>
                </div>
            </div>
            <?php endif; ?>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Auto-submit form when filters change (for better UX)
        document.querySelectorAll('select[name], input[name="date_from"], input[name="date_to"]').forEach(element => {
            element.addEventListener('change', function() {
                // Optional: Auto-submit on change
                // this.form.submit();
            });
        });

        // Clear all filters
        function clearFilters() {
            window.location.href = 'activity_logs.php';
        }

        // Add clear filters button if any filters are active
        <?php if ($user_filter || $action_filter || $table_filter || $date_from || $date_to): ?>
        document.addEventListener('DOMContentLoaded', function() {
            const cardHeader = document.querySelector('.card .card-header');
            if (cardHeader) {
                const clearBtn = document.createElement('button');
                clearBtn.className = 'btn btn-outline-secondary btn-sm ms-2';
                clearBtn.innerHTML = '<i class="fas fa-times me-1"></i>Clear Filters';
                clearBtn.onclick = clearFilters;
                cardHeader.querySelector('h5').appendChild(clearBtn);
            }
        });
        <?php endif; ?>
    </script>
</body>
</html>
