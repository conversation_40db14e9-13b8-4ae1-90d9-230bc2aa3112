<?php
session_start();
require_once '../config/database.php';
require_once '../includes/functions.php';
require_once '../includes/report_access_control.php';

// Check access
enforceReportAccess();

// Initialize database connection
$database = new Database();
$db = $database->getConnection();

$user = getUserById($_SESSION['user_id'], $db);
if (!$user) {
    header('Location: ../login.php');
    exit();
}

// Get filter parameters
$date_from = $_GET['date_from'] ?? date('Y-m-01');
$date_to = $_GET['date_to'] ?? date('Y-m-t');

// Build WHERE conditions
$where_conditions = ["e.job_open_date >= ?", "e.job_open_date <= ?"];
$params = [$date_from, $date_to];

// Non-admin users see only their own data
if ($user['role'] !== 'administrator') {
    $where_conditions[] = "e.created_by = ?";
    $params[] = $user['id'];
}

$where_clause = 'WHERE ' . implode(' AND ', $where_conditions);

// Get workflow performance metrics
$workflow_query = "
    SELECT 
        COUNT(*) as total_expenses,
        COUNT(CASE WHEN e.status = 'open' THEN 1 END) as open_count,
        COUNT(CASE WHEN e.status = 'pending' THEN 1 END) as pending_count,
        COUNT(CASE WHEN e.status = 'success' THEN 1 END) as success_count,
        COUNT(CASE WHEN e.status = 'rejected' THEN 1 END) as rejected_count,
        COUNT(CASE WHEN e.status = 'returned' THEN 1 END) as returned_count,
        
        -- Average processing times
        AVG(CASE 
            WHEN e.verification_date IS NOT NULL 
            THEN TIMESTAMPDIFF(HOUR, e.created_at, e.verification_date) 
        END) as avg_verification_time_hours,
        
        AVG(CASE
            WHEN e.reviewer_date IS NOT NULL
            THEN TIMESTAMPDIFF(HOUR, e.verification_date, e.reviewer_date)
        END) as avg_review_time_hours,

        AVG(CASE
            WHEN e.status = 'success' AND e.reviewer_date IS NOT NULL
            THEN TIMESTAMPDIFF(HOUR, e.created_at, e.reviewer_date)
        END) as avg_total_processing_time_hours,
        
        -- Rejection and return rates
        ROUND((COUNT(CASE WHEN e.status = 'rejected' THEN 1 END) / COUNT(*)) * 100, 2) as rejection_rate,
        ROUND((COUNT(CASE WHEN e.status = 'returned' THEN 1 END) / COUNT(*)) * 100, 2) as return_rate,
        ROUND((COUNT(CASE WHEN e.status = 'success' THEN 1 END) / COUNT(*)) * 100, 2) as approval_rate
        
    FROM expenses e
    $where_clause
";

$stmt = $db->prepare($workflow_query);
$stmt->execute($params);
$workflow_stats = $stmt->fetch();

// Get user performance (only for admin)
$user_performance = [];
if ($user['role'] === 'administrator') {
    $user_perf_query = "
        SELECT 
            u.full_name,
            u.role,
            COUNT(e.id) as total_expenses,
            COUNT(CASE WHEN e.status = 'success' THEN 1 END) as approved_count,
            COUNT(CASE WHEN e.status = 'rejected' THEN 1 END) as rejected_count,
            COUNT(CASE WHEN e.status = 'returned' THEN 1 END) as returned_count,
            ROUND(AVG(e.total_amount), 2) as avg_expense_amount,
            SUM(e.total_amount) as total_amount,
            
            -- Performance metrics for verification role
            COUNT(CASE WHEN u.role = 'verification' AND e.verification_date IS NOT NULL THEN 1 END) as verifications_done,
            AVG(CASE 
                WHEN u.role = 'verification' AND e.verification_date IS NOT NULL 
                THEN TIMESTAMPDIFF(HOUR, e.created_at, e.verification_date) 
            END) as avg_verification_time,
            
            -- Performance metrics for reviewer role
            COUNT(CASE WHEN u.role = 'reviewer' AND e.reviewer_date IS NOT NULL THEN 1 END) as reviews_done,
            AVG(CASE
                WHEN u.role = 'reviewer' AND e.reviewer_date IS NOT NULL
                THEN TIMESTAMPDIFF(HOUR, e.verification_date, e.reviewer_date)
            END) as avg_review_time
            
        FROM users u
        LEFT JOIN expenses e ON (
            (u.role = 'data_entry' AND e.created_by = u.id) OR
            (u.role = 'verification' AND e.verification_by = u.id) OR
            (u.role = 'reviewer' AND e.reviewer_by = u.id) OR
            (u.role = 'administrator' AND e.created_by = u.id)
        ) AND e.job_open_date >= ? AND e.job_open_date <= ?
        WHERE u.role IN ('data_entry', 'verification', 'reviewer', 'administrator')
        GROUP BY u.id, u.full_name, u.role
        HAVING total_expenses > 0
        ORDER BY u.role, total_expenses DESC
    ";
    
    $stmt = $db->prepare($user_perf_query);
    $stmt->execute([$date_from, $date_to]);
    $user_performance = $stmt->fetchAll();
}

// Get daily processing trends
$daily_trends_query = "
    SELECT 
        DATE(e.created_at) as date,
        COUNT(*) as created_count,
        COUNT(CASE WHEN e.verification_date IS NOT NULL AND DATE(e.verification_date) = DATE(e.created_at) THEN 1 END) as verified_same_day,
        COUNT(CASE WHEN e.reviewer_date IS NOT NULL AND DATE(e.reviewer_date) = DATE(e.created_at) THEN 1 END) as reviewed_same_day,
        COUNT(CASE WHEN e.status = 'success' THEN 1 END) as approved_count,
        COUNT(CASE WHEN e.status = 'rejected' THEN 1 END) as rejected_count,
        COUNT(CASE WHEN e.status = 'returned' THEN 1 END) as returned_count
    FROM expenses e
    $where_clause
    GROUP BY DATE(e.created_at)
    ORDER BY DATE(e.created_at) DESC
    LIMIT 30
";

$stmt = $db->prepare($daily_trends_query);
$stmt->execute($params);
$daily_trends = $stmt->fetchAll();

// Get bottleneck analysis
$bottleneck_query = "
    SELECT
        'Verification Bottleneck' as bottleneck_type,
        COUNT(*) as count,
        'Expenses waiting for verification > 24 hours' as description
    FROM expenses e
    $where_clause
    AND e.status = 'open'
    AND TIMESTAMPDIFF(HOUR, e.created_at, NOW()) > 24

    UNION ALL

    SELECT
        'Review Bottleneck' as bottleneck_type,
        COUNT(*) as count,
        'Expenses waiting for review > 24 hours' as description
    FROM expenses e
    $where_clause
    AND e.status = 'pending'
    AND TIMESTAMPDIFF(HOUR, e.verification_date, NOW()) > 24

    UNION ALL

    SELECT
        'Stale Returns' as bottleneck_type,
        COUNT(*) as count,
        'Returned expenses not resubmitted > 7 days' as description
    FROM expenses e
    $where_clause
    AND e.status = 'returned'
    AND TIMESTAMPDIFF(DAY, e.return_date, NOW()) > 7
";

// Create parameters for bottleneck query (3 times the original params)
$bottleneck_params = array_merge($params, $params, $params);

$stmt = $db->prepare($bottleneck_query);
$stmt->execute($bottleneck_params);
$bottlenecks = $stmt->fetchAll();
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Performance Report - Expense Management System</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <style>
        .performance-card {
            transition: transform 0.2s;
            border-left: 4px solid #007bff;
        }
        .performance-card:hover {
            transform: translateY(-2px);
        }
        .metric-card {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 15px;
        }
        .chart-container {
            position: relative;
            height: 300px;
            margin: 20px 0;
        }
        .bottleneck-alert {
            border-left: 4px solid #dc3545;
        }
        .trend-positive {
            color: #28a745;
        }
        .trend-negative {
            color: #dc3545;
        }
        .filter-section {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 20px;
            margin-bottom: 20px;
        }
    </style>
</head>
<body>
    <?php include '../includes/navbar.php'; ?>
    
    <div class="container-fluid mt-4">
        <div class="row">
            <div class="col-12">
                <div class="d-flex justify-content-between align-items-center mb-4">
                    <div>
                        <h1><i class="fas fa-tachometer-alt me-2"></i>Performance Report</h1>
                        <p class="text-muted mb-0">Workflow efficiency and performance analysis</p>
                    </div>
                    <div>
                        <button class="btn btn-outline-primary" onclick="window.print()">
                            <i class="fas fa-print me-1"></i>Print Report
                        </button>
                    </div>
                </div>

                <!-- Filter Section -->
                <div class="filter-section">
                    <form method="GET" class="row g-3">
                        <div class="col-md-4">
                            <label for="date_from" class="form-label">Date From</label>
                            <input type="date" class="form-control" id="date_from" name="date_from" value="<?php echo htmlspecialchars($date_from); ?>">
                        </div>
                        <div class="col-md-4">
                            <label for="date_to" class="form-label">Date To</label>
                            <input type="date" class="form-control" id="date_to" name="date_to" value="<?php echo htmlspecialchars($date_to); ?>">
                        </div>
                        <div class="col-md-4 d-flex align-items-end">
                            <button type="submit" class="btn btn-primary me-2">
                                <i class="fas fa-search me-1"></i>Update Report
                            </button>
                            <a href="performance_report.php" class="btn btn-outline-secondary">
                                <i class="fas fa-refresh me-1"></i>Reset
                            </a>
                        </div>
                    </form>
                </div>

                <!-- Key Performance Metrics -->
                <div class="row mb-4">
                    <div class="col-md-3">
                        <div class="card metric-card h-100">
                            <div class="card-body text-center">
                                <i class="fas fa-clock fa-2x mb-2"></i>
                                <h4><?php echo number_format($workflow_stats['avg_total_processing_time_hours'] ?? 0, 1); ?> hrs</h4>
                                <p class="mb-0">Avg Processing Time</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card metric-card h-100">
                            <div class="card-body text-center">
                                <i class="fas fa-check-circle fa-2x mb-2"></i>
                                <h4><?php echo number_format($workflow_stats['approval_rate'] ?? 0, 1); ?>%</h4>
                                <p class="mb-0">Approval Rate</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card metric-card h-100">
                            <div class="card-body text-center">
                                <i class="fas fa-times-circle fa-2x mb-2"></i>
                                <h4><?php echo number_format($workflow_stats['rejection_rate'] ?? 0, 1); ?>%</h4>
                                <p class="mb-0">Rejection Rate</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card metric-card h-100">
                            <div class="card-body text-center">
                                <i class="fas fa-undo fa-2x mb-2"></i>
                                <h4><?php echo number_format($workflow_stats['return_rate'] ?? 0, 1); ?>%</h4>
                                <p class="mb-0">Return Rate</p>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Workflow Statistics -->
                <div class="row mb-4">
                    <div class="col-md-6">
                        <div class="card performance-card">
                            <div class="card-header">
                                <h5 class="card-title mb-0">
                                    <i class="fas fa-chart-pie me-1"></i>Workflow Status Distribution
                                </h5>
                            </div>
                            <div class="card-body">
                                <div class="chart-container">
                                    <canvas id="statusChart"></canvas>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="card performance-card">
                            <div class="card-header">
                                <h5 class="card-title mb-0">
                                    <i class="fas fa-stopwatch me-1"></i>Processing Time Breakdown
                                </h5>
                            </div>
                            <div class="card-body">
                                <div class="row text-center">
                                    <div class="col-6">
                                        <h4 class="text-primary"><?php echo number_format($workflow_stats['avg_verification_time_hours'] ?? 0, 1); ?> hrs</h4>
                                        <p class="text-muted mb-0">Avg Verification Time</p>
                                    </div>
                                    <div class="col-6">
                                        <h4 class="text-success"><?php echo number_format($workflow_stats['avg_review_time_hours'] ?? 0, 1); ?> hrs</h4>
                                        <p class="text-muted mb-0">Avg Review Time</p>
                                    </div>
                                </div>
                                <hr>
                                <div class="row">
                                    <div class="col-12">
                                        <div class="d-flex justify-content-between align-items-center mb-2">
                                            <span>Verification Stage</span>
                                            <span><?php echo number_format(($workflow_stats['avg_verification_time_hours'] ?? 0) / max(($workflow_stats['avg_total_processing_time_hours'] ?? 1), 1) * 100, 1); ?>%</span>
                                        </div>
                                        <div class="progress mb-3">
                                            <div class="progress-bar bg-primary" style="width: <?php echo number_format(($workflow_stats['avg_verification_time_hours'] ?? 0) / max(($workflow_stats['avg_total_processing_time_hours'] ?? 1), 1) * 100, 1); ?>%"></div>
                                        </div>
                                        <div class="d-flex justify-content-between align-items-center mb-2">
                                            <span>Review Stage</span>
                                            <span><?php echo number_format(($workflow_stats['avg_review_time_hours'] ?? 0) / max(($workflow_stats['avg_total_processing_time_hours'] ?? 1), 1) * 100, 1); ?>%</span>
                                        </div>
                                        <div class="progress">
                                            <div class="progress-bar bg-success" style="width: <?php echo number_format(($workflow_stats['avg_review_time_hours'] ?? 0) / max(($workflow_stats['avg_total_processing_time_hours'] ?? 1), 1) * 100, 1); ?>%"></div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Status Distribution Chart
        const statusCtx = document.getElementById('statusChart').getContext('2d');
        const statusChart = new Chart(statusCtx, {
            type: 'doughnut',
            data: {
                labels: ['Open', 'Pending', 'Success', 'Rejected', 'Returned'],
                datasets: [{
                    data: [
                        <?php echo $workflow_stats['open_count'] ?? 0; ?>,
                        <?php echo $workflow_stats['pending_count'] ?? 0; ?>,
                        <?php echo $workflow_stats['success_count'] ?? 0; ?>,
                        <?php echo $workflow_stats['rejected_count'] ?? 0; ?>,
                        <?php echo $workflow_stats['returned_count'] ?? 0; ?>
                    ],
                    backgroundColor: [
                        '#17a2b8',
                        '#ffc107',
                        '#28a745',
                        '#dc3545',
                        '#6c757d'
                    ],
                    borderWidth: 2,
                    borderColor: '#fff'
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        position: 'bottom'
                    }
                }
            }
        });
    </script>
</body>
</html>