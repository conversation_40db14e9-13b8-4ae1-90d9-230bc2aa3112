<?php
session_start();
require_once '../config/database.php';
require_once '../includes/functions.php';

// Check if user is logged in
if (!isset($_SESSION['user_id'])) {
    header('Location: ../login.php');
    exit();
}

// Initialize database connection
$database = new Database();
$db = $database->getConnection();

$user = getUserById($_SESSION['user_id'], $db);
if (!$user) {
    header('Location: ../login.php');
    exit();
}

// Get filter parameters from POST or GET
$date_from = $_POST['date_from'] ?? $_GET['date_from'] ?? date('Y-m-01');
$date_to = $_POST['date_to'] ?? $_GET['date_to'] ?? date('Y-m-t');
$status_filter = $_POST['status'] ?? $_GET['status'] ?? '';
$user_filter = $_POST['user_id'] ?? $_GET['user_id'] ?? '';
$item_filter = $_POST['item'] ?? $_GET['item'] ?? '';
$customer_filter = $_POST['customer'] ?? $_GET['customer'] ?? '';
$report_type = $_POST['report_type'] ?? $_GET['report_type'] ?? 'summary';

// Build WHERE conditions (same logic as advanced_reports.php)
$where_conditions = [];
$params = [];

if ($date_from) {
    $where_conditions[] = "e.job_open_date >= ?";
    $params[] = $date_from;
}
if ($date_to) {
    $where_conditions[] = "e.job_open_date <= ?";
    $params[] = $date_to;
}
if ($status_filter) {
    $where_conditions[] = "e.status = ?";
    $params[] = $status_filter;
}
if ($user_filter && $user['role'] === 'administrator') {
    $where_conditions[] = "e.created_by = ?";
    $params[] = $user_filter;
} elseif ($user['role'] !== 'administrator') {
    $where_conditions[] = "e.created_by = ?";
    $params[] = $user['id'];
}
if ($item_filter) {
    $where_conditions[] = "i.name LIKE ?";
    $params[] = "%$item_filter%";
}
if ($customer_filter) {
    $where_conditions[] = "c.name LIKE ?";
    $params[] = "%$customer_filter%";
}

$where_clause = !empty($where_conditions) ? 'WHERE ' . implode(' AND ', $where_conditions) : '';

// Get summary statistics
$summary_query = "
    SELECT 
        COUNT(*) as total_expenses,
        SUM(e.total_amount) as total_amount,
        AVG(e.total_amount) as avg_amount,
        COUNT(CASE WHEN e.status = 'open' THEN 1 END) as open_count,
        COUNT(CASE WHEN e.status = 'pending' THEN 1 END) as pending_count,
        COUNT(CASE WHEN e.status = 'success' THEN 1 END) as success_count,
        COUNT(CASE WHEN e.status = 'rejected' THEN 1 END) as rejected_count,
        COUNT(CASE WHEN e.status = 'returned' THEN 1 END) as returned_count
    FROM expenses e
    LEFT JOIN users u ON e.created_by = u.id
    LEFT JOIN items i ON e.item_id = i.id
    LEFT JOIN customers c ON e.customer_id = c.id
    $where_clause
";

$stmt = $db->prepare($summary_query);
$stmt->execute($params);
$summary = $stmt->fetch();

// Get detailed data (limit to 50 records for PDF)
$query = "
    SELECT 
        e.exno,
        e.job_open_date,
        COALESCE(i.name, 'Unknown Item') as item,
        COALESCE(c.name, 'Unknown Customer') as customer,
        e.total_amount,
        e.status,
        u.full_name as created_by_name
    FROM expenses e
    LEFT JOIN users u ON e.created_by = u.id
    LEFT JOIN items i ON e.item_id = i.id
    LEFT JOIN customers c ON e.customer_id = c.id
    $where_clause
    ORDER BY e.created_at DESC
    LIMIT 50
";

$stmt = $db->prepare($query);
$stmt->execute($params);
$data = $stmt->fetchAll();

// Set headers for PDF
header('Content-Type: text/html; charset=utf-8');
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Expense Report - <?php echo ucwords(str_replace('_', ' ', $report_type)); ?></title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            font-size: 12px;
            line-height: 1.4;
        }
        .header {
            text-align: center;
            margin-bottom: 30px;
            border-bottom: 2px solid #333;
            padding-bottom: 20px;
        }
        .company-name {
            font-size: 24px;
            font-weight: bold;
            color: #333;
            margin-bottom: 5px;
        }
        .report-title {
            font-size: 18px;
            color: #666;
            margin-bottom: 10px;
        }
        .report-info {
            font-size: 11px;
            color: #888;
        }
        .filters {
            background: #f5f5f5;
            padding: 15px;
            margin-bottom: 20px;
            border-radius: 5px;
        }
        .filters h3 {
            margin: 0 0 10px 0;
            font-size: 14px;
            color: #333;
        }
        .filter-item {
            display: inline-block;
            margin-right: 20px;
            margin-bottom: 5px;
        }
        .summary-stats {
            display: flex;
            justify-content: space-between;
            margin-bottom: 30px;
            flex-wrap: wrap;
        }
        .stat-card {
            background: #fff;
            border: 1px solid #ddd;
            border-radius: 5px;
            padding: 15px;
            text-align: center;
            min-width: 120px;
            margin-bottom: 10px;
        }
        .stat-number {
            font-size: 18px;
            font-weight: bold;
            color: #333;
        }
        .stat-label {
            font-size: 10px;
            color: #666;
            text-transform: uppercase;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 20px;
        }
        th, td {
            border: 1px solid #ddd;
            padding: 8px;
            text-align: left;
        }
        th {
            background-color: #f8f9fa;
            font-weight: bold;
            font-size: 11px;
        }
        td {
            font-size: 10px;
        }
        .text-right {
            text-align: right;
        }
        .text-center {
            text-align: center;
        }
        .status-badge {
            padding: 2px 6px;
            border-radius: 3px;
            font-size: 9px;
            font-weight: bold;
            text-transform: uppercase;
        }
        .status-open { background: #d1ecf1; color: #0c5460; }
        .status-pending { background: #fff3cd; color: #856404; }
        .status-success { background: #d4edda; color: #155724; }
        .status-rejected { background: #f8d7da; color: #721c24; }
        .status-returned { background: #e2e3e5; color: #383d41; }
        .footer {
            margin-top: 30px;
            padding-top: 20px;
            border-top: 1px solid #ddd;
            text-align: center;
            font-size: 10px;
            color: #888;
        }
        @media print {
            body { margin: 0; }
            .no-print { display: none; }
        }
    </style>
</head>
<body>
    <div class="header">
        <div class="company-name">Expense Management System</div>
        <div class="report-title">Advanced Report - <?php echo ucwords(str_replace('_', ' ', $report_type)); ?></div>
        <div class="report-info">
            Generated on <?php echo date('F j, Y \a\t g:i A'); ?> by <?php echo htmlspecialchars($user['full_name']); ?>
        </div>
    </div>

    <div class="filters">
        <h3>Report Filters</h3>
        <div class="filter-item"><strong>Date Range:</strong> <?php echo date('M j, Y', strtotime($date_from)); ?> - <?php echo date('M j, Y', strtotime($date_to)); ?></div>
        <?php if ($status_filter): ?>
        <div class="filter-item"><strong>Status:</strong> <?php echo ucfirst($status_filter); ?></div>
        <?php endif; ?>
        <?php if ($user_filter && $user['role'] === 'administrator'): ?>
            <?php
            $stmt = $db->prepare("SELECT full_name FROM users WHERE id = ?");
            $stmt->execute([$user_filter]);
            $user_name = $stmt->fetchColumn();
            ?>
        <div class="filter-item"><strong>User:</strong> <?php echo htmlspecialchars($user_name); ?></div>
        <?php endif; ?>
        <?php if ($item_filter): ?>
        <div class="filter-item"><strong>Item:</strong> <?php echo htmlspecialchars($item_filter); ?></div>
        <?php endif; ?>
        <?php if ($customer_filter): ?>
        <div class="filter-item"><strong>Customer:</strong> <?php echo htmlspecialchars($customer_filter); ?></div>
        <?php endif; ?>
    </div>

    <div class="summary-stats">
        <div class="stat-card">
            <div class="stat-number"><?php echo number_format($summary['total_expenses']); ?></div>
            <div class="stat-label">Total Expenses</div>
        </div>
        <div class="stat-card">
            <div class="stat-number">฿<?php echo number_format($summary['total_amount'], 2); ?></div>
            <div class="stat-label">Total Amount</div>
        </div>
        <div class="stat-card">
            <div class="stat-number">฿<?php echo number_format($summary['avg_amount'], 2); ?></div>
            <div class="stat-label">Average Amount</div>
        </div>
        <div class="stat-card">
            <div class="stat-number"><?php echo number_format($summary['success_count']); ?></div>
            <div class="stat-label">Approved</div>
        </div>
        <div class="stat-card">
            <div class="stat-number"><?php echo number_format($summary['pending_count']); ?></div>
            <div class="stat-label">Pending</div>
        </div>
        <div class="stat-card">
            <div class="stat-number"><?php echo number_format($summary['rejected_count']); ?></div>
            <div class="stat-label">Rejected</div>
        </div>
    </div>

    <table>
        <thead>
            <tr>
                <th>Expense No</th>
                <th>Date</th>
                <th>Item</th>
                <th>Customer</th>
                <th class="text-right">Amount</th>
                <th class="text-center">Status</th>
                <th>Created By</th>
            </tr>
        </thead>
        <tbody>
            <?php foreach ($data as $row): ?>
            <tr>
                <td><?php echo htmlspecialchars($row['exno']); ?></td>
                <td><?php echo date('M j, Y', strtotime($row['job_open_date'])); ?></td>
                <td><?php echo htmlspecialchars($row['item']); ?></td>
                <td><?php echo htmlspecialchars($row['customer']); ?></td>
                <td class="text-right">฿<?php echo number_format($row['total_amount'], 2); ?></td>
                <td class="text-center">
                    <span class="status-badge status-<?php echo $row['status']; ?>">
                        <?php echo ucfirst($row['status']); ?>
                    </span>
                </td>
                <td><?php echo htmlspecialchars($row['created_by_name']); ?></td>
            </tr>
            <?php endforeach; ?>
        </tbody>
    </table>

    <?php if (count($data) >= 50): ?>
    <p style="font-style: italic; color: #666; text-align: center;">
        * Showing first 50 records. Use Excel export for complete data.
    </p>
    <?php endif; ?>

    <div class="footer">
        <p>This report was generated automatically by the Expense Management System.</p>
        <p>For questions or support, please contact your system administrator.</p>
    </div>

    <script>
        // Auto-print when page loads
        window.onload = function() {
            window.print();
        };
    </script>
</body>
</html>
