<?php
session_start();
require_once '../config/database.php';
require_once '../includes/functions.php';

// Check if user is logged in
if (!isset($_SESSION['user_id'])) {
    header('Location: ../login.php');
    exit();
}

// Initialize database connection
$database = new Database();
$db = $database->getConnection();

$user = getUserById($_SESSION['user_id'], $db);
if (!$user) {
    header('Location: ../login.php');
    exit();
}

// Get filter parameters from POST or GET
$date_from = $_POST['date_from'] ?? $_GET['date_from'] ?? date('Y-m-01');
$date_to = $_POST['date_to'] ?? $_GET['date_to'] ?? date('Y-m-t');
$status_filter = $_POST['status'] ?? $_GET['status'] ?? '';
$user_filter = $_POST['user_id'] ?? $_GET['user_id'] ?? '';
$item_filter = $_POST['item'] ?? $_GET['item'] ?? '';
$customer_filter = $_POST['customer'] ?? $_GET['customer'] ?? '';
$report_type = $_POST['report_type'] ?? $_GET['report_type'] ?? 'summary';

// Build WHERE conditions (same logic as advanced_reports.php)
$where_conditions = [];
$params = [];

if ($date_from) {
    $where_conditions[] = "e.job_open_date >= ?";
    $params[] = $date_from;
}
if ($date_to) {
    $where_conditions[] = "e.job_open_date <= ?";
    $params[] = $date_to;
}
if ($status_filter) {
    $where_conditions[] = "e.status = ?";
    $params[] = $status_filter;
}
if ($user_filter && $user['role'] === 'administrator') {
    $where_conditions[] = "e.created_by = ?";
    $params[] = $user_filter;
} elseif ($user['role'] !== 'administrator') {
    $where_conditions[] = "e.created_by = ?";
    $params[] = $user['id'];
}
if ($item_filter) {
    $where_conditions[] = "i.name LIKE ?";
    $params[] = "%$item_filter%";
}
if ($customer_filter) {
    $where_conditions[] = "c.name LIKE ?";
    $params[] = "%$customer_filter%";
}

$where_clause = !empty($where_conditions) ? 'WHERE ' . implode(' AND ', $where_conditions) : '';

// Get data based on report type
switch ($report_type) {
    case 'by_status':
        $query = "
            SELECT 
                e.status,
                COUNT(*) as count,
                SUM(e.total_amount) as total_amount,
                AVG(e.total_amount) as avg_amount
            FROM expenses e
            LEFT JOIN users u ON e.created_by = u.id
            LEFT JOIN items i ON e.item_id = i.id
            LEFT JOIN customers c ON e.customer_id = c.id
            $where_clause
            GROUP BY e.status
            ORDER BY total_amount DESC
        ";
        $headers = ['Status', 'Count', 'Total Amount', 'Average Amount'];
        break;
        
    case 'by_user':
        if ($user['role'] === 'administrator') {
            $query = "
                SELECT 
                    u.full_name as user_name,
                    u.role,
                    COUNT(*) as count,
                    SUM(e.total_amount) as total_amount,
                    AVG(e.total_amount) as avg_amount,
                    COUNT(CASE WHEN e.status = 'success' THEN 1 END) as approved_count,
                    COUNT(CASE WHEN e.status = 'rejected' THEN 1 END) as rejected_count
                FROM expenses e
                LEFT JOIN users u ON e.created_by = u.id
                LEFT JOIN items i ON e.item_id = i.id
                LEFT JOIN customers c ON e.customer_id = c.id
                $where_clause
                GROUP BY e.created_by, u.full_name, u.role
                ORDER BY total_amount DESC
            ";
            $headers = ['User Name', 'Role', 'Count', 'Total Amount', 'Average Amount', 'Approved', 'Rejected'];
        } else {
            // Non-admin can't export by user
            header('HTTP/1.1 403 Forbidden');
            exit('Access denied');
        }
        break;
        
    case 'by_item':
        $query = "
            SELECT
                COALESCE(i.name, 'Unknown Item') as item,
                COUNT(*) as count,
                SUM(e.total_amount) as total_amount,
                AVG(e.total_amount) as avg_amount
            FROM expenses e
            LEFT JOIN users u ON e.created_by = u.id
            LEFT JOIN items i ON e.item_id = i.id
            LEFT JOIN customers c ON e.customer_id = c.id
            $where_clause
            GROUP BY i.name
            ORDER BY total_amount DESC
        ";
        $headers = ['Item', 'Count', 'Total Amount', 'Average Amount'];
        break;
        
    case 'by_customer':
        $query = "
            SELECT
                COALESCE(c.name, 'Unknown Customer') as customer,
                COUNT(*) as count,
                SUM(e.total_amount) as total_amount,
                AVG(e.total_amount) as avg_amount
            FROM expenses e
            LEFT JOIN users u ON e.created_by = u.id
            LEFT JOIN items i ON e.item_id = i.id
            LEFT JOIN customers c ON e.customer_id = c.id
            $where_clause
            GROUP BY c.name
            ORDER BY total_amount DESC
        ";
        $headers = ['Customer', 'Count', 'Total Amount', 'Average Amount'];
        break;
        
    case 'by_date':
        $query = "
            SELECT
                DATE(e.job_open_date) as expense_date,
                COUNT(*) as count,
                SUM(e.total_amount) as total_amount,
                AVG(e.total_amount) as avg_amount
            FROM expenses e
            LEFT JOIN users u ON e.created_by = u.id
            LEFT JOIN items i ON e.item_id = i.id
            LEFT JOIN customers c ON e.customer_id = c.id
            $where_clause
            GROUP BY DATE(e.job_open_date)
            ORDER BY expense_date DESC
        ";
        $headers = ['Date', 'Count', 'Total Amount', 'Average Amount'];
        break;
        
    default: // summary
        $query = "
            SELECT
                e.exno,
                e.job_open_date,
                COALESCE(i.name, 'Unknown Item') as item,
                COALESCE(c.name, 'Unknown Customer') as customer,
                COALESCE(d.name, 'Unknown Driver') as driver,
                e.total_amount,
                e.status,
                u.full_name as created_by_name,
                e.created_at,
                e.verification_amount,
                e.review_amount
            FROM expenses e
            LEFT JOIN users u ON e.created_by = u.id
            LEFT JOIN items i ON e.item_id = i.id
            LEFT JOIN customers c ON e.customer_id = c.id
            LEFT JOIN drivers d ON e.driver_id = d.id
            $where_clause
            ORDER BY e.created_at DESC
        ";
        $headers = ['Expense No', 'Date', 'Item', 'Customer', 'Driver', 'Amount', 'Status', 'Created By', 'Created At', 'Verification Amount', 'Review Amount'];
        break;
}

$stmt = $db->prepare($query);
$stmt->execute($params);
$data = $stmt->fetchAll();

// Set headers for Excel download
$filename = 'expense_report_' . $report_type . '_' . date('Y-m-d_H-i-s') . '.csv';
header('Content-Type: text/csv; charset=utf-8');
header('Content-Disposition: attachment; filename="' . $filename . '"');

// Create file pointer connected to the output stream
$output = fopen('php://output', 'w');

// Add BOM for UTF-8
fprintf($output, chr(0xEF).chr(0xBB).chr(0xBF));

// Add report title and filters
fputcsv($output, ['Expense Management System - Advanced Report']);
fputcsv($output, ['Report Type: ' . ucwords(str_replace('_', ' ', $report_type))]);
fputcsv($output, ['Generated: ' . date('Y-m-d H:i:s')]);
fputcsv($output, ['Date Range: ' . $date_from . ' to ' . $date_to]);
if ($status_filter) fputcsv($output, ['Status Filter: ' . ucfirst($status_filter)]);
if ($user_filter && $user['role'] === 'administrator') {
    $stmt = $db->prepare("SELECT full_name FROM users WHERE id = ?");
    $stmt->execute([$user_filter]);
    $user_name = $stmt->fetchColumn();
    fputcsv($output, ['User Filter: ' . $user_name]);
}
if ($item_filter) fputcsv($output, ['Item Filter: ' . $item_filter]);
if ($customer_filter) fputcsv($output, ['Customer Filter: ' . $customer_filter]);
fputcsv($output, []); // Empty row

// Add headers
fputcsv($output, $headers);

// Add data rows
foreach ($data as $row) {
    $csv_row = [];
    
    switch ($report_type) {
        case 'by_status':
            $csv_row = [
                ucfirst($row['status']),
                $row['count'],
                number_format($row['total_amount'], 2),
                number_format($row['avg_amount'], 2)
            ];
            break;
            
        case 'by_user':
            $csv_row = [
                $row['user_name'],
                ucfirst($row['role']),
                $row['count'],
                number_format($row['total_amount'], 2),
                number_format($row['avg_amount'], 2),
                $row['approved_count'],
                $row['rejected_count']
            ];
            break;
            
        case 'by_item':
            $csv_row = [
                $row['item'],
                $row['count'],
                number_format($row['total_amount'], 2),
                number_format($row['avg_amount'], 2)
            ];
            break;
            
        case 'by_customer':
            $csv_row = [
                $row['customer'],
                $row['count'],
                number_format($row['total_amount'], 2),
                number_format($row['avg_amount'], 2)
            ];
            break;
            
        case 'by_date':
            $csv_row = [
                $row['expense_date'],
                $row['count'],
                number_format($row['total_amount'], 2),
                number_format($row['avg_amount'], 2)
            ];
            break;
            
        default: // summary
            $csv_row = [
                $row['exno'],
                $row['date'],
                $row['item'],
                $row['customer'],
                $row['driver'],
                number_format($row['amount'], 2),
                ucfirst($row['status']),
                $row['created_by_name'],
                $row['created_at'],
                $row['verification_amount'] ? number_format($row['verification_amount'], 2) : '',
                $row['review_amount'] ? number_format($row['review_amount'], 2) : ''
            ];
            break;
    }
    
    fputcsv($output, $csv_row);
}

// Add summary at the end
fputcsv($output, []); // Empty row
fputcsv($output, ['Summary:']);
fputcsv($output, ['Total Records: ' . count($data)]);

if ($report_type === 'summary') {
    $total_amount = array_sum(array_column($data, 'amount'));
    $avg_amount = count($data) > 0 ? $total_amount / count($data) : 0;
    fputcsv($output, ['Total Amount: ' . number_format($total_amount, 2)]);
    fputcsv($output, ['Average Amount: ' . number_format($avg_amount, 2)]);
}

fclose($output);
exit();
?>
