<?php
session_start();
require_once '../config/database.php';
require_once '../includes/functions.php';
require_once '../includes/report_access_control.php';

// Check access
enforceReportAccess();

// Get user info
$database = new Database();
$db = $database->getConnection();
$user = getUserById($_SESSION['user_id'], $db);

// Date range parameters
$date_from = $_GET['date_from'] ?? date('Y-m-01'); // First day of current month
$date_to = $_GET['date_to'] ?? date('Y-m-t'); // Last day of current month
$status_filter = $_GET['status'] ?? '';
$user_filter = $_GET['user'] ?? '';

// Build WHERE conditions
$where_conditions = ["e.job_open_date >= ?", "e.job_open_date <= ?"];
$params = [$date_from, $date_to];

// Non-admin users see only their own data
if ($user['role'] !== 'administrator') {
    $where_conditions[] = "e.created_by = ?";
    $params[] = $user['id'];
}

// Status filter
if ($status_filter) {
    $where_conditions[] = "e.status = ?";
    $params[] = $status_filter;
}

// User filter (admin only)
if ($user_filter && $user['role'] === 'administrator') {
    $where_conditions[] = "e.created_by = ?";
    $params[] = $user_filter;
}

$where_clause = "WHERE " . implode(" AND ", $where_conditions);

// Get summary statistics
$summary_query = "
    SELECT
        COUNT(*) as total_expenses,
        SUM(e.total_amount) as total_amount,
        AVG(e.total_amount) as avg_amount,
        MIN(e.total_amount) as min_amount,
        MAX(e.total_amount) as max_amount,
        COUNT(CASE WHEN e.status = 'open' THEN 1 END) as open_count,
        COUNT(CASE WHEN e.status = 'pending' THEN 1 END) as pending_count,
        COUNT(CASE WHEN e.status = 'success' THEN 1 END) as success_count,
        COUNT(CASE WHEN e.status = 'rejected' THEN 1 END) as rejected_count,
        COUNT(CASE WHEN e.status = 'returned' THEN 1 END) as returned_count,
        SUM(CASE WHEN e.status = 'open' THEN e.total_amount ELSE 0 END) as open_amount,
        SUM(CASE WHEN e.status = 'pending' THEN e.total_amount ELSE 0 END) as pending_amount,
        SUM(CASE WHEN e.status = 'success' THEN e.total_amount ELSE 0 END) as success_amount,
        SUM(CASE WHEN e.status = 'rejected' THEN e.total_amount ELSE 0 END) as rejected_amount,
        SUM(CASE WHEN e.status = 'returned' THEN e.total_amount ELSE 0 END) as returned_amount
    FROM expenses e
    LEFT JOIN users u ON e.created_by = u.id
    LEFT JOIN items i ON e.item_id = i.id
    LEFT JOIN customers c ON e.customer_id = c.id
    $where_clause
";

$stmt = $db->prepare($summary_query);
$stmt->execute($params);
$summary = $stmt->fetch();

// Get monthly trends (last 6 months)
$monthly_query = "
    SELECT 
        DATE_FORMAT(e.job_open_date, '%Y-%m') as month,
        COUNT(*) as count,
        SUM(e.total_amount) as total_amount,
        AVG(e.total_amount) as avg_amount
    FROM expenses e
    LEFT JOIN users u ON e.created_by = u.id
    WHERE e.job_open_date >= DATE_SUB(CURDATE(), INTERVAL 6 MONTH)
    " . ($user['role'] !== 'administrator' ? "AND e.created_by = " . $user['id'] : "") . "
    GROUP BY DATE_FORMAT(e.job_open_date, '%Y-%m')
    ORDER BY month DESC
    LIMIT 6
";

$stmt = $db->prepare($monthly_query);
$stmt->execute();
$monthly_trends = $stmt->fetchAll();

// Get top items
$top_items_query = "
    SELECT 
        COALESCE(i.name, 'Unknown Item') as item_name,
        COUNT(*) as count,
        SUM(e.total_amount) as total_amount
    FROM expenses e
    LEFT JOIN items i ON e.item_id = i.id
    LEFT JOIN users u ON e.created_by = u.id
    $where_clause
    GROUP BY i.name
    ORDER BY total_amount DESC
    LIMIT 10
";

$stmt = $db->prepare($top_items_query);
$stmt->execute($params);
$top_items = $stmt->fetchAll();

// Get top customers
$top_customers_query = "
    SELECT 
        COALESCE(c.name, 'Unknown Customer') as customer_name,
        COUNT(*) as count,
        SUM(e.total_amount) as total_amount
    FROM expenses e
    LEFT JOIN customers c ON e.customer_id = c.id
    LEFT JOIN users u ON e.created_by = u.id
    $where_clause
    GROUP BY c.name
    ORDER BY total_amount DESC
    LIMIT 10
";

$stmt = $db->prepare($top_customers_query);
$stmt->execute($params);
$top_customers = $stmt->fetchAll();

// Get recent expenses
$recent_query = "
    SELECT 
        e.exno,
        e.job_open_date,
        COALESCE(i.name, 'Unknown Item') as item_name,
        COALESCE(c.name, 'Unknown Customer') as customer_name,
        e.total_amount,
        e.status,
        u.full_name as created_by_name,
        e.created_at
    FROM expenses e
    LEFT JOIN users u ON e.created_by = u.id
    LEFT JOIN items i ON e.item_id = i.id
    LEFT JOIN customers c ON e.customer_id = c.id
    $where_clause
    ORDER BY e.created_at DESC
    LIMIT 20
";

$stmt = $db->prepare($recent_query);
$stmt->execute($params);
$recent_expenses = $stmt->fetchAll();

// Get users for filter (admin only)
$users = [];
if ($user['role'] === 'administrator') {
    $users_query = "SELECT id, full_name FROM users WHERE role != 'administrator' ORDER BY full_name";
    $stmt = $db->prepare($users_query);
    $stmt->execute();
    $users = $stmt->fetchAll();
}
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Expense Summary - Expense Management System</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <style>
        .summary-card {
            transition: transform 0.2s;
            border-left: 4px solid #007bff;
        }
        .summary-card:hover {
            transform: translateY(-2px);
        }
        .chart-container {
            position: relative;
            height: 300px;
        }
        .table-responsive {
            border-radius: 0.375rem;
        }
        .status-badge {
            font-size: 0.75rem;
        }
        .metric-card {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }
        .metric-card .card-body {
            padding: 1.5rem;
        }
        .print-btn {
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
            border: none;
            color: white;
        }
        @media print {
            .no-print { display: none !important; }
            .card { break-inside: avoid; }
        }
    </style>
</head>
<body>
    <?php include '../includes/navbar.php'; ?>

    <div class="container-fluid mt-4">
        <div class="row">
            <div class="col-12">
                <div class="d-flex justify-content-between align-items-center mb-4">
                    <h2><i class="fas fa-chart-line me-2"></i>Expense Summary Report</h2>
                    <div class="no-print">
                        <button onclick="window.print()" class="btn print-btn">
                            <i class="fas fa-print me-2"></i>Print Report
                        </button>
                        <a href="advanced_reports.php" class="btn btn-outline-primary">
                            <i class="fas fa-chart-bar me-2"></i>Advanced Reports
                        </a>
                    </div>
                </div>

                <!-- Filters -->
                <div class="card mb-4 no-print">
                    <div class="card-header">
                        <h5 class="card-title mb-0"><i class="fas fa-filter me-2"></i>Filters</h5>
                    </div>
                    <div class="card-body">
                        <form method="GET" class="row g-3">
                            <div class="col-md-3">
                                <label for="date_from" class="form-label">From Date</label>
                                <input type="date" class="form-control" id="date_from" name="date_from" value="<?php echo $date_from; ?>">
                            </div>
                            <div class="col-md-3">
                                <label for="date_to" class="form-label">To Date</label>
                                <input type="date" class="form-control" id="date_to" name="date_to" value="<?php echo $date_to; ?>">
                            </div>
                            <div class="col-md-2">
                                <label for="status" class="form-label">Status</label>
                                <select class="form-select" id="status" name="status">
                                    <option value="">All Status</option>
                                    <option value="open" <?php echo $status_filter === 'open' ? 'selected' : ''; ?>>Open</option>
                                    <option value="pending" <?php echo $status_filter === 'pending' ? 'selected' : ''; ?>>Pending</option>
                                    <option value="success" <?php echo $status_filter === 'success' ? 'selected' : ''; ?>>Approved</option>
                                    <option value="rejected" <?php echo $status_filter === 'rejected' ? 'selected' : ''; ?>>Rejected</option>
                                    <option value="returned" <?php echo $status_filter === 'returned' ? 'selected' : ''; ?>>Returned</option>
                                </select>
                            </div>
                            <?php if ($user['role'] === 'administrator'): ?>
                            <div class="col-md-2">
                                <label for="user" class="form-label">User</label>
                                <select class="form-select" id="user" name="user">
                                    <option value="">All Users</option>
                                    <?php foreach ($users as $u): ?>
                                    <option value="<?php echo $u['id']; ?>" <?php echo $user_filter == $u['id'] ? 'selected' : ''; ?>>
                                        <?php echo htmlspecialchars($u['full_name']); ?>
                                    </option>
                                    <?php endforeach; ?>
                                </select>
                            </div>
                            <?php endif; ?>
                            <div class="col-md-2">
                                <label class="form-label">&nbsp;</label>
                                <button type="submit" class="btn btn-primary d-block w-100">
                                    <i class="fas fa-search me-2"></i>Filter
                                </button>
                            </div>
                        </form>
                    </div>
                </div>

                <!-- Summary Cards -->
                <div class="row mb-4">
                    <div class="col-md-3">
                        <div class="card summary-card h-100">
                            <div class="card-body text-center">
                                <i class="fas fa-receipt fa-2x text-primary mb-2"></i>
                                <h4 class="card-title"><?php echo number_format($summary['total_expenses']); ?></h4>
                                <p class="card-text text-muted">Total Expenses</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card summary-card h-100">
                            <div class="card-body text-center">
                                <i class="fas fa-dollar-sign fa-2x text-success mb-2"></i>
                                <h4 class="card-title">฿<?php echo number_format($summary['total_amount'] ?? 0, 2); ?></h4>
                                <p class="card-text text-muted">Total Amount</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card summary-card h-100">
                            <div class="card-body text-center">
                                <i class="fas fa-chart-line fa-2x text-info mb-2"></i>
                                <h4 class="card-title">฿<?php echo number_format($summary['avg_amount'] ?? 0, 2); ?></h4>
                                <p class="card-text text-muted">Average Amount</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card summary-card h-100">
                            <div class="card-body text-center">
                                <i class="fas fa-check-circle fa-2x text-success mb-2"></i>
                                <h4 class="card-title"><?php echo number_format($summary['success_count']); ?></h4>
                                <p class="card-text text-muted">Approved</p>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Status Breakdown -->
                <div class="row mb-4">
                    <div class="col-md-6">
                        <div class="card">
                            <div class="card-header">
                                <h5 class="card-title mb-0"><i class="fas fa-pie-chart me-2"></i>Status Distribution</h5>
                            </div>
                            <div class="card-body">
                                <div class="chart-container">
                                    <canvas id="statusChart"></canvas>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="card">
                            <div class="card-header">
                                <h5 class="card-title mb-0"><i class="fas fa-list me-2"></i>Status Summary</h5>
                            </div>
                            <div class="card-body">
                                <div class="row">
                                    <div class="col-6 mb-3">
                                        <div class="d-flex align-items-center">
                                            <div class="bg-info rounded-circle me-3" style="width: 12px; height: 12px;"></div>
                                            <div>
                                                <div class="fw-bold"><?php echo number_format($summary['open_count']); ?></div>
                                                <small class="text-muted">Open (฿<?php echo number_format($summary['open_amount'] ?? 0, 0); ?>)</small>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-6 mb-3">
                                        <div class="d-flex align-items-center">
                                            <div class="bg-warning rounded-circle me-3" style="width: 12px; height: 12px;"></div>
                                            <div>
                                                <div class="fw-bold"><?php echo number_format($summary['pending_count']); ?></div>
                                                <small class="text-muted">Pending (฿<?php echo number_format($summary['pending_amount'] ?? 0, 0); ?>)</small>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-6 mb-3">
                                        <div class="d-flex align-items-center">
                                            <div class="bg-success rounded-circle me-3" style="width: 12px; height: 12px;"></div>
                                            <div>
                                                <div class="fw-bold"><?php echo number_format($summary['success_count']); ?></div>
                                                <small class="text-muted">Approved (฿<?php echo number_format($summary['success_amount'] ?? 0, 0); ?>)</small>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-6 mb-3">
                                        <div class="d-flex align-items-center">
                                            <div class="bg-danger rounded-circle me-3" style="width: 12px; height: 12px;"></div>
                                            <div>
                                                <div class="fw-bold"><?php echo number_format($summary['rejected_count'] ?? 0); ?></div>
                                                <small class="text-muted">Rejected (฿<?php echo number_format($summary['rejected_amount'] ?? 0, 0); ?>)</small>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Monthly Trends -->
                <?php if (!empty($monthly_trends)): ?>
                <div class="row mb-4">
                    <div class="col-12">
                        <div class="card">
                            <div class="card-header">
                                <h5 class="card-title mb-0"><i class="fas fa-chart-bar me-2"></i>Monthly Trends (Last 6 Months)</h5>
                            </div>
                            <div class="card-body">
                                <div class="chart-container">
                                    <canvas id="monthlyChart"></canvas>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <?php endif; ?>

                <!-- Top Items and Customers -->
                <div class="row mb-4">
                    <div class="col-md-6">
                        <div class="card">
                            <div class="card-header">
                                <h5 class="card-title mb-0"><i class="fas fa-trophy me-2"></i>Top Items by Amount</h5>
                            </div>
                            <div class="card-body">
                                <?php if (!empty($top_items)): ?>
                                <div class="table-responsive">
                                    <table class="table table-sm">
                                        <thead>
                                            <tr>
                                                <th>Item</th>
                                                <th class="text-center">Count</th>
                                                <th class="text-end">Amount</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <?php foreach ($top_items as $item): ?>
                                            <tr>
                                                <td><?php echo htmlspecialchars($item['item_name']); ?></td>
                                                <td class="text-center"><?php echo number_format($item['count']); ?></td>
                                                <td class="text-end">฿<?php echo number_format($item['total_amount'], 2); ?></td>
                                            </tr>
                                            <?php endforeach; ?>
                                        </tbody>
                                    </table>
                                </div>
                                <?php else: ?>
                                <p class="text-muted text-center">No data available</p>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="card">
                            <div class="card-header">
                                <h5 class="card-title mb-0"><i class="fas fa-users me-2"></i>Top Customers by Amount</h5>
                            </div>
                            <div class="card-body">
                                <?php if (!empty($top_customers)): ?>
                                <div class="table-responsive">
                                    <table class="table table-sm">
                                        <thead>
                                            <tr>
                                                <th>Customer</th>
                                                <th class="text-center">Count</th>
                                                <th class="text-end">Amount</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <?php foreach ($top_customers as $customer): ?>
                                            <tr>
                                                <td><?php echo htmlspecialchars($customer['customer_name']); ?></td>
                                                <td class="text-center"><?php echo number_format($customer['count']); ?></td>
                                                <td class="text-end">฿<?php echo number_format($customer['total_amount'], 2); ?></td>
                                            </tr>
                                            <?php endforeach; ?>
                                        </tbody>
                                    </table>
                                </div>
                                <?php else: ?>
                                <p class="text-muted text-center">No data available</p>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Recent Expenses -->
                <div class="row mb-4">
                    <div class="col-12">
                        <div class="card">
                            <div class="card-header">
                                <h5 class="card-title mb-0"><i class="fas fa-clock me-2"></i>Recent Expenses (Last 20)</h5>
                            </div>
                            <div class="card-body">
                                <?php if (!empty($recent_expenses)): ?>
                                <div class="table-responsive">
                                    <table class="table table-hover">
                                        <thead>
                                            <tr>
                                                <th>EX No.</th>
                                                <th>Date</th>
                                                <th>Item</th>
                                                <th>Customer</th>
                                                <th class="text-end">Amount</th>
                                                <th>Status</th>
                                                <th>Created By</th>
                                                <th>Created At</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <?php foreach ($recent_expenses as $expense): ?>
                                            <tr>
                                                <td>
                                                    <a href="../expenses/view.php?id=<?php echo $expense['exno']; ?>" class="text-decoration-none">
                                                        <?php echo htmlspecialchars($expense['exno']); ?>
                                                    </a>
                                                </td>
                                                <td><?php echo date('M j, Y', strtotime($expense['job_open_date'])); ?></td>
                                                <td><?php echo htmlspecialchars($expense['item_name']); ?></td>
                                                <td><?php echo htmlspecialchars($expense['customer_name']); ?></td>
                                                <td class="text-end">฿<?php echo number_format($expense['total_amount'], 2); ?></td>
                                                <td><?php echo getStatusBadge($expense['status']); ?></td>
                                                <td><?php echo htmlspecialchars($expense['created_by_name']); ?></td>
                                                <td><?php echo date('M j, Y g:i A', strtotime($expense['created_at'])); ?></td>
                                            </tr>
                                            <?php endforeach; ?>
                                        </tbody>
                                    </table>
                                </div>
                                <?php else: ?>
                                <p class="text-muted text-center">No expenses found</p>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Report Footer -->
                <div class="row">
                    <div class="col-12">
                        <div class="card bg-light">
                            <div class="card-body text-center">
                                <p class="mb-0 text-muted">
                                    <i class="fas fa-calendar me-2"></i>Report generated on <?php echo date('F j, Y \a\t g:i A'); ?>
                                    | Period: <?php echo date('M j, Y', strtotime($date_from)); ?> - <?php echo date('M j, Y', strtotime($date_to)); ?>
                                    <?php if ($user['role'] !== 'administrator'): ?>
                                    | User: <?php echo htmlspecialchars($user['full_name']); ?>
                                    <?php endif; ?>
                                </p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Status Distribution Chart
        const statusCtx = document.getElementById('statusChart').getContext('2d');
        const statusChart = new Chart(statusCtx, {
            type: 'doughnut',
            data: {
                labels: ['Open', 'Pending', 'Approved', 'Rejected', 'Returned'],
                datasets: [{
                    data: [
                        <?php echo $summary['open_count']; ?>,
                        <?php echo $summary['pending_count']; ?>,
                        <?php echo $summary['success_count']; ?>,
                        <?php echo $summary['rejected_count'] ?? 0; ?>,
                        <?php echo $summary['returned_count'] ?? 0; ?>
                    ],
                    backgroundColor: [
                        '#17a2b8',
                        '#ffc107',
                        '#28a745',
                        '#dc3545',
                        '#6c757d'
                    ],
                    borderWidth: 2,
                    borderColor: '#fff'
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        position: 'bottom',
                        labels: {
                            padding: 20,
                            usePointStyle: true
                        }
                    }
                }
            }
        });

        // Monthly Trends Chart
        <?php if (!empty($monthly_trends)): ?>
        const monthlyCtx = document.getElementById('monthlyChart').getContext('2d');
        const monthlyChart = new Chart(monthlyCtx, {
            type: 'bar',
            data: {
                labels: [<?php echo implode(',', array_map(function($row) { return "'" . date('M Y', strtotime($row['month'] . '-01')) . "'"; }, array_reverse($monthly_trends))); ?>],
                datasets: [{
                    label: 'Count',
                    data: [<?php echo implode(',', array_column(array_reverse($monthly_trends), 'count')); ?>],
                    backgroundColor: 'rgba(54, 162, 235, 0.5)',
                    borderColor: 'rgba(54, 162, 235, 1)',
                    borderWidth: 1,
                    yAxisID: 'y'
                }, {
                    label: 'Amount (฿)',
                    data: [<?php echo implode(',', array_column(array_reverse($monthly_trends), 'total_amount')); ?>],
                    backgroundColor: 'rgba(255, 99, 132, 0.5)',
                    borderColor: 'rgba(255, 99, 132, 1)',
                    borderWidth: 1,
                    yAxisID: 'y1'
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                interaction: {
                    mode: 'index',
                    intersect: false,
                },
                scales: {
                    x: {
                        display: true,
                        title: {
                            display: true,
                            text: 'Month'
                        }
                    },
                    y: {
                        type: 'linear',
                        display: true,
                        position: 'left',
                        title: {
                            display: true,
                            text: 'Count'
                        }
                    },
                    y1: {
                        type: 'linear',
                        display: true,
                        position: 'right',
                        title: {
                            display: true,
                            text: 'Amount (฿)'
                        },
                        grid: {
                            drawOnChartArea: false,
                        },
                    }
                }
            }
        });
        <?php endif; ?>
    </script>
</body>
</html>
