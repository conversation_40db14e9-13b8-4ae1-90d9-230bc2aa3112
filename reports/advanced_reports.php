<?php
session_start();
require_once '../config/database.php';
require_once '../includes/functions.php';
require_once '../includes/report_access_control.php';

// Check access
enforceReportAccess();

// Initialize database connection
$database = new Database();
$db = $database->getConnection();

$user = getUserById($_SESSION['user_id'], $db);
if (!$user) {
    header('Location: ../login.php');
    exit();
}

// Get filter parameters
$date_from = $_GET['date_from'] ?? date('Y-m-01'); // First day of current month
$date_to = $_GET['date_to'] ?? date('Y-m-t'); // Last day of current month
$status_filter = $_GET['status'] ?? '';
$user_filter = $_GET['user_id'] ?? '';
$item_filter = $_GET['item'] ?? '';
$customer_filter = $_GET['customer'] ?? '';
$report_type = $_GET['report_type'] ?? 'summary';

// Build WHERE conditions
$where_conditions = [];
$params = [];

// Date range filter
if ($date_from) {
    $where_conditions[] = "e.job_open_date >= ?";
    $params[] = $date_from;
}
if ($date_to) {
    $where_conditions[] = "e.job_open_date <= ?";
    $params[] = $date_to;
}

// Status filter
if ($status_filter) {
    $where_conditions[] = "e.status = ?";
    $params[] = $status_filter;
}

// User filter and role-based access control
if ($user_filter && $user['role'] === 'administrator') {
    $where_conditions[] = "e.created_by = ?";
    $params[] = $user_filter;
} elseif ($user['role'] === 'data_entry') {
    // Data entry users see only their own data
    $where_conditions[] = "e.created_by = ?";
    $params[] = $user['id'];
}
// verification, reviewer, and administrator can see all data (no additional filter)

// Item filter
if ($item_filter) {
    $where_conditions[] = "i.name LIKE ?";
    $params[] = "%$item_filter%";
}

// Customer filter
if ($customer_filter) {
    $where_conditions[] = "c.name LIKE ?";
    $params[] = "%$customer_filter%";
}

$where_clause = !empty($where_conditions) ? 'WHERE ' . implode(' AND ', $where_conditions) : '';

// Get summary statistics
$summary_query = "
    SELECT
        COUNT(*) as total_expenses,
        SUM(e.total_amount) as total_amount,
        AVG(e.total_amount) as avg_amount,
        MIN(e.total_amount) as min_amount,
        MAX(e.total_amount) as max_amount,
        COUNT(CASE WHEN e.status = 'open' THEN 1 END) as open_count,
        COUNT(CASE WHEN e.status = 'pending' THEN 1 END) as pending_count,
        COUNT(CASE WHEN e.status = 'success' THEN 1 END) as success_count,
        COUNT(CASE WHEN e.status = 'rejected' THEN 1 END) as rejected_count,
        COUNT(CASE WHEN e.status = 'returned' THEN 1 END) as returned_count,
        SUM(CASE WHEN e.status = 'open' THEN e.total_amount ELSE 0 END) as open_amount,
        SUM(CASE WHEN e.status = 'pending' THEN e.total_amount ELSE 0 END) as pending_amount,
        SUM(CASE WHEN e.status = 'success' THEN e.total_amount ELSE 0 END) as success_amount,
        SUM(CASE WHEN e.status = 'rejected' THEN e.total_amount ELSE 0 END) as rejected_amount,
        SUM(CASE WHEN e.status = 'returned' THEN e.total_amount ELSE 0 END) as returned_amount
    FROM expenses e
    LEFT JOIN users u ON e.created_by = u.id
    LEFT JOIN items i ON e.item_id = i.id
    LEFT JOIN customers c ON e.customer_id = c.id
    $where_clause
";

$stmt = $db->prepare($summary_query);
$stmt->execute($params);
$summary = $stmt->fetch();

// Get detailed data based on report type
$detailed_data = [];
switch ($report_type) {
    case 'by_status':
        $query = "
            SELECT
                e.status,
                COUNT(*) as count,
                SUM(e.total_amount) as total_amount,
                AVG(e.total_amount) as avg_amount
            FROM expenses e
            LEFT JOIN users u ON e.created_by = u.id
            LEFT JOIN items i ON e.item_id = i.id
            LEFT JOIN customers c ON e.customer_id = c.id
            $where_clause
            GROUP BY e.status
            ORDER BY total_amount DESC
        ";
        break;
        
    case 'by_user':
        if ($user['role'] === 'administrator') {
            $query = "
                SELECT 
                    u.full_name as user_name,
                    u.role,
                    COUNT(*) as count,
                    SUM(e.total_amount) as total_amount,
                    AVG(e.total_amount) as avg_amount,
                    COUNT(CASE WHEN e.status = 'success' THEN 1 END) as approved_count,
                    COUNT(CASE WHEN e.status = 'rejected' THEN 1 END) as rejected_count
                FROM expenses e
                LEFT JOIN users u ON e.created_by = u.id
                LEFT JOIN items i ON e.item_id = i.id
                LEFT JOIN customers c ON e.customer_id = c.id
                $where_clause
                GROUP BY e.created_by, u.full_name, u.role
                ORDER BY total_amount DESC
            ";
        } else {
            $detailed_data = []; // Non-admin can't see by user report
        }
        break;
        
    case 'by_item':
        $query = "
            SELECT
                COALESCE(i.name, 'Unknown Item') as item,
                COUNT(*) as count,
                SUM(e.total_amount) as total_amount,
                AVG(e.total_amount) as avg_amount
            FROM expenses e
            LEFT JOIN users u ON e.created_by = u.id
            LEFT JOIN items i ON e.item_id = i.id
            LEFT JOIN customers c ON e.customer_id = c.id
            $where_clause
            GROUP BY i.name
            ORDER BY total_amount DESC
            LIMIT 20
        ";
        break;
        
    case 'by_customer':
        $query = "
            SELECT
                COALESCE(c.name, 'Unknown Customer') as customer,
                COUNT(*) as count,
                SUM(e.total_amount) as total_amount,
                AVG(e.total_amount) as avg_amount
            FROM expenses e
            LEFT JOIN users u ON e.created_by = u.id
            LEFT JOIN items i ON e.item_id = i.id
            LEFT JOIN customers c ON e.customer_id = c.id
            $where_clause
            GROUP BY c.name
            ORDER BY total_amount DESC
            LIMIT 20
        ";
        break;
        
    case 'by_date':
        $query = "
            SELECT
                DATE(e.job_open_date) as expense_date,
                COUNT(*) as count,
                SUM(e.total_amount) as total_amount,
                AVG(e.total_amount) as avg_amount
            FROM expenses e
            LEFT JOIN users u ON e.created_by = u.id
            LEFT JOIN items i ON e.item_id = i.id
            LEFT JOIN customers c ON e.customer_id = c.id
            $where_clause
            GROUP BY DATE(e.job_open_date)
            ORDER BY expense_date DESC
            LIMIT 30
        ";
        break;
        
    default: // summary
        $query = "
            SELECT
                e.id,
                e.exno,
                e.job_open_date,
                COALESCE(i.name, 'Unknown Item') as item,
                COALESCE(c.name, 'Unknown Customer') as customer,
                e.total_amount,
                e.status,
                u.full_name as created_by_name,
                e.created_at
            FROM expenses e
            LEFT JOIN users u ON e.created_by = u.id
            LEFT JOIN items i ON e.item_id = i.id
            LEFT JOIN customers c ON e.customer_id = c.id
            $where_clause
            ORDER BY e.created_at DESC
            LIMIT 100
        ";
        break;
}

if (isset($query) && ($user['role'] === 'administrator' || $report_type !== 'by_user')) {
    $stmt = $db->prepare($query);
    $stmt->execute($params);
    $detailed_data = $stmt->fetchAll();
}

// Get filter options
$users_query = "SELECT id, full_name, role FROM users ORDER BY full_name";
$users = $db->query($users_query)->fetchAll();

$items_query = "SELECT DISTINCT i.name as item FROM expenses e LEFT JOIN items i ON e.item_id = i.id WHERE i.name IS NOT NULL AND i.name != '' ORDER BY i.name";
$items = $db->query($items_query)->fetchAll();

$customers_query = "SELECT DISTINCT c.name as customer FROM expenses e LEFT JOIN customers c ON e.customer_id = c.id WHERE c.name IS NOT NULL AND c.name != '' ORDER BY c.name";
$customers = $db->query($customers_query)->fetchAll();
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Advanced Reports - Expense Management System</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <style>
        .report-card {
            transition: transform 0.2s;
        }
        .report-card:hover {
            transform: translateY(-2px);
        }
        .chart-container {
            position: relative;
            height: 400px;
            margin: 20px 0;
        }
        .filter-section {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 20px;
            margin-bottom: 20px;
        }
        .export-buttons {
            margin: 10px 0;
        }
        @media print {
            .no-print {
                display: none !important;
            }
            .chart-container {
                height: 300px;
            }
        }
    </style>
</head>
<body>
    <?php include '../includes/navbar.php'; ?>
    
    <div class="container-fluid mt-4">
        <div class="row">
            <div class="col-12">
                <div class="d-flex justify-content-between align-items-center mb-4">
                    <div>
                        <h1><i class="fas fa-chart-line me-2"></i>Advanced Reports</h1>
                        <p class="text-muted mb-0">Comprehensive expense analysis and insights</p>
                    </div>
                    <div class="export-buttons no-print">
                        <button class="btn btn-outline-success" onclick="exportToExcel()">
                            <i class="fas fa-file-excel me-1"></i>Export Excel
                        </button>
                        <button class="btn btn-outline-danger" onclick="exportToPDF()">
                            <i class="fas fa-file-pdf me-1"></i>Export PDF
                        </button>
                        <button class="btn btn-outline-secondary" onclick="window.print()">
                            <i class="fas fa-print me-1"></i>Print
                        </button>
                    </div>
                </div>

                <!-- Filter Section -->
                <div class="filter-section no-print">
                    <form method="GET" class="row g-3">
                        <div class="col-md-3">
                            <label for="date_from" class="form-label">Date From</label>
                            <input type="date" class="form-control" id="date_from" name="date_from" value="<?php echo htmlspecialchars($date_from); ?>">
                        </div>
                        <div class="col-md-3">
                            <label for="date_to" class="form-label">Date To</label>
                            <input type="date" class="form-control" id="date_to" name="date_to" value="<?php echo htmlspecialchars($date_to); ?>">
                        </div>
                        <div class="col-md-2">
                            <label for="status" class="form-label">Status</label>
                            <select class="form-select" id="status" name="status">
                                <option value="">All Statuses</option>
                                <option value="open" <?php echo $status_filter === 'open' ? 'selected' : ''; ?>>Open</option>
                                <option value="pending" <?php echo $status_filter === 'pending' ? 'selected' : ''; ?>>Pending</option>
                                <option value="success" <?php echo $status_filter === 'success' ? 'selected' : ''; ?>>Approved</option>
                                <option value="rejected" <?php echo $status_filter === 'rejected' ? 'selected' : ''; ?>>Rejected</option>
                                <option value="returned" <?php echo $status_filter === 'returned' ? 'selected' : ''; ?>>Returned</option>
                            </select>
                        </div>
                        <?php if ($user['role'] === 'administrator'): ?>
                        <div class="col-md-2">
                            <label for="user_id" class="form-label">User</label>
                            <select class="form-select" id="user_id" name="user_id">
                                <option value="">All Users</option>
                                <?php foreach ($users as $u): ?>
                                    <option value="<?php echo $u['id']; ?>" <?php echo $user_filter == $u['id'] ? 'selected' : ''; ?>>
                                        <?php echo htmlspecialchars($u['full_name']); ?> (<?php echo ucfirst($u['role']); ?>)
                                    </option>
                                <?php endforeach; ?>
                            </select>
                        </div>
                        <?php endif; ?>
                        <div class="col-md-2">
                            <label for="report_type" class="form-label">Report Type</label>
                            <select class="form-select" id="report_type" name="report_type">
                                <option value="summary" <?php echo $report_type === 'summary' ? 'selected' : ''; ?>>Summary</option>
                                <option value="by_status" <?php echo $report_type === 'by_status' ? 'selected' : ''; ?>>By Status</option>
                                <?php if ($user['role'] === 'administrator'): ?>
                                <option value="by_user" <?php echo $report_type === 'by_user' ? 'selected' : ''; ?>>By User</option>
                                <?php endif; ?>
                                <option value="by_item" <?php echo $report_type === 'by_item' ? 'selected' : ''; ?>>By Item</option>
                                <option value="by_customer" <?php echo $report_type === 'by_customer' ? 'selected' : ''; ?>>By Customer</option>
                                <option value="by_date" <?php echo $report_type === 'by_date' ? 'selected' : ''; ?>>By Date</option>
                            </select>
                        </div>
                        <div class="col-md-6">
                            <label for="item" class="form-label">Item (Optional)</label>
                            <input type="text" class="form-control" id="item" name="item" value="<?php echo htmlspecialchars($item_filter); ?>" placeholder="Search by item...">
                        </div>
                        <div class="col-md-6">
                            <label for="customer" class="form-label">Customer (Optional)</label>
                            <input type="text" class="form-control" id="customer" name="customer" value="<?php echo htmlspecialchars($customer_filter); ?>" placeholder="Search by customer...">
                        </div>
                        <div class="col-12">
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-search me-1"></i>Generate Report
                            </button>
                            <a href="advanced_reports.php" class="btn btn-outline-secondary">
                                <i class="fas fa-refresh me-1"></i>Reset Filters
                            </a>
                        </div>
                    </form>
                </div>

                <!-- Summary Statistics -->
                <div class="row mb-4">
                    <div class="col-md-2">
                        <div class="card report-card h-100">
                            <div class="card-body text-center">
                                <i class="fas fa-receipt fa-2x text-primary mb-2"></i>
                                <h5 class="card-title"><?php echo number_format($summary['total_expenses'] ?? 0); ?></h5>
                                <p class="card-text text-muted">Total Expenses</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-2">
                        <div class="card report-card h-100">
                            <div class="card-body text-center">
                                <i class="fas fa-dollar-sign fa-2x text-success mb-2"></i>
                                <h5 class="card-title">฿<?php echo number_format($summary['total_amount'] ?? 0, 2); ?></h5>
                                <p class="card-text text-muted">Total Amount</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-2">
                        <div class="card report-card h-100">
                            <div class="card-body text-center">
                                <i class="fas fa-chart-line fa-2x text-info mb-2"></i>
                                <h5 class="card-title">฿<?php echo number_format($summary['avg_amount'] ?? 0, 2); ?></h5>
                                <p class="card-text text-muted">Average Amount</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-2">
                        <div class="card report-card h-100">
                            <div class="card-body text-center">
                                <i class="fas fa-check-circle fa-2x text-success mb-2"></i>
                                <h5 class="card-title"><?php echo number_format($summary['success_count'] ?? 0); ?></h5>
                                <p class="card-text text-muted">Approved</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-2">
                        <div class="card report-card h-100">
                            <div class="card-body text-center">
                                <i class="fas fa-times-circle fa-2x text-danger mb-2"></i>
                                <h5 class="card-title"><?php echo number_format($summary['rejected_count'] ?? 0); ?></h5>
                                <p class="card-text text-muted">Rejected</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-2">
                        <div class="card report-card h-100">
                            <div class="card-body text-center">
                                <i class="fas fa-undo fa-2x text-secondary mb-2"></i>
                                <h5 class="card-title"><?php echo number_format($summary['returned_count'] ?? 0); ?></h5>
                                <p class="card-text text-muted">Returned</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-2">
                        <div class="card report-card h-100">
                            <div class="card-body text-center">
                                <i class="fas fa-clock fa-2x text-warning mb-2"></i>
                                <h5 class="card-title"><?php echo number_format($summary['pending_count'] ?? 0); ?></h5>
                                <p class="card-text text-muted">Pending</p>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Charts Section -->
                <?php if ($report_type === 'by_status' && !empty($detailed_data)): ?>
                <div class="row mb-4">
                    <div class="col-md-6">
                        <div class="card">
                            <div class="card-header">
                                <h5 class="card-title mb-0">Expenses by Status (Count)</h5>
                            </div>
                            <div class="card-body">
                                <div class="chart-container">
                                    <canvas id="statusCountChart"></canvas>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="card">
                            <div class="card-header">
                                <h5 class="card-title mb-0">Expenses by Status (Amount)</h5>
                            </div>
                            <div class="card-body">
                                <div class="chart-container">
                                    <canvas id="statusAmountChart"></canvas>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <?php endif; ?>

                <!-- Data Table -->
                <div class="card">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <h5 class="card-title mb-0">
                            <i class="fas fa-table me-1"></i>
                            <?php
                            $report_titles = [
                                'summary' => 'Expense Summary',
                                'by_status' => 'Expenses by Status',
                                'by_user' => 'Expenses by User',
                                'by_item' => 'Expenses by Item',
                                'by_customer' => 'Expenses by Customer',
                                'by_date' => 'Expenses by Date'
                            ];
                            echo $report_titles[$report_type] ?? 'Report Data';
                            ?>
                        </h5>
                        <span class="badge bg-primary"><?php echo count($detailed_data); ?> records</span>
                    </div>
                    <div class="card-body">
                        <?php if (!empty($detailed_data)): ?>
                        <div class="table-responsive">
                            <table class="table table-striped table-hover">
                                <thead class="table-dark">
                                    <tr>
                                        <?php if ($report_type === 'summary'): ?>
                                            <th>Expense No</th>
                                            <th>Date</th>
                                            <th>Item</th>
                                            <th>Customer</th>
                                            <th>Amount</th>
                                            <th>Status</th>
                                            <th>Created By</th>
                                            <th>Created At</th>
                                        <?php elseif ($report_type === 'by_status'): ?>
                                            <th>Status</th>
                                            <th>Count</th>
                                            <th>Total Amount</th>
                                            <th>Average Amount</th>
                                        <?php elseif ($report_type === 'by_user'): ?>
                                            <th>User</th>
                                            <th>Role</th>
                                            <th>Count</th>
                                            <th>Total Amount</th>
                                            <th>Average Amount</th>
                                            <th>Approved</th>
                                            <th>Rejected</th>
                                        <?php elseif ($report_type === 'by_item'): ?>
                                            <th>Item</th>
                                            <th>Count</th>
                                            <th>Total Amount</th>
                                            <th>Average Amount</th>
                                        <?php elseif ($report_type === 'by_customer'): ?>
                                            <th>Customer</th>
                                            <th>Count</th>
                                            <th>Total Amount</th>
                                            <th>Average Amount</th>
                                        <?php elseif ($report_type === 'by_date'): ?>
                                            <th>Date</th>
                                            <th>Count</th>
                                            <th>Total Amount</th>
                                            <th>Average Amount</th>
                                        <?php endif; ?>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($detailed_data as $row): ?>
                                    <tr>
                                        <?php if ($report_type === 'summary'): ?>
                                            <td>
                                                <a href="../expenses/view.php?id=<?php echo $row['id']; ?>" class="text-decoration-none">
                                                    <?php echo htmlspecialchars($row['exno']); ?>
                                                </a>
                                            </td>
                                            <td><?php echo date('M j, Y', strtotime($row['job_open_date'])); ?></td>
                                            <td><?php echo htmlspecialchars($row['item']); ?></td>
                                            <td><?php echo htmlspecialchars($row['customer']); ?></td>
                                            <td>฿<?php echo number_format($row['total_amount'] ?? 0, 2); ?></td>
                                            <td><?php echo getStatusBadge($row['status']); ?></td>
                                            <td><?php echo htmlspecialchars($row['created_by_name']); ?></td>
                                            <td><?php echo date('M j, Y g:i A', strtotime($row['created_at'])); ?></td>
                                        <?php elseif ($report_type === 'by_status'): ?>
                                            <td><?php echo getStatusBadge($row['status']); ?></td>
                                            <td><?php echo number_format($row['count'] ?? 0); ?></td>
                                            <td>฿<?php echo number_format($row['total_amount'] ?? 0, 2); ?></td>
                                            <td>฿<?php echo number_format($row['avg_amount'] ?? 0, 2); ?></td>
                                        <?php elseif ($report_type === 'by_user'): ?>
                                            <td><?php echo htmlspecialchars($row['user_name']); ?></td>
                                            <td><span class="badge bg-secondary"><?php echo ucfirst($row['role']); ?></span></td>
                                            <td><?php echo number_format($row['count'] ?? 0); ?></td>
                                            <td>฿<?php echo number_format($row['total_amount'] ?? 0, 2); ?></td>
                                            <td>฿<?php echo number_format($row['avg_amount'] ?? 0, 2); ?></td>
                                            <td><span class="badge bg-success"><?php echo number_format($row['approved_count'] ?? 0); ?></span></td>
                                            <td><span class="badge bg-danger"><?php echo number_format($row['rejected_count'] ?? 0); ?></span></td>
                                        <?php elseif ($report_type === 'by_item'): ?>
                                            <td><?php echo htmlspecialchars($row['item']); ?></td>
                                            <td><?php echo number_format($row['count'] ?? 0); ?></td>
                                            <td>฿<?php echo number_format($row['total_amount'] ?? 0, 2); ?></td>
                                            <td>฿<?php echo number_format($row['avg_amount'] ?? 0, 2); ?></td>
                                        <?php elseif ($report_type === 'by_customer'): ?>
                                            <td><?php echo htmlspecialchars($row['customer']); ?></td>
                                            <td><?php echo number_format($row['count'] ?? 0); ?></td>
                                            <td>฿<?php echo number_format($row['total_amount'] ?? 0, 2); ?></td>
                                            <td>฿<?php echo number_format($row['avg_amount'] ?? 0, 2); ?></td>
                                        <?php elseif ($report_type === 'by_date'): ?>
                                            <td><?php echo date('M j, Y', strtotime($row['expense_date'])); ?></td>
                                            <td><?php echo number_format($row['count'] ?? 0); ?></td>
                                            <td>฿<?php echo number_format($row['total_amount'] ?? 0, 2); ?></td>
                                            <td>฿<?php echo number_format($row['avg_amount'] ?? 0, 2); ?></td>
                                        <?php endif; ?>
                                    </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                        <?php else: ?>
                        <div class="text-center py-4">
                            <i class="fas fa-chart-line fa-3x text-muted mb-3"></i>
                            <h5 class="text-muted">No data found</h5>
                            <p class="text-muted">Try adjusting your filters to see results.</p>
                        </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Chart.js for status reports
        <?php if ($report_type === 'by_status' && !empty($detailed_data)): ?>
        // Status Count Chart
        const statusCountCtx = document.getElementById('statusCountChart').getContext('2d');
        const statusCountChart = new Chart(statusCountCtx, {
            type: 'doughnut',
            data: {
                labels: [<?php echo implode(',', array_map(function($row) { return "'" . ucfirst($row['status']) . "'"; }, $detailed_data)); ?>],
                datasets: [{
                    data: [<?php echo implode(',', array_column($detailed_data, 'count')); ?>],
                    backgroundColor: [
                        '#17a2b8', '#ffc107', '#28a745', '#dc3545', '#6c757d'
                    ],
                    borderWidth: 2,
                    borderColor: '#fff'
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        position: 'bottom'
                    },
                    tooltip: {
                        callbacks: {
                            label: function(context) {
                                return context.label + ': ' + context.parsed.toLocaleString() + ' expenses';
                            }
                        }
                    }
                }
            }
        });

        // Status Amount Chart
        const statusAmountCtx = document.getElementById('statusAmountChart').getContext('2d');
        const statusAmountChart = new Chart(statusAmountCtx, {
            type: 'bar',
            data: {
                labels: [<?php echo implode(',', array_map(function($row) { return "'" . ucfirst($row['status']) . "'"; }, $detailed_data)); ?>],
                datasets: [{
                    label: 'Total Amount (฿)',
                    data: [<?php echo implode(',', array_map(function($row) { return number_format($row['total_amount'] ?? 0, 2, '.', ''); }, $detailed_data)); ?>],
                    backgroundColor: [
                        '#17a2b8', '#ffc107', '#28a745', '#dc3545', '#6c757d'
                    ],
                    borderWidth: 1
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                scales: {
                    y: {
                        beginAtZero: true,
                        ticks: {
                            callback: function(value) {
                                return '฿' + value.toLocaleString();
                            }
                        }
                    }
                },
                plugins: {
                    legend: {
                        display: false
                    },
                    tooltip: {
                        callbacks: {
                            label: function(context) {
                                return 'Total Amount: ฿' + context.parsed.y.toLocaleString();
                            }
                        }
                    }
                }
            }
        });
        <?php endif; ?>

        // Export functions
        function exportToExcel() {
            // Create a form to submit with current filters
            const form = document.createElement('form');
            form.method = 'POST';
            form.action = 'export_excel.php';

            // Add current filter parameters
            const params = new URLSearchParams(window.location.search);
            for (const [key, value] of params) {
                const input = document.createElement('input');
                input.type = 'hidden';
                input.name = key;
                input.value = value;
                form.appendChild(input);
            }

            document.body.appendChild(form);
            form.submit();
            document.body.removeChild(form);
        }

        function exportToPDF() {
            // Create a form to submit with current filters
            const form = document.createElement('form');
            form.method = 'POST';
            form.action = 'export_pdf.php';

            // Add current filter parameters
            const params = new URLSearchParams(window.location.search);
            for (const [key, value] of params) {
                const input = document.createElement('input');
                input.type = 'hidden';
                input.name = key;
                input.value = value;
                form.appendChild(input);
            }

            document.body.appendChild(form);
            form.submit();
            document.body.removeChild(form);
        }

        // Auto-refresh every 5 minutes
        setTimeout(function() {
            location.reload();
        }, 300000);
    </script>
</body>
</html>
