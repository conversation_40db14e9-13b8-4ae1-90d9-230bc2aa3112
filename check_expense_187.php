<?php
require_once 'config/database.php';

$database = new Database();
$db = $database->getConnection();

echo "<h2>Check Expense ID 187</h2>";

// Get expense 187 data
$stmt = $db->prepare('SELECT * FROM expenses WHERE id = 187');
$stmt->execute();
$expense = $stmt->fetch();

if ($expense) {
    echo "<h3>Expense 187 Details:</h3>";
    echo "<table border='1' style='border-collapse: collapse;'>";
    echo "<tr><th>Field</th><th>Value</th></tr>";
    echo "<tr><td>ID</td><td>{$expense['id']}</td></tr>";
    echo "<tr><td>Expense No</td><td>{$expense['exno']}</td></tr>";
    echo "<tr><td>Transfer Slip Image</td><td>{$expense['transfer_slip_image']}</td></tr>";
    echo "<tr><td>Transfer Amount</td><td>{$expense['transfer_amount']}</td></tr>";
    echo "<tr><td>Created At</td><td>{$expense['created_at']}</td></tr>";
    echo "<tr><td>Created By</td><td>{$expense['created_by']}</td></tr>";
    echo "</table>";
    
    $filename = $expense['transfer_slip_image'];
    if ($filename) {
        echo "<h3>File Analysis:</h3>";
        $file_path = 'uploads/transfer_slips/' . $filename;
        echo "<ul>";
        echo "<li><strong>Filename:</strong> $filename</li>";
        echo "<li><strong>Expected path:</strong> $file_path</li>";
        echo "<li><strong>File exists:</strong> " . (file_exists($file_path) ? '✅ YES' : '❌ NO') . "</li>";
        echo "</ul>";
        
        if (!file_exists($file_path)) {
            echo "<h4>🔍 Looking for similar files...</h4>";
            
            // Get creation time
            $created_time = strtotime($expense['created_at']);
            echo "<p>Expense created at: " . date('Y-m-d H:i:s', $created_time) . "</p>";
            
            // Find files created around the same time (within 2 hours)
            $time_range = 7200; // 2 hours
            $start_time = $created_time - $time_range;
            $end_time = $created_time + $time_range;
            
            $all_files = glob('uploads/transfer_slips/transfer_*.{jpg,jpeg,png}', GLOB_BRACE);
            $candidates = [];
            
            foreach ($all_files as $file) {
                $file_time = filemtime($file);
                if ($file_time >= $start_time && $file_time <= $end_time) {
                    $time_diff = abs($file_time - $created_time);
                    $candidates[] = [
                        'path' => $file,
                        'filename' => basename($file),
                        'time_diff' => $time_diff,
                        'file_time' => $file_time
                    ];
                }
            }
            
            // Sort by time difference
            usort($candidates, function($a, $b) {
                return $a['time_diff'] - $b['time_diff'];
            });
            
            if ($candidates) {
                echo "<h4>📋 Candidate files (within 2 hours):</h4>";
                echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
                echo "<tr><th>Filename</th><th>Time Difference</th><th>File Time</th><th>Actions</th></tr>";
                
                foreach (array_slice($candidates, 0, 5) as $candidate) {
                    $time_diff_min = round($candidate['time_diff'] / 60);
                    $file_time_str = date('Y-m-d H:i:s', $candidate['file_time']);
                    
                    echo "<tr>";
                    echo "<td>{$candidate['filename']}</td>";
                    echo "<td>{$time_diff_min} minutes</td>";
                    echo "<td>$file_time_str</td>";
                    echo "<td>";
                    echo "<a href='api/view_file.php?file=" . urlencode($candidate['filename']) . "&type=transfer_slip' target='_blank'>Preview</a> | ";
                    echo "<a href='?update_expense=187&new_file=" . urlencode($candidate['filename']) . "'>Use This File</a>";
                    echo "</td>";
                    echo "</tr>";
                }
                echo "</table>";
            } else {
                echo "<p>❌ No candidate files found within 2 hours of creation time.</p>";
            }
            
            // Show recent files anyway
            echo "<h4>📁 Recent files in directory:</h4>";
            $recent_files = array_slice($all_files, -10);
            echo "<ul>";
            foreach ($recent_files as $file) {
                $filename = basename($file);
                $file_time = date('Y-m-d H:i:s', filemtime($file));
                echo "<li>";
                echo "$filename (modified: $file_time) - ";
                echo "<a href='api/view_file.php?file=" . urlencode($filename) . "&type=transfer_slip' target='_blank'>Preview</a>";
                echo "</li>";
            }
            echo "</ul>";
        } else {
            echo "<h4>✅ File exists! Testing view...</h4>";
            $test_url = "api/view_file.php?file=" . urlencode($filename) . "&type=transfer_slip";
            echo "<p><a href='$test_url' target='_blank'>Test View File</a></p>";
            echo "<img src='$test_url' style='max-width: 300px; border: 1px solid #ccc;' alt='Transfer Slip'>";
        }
    } else {
        echo "<p>❌ No transfer slip image filename in database</p>";
    }
} else {
    echo "<p>❌ Expense ID 187 not found</p>";
}

// Handle update request
if (isset($_GET['update_expense']) && isset($_GET['new_file'])) {
    $expense_id = (int)$_GET['update_expense'];
    $new_filename = $_GET['new_file'];
    
    // Verify file exists
    if (file_exists('uploads/transfer_slips/' . $new_filename)) {
        $stmt = $db->prepare("UPDATE expenses SET transfer_slip_image = ? WHERE id = ?");
        if ($stmt->execute([$new_filename, $expense_id])) {
            echo "<div style='background: #d4edda; border: 1px solid #c3e6cb; padding: 10px; margin: 10px 0; border-radius: 5px;'>";
            echo "<strong>✅ Success!</strong> Updated expense ID $expense_id to use file: $new_filename";
            echo "<br><a href='expenses/view.php?id=$expense_id'>View Updated Expense</a>";
            echo "<br><a href='?'>Refresh this page</a>";
            echo "</div>";
        } else {
            echo "<div style='background: #f8d7da; border: 1px solid #f5c6cb; padding: 10px; margin: 10px 0; border-radius: 5px;'>";
            echo "<strong>❌ Error!</strong> Failed to update database.";
            echo "</div>";
        }
    } else {
        echo "<div style='background: #f8d7da; border: 1px solid #f5c6cb; padding: 10px; margin: 10px 0; border-radius: 5px;'>";
        echo "<strong>❌ Error!</strong> File does not exist: $new_filename";
        echo "</div>";
    }
}

// Check if there are other expenses with missing files
echo "<h3>🔍 Other expenses with missing files:</h3>";
$stmt = $db->prepare("
    SELECT id, exno, transfer_slip_image, created_at
    FROM expenses 
    WHERE transfer_slip_image IS NOT NULL 
    AND transfer_slip_image != ''
    ORDER BY id DESC
    LIMIT 10
");
$stmt->execute();
$recent_expenses = $stmt->fetchAll();

echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
echo "<tr><th>ID</th><th>Expense No</th><th>Image File</th><th>File Exists</th><th>Action</th></tr>";

foreach ($recent_expenses as $exp) {
    $file_path = 'uploads/transfer_slips/' . $exp['transfer_slip_image'];
    $exists = file_exists($file_path);
    
    echo "<tr>";
    echo "<td>{$exp['id']}</td>";
    echo "<td>{$exp['exno']}</td>";
    echo "<td>{$exp['transfer_slip_image']}</td>";
    echo "<td style='color: " . ($exists ? 'green' : 'red') . "'>" . ($exists ? '✅ YES' : '❌ NO') . "</td>";
    echo "<td>";
    if ($exists) {
        echo "<a href='api/view_file.php?file=" . urlencode($exp['transfer_slip_image']) . "&type=transfer_slip' target='_blank'>View</a>";
    } else {
        echo "<a href='?check_expense={$exp['id']}'>Fix</a>";
    }
    echo "</td>";
    echo "</tr>";
}
echo "</table>";
?>
