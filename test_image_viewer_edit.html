<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Image Viewer Test - Edit Page</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        .current-file {
            padding: 10px;
            border: 1px solid #dee2e6;
            border-radius: 5px;
            background: #f8f9fa;
        }
        
        .receipt-item {
            border: 1px solid #dee2e6;
            border-radius: 5px;
            padding: 10px;
            margin-bottom: 10px;
            background: #fff;
        }
        
        .demo-section {
            margin-bottom: 30px;
            padding: 20px;
            border: 2px dashed #dee2e6;
            border-radius: 10px;
        }
    </style>
</head>
<body>
    <div class="container mt-4">
        <h1><i class="fas fa-edit me-2"></i>Image Viewer Test - Edit Page</h1>
        <p class="text-muted">Testing image viewer functionality for transfer slip and receipt images</p>
        
        <!-- Transfer Slip Section -->
        <div class="demo-section">
            <h4 class="section-title"><i class="fas fa-file-invoice me-2"></i>Transfer Slip Image</h4>
            <div class="row">
                <div class="col-md-6">
                    <div class="mb-3">
                        <label class="form-label">Current Transfer Slip</label>
                        <div class="current-file">
                            <button type="button" class="btn btn-outline-primary btn-sm image-viewer-trigger"
                                    data-image-src="https://via.placeholder.com/800x600/007bff/ffffff?text=Transfer+Slip+Sample"
                                    data-image-title="Transfer Slip: 20251022-001">
                                <i class="fas fa-file-image me-1"></i>View Current Transfer Slip
                            </button>
                            <div class="mt-2">
                                <small class="text-muted">Click to view in image viewer modal</small>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="alert alert-info">
                        <h6><i class="fas fa-info-circle me-1"></i>Before (Old Method):</h6>
                        <p class="mb-0">
                            <code>&lt;a href="../uploads/transfer_slips/file.jpg" target="_blank"&gt;</code><br>
                            Opens in new tab/window
                        </p>
                        
                        <h6 class="mt-3"><i class="fas fa-check-circle me-1"></i>After (New Method):</h6>
                        <p class="mb-0">
                            <code>&lt;button class="image-viewer-trigger" data-image-src="..."&gt;</code><br>
                            Opens in modal with zoom, rotate, download features
                        </p>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Receipt Images Section -->
        <div class="demo-section">
            <h4 class="section-title"><i class="fas fa-receipt me-2"></i>Receipt Images</h4>
            <div class="row">
                <div class="col-md-8">
                    <h6>Current Receipt Numbers</h6>
                    
                    <!-- Receipt 1 -->
                    <div class="receipt-item">
                        <div class="d-flex justify-content-between align-items-center">
                            <div>
                                <strong>REC-001</strong>
                                <span class="text-success">(1,500.00 บาท)</span>
                                <div class="small text-muted">Fuel expense</div>
                            </div>
                            <button type="button" class="btn btn-outline-primary btn-sm image-viewer-trigger"
                                    data-image-src="https://via.placeholder.com/600x800/28a745/ffffff?text=Receipt+REC-001"
                                    data-image-title="Receipt: REC-001"
                                    data-gallery="receipts"
                                    data-index="0">
                                <i class="fas fa-file-image me-1"></i>View
                            </button>
                        </div>
                    </div>

                    <!-- Receipt 2 -->
                    <div class="receipt-item">
                        <div class="d-flex justify-content-between align-items-center">
                            <div>
                                <strong>REC-002</strong>
                                <span class="text-success">(800.00 บาท)</span>
                                <div class="small text-muted">Toll fee</div>
                            </div>
                            <button type="button" class="btn btn-outline-primary btn-sm image-viewer-trigger"
                                    data-image-src="https://via.placeholder.com/600x400/ffc107/000000?text=Receipt+REC-002"
                                    data-image-title="Receipt: REC-002"
                                    data-gallery="receipts"
                                    data-index="1">
                                <i class="fas fa-file-image me-1"></i>View
                            </button>
                        </div>
                    </div>

                    <!-- Receipt 3 -->
                    <div class="receipt-item">
                        <div class="d-flex justify-content-between align-items-center">
                            <div>
                                <strong>REC-003</strong>
                                <span class="text-success">(300.00 บาท)</span>
                                <div class="small text-muted">Parking fee</div>
                            </div>
                            <button type="button" class="btn btn-outline-primary btn-sm image-viewer-trigger"
                                    data-image-src="https://via.placeholder.com/500x700/dc3545/ffffff?text=Receipt+REC-003"
                                    data-image-title="Receipt: REC-003"
                                    data-gallery="receipts"
                                    data-index="2">
                                <i class="fas fa-file-image me-1"></i>View
                            </button>
                        </div>
                    </div>
                </div>
                
                <div class="col-md-4">
                    <div class="alert alert-success">
                        <h6><i class="fas fa-magic me-1"></i>Image Viewer Features:</h6>
                        <ul class="mb-0">
                            <li><i class="fas fa-search-plus me-1"></i>Zoom In/Out</li>
                            <li><i class="fas fa-undo me-1"></i>Rotate Left/Right</li>
                            <li><i class="fas fa-expand-arrows-alt me-1"></i>Reset View</li>
                            <li><i class="fas fa-download me-1"></i>Download Image</li>
                            <li><i class="fas fa-arrows-alt me-1"></i>Drag to Pan</li>
                            <li><i class="fas fa-keyboard me-1"></i>Keyboard Shortcuts</li>
                            <li><i class="fas fa-images me-1"></i><strong>Gallery Navigation</strong></li>
                            <li><i class="fas fa-arrow-left me-1"></i>Previous/Next Images</li>
                            <li><i class="fas fa-list-ol me-1"></i>Correct Image Counter</li>
                        </ul>
                    </div>
                    
                    <div class="alert alert-warning">
                        <h6><i class="fas fa-keyboard me-1"></i>Keyboard Shortcuts:</h6>
                        <ul class="mb-0 small">
                            <li><strong>+/-</strong> Zoom In/Out</li>
                            <li><strong>R</strong> Rotate Right</li>
                            <li><strong>L</strong> Rotate Left</li>
                            <li><strong>0</strong> Reset View</li>
                            <li><strong>Esc</strong> Close Modal</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Implementation Details -->
        <div class="demo-section">
            <h4 class="section-title"><i class="fas fa-code me-2"></i>Implementation Details</h4>
            <div class="row">
                <div class="col-md-6">
                    <h6>Transfer Slip Code:</h6>
                    <pre class="bg-light p-3 rounded"><code>&lt;button type="button" 
        class="btn btn-outline-primary btn-sm image-viewer-trigger"
        data-image-src="../api/view_file.php?type=transfer_slip&file=<?php echo urlencode($expense['transfer_slip_image']); ?>"
        data-image-title="Transfer Slip: <?php echo htmlspecialchars($expense['exno']); ?>"&gt;
    &lt;i class="fas fa-file-image me-1"&gt;&lt;/i&gt;View Current Transfer Slip
&lt;/button&gt;</code></pre>
                </div>
                
                <div class="col-md-6">
                    <h6>Receipt Code (Gallery):</h6>
                    <pre class="bg-light p-3 rounded"><code>&lt;button type="button"
        class="btn btn-outline-primary btn-sm image-viewer-trigger"
        data-image-src="../api/view_file.php?type=receipt&file=<?php echo urlencode($receipt['receipt_image']); ?>"
        data-image-title="Receipt: <?php echo htmlspecialchars($receipt['receipt_number']); ?>"
        data-gallery="receipts"
        data-index="<?php echo $index; ?>"&gt;
    &lt;i class="fas fa-file-image me-1"&gt;&lt;/i&gt;View
&lt;/button&gt;</code></pre>
                </div>
            </div>
            
            <div class="row mt-3">
                <div class="col-12">
                    <h6>JavaScript Initialization (Gallery Support):</h6>
                    <pre class="bg-light p-3 rounded"><code>// Initialize Image Viewer
const imageViewer = new ImageViewer();

// Handle image viewer triggers with gallery support
$(document).on('click', '.image-viewer-trigger', function() {
    const imageSrc = $(this).data('image-src');
    const imageTitle = $(this).data('image-title');
    const gallery = $(this).data('gallery');
    const index = parseInt($(this).data('index')) || 0;

    if (imageSrc) {
        if (gallery) {
            // Get all images in the same gallery
            const galleryImages = [];
            $(`.image-viewer-trigger[data-gallery="${gallery}"]`).each(function() {
                galleryImages.push($(this).data('image-src'));
            });

            // Show gallery with correct index
            imageViewer.showImage(imageSrc, galleryImages, index);
        } else {
            // Show single image
            imageViewer.showImage(imageSrc, [], 0);
        }
    }
});</code></pre>
                </div>
            </div>
        </div>
        
        <!-- Test Instructions -->
        <div class="alert alert-primary">
            <h6><i class="fas fa-play-circle me-2"></i>Test Instructions:</h6>
            <ol class="mb-0">
                <li><strong>Single Image:</strong> Click "View Current Transfer Slip" to test single image viewer</li>
                <li><strong>Gallery:</strong> Click any receipt "View" button to test gallery mode</li>
                <li><strong>Navigation:</strong> In gallery mode, use Previous/Next buttons or arrow keys</li>
                <li><strong>Counter:</strong> Check that image counter shows correct numbers (e.g., "2 of 3")</li>
                <li><strong>Zoom/Pan:</strong> Try zooming in/out using toolbar or mouse wheel</li>
                <li><strong>Rotate:</strong> Try rotating images using toolbar buttons</li>
                <li><strong>Keyboard:</strong> Try keyboard shortcuts (listed in the warning box)</li>
                <li><strong>Close:</strong> Click outside modal or press Esc to close</li>
            </ol>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script src="assets/js/image-viewer.js"></script>
    
    <script>
        $(document).ready(function() {
            // Initialize Image Viewer
            const imageViewer = new ImageViewer();
            
            // Handle image viewer triggers
            $(document).on('click', '.image-viewer-trigger', function() {
                const imageSrc = $(this).data('image-src');
                const imageTitle = $(this).data('image-title');
                const gallery = $(this).data('gallery');
                const index = parseInt($(this).data('index')) || 0;

                if (imageSrc) {
                    if (gallery) {
                        // Get all images in the same gallery
                        const galleryImages = [];
                        $(`.image-viewer-trigger[data-gallery="${gallery}"]`).each(function() {
                            galleryImages.push($(this).data('image-src'));
                        });

                        // Show gallery with correct index using ImageViewer's format
                        imageViewer.showImage(imageSrc, galleryImages, index);
                    } else {
                        // Show single image
                        imageViewer.showImage(imageSrc, [], 0);
                    }
                }
            });
            
            // Show success message
            setTimeout(function() {
                const toast = `
                    <div class="toast-container position-fixed bottom-0 end-0 p-3">
                        <div class="toast show" role="alert">
                            <div class="toast-header">
                                <i class="fas fa-check-circle text-success me-2"></i>
                                <strong class="me-auto">Image Viewer Ready</strong>
                                <button type="button" class="btn-close" data-bs-dismiss="toast"></button>
                            </div>
                            <div class="toast-body">
                                Image viewer system is loaded and ready to use!
                            </div>
                        </div>
                    </div>
                `;
                $('body').append(toast);
                
                setTimeout(function() {
                    $('.toast').toast('hide');
                }, 3000);
            }, 1000);
        });
    </script>
</body>
</html>
