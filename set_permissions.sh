#!/bin/bash
# Set proper file permissions for production server

echo "🔒 Setting file permissions for production..."

# Set file permissions (644 for files, 755 for directories)
find . -type f -name "*.php" -exec chmod 644 {} \;
find . -type f -name "*.html" -exec chmod 644 {} \;
find . -type f -name "*.css" -exec chmod 644 {} \;
find . -type f -name "*.js" -exec chmod 644 {} \;
find . -type f -name "*.sql" -exec chmod 644 {} \;
find . -type f -name "*.md" -exec chmod 644 {} \;
find . -type f -name "*.txt" -exec chmod 644 {} \;

# Set directory permissions
find . -type d -exec chmod 755 {} \;

# Secure config file
chmod 600 config/database.php

# Make upload directories writable
chmod 755 uploads/
chmod 755 uploads/receipts/
chmod 755 uploads/transfer_slips/
chmod 755 uploads/verification_slips/
chmod 755 uploads/review_slips/
chmod 755 uploads/batch_documents/
chmod 755 uploads/batch_documents/verification/
chmod 755 uploads/batch_documents/review/
chmod 755 uploads/bulk_operations/
chmod 755 backups/

# Make scripts executable
chmod +x create_directories.sh
chmod +x set_permissions.sh

echo "✅ File permissions set successfully!"
echo ""
echo "📋 Permission Summary:"
echo "   - PHP files: 644"
echo "   - Directories: 755"
echo "   - config/database.php: 600 (secure)"
echo "   - Upload directories: 755 (writable)"
echo "   - Scripts: 755 (executable)"
