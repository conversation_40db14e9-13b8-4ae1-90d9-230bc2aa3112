<?php
/**
 * Simple CSV Test
 * ทดสอบ CSV export แบบง่ายๆ เพื่อดูว่า server รองรับหรือไม่
 * ลบไฟล์นี้หลังจากทดสอบเสร็จแล้ว
 */

// Test 1: Simple CSV without database
if (isset($_GET['test']) && $_GET['test'] === 'simple') {
    $filename = 'test_simple_' . date('Y-m-d_H-i-s') . '.csv';
    header('Content-Type: text/csv; charset=utf-8');
    header('Content-Disposition: attachment; filename="' . $filename . '"');
    
    $output = fopen('php://output', 'w');
    
    // Add BOM for UTF-8
    fprintf($output, chr(0xEF).chr(0xBB).chr(0xBF));
    
    // Headers
    fputcsv($output, ['Test Column 1', 'Test Column 2', 'Test Column 3']);
    
    // Sample data
    fputcsv($output, ['Data 1', 'Data 2', 'Data 3']);
    fputcsv($output, ['ทดสอบ', 'ภาษาไทย', '123.45']);
    
    fclose($output);
    exit();
}

// Test 2: CSV with database connection
if (isset($_GET['test']) && $_GET['test'] === 'database') {
    session_start();
    
    if (!isset($_SESSION['user_id'])) {
        die('Please login first');
    }
    
    require_once 'config/database.php';
    
    try {
        $database = new Database();
        $db = $database->getConnection();
        
        // Simple query
        $stmt = $db->prepare("SELECT id, exno, status FROM expenses LIMIT 5");
        $stmt->execute();
        $results = $stmt->fetchAll();
        
        $filename = 'test_database_' . date('Y-m-d_H-i-s') . '.csv';
        header('Content-Type: text/csv; charset=utf-8');
        header('Content-Disposition: attachment; filename="' . $filename . '"');
        
        $output = fopen('php://output', 'w');
        
        // Add BOM for UTF-8
        fprintf($output, chr(0xEF).chr(0xBB).chr(0xBF));
        
        // Headers
        fputcsv($output, ['ID', 'Expense No', 'Status']);
        
        // Data
        foreach ($results as $row) {
            fputcsv($output, [$row['id'], $row['exno'], $row['status']]);
        }
        
        fclose($output);
        exit();
        
    } catch (Exception $e) {
        die('Database error: ' . $e->getMessage());
    }
}

?>
<!DOCTYPE html>
<html lang="th">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>CSV Export Test - Expenses System</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
</head>
<body class="bg-light">
    <div class="container mt-4">
        <div class="row justify-content-center">
            <div class="col-md-8">
                <div class="card shadow">
                    <div class="card-header bg-warning text-dark">
                        <h3><i class="fas fa-file-csv me-2"></i>CSV Export Test</h3>
                        <p class="mb-0">ทดสอบ CSV export เพื่อหาสาเหตุหน้าขาว</p>
                    </div>
                    <div class="card-body">
                        
                        <h4><i class="fas fa-bug me-2"></i>Debug Steps</h4>
                        
                        <div class="alert alert-info">
                            <h6><i class="fas fa-info-circle me-2"></i>ปัญหาที่พบ:</h6>
                            <p>เมื่อกด "Export CSV (Per Receipt)" ขึ้นหน้าขาว</p>
                            <p><strong>สาเหตุที่เป็นไปได้:</strong></p>
                            <ul class="mb-0">
                                <li>PHP Error ใน export_csv_per_receipt.php</li>
                                <li>Database connection error</li>
                                <li>Query error</li>
                                <li>Memory limit exceeded</li>
                                <li>Output buffering issues</li>
                            </ul>
                        </div>

                        <h5><i class="fas fa-play me-2"></i>Test Cases</h5>
                        
                        <div class="row mb-3">
                            <div class="col-md-6">
                                <div class="card border-success">
                                    <div class="card-header bg-success text-white">
                                        <h6><i class="fas fa-check-circle me-2"></i>Test 1: Simple CSV</h6>
                                    </div>
                                    <div class="card-body">
                                        <p>ทดสอบ CSV export แบบง่ายๆ ไม่ใช้ database</p>
                                        <a href="?test=simple" class="btn btn-success btn-sm">
                                            <i class="fas fa-download me-1"></i>Download Simple CSV
                                        </a>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="card border-info">
                                    <div class="card-header bg-info text-white">
                                        <h6><i class="fas fa-database me-2"></i>Test 2: Database CSV</h6>
                                    </div>
                                    <div class="card-body">
                                        <p>ทดสอบ CSV export ที่ใช้ database</p>
                                        <a href="?test=database" class="btn btn-info btn-sm">
                                            <i class="fas fa-download me-1"></i>Download Database CSV
                                        </a>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <h5><i class="fas fa-tools me-2"></i>Debug Tools</h5>
                        
                        <div class="row mb-3">
                            <div class="col-md-6">
                                <div class="card border-warning">
                                    <div class="card-header bg-warning text-dark">
                                        <h6><i class="fas fa-search me-2"></i>Debug Per Receipt</h6>
                                    </div>
                                    <div class="card-body">
                                        <p>ตรวจสอบ export_csv_per_receipt.php ทีละขั้นตอน</p>
                                        <a href="debug_csv_per_receipt.php" class="btn btn-warning btn-sm">
                                            <i class="fas fa-bug me-1"></i>Debug Per Receipt
                                        </a>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="card border-secondary">
                                    <div class="card-header bg-secondary text-white">
                                        <h6><i class="fas fa-file-alt me-2"></i>Original Files</h6>
                                    </div>
                                    <div class="card-body">
                                        <p>ทดสอบไฟล์ CSV export ต้นฉบับ</p>
                                        <a href="api/export_csv.php" class="btn btn-secondary btn-sm me-2">
                                            <i class="fas fa-download me-1"></i>Regular CSV
                                        </a>
                                        <a href="api/export_csv_per_receipt.php" class="btn btn-dark btn-sm">
                                            <i class="fas fa-download me-1"></i>Per Receipt CSV
                                        </a>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <h5><i class="fas fa-clipboard-list me-2"></i>Expected Results</h5>
                        
                        <div class="alert alert-success">
                            <h6><i class="fas fa-check me-2"></i>หากทำงานปกติ:</h6>
                            <ul class="mb-0">
                                <li>✅ Browser จะ download ไฟล์ CSV</li>
                                <li>✅ ไฟล์จะมีข้อมูล expenses และ receipts</li>
                                <li>✅ ไม่มีหน้าขาวหรือ error</li>
                            </ul>
                        </div>

                        <div class="alert alert-danger">
                            <h6><i class="fas fa-times me-2"></i>หากมีปัญหา:</h6>
                            <ul class="mb-0">
                                <li>❌ หน้าขาว (White screen)</li>
                                <li>❌ Error message</li>
                                <li>❌ ไฟล์ไม่ download</li>
                                <li>❌ ไฟล์ว่างเปล่า</li>
                            </ul>
                        </div>

                        <h5><i class="fas fa-wrench me-2"></i>Solutions</h5>
                        
                        <div class="accordion" id="solutionsAccordion">
                            <div class="accordion-item">
                                <h2 class="accordion-header" id="headingOne">
                                    <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#collapseOne">
                                        <i class="fas fa-code me-2"></i>PHP Error Solutions
                                    </button>
                                </h2>
                                <div id="collapseOne" class="accordion-collapse collapse" data-bs-parent="#solutionsAccordion">
                                    <div class="accordion-body">
                                        <ul>
                                            <li>เพิ่ม error_reporting(E_ALL) และ ini_set('display_errors', 1)</li>
                                            <li>ตรวจสอบ PHP error log</li>
                                            <li>เพิ่ม try-catch blocks</li>
                                            <li>ตรวจสอบ required files</li>
                                        </ul>
                                    </div>
                                </div>
                            </div>
                            <div class="accordion-item">
                                <h2 class="accordion-header" id="headingTwo">
                                    <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#collapseTwo">
                                        <i class="fas fa-database me-2"></i>Database Error Solutions
                                    </button>
                                </h2>
                                <div id="collapseTwo" class="accordion-collapse collapse" data-bs-parent="#solutionsAccordion">
                                    <div class="accordion-body">
                                        <ul>
                                            <li>ตรวจสอบ database connection</li>
                                            <li>ตรวจสอบ table structure</li>
                                            <li>ตรวจสอบ SQL query syntax</li>
                                            <li>ตรวจสอบ user permissions</li>
                                        </ul>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="alert alert-warning mt-4">
                            <h6><i class="fas fa-shield-alt me-2"></i>Security Notice</h6>
                            <p><strong>ลบไฟล์ test_csv_simple.php และ debug_csv_per_receipt.php หลังจากทดสอบเสร็จแล้ว!</strong></p>
                        </div>

                        <div class="text-center mt-4">
                            <a href="reports/" class="btn btn-primary me-3">
                                <i class="fas fa-chart-bar me-2"></i>Reports
                            </a>
                            <a href="dashboard.php" class="btn btn-secondary">
                                <i class="fas fa-home me-2"></i>Dashboard
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
