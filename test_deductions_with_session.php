<?php
session_start();
require_once 'config/database.php';

// Check if user is logged in
if (!isset($_SESSION['user_id'])) {
    // For testing, create a fake session
    $_SESSION['user_id'] = 1;
    $_SESSION['username'] = 'test_user';
    $_SESSION['role'] = 'admin';
}

$database = new Database();
$db = $database->getConnection();

// Get receipt data for testing
$stmt = $db->prepare("SELECT * FROM receipt_numbers WHERE id = 1");
$stmt->execute();
$receipt = $stmt->fetch();

if (!$receipt) {
    die("No receipt found with ID 1");
}

// Get existing deductions
$stmt = $db->prepare("SELECT * FROM receipt_deductions WHERE receipt_number_id = 1");
$stmt->execute();
$deductions = $stmt->fetchAll();

?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Receipt Deductions Test (With Session)</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
</head>
<body>
    <div class="container mt-4">
        <h1><i class="fas fa-test-tube me-2"></i>Receipt Deductions Test (With Session)</h1>
        
        <!-- Session Info -->
        <div class="alert alert-info">
            <strong>Session Active:</strong> User ID <?php echo $_SESSION['user_id']; ?> (<?php echo $_SESSION['username']; ?>)
        </div>
        
        <!-- Receipt Info -->
        <div class="card mb-4">
            <div class="card-header">
                <h5><i class="fas fa-receipt me-2"></i>Receipt Information</h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-3"><strong>Receipt ID:</strong> <?php echo $receipt['id']; ?></div>
                    <div class="col-md-3"><strong>Receipt Number:</strong> <?php echo htmlspecialchars($receipt['receipt_number']); ?></div>
                    <div class="col-md-3"><strong>Amount:</strong> <?php echo number_format($receipt['amount'], 2); ?> บาท</div>
                    <div class="col-md-3"><strong>Gross Amount:</strong> <?php echo number_format($receipt['gross_amount'], 2); ?> บาท</div>
                </div>
                <div class="row mt-2">
                    <div class="col-md-3"><strong>Has Deductions:</strong> <?php echo $receipt['has_deductions'] ? 'Yes' : 'No'; ?></div>
                    <div class="col-md-3"><strong>Net Amount:</strong> <?php echo number_format($receipt['net_amount_calculated'], 2); ?> บาท</div>
                    <div class="col-md-6"><strong>Description:</strong> <?php echo htmlspecialchars($receipt['description']); ?></div>
                </div>
            </div>
        </div>
        
        <!-- Existing Deductions -->
        <div class="card mb-4">
            <div class="card-header">
                <h5><i class="fas fa-list me-2"></i>Existing Deductions (<?php echo count($deductions); ?>)</h5>
            </div>
            <div class="card-body">
                <?php if (empty($deductions)): ?>
                    <p class="text-muted">No deductions found for this receipt.</p>
                <?php else: ?>
                    <div class="table-responsive">
                        <table class="table table-sm">
                            <thead>
                                <tr>
                                    <th>ID</th>
                                    <th>Type</th>
                                    <th>Amount</th>
                                    <th>Percentage</th>
                                    <th>Description</th>
                                    <th>Created</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($deductions as $deduction): ?>
                                <tr>
                                    <td><?php echo $deduction['id']; ?></td>
                                    <td><?php echo $deduction['deduction_type']; ?></td>
                                    <td><?php echo number_format($deduction['amount'], 2); ?></td>
                                    <td><?php echo $deduction['percentage'] ? $deduction['percentage'] . '%' : '-'; ?></td>
                                    <td><?php echo htmlspecialchars($deduction['description']); ?></td>
                                    <td><?php echo $deduction['created_at']; ?></td>
                                </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>
                <?php endif; ?>
            </div>
        </div>
        
        <!-- API Test Buttons -->
        <div class="card mb-4">
            <div class="card-header">
                <h5><i class="fas fa-code me-2"></i>API Testing</h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-3">
                        <button type="button" class="btn btn-info w-100" onclick="testGetDeductions()">
                            <i class="fas fa-download me-1"></i>GET Deductions
                        </button>
                    </div>
                    <div class="col-md-3">
                        <button type="button" class="btn btn-success w-100" onclick="testAddDeduction()">
                            <i class="fas fa-plus me-1"></i>ADD Deduction
                        </button>
                    </div>
                    <div class="col-md-3">
                        <button type="button" class="btn btn-warning w-100" onclick="testUpdateDeduction()">
                            <i class="fas fa-edit me-1"></i>UPDATE Deduction
                        </button>
                    </div>
                    <div class="col-md-3">
                        <button type="button" class="btn btn-danger w-100" onclick="testDeleteDeduction()">
                            <i class="fas fa-trash me-1"></i>DELETE Deduction
                        </button>
                    </div>
                </div>
                
                <div class="mt-3">
                    <h6>API Response:</h6>
                    <pre id="api-response" class="bg-light p-3 rounded" style="max-height: 400px; overflow-y: auto;">
Ready for testing...
                    </pre>
                </div>
            </div>
        </div>
        
        <!-- Manual Deduction Form -->
        <div class="card">
            <div class="card-header">
                <h5><i class="fas fa-plus me-2"></i>Add Deduction Manually</h5>
            </div>
            <div class="card-body">
                <form id="manual-deduction-form">
                    <div class="row">
                        <div class="col-md-4">
                            <label for="deduction_type" class="form-label">Deduction Type</label>
                            <select class="form-select" id="deduction_type" required>
                                <option value="">Select Type</option>
                                <option value="tax_vat">VAT</option>
                                <option value="tax_withholding">Withholding Tax</option>
                                <option value="service_fee">Service Fee</option>
                                <option value="discount">Discount</option>
                                <option value="penalty">Penalty</option>
                                <option value="commission">Commission</option>
                                <option value="other">Other</option>
                            </select>
                        </div>
                        <div class="col-md-2">
                            <label for="amount" class="form-label">Amount</label>
                            <input type="number" class="form-control" id="amount" step="0.01" min="0" required>
                        </div>
                        <div class="col-md-2">
                            <label for="percentage" class="form-label">Percentage</label>
                            <input type="number" class="form-control" id="percentage" step="0.01" min="0" max="100">
                        </div>
                        <div class="col-md-3">
                            <label for="description" class="form-label">Description</label>
                            <input type="text" class="form-control" id="description">
                        </div>
                        <div class="col-md-1">
                            <label class="form-label">&nbsp;</label>
                            <button type="submit" class="btn btn-primary w-100">
                                <i class="fas fa-plus"></i>
                            </button>
                        </div>
                    </div>
                    <div class="row mt-2">
                        <div class="col-md-12">
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="is_percentage_based">
                                <label class="form-check-label" for="is_percentage_based">
                                    Calculate from percentage
                                </label>
                            </div>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    
    <script>
        function testGetDeductions() {
            fetch('api/receipt_deductions.php?receipt_id=1')
                .then(response => response.json())
                .then(data => {
                    document.getElementById('api-response').textContent = JSON.stringify(data, null, 2);
                })
                .catch(error => {
                    document.getElementById('api-response').textContent = 'Error: ' + error.message;
                });
        }
        
        function testAddDeduction() {
            const testData = {
                receipt_id: 1,
                deduction_data: {
                    deduction_type: 'tax_withholding',
                    amount: 150,
                    percentage: 1.5,
                    description: 'Test API deduction',
                    is_percentage_based: true
                }
            };
            
            fetch('api/receipt_deductions.php', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(testData)
            })
            .then(response => response.json())
            .then(data => {
                document.getElementById('api-response').textContent = JSON.stringify(data, null, 2);
                if (data.success) {
                    setTimeout(() => location.reload(), 2000);
                }
            })
            .catch(error => {
                document.getElementById('api-response').textContent = 'Error: ' + error.message;
            });
        }
        
        function testUpdateDeduction() {
            // Get first deduction ID
            const firstDeductionId = <?php echo !empty($deductions) ? $deductions[0]['id'] : 'null'; ?>;
            
            if (!firstDeductionId) {
                alert('No deductions to update. Add one first.');
                return;
            }
            
            const testData = {
                deduction_id: firstDeductionId,
                deduction_data: {
                    deduction_type: 'service_fee',
                    amount: 200,
                    description: 'Updated via API test'
                }
            };
            
            fetch('api/receipt_deductions.php', {
                method: 'PUT',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(testData)
            })
            .then(response => response.json())
            .then(data => {
                document.getElementById('api-response').textContent = JSON.stringify(data, null, 2);
                if (data.success) {
                    setTimeout(() => location.reload(), 2000);
                }
            })
            .catch(error => {
                document.getElementById('api-response').textContent = 'Error: ' + error.message;
            });
        }
        
        function testDeleteDeduction() {
            const firstDeductionId = <?php echo !empty($deductions) ? $deductions[0]['id'] : 'null'; ?>;
            
            if (!firstDeductionId) {
                alert('No deductions to delete. Add one first.');
                return;
            }
            
            if (!confirm('Delete deduction ID ' + firstDeductionId + '?')) {
                return;
            }
            
            fetch('api/receipt_deductions.php?deduction_id=' + firstDeductionId, {
                method: 'DELETE'
            })
            .then(response => response.json())
            .then(data => {
                document.getElementById('api-response').textContent = JSON.stringify(data, null, 2);
                if (data.success) {
                    setTimeout(() => location.reload(), 2000);
                }
            })
            .catch(error => {
                document.getElementById('api-response').textContent = 'Error: ' + error.message;
            });
        }
        
        // Manual form submission
        $('#manual-deduction-form').on('submit', function(e) {
            e.preventDefault();
            
            const formData = {
                receipt_id: 1,
                deduction_data: {
                    deduction_type: $('#deduction_type').val(),
                    amount: parseFloat($('#amount').val()),
                    percentage: parseFloat($('#percentage').val()) || null,
                    description: $('#description').val(),
                    is_percentage_based: $('#is_percentage_based').is(':checked')
                }
            };
            
            fetch('api/receipt_deductions.php', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(formData)
            })
            .then(response => response.json())
            .then(data => {
                document.getElementById('api-response').textContent = JSON.stringify(data, null, 2);
                if (data.success) {
                    this.reset();
                    setTimeout(() => location.reload(), 2000);
                }
            })
            .catch(error => {
                document.getElementById('api-response').textContent = 'Error: ' + error.message;
            });
        });
        
        // Auto-load deductions on page load
        $(document).ready(function() {
            testGetDeductions();
        });
    </script>
</body>
</html>
