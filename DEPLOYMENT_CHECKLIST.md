# 🚀 Production Deployment Checklist

## 📋 Pre-Deployment Checklist

### ✅ Environment Requirements
- [ ] **PHP 8.1.33** installed on production server
- [ ] **MariaDB 5.5.68** running and accessible
- [ ] Web server (Apache/Nginx) configured
- [ ] SSL certificate installed (recommended)
- [ ] Backup system in place

### ✅ File Upload & Structure
- [ ] Upload all project files to production server
- [ ] Create required directories:
  ```bash
  mkdir -p uploads/{receipts,transfer_slips,verification_slips,review_slips,batch_documents,bulk_operations}
  mkdir -p backups
  ```
- [ ] Set proper file permissions:
  ```bash
  chmod 755 uploads/ backups/
  chmod 644 *.php
  chmod 600 config/database.php
  ```

### ✅ Database Setup
- [ ] Create production database
- [ ] Import schema: `database/batch_operations_schema_mariadb55.sql`
- [ ] Import workflow: `database/add_reject_return_workflow_mariadb55.sql`
- [ ] **Run fixes: `database/fix_production_issues.sql`**
- [ ] Create database user with appropriate permissions
- [ ] Test database connection

### ✅ Configuration
- [ ] Copy `config/database_production.php` to `config/database.php`
- [ ] Update database credentials in `config/database.php`:
  ```php
  private $host = "your_production_host";
  private $db_name = "your_production_database";
  private $username = "your_production_user";
  private $password = "your_production_password";
  ```
- [ ] Update any hardcoded URLs from localhost to production domain

## 🛠️ Deployment Steps

### Step 1: Initial Setup
1. **Upload files** to production server
2. **Run compatibility check**:
   ```
   https://yourdomain.com/compatibility_check.php
   ```
3. **Run production setup**:
   ```
   https://yourdomain.com/setup_production.php
   ```

### Step 2: Database Initialization
1. **Import MariaDB 5.5.68 compatible schema**
2. **Create initial admin user**
3. **Import sample data** (customers, items)
4. **Test database connectivity**

### Step 3: System Testing
1. **Run final system check**:
   ```
   https://yourdomain.com/final_system_check.php
   ```
2. **Test core functionality**:
   - [ ] User login/logout
   - [ ] Expense creation
   - [ ] File uploads
   - [ ] Batch operations
   - [ ] Reports generation
   - [ ] Admin functions

### Step 4: Security Hardening
1. **Remove setup files**:
   ```bash
   rm setup_production.php
   rm compatibility_check.php
   rm final_system_check.php
   rm test_*.php
   rm debug_*.php
   ```
2. **Set restrictive permissions**:
   ```bash
   chmod 600 config/database.php
   chmod 644 *.php
   find . -name "*.php" -path "*/admin/*" -exec chmod 640 {} \;
   ```
3. **Configure web server security headers**
4. **Enable HTTPS redirect**

## 🔧 MariaDB 5.5.68 Specific Configurations

### JSON Compatibility
- ✅ **No JSON data types used** - all JSON stored as TEXT
- ✅ **No JSON functions used** - replaced with string manipulation
- ✅ **TransactionHelper implemented** - compatible with older PDO versions

### Schema Differences
- `workflow_history` column: `TEXT` instead of `JSON`
- `batch_documents.metadata`: `TEXT` instead of `JSON`
- All JSON operations replaced with string concatenation

### Performance Considerations
- Added proper indexes for MariaDB 5.5.68
- Optimized queries for older MySQL optimizer
- Limited use of complex JOINs

## 📊 Post-Deployment Verification

### Functional Tests
- [ ] **User Management**: Create, edit, delete users
- [ ] **Expense Workflow**: Submit → Verify → Review → Complete
- [ ] **Batch Operations**: Multi-select verification and review
- [ ] **File Uploads**: Receipt images, transfer slips
- [ ] **Reports**: CSV export, dashboard statistics
- [ ] **Admin Functions**: Cleanup tools, user management

### Performance Tests
- [ ] **Page Load Times**: < 3 seconds for main pages
- [ ] **File Upload**: Images up to 10MB
- [ ] **Batch Processing**: 50+ items simultaneously
- [ ] **Database Queries**: No slow query warnings
- [ ] **Memory Usage**: Within server limits

### Security Tests
- [ ] **Authentication**: Login required for all pages
- [ ] **Authorization**: Role-based access control working
- [ ] **File Access**: Direct file access blocked
- [ ] **SQL Injection**: Prepared statements used
- [ ] **XSS Protection**: Input sanitization working

## 🚨 Troubleshooting

### Common Issues

#### Database Connection Errors
```php
// Check config/database.php credentials
// Verify MariaDB service is running
// Check firewall settings
```

#### File Upload Issues
```bash
# Check directory permissions
chmod 755 uploads/
# Check PHP upload limits
php -i | grep upload_max_filesize
```

#### Transaction Errors
```php
// TransactionHelper should handle MariaDB 5.5.68 compatibility
// Check error logs for specific issues
```

### Log Files to Monitor
- **PHP Error Log**: `/var/log/php/error.log`
- **Web Server Log**: `/var/log/apache2/error.log` or `/var/log/nginx/error.log`
- **MariaDB Log**: `/var/log/mysql/error.log`
- **Application Log**: Check `user_activities` table

## 📞 Support Information

### System Requirements Met
- ✅ PHP 8.1.33 compatibility
- ✅ MariaDB 5.5.68 compatibility
- ✅ No JSON functions dependency
- ✅ Transaction handling for older PDO
- ✅ Image compression with fallback
- ✅ CSV export functionality
- ✅ Role-based access control
- ✅ Batch operations system
- ✅ File upload management
- ✅ Admin cleanup tools

### Key Features Verified
1. **Expense Management System** - Complete workflow
2. **Batch Operations** - Multi-item processing
3. **Role-Based Access** - 5 user roles with permissions
4. **File Management** - Image compression and storage
5. **Reporting System** - CSV export and dashboard
6. **Admin Tools** - User management and cleanup
7. **Audit Trail** - Complete activity logging
8. **Production Ready** - MariaDB 5.5.68 compatible

---

**✅ System is ready for production deployment!**

*Last updated: 2025-01-19*
