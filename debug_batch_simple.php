<?php
session_start();
require_once 'config/database.php';
require_once 'includes/functions.php';
require_once 'includes/BatchOperations.php';

// Create database connection
$database = new Database();
$db = $database->getConnection();

// Check if user is logged in
if (!isset($_SESSION['user_id'])) {
    echo "Please login first";
    exit;
}

$user_id = $_SESSION['user_id'];
$user_role = $_SESSION['role'];

echo "<h2>Debug Batch Operations</h2>";
echo "<p>User ID: {$user_id}, Role: {$user_role}</p>";

// Test 1: Check if we can create a simple log
echo "<h3>Test 1: Create Simple Log</h3>";
try {
    logActivity(
        $db,
        $user_id,
        'test_debug',
        'test_table',
        999,
        'Debug test log entry',
        $_SERVER['REMOTE_ADDR'],
        $_SERVER['HTTP_USER_AGENT']
    );
    echo "✅ Simple log created successfully<br>";
} catch (Exception $e) {
    echo "❌ Error creating simple log: " . $e->getMessage() . "<br>";
}

// Test 2: Check recent logs
echo "<h3>Test 2: Recent Activity Logs</h3>";
try {
    $stmt = $db->prepare("
        SELECT * 
        FROM activity_logs 
        WHERE user_id = ? 
        ORDER BY created_at DESC 
        LIMIT 5
    ");
    $stmt->execute([$user_id]);
    $logs = $stmt->fetchAll();
    
    if (empty($logs)) {
        echo "❌ No logs found for user {$user_id}<br>";
    } else {
        echo "✅ Found " . count($logs) . " logs:<br>";
        foreach ($logs as $log) {
            echo "- " . $log['created_at'] . " | " . $log['action_type'] . " | " . $log['description'] . "<br>";
        }
    }
} catch (Exception $e) {
    echo "❌ Error fetching logs: " . $e->getMessage() . "<br>";
}

// Test 3: Check open expenses
echo "<h3>Test 3: Available Open Expenses</h3>";
try {
    $stmt = $db->prepare("
        SELECT id, exno, total_amount 
        FROM expenses 
        WHERE status = 'open' AND verification_by IS NULL 
        LIMIT 3
    ");
    $stmt->execute();
    $expenses = $stmt->fetchAll();
    
    if (empty($expenses)) {
        echo "❌ No open expenses found<br>";
    } else {
        echo "✅ Found " . count($expenses) . " open expenses:<br>";
        foreach ($expenses as $expense) {
            echo "- ID: {$expense['id']}, ExNo: {$expense['exno']}, Amount: {$expense['total_amount']}<br>";
        }
        
        // Test 4: Try to create a batch with these expenses
        echo "<h3>Test 4: Create Batch Operation</h3>";
        try {
            $batchOps = new BatchOperations($db);
            
            $expense_items = [];
            $total_amount = 0;
            
            foreach ($expenses as $expense) {
                $expense_items[] = [
                    'expense_id' => $expense['id'],
                    'amount' => $expense['total_amount']
                ];
                $total_amount += $expense['total_amount'];
            }
            
            echo "Creating batch with " . count($expense_items) . " items, total: {$total_amount}<br>";
            
            $batch_result = $batchOps->createBatch(
                'verification',
                $user_id,
                $expense_items,
                $total_amount,
                'Debug test batch'
            );
            
            if ($batch_result['success']) {
                echo "✅ Batch created successfully: " . $batch_result['batch_id'] . "<br>";
                
                // Test 5: Process the batch
                echo "<h3>Test 5: Process Batch Verification</h3>";
                try {
                    $process_result = $batchOps->processBatchVerification(
                        $batch_result['batch_id'],
                        'debug_verification_slip.jpg',
                        $user_id,
                        'DEBUG_TRANSFER_' . time(),
                        []
                    );
                    
                    if ($process_result['success']) {
                        echo "✅ Batch processed successfully<br>";
                        echo "- Processed: " . $process_result['processed'] . "<br>";
                        echo "- Success: " . $process_result['success_count'] . "<br>";
                        echo "- Errors: " . $process_result['error_count'] . "<br>";
                        
                        // Test 6: Create batch-level log
                        echo "<h3>Test 6: Create Batch-Level Log</h3>";
                        try {
                            logActivity(
                                $db,
                                $user_id,
                                'batch_verification',
                                'expenses',
                                null,
                                "Debug batch verification: {$batch_result['batch_id']}, Items: {$process_result['processed']}, Success: {$process_result['success_count']}",
                                $_SERVER['REMOTE_ADDR'],
                                $_SERVER['HTTP_USER_AGENT']
                            );
                            echo "✅ Batch-level log created successfully<br>";
                        } catch (Exception $e) {
                            echo "❌ Error creating batch-level log: " . $e->getMessage() . "<br>";
                        }
                        
                    } else {
                        echo "❌ Batch processing failed: " . $process_result['error'] . "<br>";
                    }
                } catch (Exception $e) {
                    echo "❌ Error processing batch: " . $e->getMessage() . "<br>";
                }
                
            } else {
                echo "❌ Batch creation failed: " . $batch_result['error'] . "<br>";
            }
            
        } catch (Exception $e) {
            echo "❌ Error creating batch: " . $e->getMessage() . "<br>";
        }
    }
} catch (Exception $e) {
    echo "❌ Error fetching expenses: " . $e->getMessage() . "<br>";
}

// Test 7: Check all logs after operations
echo "<h3>Test 7: All Recent Logs After Operations</h3>";
try {
    $stmt = $db->prepare("
        SELECT * 
        FROM activity_logs 
        WHERE user_id = ? 
        ORDER BY created_at DESC 
        LIMIT 10
    ");
    $stmt->execute([$user_id]);
    $all_logs = $stmt->fetchAll();
    
    if (empty($all_logs)) {
        echo "❌ No logs found after operations<br>";
    } else {
        echo "✅ Found " . count($all_logs) . " logs after operations:<br>";
        echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
        echo "<tr><th>Time</th><th>Action</th><th>Table</th><th>Record ID</th><th>Description</th></tr>";
        foreach ($all_logs as $log) {
            echo "<tr>";
            echo "<td>" . date('H:i:s', strtotime($log['created_at'])) . "</td>";
            echo "<td>" . $log['action_type'] . "</td>";
            echo "<td>" . ($log['table_name'] ?: '-') . "</td>";
            echo "<td>" . ($log['record_id'] ?: '-') . "</td>";
            echo "<td>" . substr($log['description'], 0, 50) . "</td>";
            echo "</tr>";
        }
        echo "</table>";
    }
} catch (Exception $e) {
    echo "❌ Error fetching final logs: " . $e->getMessage() . "<br>";
}

echo "<br><a href='dashboard.php'>Back to Dashboard</a>";
echo " | <a href='debug_logs.php'>Debug Logs</a>";
echo " | <a href='test_simple_batch.php'>Test Simple Batch</a>";
?>
