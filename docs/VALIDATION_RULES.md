# Validation Rules for Expenses System

## Overview
This document outlines all validation rules implemented in the Expenses Management System.

## Field Requirements

### Required Fields
| Field | Create | Edit | Notes |
|-------|--------|------|-------|
| **Job Open Date** | ✅ Required | ✅ Required | Must be valid date |
| **Withdrawal Date** | ✅ Required | ✅ Required | Must be valid date, not more than 1 month in past |
| **Transfer Amount** | ✅ Required | ✅ Required | Must equal Receipt Total |

### Optional Fields
| Field | Create | Edit | Notes |
|-------|--------|------|-------|
| **Booking NO / BL** | ❌ Optional | ❌ Optional | Not required for all transactions |
| **Customer** | ❌ Optional | ❌ Optional | Can be left empty |
| **Container NO** | ❌ Optional | ❌ Optional | Not required for all transactions |
| **Item** | ❌ Optional | ❌ Optional | Can select from list or add new |
| **Driver** | ❌ Optional | ❌ Optional | Can select from list |

## Amount Validation

### Transfer Amount = Receipt Total Rule
**Critical Validation**: Transfer Amount must exactly equal the sum of all receipt amounts.

#### Implementation:
- **Server-side validation** in `expenses/create.php` and `expenses/edit.php`
- **Client-side validation** in `assets/js/expense-form.js`
- **Real-time feedback** with visual indicators

#### Error Messages:
- **Server**: "Transfer Amount (X.XX บาท) must equal Receipt Total (Y.YY บาท). Please check your amounts."
- **Client**: "Transfer Amount (X.XX บาท) must equal Receipt Total (Y.YY บาท)"

#### Tolerance:
- **Floating point precision**: Allows 0.01 บาท difference for floating point calculations
- **Formula**: `Math.abs(transferAmount - receiptTotal) <= 0.01`

### Visual Feedback:
- **✅ Green**: Amounts match exactly
- **❌ Red**: Amounts don't match (with "Must be equal!" message)
- **ℹ️ Gray**: No amounts entered yet

## Date Validation

### Job Open Date
- **Required**: Yes
- **Format**: YYYY-MM-DD
- **Validation**: Must be valid date format

### Withdrawal Date
- **Required**: Yes
- **Format**: YYYY-MM-DD
- **Validation**: 
  - Must be valid date format
  - Cannot be more than 1 month in the past
  - **Formula**: `withdrawalDate >= (today - 1 month)`

## File Upload Validation

### File Size Limits
- **Transfer Slip**: Maximum 5MB
- **Receipt Images**: Maximum 5MB per file
- **Supported Formats**: JPG, JPEG, PNG, PDF

### Image Compression
- **Automatic compression** applied to all uploaded images
- **Maximum dimensions**: 1200x1200 pixels
- **Quality settings**:
  - JPEG: 85% quality
  - PNG: Compression level 6

## Form Submission Validation

### Client-side Validation (JavaScript)
**File**: `assets/js/expense-form.js`

#### Checks performed:
1. **Required fields**: Job Open Date, Withdrawal Date
2. **Date validation**: Withdrawal date not too old
3. **File size validation**: All files under 5MB limit
4. **Amount validation**: Transfer Amount = Receipt Total
5. **File format validation**: Only allowed image/PDF types

#### Error Display:
- **Alert box** with list of all validation errors
- **Form submission prevented** if validation fails
- **Scroll to top** to show error messages

### Server-side Validation (PHP)
**Files**: `expenses/create.php`, `expenses/edit.php`

#### Checks performed:
1. **Required fields validation**
2. **Date format validation**
3. **Amount equality validation**
4. **File upload validation**
5. **Database constraints validation**

#### Error Handling:
- **Exception throwing** for validation failures
- **Transaction rollback** on errors
- **User-friendly error messages**
- **Activity logging** for audit trail

## Batch Processing Validation

### Batch Creation
- **No additional validation** required for amounts
- **Uses existing expense data** that has already been validated
- **Status validation**: Only appropriate status expenses can be batched

### Batch Processing
- **File upload validation**: Document must be provided
- **Transfer number validation**: Must be provided
- **Permission validation**: User must have appropriate role

## Status Change Validation

### Requirements for Status Changes
| From Status | To Status | Requirements |
|-------------|-----------|--------------|
| **open** | **pending** | Transfer slip + Transfer amount + Amounts match |
| **pending** | **success** | Verification amount = Transfer amount |
| **pending** | **rejected** | Reason required |
| **pending** | **returned** | Reason required |
| **returned** | **pending** | Same as open → pending |

### Amount Matching Validation
- **Transfer Amount** must equal **Receipt Total**
- **Verification Amount** must equal **Transfer Amount** (for success status)
- **Review Amount** must equal **Verification Amount** (for final approval)

## Error Messages

### Standard Error Format
```php
throw new Exception("Field validation message with specific details");
```

### User-friendly Messages
- **Thai language support** for amount-related messages
- **Specific values shown** in error messages
- **Clear instructions** on how to fix the error

### JavaScript Error Display
```javascript
// Error alert with multiple errors
showErrors([
    'Error message 1',
    'Error message 2'
]);
```

## Best Practices

### For Users
1. **Enter transfer amount carefully** - it must match receipt total exactly
2. **Check amounts before submission** - use the real-time comparison
3. **Upload clear images** - system will compress automatically
4. **Fill optional fields when relevant** - improves record keeping

### For Developers
1. **Always validate on both client and server side**
2. **Use transactions for data integrity**
3. **Provide clear, specific error messages**
4. **Log validation failures for debugging**
5. **Test with edge cases** (floating point precision, large files, etc.)

### For Administrators
1. **Monitor validation errors** in activity logs
2. **Train users on amount matching requirement**
3. **Review rejected/returned expenses** for common validation issues
4. **Ensure file storage has adequate space** for compressed images

## Implementation Files

### Core Validation Files
- `expenses/create.php` - Create form validation
- `expenses/edit.php` - Edit form validation
- `assets/js/expense-form.js` - Client-side validation
- `includes/functions.php` - Validation helper functions

### Supporting Files
- `includes/ImageUploadHelper.php` - File upload and compression
- `config/database.php` - Database constraints
- `admin/logs.php` - Validation error monitoring

## Testing Validation

### Test Cases
1. **Amount mismatch**: Transfer amount ≠ Receipt total
2. **Missing required fields**: Empty job open date, withdrawal date
3. **Invalid dates**: Future withdrawal date, invalid format
4. **Large files**: Files over 5MB limit
5. **Invalid file types**: Non-image/PDF files
6. **Floating point precision**: Amounts with many decimal places

### Expected Behavior
- **Client-side**: Immediate feedback, form submission blocked
- **Server-side**: Exception thrown, transaction rolled back
- **User experience**: Clear error messages, form data preserved
