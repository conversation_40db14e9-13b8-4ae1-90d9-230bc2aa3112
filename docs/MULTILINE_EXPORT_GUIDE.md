# 📊 Multi-line Export Feature Guide

## 🎯 **Overview**

The Multi-line Export feature allows users to export expense reports with Receipt Numbers displayed on separate lines within the same cell, making it easier to read and analyze expenses with multiple receipts.

---

## 🆚 **Comparison: Regular vs Multi-line Export**

### **Regular Export (Original):**
```
Receipt Numbers: "RCP001 (150.00), RCP002 (200.00), RCP003 (100.00)"
```

### **Multi-line Export (New):**
```
Receipt Numbers: "RCP001 (150.00)
                  RCP002 (200.00)
                  RCP003 (100.00)"
```

---

## 📍 **Available Locations**

### **1. Reports Home (`reports/index.php`)**

#### **Quick Export Section:**
- **Export CSV (Multi-line)** - Green solid button
- **Export Excel (Multi-line)** - Blue solid button  
- **Export PDF (Multi-line)** - Red solid button

#### **Expenses List Section:**
- **CSV / CSV (Multi-line)** - Button group
- **Excel (Multi-line)** - Blue button
- **PDF (Multi-line)** - Red button

### **2. Receipt Summary (`reports/receipt_summary.php`)**

#### **Export Button Group:**
- **CSV** - Regular export (outline)
- **CSV (Multi-line)** - Green solid button
- **Excel (Multi-line)** - Blue button
- **PDF (Multi-line)** - Red button

---

## 🔧 **Technical Implementation**

### **File Structure:**
```
api/
├── export_csv_multiline.php      # CSV with \n line breaks
├── export_excel_multiline.php    # Excel with wrapped text
└── export_pdf_multiline.php      # PDF with HTML line breaks
```

### **Key Features:**

#### **CSV Export (`export_csv_multiline.php`):**
- Uses `\n` (newline) instead of `, ` (comma-space)
- UTF-8 BOM for proper encoding
- Same filtering and role-based access as regular export

#### **Excel Export (`export_excel_multiline.php`):**
- Uses PhpSpreadsheet library
- `setWrapText(true)` for multi-line cells
- Auto-sizing columns with minimum width for receipt column
- Auto row height for better display

#### **PDF Export (`export_pdf_multiline.php`):**
- Uses TCPDF library
- HTML `<br>` tags for line breaks
- Dynamic row height based on number of receipts
- Landscape orientation for better table display
- Limited to 100 records for performance

---

## 🎨 **UI Design**

### **Button Styles:**

#### **Multi-line Buttons (Solid Colors):**
- **CSV (Multi-line):** `btn-success` (Green)
- **Excel (Multi-line):** `btn-primary` (Blue)  
- **PDF (Multi-line):** `btn-danger` (Red)

#### **Regular Buttons (Outline):**
- **CSV:** `btn-outline-success`
- **Print:** `btn-outline-secondary`

#### **Icons:**
- **Multi-line:** `fas fa-align-left` (Text align icon)
- **Excel:** `fas fa-file-excel`
- **PDF:** `fas fa-file-pdf`
- **Regular CSV:** `fas fa-download`

---

## 🔐 **Access Control**

### **Role-based Access:**
- **Data Entry:** Can export own expenses only
- **Verification/Reviewer/Report Viewer/Admin:** Can export all expenses
- Same filtering and permissions as regular export

### **Required Permissions:**
- Must be logged in
- Must have report access (enforced by `report_access_control.php`)

---

## 📋 **Usage Instructions**

### **For End Users:**

#### **1. Access Reports:**
- Navigate to **Reports** → **Reports Home** or **Receipt Summary**

#### **2. Apply Filters (Optional):**
- Date range
- Status filter  
- Search terms
- User filter (if applicable)

#### **3. Choose Export Format:**
- **CSV (Multi-line):** For spreadsheet analysis with line breaks
- **Excel (Multi-line):** For formatted Excel files with wrapped text
- **PDF (Multi-line):** For printable reports with proper formatting

#### **4. Download:**
- Click desired export button
- File will download automatically
- Filename includes timestamp: `expenses_multiline_2025-01-18_14-30-45.xlsx`

### **For Administrators:**

#### **1. Monitor Usage:**
- Check server logs for export activity
- Monitor file sizes (PDF exports are limited to 100 records)

#### **2. Dependencies:**
- **PhpSpreadsheet:** Required for Excel export
- **TCPDF:** Required for PDF export
- Falls back to CSV if libraries unavailable

---

## 🔍 **Troubleshooting**

### **Common Issues:**

#### **1. Excel Export Not Working:**
```
Issue: "Class PhpOffice\PhpSpreadsheet\Spreadsheet not found"
Solution: Install PhpSpreadsheet via Composer or fallback to CSV
```

#### **2. PDF Export Not Working:**
```
Issue: "Class TCPDF not found"  
Solution: Install TCPDF library or fallback to CSV
```

#### **3. Multi-line Not Displaying:**
```
Issue: Receipt numbers showing on single line
Solution: 
- CSV: Ensure application supports \n line breaks
- Excel: Check if text wrapping is enabled
- PDF: Verify HTML rendering
```

#### **4. Large File Sizes:**
```
Issue: PDF files too large
Solution: PDF export limited to 100 records automatically
```

### **Fallback Behavior:**
- If Excel/PDF libraries unavailable, automatically redirects to CSV multi-line
- If CSV multi-line fails, users can use regular CSV export

---

## 📊 **File Format Details**

### **CSV Format:**
```csv
"Expense No","Receipt Numbers (Multi-line)","Receipt Total"
"EXP001","RCP001 (150.00)
RCP002 (200.00)","350.00"
```

### **Excel Format:**
- Column M: Receipt Numbers with text wrapping enabled
- Auto-sized columns with minimum 30-character width for receipt column
- Auto row height for proper multi-line display

### **PDF Format:**
- Landscape A4 orientation
- 6-point font for data
- Dynamic row height based on receipt count
- Automatic page breaks

---

## 🚀 **Future Enhancements**

### **Planned Features:**
- **Batch Export:** Export multiple date ranges
- **Custom Formatting:** User-selectable line separators
- **Email Export:** Send reports via email
- **Scheduled Exports:** Automated report generation

### **Performance Improvements:**
- **Streaming Export:** For large datasets
- **Compression:** ZIP files for multiple formats
- **Caching:** Temporary file caching for repeated exports

---

**Last Updated:** October 2025  
**Feature Version:** 1.0  
**Compatible With:** All user roles with report access
