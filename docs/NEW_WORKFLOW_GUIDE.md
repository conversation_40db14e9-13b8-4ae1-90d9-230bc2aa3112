# 📋 New Expense Workflow Guide

## 🎯 Overview

ระบบ Expense Management ได้รับการปรับปรุงใหม่เพื่อเพิ่มความปลอดภัยและการควบคุมที่ดีขึ้น โดยมีการเปลี่ยนแปลงหลักดังนี้:

### ✅ **สิ่งที่เปลี่ยนแปลง:**
- ❌ **ลบการแก้ไข (Edit)**: ไม่สามารถแก้ไขรายการ expense ได้อีกต่อไป
- 🔄 **เปลี่ยน Return เป็น Check**: ปุ่ม Return กลายเป็น Check สำหรับการตรวจสอบ
- ✅ **เพิ่ม Checked Status**: สถานะใหม่สำหรับรายการที่ผ่านการตรวจสอบแล้ว
- 🔁 **อนุญาต Duplicate**: สามารถใช้ transfer_no และ receipt_numbers ซ้ำได้สำหรับรายการที่ถูก reject
- 📦 **Enhanced Batch Process**: รองรับการอัพโหลดหลายไฟล์พร้อมกัน

---

## 🔄 **New Workflow**

### **1. Data Entry Role**
```
สร้าง Expense → Status: Open
```
- ✅ สร้างรายการ expense ใหม่ได้
- ❌ ไม่สามารถแก้ไขรายการได้
- 🔁 สามารถสร้างรายการใหม่ด้วย transfer_no ที่ซ้ำจากรายการที่ถูก reject

### **2. Verification Role**
```
Open → Check → Checked → Verify → Pending
                ↓
              Reject → Open (สำหรับ Data Entry สร้างใหม่)
```

#### **2.1 Check Process (ใหม่)**
- เข้าไปที่รายการ expense (view.php)
- กดปุ่ม **"Check - ตรวจสอบแล้ว"**
- ระบุหมายเหตุการตรวจสอบ (ไม่บังคับ)
- รายการจะเปลี่ยนเป็น status: **Checked**

#### **2.2 Verification Process**
- ใช้ **Multi-Verification** เลือกเฉพาะรายการที่ status = **Checked**
- สร้าง Batch และทำการ Verify
- รายการจะเปลี่ยนเป็น status: **Pending**

#### **2.3 Reject Process**
- หากพบรายการผิด ให้ใช้ **Reject** แทน Return
- รายการจะกลับเป็น status: **Open**
- Data Entry สามารถสร้างรายการใหม่ด้วย transfer_no เดิมได้

### **3. Reviewer Role**
```
Pending Batches → Review → Approve/Reject → Success/Open
```

#### **3.1 Review Completed Batches**
- เข้าไปที่ **Multi-Review** จะแสดงรายการ Batch ที่ผ่าน Verification แล้ว
- เลือก Batch ที่ต้องการ Review
- กดปุ่ม **Review Batch**

#### **3.2 Batch Review Process**
- ตรวจสอบรายการทั้งหมดใน Batch
- ดูเอกสารแนบ (ถ้ามี)
- ตัดสินใจ:
  - **Approve**: รายการทั้งหมดเปลี่ยนเป็น **Success**
  - **Reject**: รายการทั้งหมดกลับเป็น **Open** สำหรับ re-verification

---

## 🆕 **New Features**

### **1. Enhanced Batch Process**
- รองรับการอัพโหลดหลายไฟล์พร้อมกัน
- ระบุ transfer_number และ amount แยกสำหรับแต่ละไฟล์
- ตรวจสอบยอดรวมอัตโนมัติ
- Drag & Drop interface

### **2. Status Filter**
- **Multi-Verification**: เลือกได้ระหว่าง Open/Checked (แนะนำ Checked)
- **Dashboard**: แสดงสถิติ Checked status
- **List**: เพิ่ม filter สำหรับ Checked status

### **3. Enhanced Logging**
- System logs สำหรับ system-level events
- Batch operation logs สำหรับติดตาม batch processing
- File operation logs สำหรับ file uploads
- Activity logs ที่ครบถ้วนขึ้น

---

## 🎯 **Benefits**

### **🔒 Security**
- ป้องกันการแก้ไขข้อมูลโดยไม่ได้รับอนุญาต
- ตรวจสอบได้ทุกขั้นตอน (audit trail)
- แยกหน้าที่ชัดเจน (separation of duties)

### **⚡ Efficiency**
- ลดขั้นตอนที่ซับซ้อน
- Multi-verification และ multi-review
- Enhanced batch processing
- Real-time validation

### **📊 Control**
- Workflow ที่เป็นระบบ
- Status tracking ที่ชัดเจน
- Enhanced reporting
- พร้อมสำหรับ Multi-Verification ในอนาคต

---

## 🚀 **Quick Start Guide**

### **For Data Entry:**
1. สร้าง expense ใหม่ตามปกติ
2. หากรายการถูก reject ให้สร้างใหม่ (สามารถใช้ transfer_no เดิมได้)
3. ❌ ไม่สามารถแก้ไขรายการได้อีกต่อไป

### **For Verification:**
1. เข้าไปที่รายการ expense → กด **Check** ก่อน
2. ใช้ **Multi-Verification** เลือกรายการที่ **Checked**
3. สร้าง Batch และ Verify
4. หากผิด ให้ใช้ **Reject** แทน Return

### **For Reviewer:**
1. เข้าไปที่ **Multi-Review** 
2. เลือก Completed Batch
3. กด **Review Batch**
4. ตัดสินใจ **Approve** หรือ **Reject**

---

## 📞 **Support**

หากมีปัญหาหรือข้อสงสัย กรุณาติดต่อ:
- **System Administrator**
- **IT Support Team**

---

## 📝 **Version History**

- **v2.0** - New Workflow Implementation
  - Added Check functionality
  - Removed Edit capability
  - Enhanced Batch Processing
  - Improved Security & Audit Trail
