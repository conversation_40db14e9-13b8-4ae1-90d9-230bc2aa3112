# Report Viewer Role Guide

## 📊 **Overview**

The **Report Viewer** role is designed for users who need read-only access to reports and data viewing without the ability to modify any records. This role is perfect for managers, auditors, or stakeholders who need to monitor expenses and generate reports.

---

## 🔐 **Permissions & Access**

### ✅ **What Report Viewers CAN Do:**

#### **Reports Access:**
- 📈 View all expense reports
- 📊 Access advanced analytics and charts
- 📋 View performance reports
- 📄 Export reports to PDF/Excel/CSV
- 📝 View activity logs (read-only)

#### **Data Viewing:**
- 👁️ View all expenses (read-only)
- 📋 View expense details and documents
- 💾 Download files and receipts
- 🔍 Search and filter expenses
- 📊 View master data (items, customers, drivers)

#### **Dashboard Access:**
- 📊 Quick access to reports from dashboard
- 📈 View summary statistics
- 🔗 Direct links to all report types

### ❌ **What Report Viewers CANNOT Do:**

#### **Data Modification:**
- ❌ Create new expenses
- ❌ Edit existing expenses
- ❌ Delete any records
- ❌ Change expense status
- ❌ Upload new documents

#### **Administrative Functions:**
- ❌ Manage users
- ❌ Access admin panel
- ❌ Modify system settings
- ❌ Manage master data

#### **Workflow Actions:**
- ❌ Approve/reject expenses
- ❌ Process batch operations
- ❌ Change workflow status

---

## 🎯 **Role Hierarchy**

```
1. Data Entry (Level 1)     - Create/edit own records
2. Verification (Level 2)   - Review and approve to pending
3. Reviewer (Level 3)       - Final approval to success
4. Report Viewer (Level 4)  - Read-only reports access
5. Administrator (Level 5)  - Full system access
```

**Note:** Report Viewer has higher level than Reviewer but with restricted permissions focused on reporting only.

---

## 🚀 **Getting Started**

### **1. Login & Dashboard**
- Login with your report_viewer credentials
- Dashboard shows quick access to all report types
- No expense creation/editing options visible

### **2. Accessing Reports**
- **Navigation Menu:** Reports → [Report Type]
- **Dashboard:** Click on report quick action buttons
- **Direct URLs:** All report pages are accessible

### **3. Available Reports**
1. **Reports Home** (`/reports/`) - Overview of all reports
2. **Expense Summary** - Summary statistics and charts
3. **Advanced Reports** - Detailed analytics
4. **Performance Report** - System performance metrics
5. **Receipt Summary** - Receipt-specific analysis
6. **Activity Logs** - System activity tracking

---

## 📋 **Common Tasks**

### **Viewing Expense Reports**
1. Go to **Reports** → **Expense Summary**
2. Set date range and filters
3. View charts and statistics
4. Export if needed

### **Searching Expenses**
1. Go to **Expenses** → **List**
2. Use search filters
3. View expense details (read-only)
4. Download documents if needed

### **Exporting Data**
1. Navigate to any report
2. Set desired filters
3. Click **Export** button
4. Choose format (PDF/Excel/CSV)

---

## 🔧 **Technical Implementation**

### **Database Schema**
```sql
-- Role added to users table
ALTER TABLE users MODIFY COLUMN role ENUM(
    'data_entry', 
    'verification', 
    'reviewer', 
    'report_viewer', 
    'administrator'
) NOT NULL DEFAULT 'data_entry';
```

### **Permission Functions**
```php
// Role hierarchy
$roles = [
    'data_entry' => 1,
    'verification' => 2,
    'reviewer' => 3,
    'report_viewer' => 4,
    'administrator' => 5
];

// Access control
function hasReportAccess($user_role) {
    return in_array($user_role, [
        'verification', 'reviewer', 'report_viewer', 'administrator'
    ]);
}
```

### **Navigation Access**
- Reports menu visible for: verification, reviewer, report_viewer, administrator
- Admin menu hidden for report_viewer
- Expense creation/editing hidden for report_viewer

---

## 🎨 **User Interface**

### **Dashboard Features**
- **Report Quick Actions:** 4 main report buttons
- **No Edit Buttons:** Create/Edit options hidden
- **Read-only Indicators:** Clear visual cues for read-only access

### **Color Coding**
- **Role Badge:** Primary blue (`bg-primary`)
- **Icon:** Chart bar (`fas fa-chart-bar`)
- **Theme:** Professional blue theme

---

## 🔍 **Security Features**

### **Access Control**
- Server-side permission validation
- Role-based menu filtering
- Function-level security checks
- Database-level access restrictions

### **Data Protection**
- Read-only database access
- No modification endpoints accessible
- Secure file download only
- Activity logging for all actions

---

## 📞 **Support & Troubleshooting**

### **Common Issues**
1. **Cannot see reports:** Check role assignment
2. **Access denied:** Verify user is active
3. **Missing data:** Check date range filters
4. **Export fails:** Verify export permissions

### **Contact Information**
- **System Administrator:** For role changes
- **Technical Support:** For system issues
- **Documentation:** This guide and system help

---

## 🔄 **Future Enhancements**

### **Planned Features**
- Custom dashboard widgets
- Scheduled report delivery
- Advanced filtering options
- Real-time data refresh
- Mobile-responsive improvements

### **Feedback**
Report Viewer role feedback is welcome to improve the user experience and add relevant features for reporting needs.

---

**Last Updated:** October 2025  
**Version:** 1.0  
**Role Level:** 4 (Read-only Reports Access)
