# Batch Operations Permissions Guide

## Overview
This guide explains the permissions and access control for batch operations in the Expenses Management System.

## Role-based Permissions Matrix

### 1. Batch Creation
| Role | Verification Batch | Review Batch | Notes |
|------|-------------------|--------------|-------|
| **data_entry** | ❌ | ❌ | Cannot create batch operations |
| **verification** | ✅ | ❌ | Can create verification batches only |
| **reviewer** | ❌ | ✅ | Can create review batches only |
| **administrator** | ✅ | ✅ | Can create all types of batches |

### 2. Batch Processing
| Role | Own Batches | Others' Batches | Notes |
|------|-------------|-----------------|-------|
| **data_entry** | ❌ | ❌ | Cannot process any batches |
| **verification** | ✅ | ❌ | Can process own verification batches only |
| **reviewer** | ✅ | ❌ | Can process own review batches only |
| **administrator** | ✅ | ✅ | Can process all batches |

### 3. Batch Management (Cancel/Retry)
| Role | Own Pending/Failed | Others' Batches | Notes |
|------|-------------------|-----------------|-------|
| **data_entry** | ❌ | ❌ | No batch management access |
| **verification** | ✅ | ❌ | Can manage own verification batches |
| **reviewer** | ✅ | ❌ | Can manage own review batches |
| **administrator** | ✅ | ✅ | Can manage all batches |

### 4. Batch Viewing
| Role | Own Batches | Others' Batches | Admin Panel |
|------|-------------|-----------------|-------------|
| **data_entry** | ❌ | ❌ | ❌ |
| **verification** | ✅ | ❌ | ❌ |
| **reviewer** | ✅ | ❌ | ❌ |
| **administrator** | ✅ | ✅ | ✅ |

## Batch Status Permissions

### Status-based Actions
| Status | Retry | Cancel | Process | View |
|--------|-------|--------|---------|------|
| **pending** | ❌ | ✅ | ✅ | ✅ |
| **processing** | ❌ | ❌ | ❌ | ✅ |
| **completed** | ❌ | ❌ | ❌ | ✅ |
| **failed** | ✅ | ✅ | ❌ | ✅ |
| **cancelled** | ❌ | ❌ | ❌ | ✅ |

## Access Points

### 1. Regular Users (verification/reviewer)
- **Create Batch**: `expenses/multi_verification.php` or `expenses/multi_review.php`
- **Process Batch**: `expenses/batch_process.php?batch_id=XXX&type=verification/review`
- **Manage Batch**: Buttons in `batch_process.php` (for own batches only)

### 2. Administrators
- **All User Functions**: Same as above
- **Admin Panel**: `admin/batch_management.php`
- **Centralized Management**: View, retry, cancel all batches

## API Endpoints

### 1. Batch Creation
- **POST** `/api/batch_verification.php` - Create verification batch
- **POST** `/api/batch_review.php` - Create review batch

### 2. Batch Processing
- **POST** `/api/batch_verification.php` (action=process) - Process verification batch
- **POST** `/api/batch_review.php` (action=process) - Process review batch

### 3. Batch Management
- **POST** `/api/batch_retry.php` - Retry failed batch
- **POST** `/api/batch_cancel.php` - Cancel pending/failed batch

## Activity Logging

### New Action Types
- `batch_verification` - Batch verification processing
- `batch_review` - Batch review processing
- `batch_cancel` - Batch cancellation (by owner)
- `batch_retry` - Batch retry (by owner)
- `admin_override` - Admin actions on others' batches

### Log Details
All batch operations are logged with:
- User ID and role
- Batch ID and operation type
- Action performed
- IP address and user agent
- Timestamp

## Security Considerations

### 1. Ownership Validation
- Users can only manage their own batches (except admin)
- Batch ownership checked via `user_id` in `batch_operations` table

### 2. Role Validation
- Verification users cannot access review batches
- Review users cannot access verification batches
- Role checked against batch `operation_type`

### 3. Status Validation
- Actions only allowed on appropriate statuses
- Processing batches cannot be modified
- Completed batches are read-only

### 4. Permission Inheritance
- Higher roles inherit lower role permissions
- Administrator has full access to all operations

## Error Handling

### Common Error Messages
- "Access denied to this batch" - User doesn't own the batch
- "You do not have permission to [action] this batch" - Role/permission issue
- "Only [status] batches can be [action]" - Status validation failure
- "Batch not found" - Invalid batch ID

### Error Responses
All API endpoints return consistent error format:
```json
{
    "success": false,
    "error": "Error message description"
}
```

## Best Practices

### 1. For Users
- Only create batches when you have multiple items to process
- Check batch status before attempting actions
- Use retry function for failed batches instead of recreating

### 2. For Administrators
- Use admin panel for centralized batch management
- Monitor failed batches and assist users with retry
- Review activity logs for audit purposes

### 3. For Developers
- Always validate permissions before batch operations
- Log all batch actions for audit trail
- Use transactions for batch status changes
- Implement proper error handling and user feedback
