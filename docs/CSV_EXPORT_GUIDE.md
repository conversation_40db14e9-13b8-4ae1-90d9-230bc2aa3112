# 📊 CSV Export System User Guide

## 🎯 **Overview**

The CSV Export System provides two export formats for expense data:
1. **CSV (Regular)** - Traditional format with receipt numbers in a single line
2. **CSV (Per Receipt)** - Detailed format with each receipt as a separate row

---

## 🆚 **Comparison: Regular vs Per Receipt Export**

### **CSV (Regular) Format:**
```csv
Expense No,Customer,Receipt Numbers,Receipt Total
EXP001,Customer A,"RCP001 (600.00), RCP002 (400.00)",1000.00
EXP002,Customer B,"RCP003 (800.00)",800.00
```

### **CSV (Per Receipt) Format:**
```csv
Expense No,Customer,Receipt Number,Receipt Amount,Receipt Total
EXP001,Customer A,RCP001,600.00,1000.00
,,,RCP002,400.00,
EXP002,Customer B,RCP003,800.00,800.00
```

---

## 📍 **Available Locations**

### **1. Reports Home (`reports/index.php`)**
- **Top Section:** Large buttons for primary export actions
- **Bottom Section:** Small button group for quick export

### **2. Receipt Summary (`reports/receipt_summary.php`)**
- **Export Section:** Button group with both export options

---

## 🎨 **UI Design**

### **Button Styles:**
- **CSV (Regular):** `btn-outline-success` with download icon
- **CSV (Per Receipt):** `btn-success` with list icon

### **Button Groups:**
```html
<div class="btn-group" role="group">
    <a href="../api/export_csv.php" class="btn btn-outline-success btn-sm">
        <i class="fas fa-download me-1"></i>CSV (Regular)
    </a>
    <a href="../api/export_csv_per_receipt.php" class="btn btn-success btn-sm">
        <i class="fas fa-list me-1"></i>CSV (Per Receipt)
    </a>
</div>
```

---

## 🔐 **Access Control**

### **Role-based Data Access:**
- **Administrator:** Can export all expenses
- **Verification:** Can export all expenses
- **Reviewer:** Can export all expenses
- **Report Viewer:** Can export all expenses (read-only)
- **Data Entry:** Can export only their own expenses

### **Filter Support:**
Both export formats support all existing filters:
- Search terms (expense no, booking no, item, customer, driver)
- Status filter (open, pending, success, rejected, returned)
- Date range filter (job open date)

---

## 📋 **Usage Instructions**

### **Step 1: Navigate to Reports**
1. Login to the system
2. Go to **Reports** → **Expense Reports** or **Receipt Summary**

### **Step 2: Apply Filters (Optional)**
1. Use search box to filter by keywords
2. Select status from dropdown
3. Set date range if needed
4. Click **Filter** to apply

### **Step 3: Choose Export Format**
1. **For Summary Analysis:** Click **CSV (Regular)**
2. **For Detailed Analysis:** Click **CSV (Per Receipt)**

### **Step 4: Open in Spreadsheet**
1. Download will start automatically
2. Open file in Excel, Google Sheets, or LibreOffice Calc
3. Data will be properly formatted with UTF-8 encoding

---

## 📊 **File Format Details**

### **CSV (Regular) - `expenses_export_YYYY-MM-DD_HH-ii-ss.csv`**

**Headers:**
```
Expense No, Job Date, Booking No, Item, Customer, Driver, Container No, 
Vehicle Plate, Payment Account, Requester, Receiver, Payer, 
Withdrawal Date, Transfer No, Transfer Amount, Receipt Numbers, 
Receipt Total, Amount Match, Status, Additional Details, Created By, Created Date
```

**Data Format:**
- One row per expense
- Receipt numbers combined in single cell: "RCP001 (600.00), RCP002 (400.00)"
- Receipt total calculated from all receipts

### **CSV (Per Receipt) - `expenses_per_receipt_YYYY-MM-DD_HH-ii-ss.csv`**

**Headers:**
```
Expense No, Job Date, Booking No, Item, Customer, Driver, Container No, 
Vehicle Plate, Payment Account, Requester, Receiver, Payer, 
Withdrawal Date, Transfer No, Transfer Amount, Receipt Number, 
Receipt Amount, Receipt Description, Receipt Total, Amount Match, 
Status, Additional Details, Created By, Created Date
```

**Data Format:**
- One row per receipt number
- Expense data shown only on first receipt row
- Subsequent receipt rows have empty expense columns
- Individual receipt amount and description per row

---

## 🔧 **Technical Implementation**

### **Database Query:**
```sql
-- Per Receipt Export uses JOIN to get all receipt rows
SELECT e.*, rn.receipt_number, rn.amount, rn.description,
       ROW_NUMBER() OVER (PARTITION BY e.id ORDER BY rn.id) as receipt_row_num
FROM expenses e
LEFT JOIN receipt_numbers rn ON e.id = rn.expense_id
ORDER BY e.created_at DESC, rn.id ASC
```

### **PHP Logic:**
```php
$current_expense_id = null;
foreach ($results as $row) {
    $is_first_receipt = ($current_expense_id !== $row['expense_id']);
    
    if ($is_first_receipt) {
        // Show all expense data
        $csv_row = [$expense_data..., $receipt_data...];
    } else {
        // Show only receipt data, empty expense columns
        $csv_row = ['', '', '', ..., $receipt_data...];
    }
}
```

---

## 🚀 **Use Cases**

### **CSV (Regular) - Best For:**
- Executive summaries
- High-level reporting
- Quick overviews
- When receipt details are not needed

### **CSV (Per Receipt) - Best For:**
- Detailed analysis
- Pivot tables and data analysis
- Receipt-level reporting
- Accounting reconciliation
- When you need to analyze individual receipts

---

## 🔍 **Troubleshooting**

### **Common Issues:**

1. **File appears empty**
   - Check if you're logged in
   - Verify you have data matching your filters

2. **Characters display incorrectly**
   - File uses UTF-8 encoding with BOM
   - Open in Excel using "Data" → "From Text/CSV" for proper encoding

3. **Access denied**
   - Data Entry users can only export their own expenses
   - Contact administrator for broader access

4. **Large file downloads slowly**
   - Per Receipt format creates more rows
   - Consider using date range filters to limit data

---

## 📈 **Future Enhancements**

### **Potential Improvements:**
- Excel format with native .xlsx output
- PDF reports with formatted layouts
- Scheduled exports via email
- Custom column selection
- Export templates for different use cases

---

**📞 For technical support or feature requests, contact your system administrator.**
