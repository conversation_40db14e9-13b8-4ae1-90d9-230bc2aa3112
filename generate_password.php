<?php
// Generate correct password hash for admin123
$password = 'admin123';
$hash = password_hash($password, PASSWORD_DEFAULT);

echo "Password: " . $password . "\n";
echo "Hash: " . $hash . "\n";

// Test verification
if (password_verify($password, $hash)) {
    echo "✅ Password verification: SUCCESS\n";
} else {
    echo "❌ Password verification: FAILED\n";
}

// Test with the existing hash from database
$existing_hash = '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi';
echo "\nTesting existing hash from database:\n";
if (password_verify($password, $existing_hash)) {
    echo "✅ Existing hash verification: SUCCESS\n";
} else {
    echo "❌ Existing hash verification: FAILED\n";
}

// Generate SQL update statement
echo "\nSQL Update Statement:\n";
echo "UPDATE users SET password_hash = '" . $hash . "' WHERE username = 'admin';\n";
?>
