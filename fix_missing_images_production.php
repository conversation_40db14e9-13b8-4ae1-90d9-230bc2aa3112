<?php
require_once 'config/database.php';

echo "<h2>Fix Missing Images - Production Solution</h2>";

$database = new Database();
$db = $database->getConnection();

// Option 1: Find and fix missing images by updating database
echo "<h3>Option 1: Update Database to Match Available Files</h3>";

// Get expenses with missing transfer slip images
$stmt = $db->prepare("
    SELECT id, exno, transfer_slip_image, created_at
    FROM expenses 
    WHERE transfer_slip_image IS NOT NULL 
    AND transfer_slip_image != ''
    ORDER BY created_at DESC
    LIMIT 20
");
$stmt->execute();
$expenses = $stmt->fetchAll();

echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
echo "<tr><th>ID</th><th>Expense No</th><th>Current Image</th><th>File Exists</th><th>Suggested Fix</th><th>Action</th></tr>";

foreach ($expenses as $expense) {
    $current_file = $expense['transfer_slip_image'];
    $file_path = 'uploads/transfer_slips/' . $current_file;
    $file_exists = file_exists($file_path);
    
    echo "<tr>";
    echo "<td>{$expense['id']}</td>";
    echo "<td>{$expense['exno']}</td>";
    echo "<td>$current_file</td>";
    echo "<td style='color: " . ($file_exists ? 'green' : 'red') . "'>" . ($file_exists ? 'YES' : 'NO') . "</td>";
    
    if (!$file_exists) {
        // Try to find similar files by timestamp or pattern
        $created_time = strtotime($expense['created_at']);
        $date_range_start = $created_time - 7200; // 2 hours before
        $date_range_end = $created_time + 7200;   // 2 hours after
        
        $all_files = glob('uploads/transfer_slips/transfer_*.{jpg,jpeg,png}', GLOB_BRACE);
        $candidates = [];
        
        foreach ($all_files as $file) {
            $file_time = filemtime($file);
            if ($file_time >= $date_range_start && $file_time <= $date_range_end) {
                $time_diff = abs($file_time - $created_time);
                $candidates[] = [
                    'filename' => basename($file),
                    'time_diff' => $time_diff
                ];
            }
        }
        
        // Sort by time difference
        usort($candidates, function($a, $b) {
            return $a['time_diff'] - $b['time_diff'];
        });
        
        if ($candidates) {
            $best_match = $candidates[0];
            $time_diff_min = round($best_match['time_diff'] / 60);
            echo "<td>Found: {$best_match['filename']} (±{$time_diff_min}min)</td>";
            echo "<td><a href='?update_expense={$expense['id']}&new_file=" . urlencode($best_match['filename']) . "'>Update</a></td>";
        } else {
            echo "<td>No candidates found</td>";
            echo "<td><a href='?clear_expense={$expense['id']}'>Clear Image</a></td>";
        }
    } else {
        echo "<td>File OK</td>";
        echo "<td>-</td>";
    }
    echo "</tr>";
}
echo "</table>";

// Handle update requests
if (isset($_GET['update_expense']) && isset($_GET['new_file'])) {
    $expense_id = (int)$_GET['update_expense'];
    $new_filename = $_GET['new_file'];
    
    // Verify new file exists
    if (file_exists('uploads/transfer_slips/' . $new_filename)) {
        $stmt = $db->prepare("UPDATE expenses SET transfer_slip_image = ? WHERE id = ?");
        if ($stmt->execute([$new_filename, $expense_id])) {
            echo "<div style='background: #d4edda; border: 1px solid #c3e6cb; padding: 10px; margin: 10px 0; border-radius: 5px;'>";
            echo "<strong>✅ Success!</strong> Updated expense ID $expense_id to use file: $new_filename";
            echo "<br><a href='expenses/view.php?id=$expense_id'>View Updated Expense</a>";
            echo "</div>";
        } else {
            echo "<div style='background: #f8d7da; border: 1px solid #f5c6cb; padding: 10px; margin: 10px 0; border-radius: 5px;'>";
            echo "<strong>❌ Error!</strong> Failed to update database.";
            echo "</div>";
        }
    } else {
        echo "<div style='background: #f8d7da; border: 1px solid #f5c6cb; padding: 10px; margin: 10px 0; border-radius: 5px;'>";
        echo "<strong>❌ Error!</strong> File does not exist: $new_filename";
        echo "</div>";
    }
}

// Handle clear requests
if (isset($_GET['clear_expense'])) {
    $expense_id = (int)$_GET['clear_expense'];
    
    $stmt = $db->prepare("UPDATE expenses SET transfer_slip_image = NULL WHERE id = ?");
    if ($stmt->execute([$expense_id])) {
        echo "<div style='background: #fff3cd; border: 1px solid #ffeaa7; padding: 10px; margin: 10px 0; border-radius: 5px;'>";
        echo "<strong>⚠️ Cleared!</strong> Removed image reference for expense ID $expense_id";
        echo "</div>";
    }
}

// Option 2: Upload missing files manually
echo "<h3>Option 2: Manual File Upload</h3>";
echo "<p>If you have the missing files, you can upload them manually:</p>";
echo "<ol>";
echo "<li>Use FTP/SFTP to upload files to <code>uploads/transfer_slips/</code></li>";
echo "<li>Ensure file permissions are set to 644</li>";
echo "<li>File names must match exactly what's in the database</li>";
echo "</ol>";

// Option 3: Bulk fix for production
echo "<h3>Option 3: Production Server Instructions</h3>";
echo "<div style='background: #e7f3ff; border: 1px solid #b3d9ff; padding: 15px; border-radius: 5px;'>";
echo "<h4>For Production Server (nkslgroup.com):</h4>";
echo "<ol>";
echo "<li><strong>Copy files from V1 to current system:</strong>";
echo "<pre>cp -r /path/to/expenses_system-V1/uploads/* /path/to/expenses_system/uploads/</pre>";
echo "</li>";
echo "<li><strong>Set proper permissions:</strong>";
echo "<pre>chmod -R 644 /path/to/expenses_system/uploads/*
chmod -R 755 /path/to/expenses_system/uploads/*/</pre>";
echo "</li>";
echo "<li><strong>Verify ownership:</strong>";
echo "<pre>chown -R www-data:www-data /path/to/expenses_system/uploads/</pre>";
echo "</li>";
echo "</ol>";
echo "</div>";

// Test specific files
echo "<h3>Test Specific Files:</h3>";
$test_files = [
    'transfer_68fb62282cbe1_1761305128.jpg',
    'transfer_68fb847fcc6db_1761313919.jpg',
    'transfer_68fb856aef8b7_1761314154.jpg'
];

foreach ($test_files as $filename) {
    $file_path = 'uploads/transfer_slips/' . $filename;
    $exists = file_exists($file_path);
    
    echo "<p><strong>$filename:</strong> ";
    if ($exists) {
        echo "<span style='color: green;'>✅ EXISTS</span> ";
        echo "- <a href='api/view_file.php?file=" . urlencode($filename) . "&type=transfer_slip' target='_blank'>Test View</a>";
    } else {
        echo "<span style='color: red;'>❌ MISSING</span>";
    }
    echo "</p>";
}

// Directory status
echo "<h3>Directory Status:</h3>";
$upload_dirs = ['receipts', 'transfer_slips', 'verification_slips', 'review_slips'];

foreach ($upload_dirs as $dir) {
    $path = "uploads/$dir/";
    $exists = is_dir($path);
    $writable = is_writable($path);
    $file_count = $exists ? count(glob($path . '*')) : 0;
    
    echo "<p><strong>$dir/:</strong> ";
    echo $exists ? "✅ EXISTS" : "❌ MISSING";
    echo " | ";
    echo $writable ? "✅ WRITABLE" : "❌ NOT WRITABLE";
    echo " | Files: $file_count";
    echo "</p>";
}
?>
