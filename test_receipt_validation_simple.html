<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Receipt Validation - Simple</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        /* Receipt number and transfer number validation styling */
        .receipt-number-input.is-invalid,
        #transfer_no.is-invalid {
            border-color: #dc3545;
            background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 12 12' width='12' height='12' fill='none' stroke='%23dc3545'%3e%3ccircle cx='6' cy='6' r='4.5'/%3e%3cpath d='m5.8 3.6h.4L6 6.5z'/%3e%3ccircle cx='6' cy='8.2' r='.6' fill='%23dc3545' stroke='none'/%3e%3c/svg%3e");
            background-repeat: no-repeat;
            background-position: right calc(0.375em + 0.1875rem) center;
            background-size: calc(0.75em + 0.375rem) calc(0.75em + 0.375rem);
        }
        
        .receipt-number-input.is-valid,
        #transfer_no.is-valid {
            border-color: #198754;
            background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 8 8'%3e%3cpath fill='%23198754' d='m2.3 6.73.94-.94 1.93 1.93 3.53-3.53.94.94L4.16 9.66z'/%3e%3c/svg%3e");
            background-repeat: no-repeat;
            background-position: right calc(0.375em + 0.1875rem) center;
            background-size: calc(0.75em + 0.375rem) calc(0.75em + 0.375rem);
        }
        
        .invalid-feedback {
            display: block;
            width: 100%;
            margin-top: 0.25rem;
            font-size: 0.875em;
            color: #dc3545;
        }
        
        .valid-feedback {
            display: block;
            width: 100%;
            margin-top: 0.25rem;
            font-size: 0.875em;
            color: #198754;
        }
        
        .receipt-number-checking,
        #transfer_no.transfer-number-checking {
            position: relative;
        }
        
        .receipt-number-checking::after {
            content: '';
            position: absolute;
            right: 10px;
            top: 50%;
            transform: translateY(-50%);
            width: 16px;
            height: 16px;
            border: 2px solid #f3f3f3;
            border-top: 2px solid #007bff;
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }
        
        @keyframes spin {
            0% { transform: translateY(-50%) rotate(0deg); }
            100% { transform: translateY(-50%) rotate(360deg); }
        }
        
        #debug-log {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 0.375rem;
            padding: 1rem;
            font-family: 'Courier New', monospace;
            font-size: 12px;
            max-height: 300px;
            overflow-y: auto;
            white-space: pre-wrap;
        }
    </style>
</head>
<body>
    <div class="container mt-4">
        <div class="row">
            <div class="col-md-10 mx-auto">
                <div class="card">
                    <div class="card-header">
                        <h4><i class="fas fa-test-tube me-2"></i>Test Receipt Validation - Create Form Simulation</h4>
                    </div>
                    <div class="card-body">
                        <div class="alert alert-info">
                            <h6><i class="fas fa-info-circle me-2"></i>Test Instructions:</h6>
                            <ul class="mb-0">
                                <li><strong>Step 1:</strong> Select receipt files to generate receipt number fields</li>
                                <li><strong>Step 2:</strong> Try entering "1111" or "2222" (existing numbers)</li>
                                <li><strong>Step 3:</strong> Try entering same number in multiple fields</li>
                                <li><strong>Step 4:</strong> Check debug log for validation messages</li>
                            </ul>
                        </div>
                        
                        <form>
                            <!-- Transfer Number -->
                            <div class="mb-3">
                                <label for="transfer_no" class="form-label">Transfer Number เลขใบโอน <span class="text-danger">*</span></label>
                                <input type="text" class="form-control" id="transfer_no" name="transfer_no"
                                       placeholder="กรอกเลขใบโอน" required>
                                <div class="invalid-feedback"></div>
                                <div class="valid-feedback"></div>
                                <div class="form-text">Try: 1111 (existing), TEST123 (new)</div>
                            </div>

                            <!-- Receipt Files -->
                            <div class="mb-3">
                                <label for="receipts" class="form-label">Receipt Files *</label>
                                <input type="file" class="form-control" id="receipts" name="receipts[]"
                                       accept="image/*,.pdf" multiple required>
                                <div class="form-text">Select multiple receipt files to test validation</div>
                            </div>
                            
                            <!-- Receipt Numbers Container -->
                            <div id="receipt-numbers-container" class="d-none">
                                <h6>Receipt Details</h6>
                                <div class="alert alert-info">
                                    <small><i class="fas fa-info-circle"></i> กรอกรายละเอียดสำหรับแต่ละใบเสร็จ</small>
                                </div>
                                <div id="receipt-numbers"></div>
                                <div class="mt-3">
                                    <strong>Total Amount: <span id="total-amount">0.00</span> บาท</strong>
                                </div>
                            </div>
                            
                            <div class="mt-4">
                                <button type="submit" class="btn btn-primary" id="submit-btn">
                                    <i class="fas fa-save me-1"></i>Test Submit
                                </button>
                                <button type="button" class="btn btn-secondary" onclick="location.reload()">
                                    <i class="fas fa-refresh me-1"></i>Reload
                                </button>
                                <button type="button" class="btn btn-info" onclick="testValidation()">
                                    <i class="fas fa-play me-1"></i>Auto Test
                                </button>
                            </div>
                        </form>
                        
                        <div class="mt-4">
                            <h6>Debug Log:</h6>
                            <div id="debug-log">Ready for testing...\n</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    
    <script>
        // Debug logging function
        function debugLog(message) {
            const timestamp = new Date().toLocaleTimeString();
            const debugDiv = document.getElementById('debug-log');
            debugDiv.textContent += `[${timestamp}] ${message}\n`;
            debugDiv.scrollTop = debugDiv.scrollHeight;
        }
        
        // Override console.log to also display in debug area
        const originalLog = console.log;
        console.log = function(...args) {
            originalLog.apply(console, args);
            const message = args.map(arg => 
                typeof arg === 'object' ? JSON.stringify(arg) : String(arg)
            ).join(' ');
            debugLog(message);
        };
        
        $(document).ready(function() {
            console.log('Document ready - jQuery version:', $.fn.jquery);

            // Event delegation for receipt number validation (for dynamically created inputs)
            $(document).on('input blur', '.receipt-number-input', function() {
                console.log('Receipt number input event triggered:', $(this).val());
                validateReceiptNumbers();
            });

            // Event listener for transfer number validation
            $('#transfer_no').on('input blur', function() {
                console.log('Transfer number input event triggered:', $(this).val());
                validateTransferNumber();
            });
            
            // Handle receipt file selection
            $('#receipts').change(function() {
                console.log('Receipt files changed, files count:', this.files.length);
                const files = this.files;
                const container = $('#receipt-numbers-container');
                const numbersDiv = $('#receipt-numbers');

                if (files.length > 0) {
                    console.log('Creating receipt number inputs for', files.length, 'files');
                    container.removeClass('d-none');
                    numbersDiv.empty();

                    for (let i = 0; i < files.length; i++) {
                        const file = files[i];
                        const inputGroup = $(`
                            <div class="card mb-3">
                                <div class="card-header">
                                    <h6 class="mb-0"><i class="fas fa-receipt"></i> ${file.name}</h6>
                                </div>
                                <div class="card-body">
                                    <div class="row">
                                        <div class="col-md-4">
                                            <label class="form-label">Receipt Number *</label>
                                            <input type="text" class="form-control receipt-number-input" name="receipt_numbers[]"
                                                   placeholder="เลขที่ใบเสร็จ" required>
                                            <div class="invalid-feedback"></div>
                                            <div class="valid-feedback"></div>
                                        </div>
                                        <div class="col-md-4">
                                            <label class="form-label">Amount (บาท) *</label>
                                            <input type="number" class="form-control receipt-amount" name="receipt_amounts[]"
                                                   placeholder="0.00" step="0.01" min="0" required>
                                        </div>
                                        <div class="col-md-4">
                                            <label class="form-label">Description</label>
                                            <input type="text" class="form-control" name="receipt_descriptions[]"
                                                   placeholder="รายละเอียด...">
                                        </div>
                                    </div>
                                </div>
                            </div>
                        `);
                        numbersDiv.append(inputGroup);
                    }
                    
                    console.log('Receipt number inputs created. Total inputs:', $('.receipt-number-input').length);

                    // Add event listeners for amount calculation
                    $('.receipt-amount').on('input', calculateTotal);
                    calculateTotal();
                } else {
                    container.addClass('d-none');
                }
            });
            
            // Form submission
            $('form').on('submit', function(e) {
                e.preventDefault();
                console.log('Form submitted');

                const hasInvalidReceiptInputs = $('.receipt-number-input.is-invalid').length > 0;
                const hasInvalidTransferInput = $('#transfer_no.is-invalid').length > 0;
                const emptyReceiptInputs = $('.receipt-number-input').filter(function() {
                    return !$(this).val().trim();
                });
                const emptyTransferInput = !$('#transfer_no').val().trim();

                if (hasInvalidReceiptInputs || hasInvalidTransferInput || emptyReceiptInputs.length > 0 || emptyTransferInput) {
                    console.log('Form validation failed - Invalid receipt inputs:', hasInvalidReceiptInputs, 'Invalid transfer input:', hasInvalidTransferInput, 'Empty receipt inputs:', emptyReceiptInputs.length, 'Empty transfer input:', emptyTransferInput);
                } else {
                    console.log('Form validation passed - All numbers are valid');
                }
            });
        });
        
        // Validate receipt numbers for duplicates
        function validateReceiptNumbers() {
            console.log('validateReceiptNumbers called');
            const receiptNumbers = [];
            const inputs = $('.receipt-number-input');
            console.log('Found receipt number inputs:', inputs.length);
            let hasDuplicates = false;
            
            // Clear previous validation states
            inputs.removeClass('is-invalid is-valid').siblings('.invalid-feedback, .valid-feedback').text('');
            
            inputs.each(function() {
                const value = $(this).val().trim();
                const input = $(this);
                
                if (value) {
                    // Check if this receipt number already exists in the form
                    const duplicateIndex = receiptNumbers.indexOf(value);
                    if (duplicateIndex !== -1) {
                        // Mark both inputs as invalid
                        input.addClass('is-invalid');
                        input.siblings('.invalid-feedback').text('Receipt number already used in this form');
                        
                        // Also mark the first occurrence
                        inputs.eq(duplicateIndex).addClass('is-invalid');
                        inputs.eq(duplicateIndex).siblings('.invalid-feedback').text('Receipt number already used in this form');
                        
                        hasDuplicates = true;
                        console.log('Duplicate receipt number found:', value);
                    } else {
                        // Check if receipt number exists in database
                        checkReceiptNumberInDatabase(value, input);
                    }
                    
                    receiptNumbers.push(value);
                }
            });
            
            // Update submit button state
            updateSubmitButtonState();
            
            return !hasDuplicates;
        }
        
        // Check if receipt number exists in database
        function checkReceiptNumberInDatabase(receiptNumber, inputElement) {
            console.log('Checking receipt number in database:', receiptNumber);
            
            // Add loading indicator
            inputElement.addClass('receipt-number-checking');
            
            // Clear previous timeout if exists
            const timeoutId = inputElement.data('timeout-id');
            if (timeoutId) {
                clearTimeout(timeoutId);
            }
            
            // Debounce the API call
            const newTimeoutId = setTimeout(function() {
                $.ajax({
                    url: 'api/check_receipt_number.php',
                    method: 'POST',
                    data: { receipt_number: receiptNumber },
                    dataType: 'json',
                    success: function(response) {
                        inputElement.removeClass('receipt-number-checking');
                        console.log('API response for', receiptNumber, ':', response);
                        
                        if (response.exists) {
                            inputElement.addClass('is-invalid').removeClass('is-valid');
                            inputElement.siblings('.invalid-feedback').text(`Receipt number already exists in expense: ${response.expense_no}`);
                            console.log('Receipt number exists:', receiptNumber, 'in expense:', response.expense_no);
                        } else {
                            inputElement.removeClass('is-invalid').addClass('is-valid');
                            inputElement.siblings('.invalid-feedback').text('');
                            inputElement.siblings('.valid-feedback').text('Receipt number is available');
                            console.log('Receipt number available:', receiptNumber);
                        }
                        updateSubmitButtonState();
                    },
                    error: function(xhr, status, error) {
                        inputElement.removeClass('receipt-number-checking');
                        console.error('Error checking receipt number in database:', error);
                        
                        // Show error but don't block submission
                        inputElement.removeClass('is-invalid is-valid');
                        inputElement.siblings('.invalid-feedback').text('Unable to verify receipt number. Please check manually.');
                        updateSubmitButtonState();
                    }
                });
            }, 500); // 500ms debounce
            
            inputElement.data('timeout-id', newTimeoutId);
        }

        // Validate transfer number for duplicates
        function validateTransferNumber() {
            console.log('validateTransferNumber called');
            const transferInput = $('#transfer_no');
            const transferNumber = transferInput.val().trim();

            // Clear previous validation states
            transferInput.removeClass('is-invalid is-valid transfer-number-checking');
            transferInput.siblings('.invalid-feedback, .valid-feedback').text('');

            if (transferNumber) {
                // Check if transfer number exists in database
                checkTransferNumberInDatabase(transferNumber, transferInput);
            }

            updateSubmitButtonState();
        }

        // Check if transfer number exists in database
        function checkTransferNumberInDatabase(transferNumber, inputElement) {
            console.log('Checking transfer number in database:', transferNumber);

            // Add loading indicator
            inputElement.addClass('transfer-number-checking');

            // Clear previous timeout if exists
            const timeoutId = inputElement.data('timeout-id');
            if (timeoutId) {
                clearTimeout(timeoutId);
            }

            // Debounce the API call
            const newTimeoutId = setTimeout(function() {
                $.ajax({
                    url: 'api/check_transfer_number.php',
                    method: 'POST',
                    data: { transfer_no: transferNumber },
                    dataType: 'json',
                    success: function(response) {
                        inputElement.removeClass('transfer-number-checking');
                        console.log('Transfer API response for', transferNumber, ':', response);

                        if (response.exists) {
                            inputElement.addClass('is-invalid').removeClass('is-valid');
                            inputElement.siblings('.invalid-feedback').text(`Transfer number already exists in expense: ${response.expense_no}`);
                            console.log('Transfer number exists:', transferNumber, 'in expense:', response.expense_no);
                        } else {
                            inputElement.removeClass('is-invalid').addClass('is-valid');
                            inputElement.siblings('.invalid-feedback').text('');
                            inputElement.siblings('.valid-feedback').text('Transfer number is available');
                            console.log('Transfer number available:', transferNumber);
                        }
                        updateSubmitButtonState();
                    },
                    error: function(xhr, status, error) {
                        inputElement.removeClass('transfer-number-checking');
                        console.error('Error checking transfer number in database:', error);

                        // Show error but don't block submission
                        inputElement.removeClass('is-invalid is-valid');
                        inputElement.siblings('.invalid-feedback').text('Unable to verify transfer number. Please check manually.');
                        updateSubmitButtonState();
                    }
                });
            }, 500); // 500ms debounce

            inputElement.data('timeout-id', newTimeoutId);
        }

        // Update submit button state based on validation
        function updateSubmitButtonState() {
            const hasInvalidReceiptInputs = $('.receipt-number-input.is-invalid').length > 0;
            const hasInvalidTransferInput = $('#transfer_no.is-invalid').length > 0;
            const hasInvalidInputs = hasInvalidReceiptInputs || hasInvalidTransferInput;
            const submitButton = $('#submit-btn');

            if (hasInvalidInputs) {
                submitButton.prop('disabled', true);
                if (hasInvalidReceiptInputs && hasInvalidTransferInput) {
                    submitButton.html('<i class="fas fa-exclamation-triangle me-1"></i>Fix Receipt & Transfer Numbers');
                } else if (hasInvalidReceiptInputs) {
                    submitButton.html('<i class="fas fa-exclamation-triangle me-1"></i>Fix Receipt Numbers');
                } else {
                    submitButton.html('<i class="fas fa-exclamation-triangle me-1"></i>Fix Transfer Number');
                }
                submitButton.removeClass('btn-primary').addClass('btn-danger');
                console.log('Submit button disabled due to invalid inputs');
            } else {
                submitButton.prop('disabled', false);
                submitButton.html('<i class="fas fa-save me-1"></i>Test Submit');
                submitButton.removeClass('btn-danger').addClass('btn-primary');
                console.log('Submit button enabled');
            }
        }
        
        // Calculate total amount
        function calculateTotal() {
            let total = 0;
            $('.receipt-amount').each(function() {
                const amount = parseFloat($(this).val()) || 0;
                total += amount;
            });
            $('#total-amount').text(total.toFixed(2));
            console.log('Total amount calculated:', total.toFixed(2));
        }
        
        // Auto test function
        function testValidation() {
            console.log('Starting auto test...');
            
            // Create fake file input to trigger receipt creation
            const fakeFiles = [
                { name: 'receipt1.jpg' },
                { name: 'receipt2.jpg' },
                { name: 'receipt3.jpg' }
            ];
            
            // Simulate file selection
            const container = $('#receipt-numbers-container');
            const numbersDiv = $('#receipt-numbers');
            
            container.removeClass('d-none');
            numbersDiv.empty();
            
            for (let i = 0; i < fakeFiles.length; i++) {
                const file = fakeFiles[i];
                const inputGroup = $(`
                    <div class="card mb-3">
                        <div class="card-header">
                            <h6 class="mb-0"><i class="fas fa-receipt"></i> ${file.name}</h6>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-4">
                                    <label class="form-label">Receipt Number *</label>
                                    <input type="text" class="form-control receipt-number-input" name="receipt_numbers[]"
                                           placeholder="เลขที่ใบเสร็จ" required>
                                    <div class="invalid-feedback"></div>
                                    <div class="valid-feedback"></div>
                                </div>
                                <div class="col-md-4">
                                    <label class="form-label">Amount (บาท) *</label>
                                    <input type="number" class="form-control receipt-amount" name="receipt_amounts[]"
                                           placeholder="0.00" step="0.01" min="0" required value="100.00">
                                </div>
                                <div class="col-md-4">
                                    <label class="form-label">Description</label>
                                    <input type="text" class="form-control" name="receipt_descriptions[]"
                                           placeholder="รายละเอียด..." value="Test item ${i+1}">
                                </div>
                            </div>
                        </div>
                    </div>
                `);
                numbersDiv.append(inputGroup);
            }
            
            console.log('Auto test: Created', fakeFiles.length, 'receipt inputs');
            
            // Fill in test values
            setTimeout(function() {
                // Test transfer number
                $('#transfer_no').val('1111').trigger('input'); // Existing transfer number

                const inputs = $('.receipt-number-input');
                inputs.eq(0).val('1111').trigger('input'); // Existing number
                inputs.eq(1).val('TEST123').trigger('input'); // New number
                inputs.eq(2).val('1111').trigger('input'); // Duplicate

                console.log('Auto test: Filled in test values');
                calculateTotal();
            }, 100);
        }
    </script>
</body>
</html>
