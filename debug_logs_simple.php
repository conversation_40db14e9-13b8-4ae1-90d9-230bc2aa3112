<?php
require_once 'config/database.php';
require_once 'includes/functions.php';

// Create database connection
$database = new Database();
$db = $database->getConnection();

echo "<h2>Debug Activity Logs (No Login Required)</h2>";

// Test 1: Check total logs
echo "<h3>Test 1: Total Activity Logs</h3>";
try {
    $stmt = $db->query("SELECT COUNT(*) as total FROM activity_logs");
    $total = $stmt->fetch()['total'];
    echo "✅ Total activity logs: " . number_format($total) . "<br>";
} catch (Exception $e) {
    echo "❌ Error: " . $e->getMessage() . "<br>";
}

// Test 2: Check action types
echo "<h3>Test 2: Action Types Breakdown</h3>";
try {
    $stmt = $db->query("
        SELECT action_type, COUNT(*) as count 
        FROM activity_logs 
        GROUP BY action_type 
        ORDER BY count DESC
    ");
    $types = $stmt->fetchAll();
    
    if (empty($types)) {
        echo "❌ No action types found<br>";
    } else {
        echo "<table border='1' style='border-collapse: collapse;'>";
        echo "<tr><th>Action Type</th><th>Count</th></tr>";
        foreach ($types as $type) {
            echo "<tr>";
            echo "<td>" . htmlspecialchars($type['action_type']) . "</td>";
            echo "<td>" . number_format($type['count']) . "</td>";
            echo "</tr>";
        }
        echo "</table>";
    }
} catch (Exception $e) {
    echo "❌ Error: " . $e->getMessage() . "<br>";
}

// Test 3: Recent logs (all users)
echo "<h3>Test 3: Recent Activity Logs (All Users)</h3>";
try {
    $stmt = $db->query("
        SELECT al.*, u.full_name, u.username
        FROM activity_logs al
        LEFT JOIN users u ON al.user_id = u.id
        ORDER BY al.created_at DESC
        LIMIT 15
    ");
    $logs = $stmt->fetchAll();
    
    if (empty($logs)) {
        echo "❌ No recent logs found<br>";
    } else {
        echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
        echo "<tr>";
        echo "<th>Date/Time</th>";
        echo "<th>User</th>";
        echo "<th>Action</th>";
        echo "<th>Table</th>";
        echo "<th>Record ID</th>";
        echo "<th>Description</th>";
        echo "</tr>";
        
        foreach ($logs as $log) {
            echo "<tr>";
            echo "<td>" . date('d/m/Y H:i:s', strtotime($log['created_at'])) . "</td>";
            echo "<td>" . htmlspecialchars($log['full_name'] ?: $log['username'] ?: 'Unknown') . "</td>";
            echo "<td><strong>" . htmlspecialchars($log['action_type']) . "</strong></td>";
            echo "<td>" . htmlspecialchars($log['table_name'] ?: '-') . "</td>";
            echo "<td>" . ($log['record_id'] ?: '-') . "</td>";
            echo "<td>" . htmlspecialchars(substr($log['description'], 0, 80)) . "</td>";
            echo "</tr>";
        }
        echo "</table>";
    }
} catch (Exception $e) {
    echo "❌ Error: " . $e->getMessage() . "<br>";
}

// Test 4: Check batch-related logs specifically
echo "<h3>Test 4: Batch-Related Logs</h3>";
try {
    $stmt = $db->query("
        SELECT * 
        FROM activity_logs 
        WHERE action_type IN ('batch_verification', 'batch_review', 'test', 'test_debug')
        ORDER BY created_at DESC
        LIMIT 10
    ");
    $batch_logs = $stmt->fetchAll();
    
    if (empty($batch_logs)) {
        echo "❌ No batch-related logs found<br>";
        echo "This suggests that batch operations are not creating logs properly.<br>";
    } else {
        echo "✅ Found " . count($batch_logs) . " batch-related logs:<br>";
        echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
        echo "<tr>";
        echo "<th>Date/Time</th>";
        echo "<th>User ID</th>";
        echo "<th>Action</th>";
        echo "<th>Record ID</th>";
        echo "<th>Description</th>";
        echo "</tr>";
        
        foreach ($batch_logs as $log) {
            echo "<tr>";
            echo "<td>" . date('d/m/Y H:i:s', strtotime($log['created_at'])) . "</td>";
            echo "<td>" . $log['user_id'] . "</td>";
            echo "<td><strong>" . htmlspecialchars($log['action_type']) . "</strong></td>";
            echo "<td>" . ($log['record_id'] ?: '-') . "</td>";
            echo "<td>" . htmlspecialchars($log['description']) . "</td>";
            echo "</tr>";
        }
        echo "</table>";
    }
} catch (Exception $e) {
    echo "❌ Error: " . $e->getMessage() . "<br>";
}

// Test 5: Test creating a log entry
echo "<h3>Test 5: Test Creating Log Entry</h3>";
try {
    $test_user_id = 1; // Assume user ID 1 exists
    
    logActivity(
        $db,
        $test_user_id,
        'test_debug',
        'test_table',
        999,
        'Test log from debug_logs_simple.php at ' . date('Y-m-d H:i:s'),
        $_SERVER['REMOTE_ADDR'] ?? 'unknown',
        $_SERVER['HTTP_USER_AGENT'] ?? 'unknown'
    );
    
    echo "✅ Test log entry created successfully<br>";
    
    // Verify it was created
    $stmt = $db->prepare("
        SELECT * 
        FROM activity_logs 
        WHERE action_type = 'test_debug' AND user_id = ?
        ORDER BY created_at DESC 
        LIMIT 1
    ");
    $stmt->execute([$test_user_id]);
    $test_log = $stmt->fetch();
    
    if ($test_log) {
        echo "✅ Test log verified in database:<br>";
        echo "- ID: " . $test_log['id'] . "<br>";
        echo "- Created: " . $test_log['created_at'] . "<br>";
        echo "- Description: " . htmlspecialchars($test_log['description']) . "<br>";
    } else {
        echo "❌ Test log not found in database<br>";
    }
    
} catch (Exception $e) {
    echo "❌ Error creating test log: " . $e->getMessage() . "<br>";
}

// Test 6: Check users table
echo "<h3>Test 6: Available Users</h3>";
try {
    $stmt = $db->query("SELECT id, username, full_name, role FROM users ORDER BY id LIMIT 5");
    $users = $stmt->fetchAll();
    
    if (empty($users)) {
        echo "❌ No users found<br>";
    } else {
        echo "<table border='1' style='border-collapse: collapse;'>";
        echo "<tr><th>ID</th><th>Username</th><th>Full Name</th><th>Role</th></tr>";
        foreach ($users as $user) {
            echo "<tr>";
            echo "<td>" . $user['id'] . "</td>";
            echo "<td>" . htmlspecialchars($user['username']) . "</td>";
            echo "<td>" . htmlspecialchars($user['full_name']) . "</td>";
            echo "<td>" . htmlspecialchars($user['role']) . "</td>";
            echo "</tr>";
        }
        echo "</table>";
    }
} catch (Exception $e) {
    echo "❌ Error: " . $e->getMessage() . "<br>";
}

echo "<br><br>";
echo "<a href='debug_logs.php'>Debug Logs (Login Required)</a>";
echo " | <a href='test_simple_batch.php'>Test Simple Batch</a>";
echo " | <a href='dashboard.php'>Dashboard</a>";
?>
