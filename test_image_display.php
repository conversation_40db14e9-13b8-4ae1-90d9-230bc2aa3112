<?php
/**
 * Test Image Display
 * ทดสอบการแสดงรูป receipt ใน view.php
 * ลบไฟล์นี้หลังจากทดสอบเสร็จแล้ว
 */
session_start();
require_once 'config/database.php';

// Check if user is logged in
if (!isset($_SESSION['user_id'])) {
    header('Location: index.php');
    exit();
}

try {
    $database = new Database();
    $db = $database->getConnection();
    
    // Get recent expenses with receipts
    $stmt = $db->prepare("
        SELECT e.id, e.exno, e.job_open_date, 
               rn.receipt_number, rn.receipt_image, rn.amount, rn.description
        FROM expenses e
        JOIN receipt_numbers rn ON e.id = rn.expense_id
        WHERE rn.receipt_image IS NOT NULL
        ORDER BY e.created_at DESC
        LIMIT 10
    ");
    $stmt->execute();
    $receipts = $stmt->fetchAll();
    
} catch (Exception $e) {
    $error = $e->getMessage();
}
?>
<!DOCTYPE html>
<html lang="th">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Image Display - Expenses System</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        .test-image { max-width: 150px; max-height: 100px; margin: 5px; }
        .status-ok { color: #28a745; }
        .status-error { color: #dc3545; }
    </style>
</head>
<body class="bg-light">
    <div class="container mt-4">
        <div class="row justify-content-center">
            <div class="col-md-12">
                <div class="card shadow">
                    <div class="card-header bg-info text-white">
                        <h3><i class="fas fa-image me-2"></i>Test Image Display</h3>
                        <p class="mb-0">ทดสอบการแสดงรูป Receipt</p>
                    </div>
                    <div class="card-body">
                        
                        <?php if (isset($error)): ?>
                            <div class="alert alert-danger">
                                <i class="fas fa-exclamation-triangle me-2"></i>
                                Error: <?php echo htmlspecialchars($error); ?>
                            </div>
                        <?php endif; ?>

                        <h4><i class="fas fa-folder me-2"></i>1. Directory Check</h4>
                        <?php
                        $upload_dirs = [
                            'uploads/receipts/',
                            'uploads/transfer_slips/',
                            'uploads/verification_slips/',
                            'uploads/review_slips/'
                        ];
                        
                        foreach ($upload_dirs as $dir) {
                            echo "<div class='mb-2'>";
                            if (is_dir($dir)) {
                                if (is_writable($dir)) {
                                    echo "<span class='status-ok'><i class='fas fa-check'></i></span> {$dir} - OK";
                                    
                                    // Count files
                                    $files = glob($dir . '*');
                                    echo " (" . count($files) . " files)";
                                } else {
                                    echo "<span class='status-error'><i class='fas fa-times'></i></span> {$dir} - Not writable";
                                }
                            } else {
                                echo "<span class='status-error'><i class='fas fa-times'></i></span> {$dir} - Missing";
                            }
                            echo "</div>";
                        }
                        ?>

                        <hr>
                        <h4><i class="fas fa-receipt me-2"></i>2. Receipt Images Test</h4>
                        
                        <?php if (empty($receipts)): ?>
                            <div class="alert alert-warning">
                                <i class="fas fa-exclamation-triangle me-2"></i>
                                ไม่พบข้อมูล receipt ที่มีรูปภาพ
                            </div>
                        <?php else: ?>
                            <div class="table-responsive">
                                <table class="table table-striped">
                                    <thead>
                                        <tr>
                                            <th>Expense No</th>
                                            <th>Receipt No</th>
                                            <th>Amount</th>
                                            <th>Image File</th>
                                            <th>File Status</th>
                                            <th>Image Preview</th>
                                            <th>API Test</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <?php foreach ($receipts as $receipt): ?>
                                            <tr>
                                                <td>
                                                    <a href="expenses/view.php?id=<?php echo $receipt['id']; ?>" target="_blank">
                                                        <?php echo htmlspecialchars($receipt['exno']); ?>
                                                    </a>
                                                </td>
                                                <td><?php echo htmlspecialchars($receipt['receipt_number']); ?></td>
                                                <td><?php echo number_format($receipt['amount'], 2); ?></td>
                                                <td>
                                                    <code><?php echo htmlspecialchars($receipt['receipt_image']); ?></code>
                                                </td>
                                                <td>
                                                    <?php
                                                    $file_path = 'uploads/receipts/' . $receipt['receipt_image'];
                                                    if (file_exists($file_path)) {
                                                        echo "<span class='status-ok'><i class='fas fa-check'></i> Exists</span>";
                                                        echo "<br><small>" . number_format(filesize($file_path) / 1024, 1) . " KB</small>";
                                                    } else {
                                                        echo "<span class='status-error'><i class='fas fa-times'></i> Missing</span>";
                                                    }
                                                    ?>
                                                </td>
                                                <td>
                                                    <?php if (file_exists($file_path)): ?>
                                                        <img src="<?php echo $file_path; ?>" 
                                                             class="test-image img-thumbnail" 
                                                             alt="Receipt"
                                                             onerror="this.style.display='none'; this.nextElementSibling.style.display='block';">
                                                        <div style="display:none;" class="text-danger">
                                                            <i class="fas fa-times"></i> Load Error
                                                        </div>
                                                    <?php else: ?>
                                                        <span class="text-muted">No file</span>
                                                    <?php endif; ?>
                                                </td>
                                                <td>
                                                    <a href="api/view_file.php?file=<?php echo urlencode($receipt['receipt_image']); ?>&type=receipt" 
                                                       target="_blank" class="btn btn-sm btn-outline-primary">
                                                        <i class="fas fa-external-link-alt"></i> Test API
                                                    </a>
                                                </td>
                                            </tr>
                                        <?php endforeach; ?>
                                    </tbody>
                                </table>
                            </div>
                        <?php endif; ?>

                        <hr>
                        <h4><i class="fas fa-cogs me-2"></i>3. API Test</h4>
                        <div class="row">
                            <div class="col-md-6">
                                <h6>Direct File Access Test:</h6>
                                <p>ทดสอบการเข้าถึงไฟล์โดยตรง</p>
                                <?php if (!empty($receipts)): ?>
                                    <?php $first_receipt = $receipts[0]; ?>
                                    <div class="border p-2">
                                        <strong>File:</strong> <?php echo htmlspecialchars($first_receipt['receipt_image']); ?><br>
                                        <strong>Direct URL:</strong> 
                                        <a href="uploads/receipts/<?php echo urlencode($first_receipt['receipt_image']); ?>" target="_blank">
                                            uploads/receipts/<?php echo htmlspecialchars($first_receipt['receipt_image']); ?>
                                        </a><br>
                                        <strong>API URL:</strong> 
                                        <a href="api/view_file.php?file=<?php echo urlencode($first_receipt['receipt_image']); ?>&type=receipt" target="_blank">
                                            api/view_file.php?file=<?php echo htmlspecialchars($first_receipt['receipt_image']); ?>&type=receipt
                                        </a>
                                    </div>
                                <?php endif; ?>
                            </div>
                            <div class="col-md-6">
                                <h6>PHP Configuration:</h6>
                                <p><strong>Upload Max Filesize:</strong> <?php echo ini_get('upload_max_filesize'); ?></p>
                                <p><strong>Post Max Size:</strong> <?php echo ini_get('post_max_size'); ?></p>
                                <p><strong>Memory Limit:</strong> <?php echo ini_get('memory_limit'); ?></p>
                                <p><strong>GD Extension:</strong> <?php echo extension_loaded('gd') ? 'Loaded' : 'Not loaded'; ?></p>
                            </div>
                        </div>

                        <div class="alert alert-warning mt-3">
                            <h6><i class="fas fa-shield-alt me-2"></i>Security Notice</h6>
                            <p><strong>ลบไฟล์นี้ (test_image_display.php) หลังจากทดสอบเสร็จแล้ว!</strong></p>
                        </div>

                        <div class="text-center mt-4">
                            <a href="expenses/" class="btn btn-primary me-3">
                                <i class="fas fa-list me-2"></i>Expenses List
                            </a>
                            <button onclick="window.location.reload()" class="btn btn-secondary">
                                <i class="fas fa-sync me-2"></i>Refresh Test
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</body>
</html>
