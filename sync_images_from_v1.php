<?php
require_once 'config/database.php';

echo "<h2>Sync Images from V1 System</h2>";

// Define paths
$v1_base_path = '/Applications/MAMP/htdocs/expenses_system-V1/uploads/';
$current_base_path = 'uploads/';

// Check if V1 system exists
if (!is_dir($v1_base_path)) {
    echo "<div style='color: red;'>V1 system not found at: $v1_base_path</div>";
    echo "<p>Please check if the V1 system exists or update the path.</p>";
    exit;
}

$database = new Database();
$db = $database->getConnection();

// Get all expenses with missing images
$stmt = $db->prepare("
    SELECT id, exno, transfer_slip_image, verification_slip_image, reviewer_slip_image, created_at
    FROM expenses 
    WHERE (transfer_slip_image IS NOT NULL AND transfer_slip_image != '')
       OR (verification_slip_image IS NOT NULL AND verification_slip_image != '')
       OR (reviewer_slip_image IS NOT NULL AND reviewer_slip_image != '')
    ORDER BY id DESC
    LIMIT 50
");
$stmt->execute();
$expenses = $stmt->fetchAll();

echo "<h3>Checking Recent 50 Expenses with Images:</h3>";

$missing_files = [];
$copied_files = [];
$errors = [];

foreach ($expenses as $expense) {
    echo "<h4>Expense #{$expense['id']} - {$expense['exno']}</h4>";
    
    // Check each image type
    $image_types = [
        'transfer_slip' => ['field' => 'transfer_slip_image', 'dir' => 'transfer_slips'],
        'verification_slip' => ['field' => 'verification_slip_image', 'dir' => 'verification_slips'],
        'reviewer_slip' => ['field' => 'reviewer_slip_image', 'dir' => 'review_slips']
    ];
    
    foreach ($image_types as $type => $config) {
        $filename = $expense[$config['field']];
        if (!$filename) continue;
        
        $current_path = $current_base_path . $config['dir'] . '/' . $filename;
        $v1_path = $v1_base_path . $config['dir'] . '/' . $filename;
        
        echo "<p><strong>$type:</strong> $filename</p>";
        echo "<ul>";
        echo "<li>Current system: " . (file_exists($current_path) ? '✅ EXISTS' : '❌ MISSING') . "</li>";
        echo "<li>V1 system: " . (file_exists($v1_path) ? '✅ EXISTS' : '❌ MISSING') . "</li>";
        
        // If missing in current but exists in V1, copy it
        if (!file_exists($current_path) && file_exists($v1_path)) {
            // Ensure directory exists
            $target_dir = dirname($current_path);
            if (!is_dir($target_dir)) {
                mkdir($target_dir, 0755, true);
            }
            
            if (copy($v1_path, $current_path)) {
                echo "<li style='color: green;'>✅ COPIED from V1 system</li>";
                $copied_files[] = $filename;
            } else {
                echo "<li style='color: red;'>❌ FAILED to copy</li>";
                $errors[] = "Failed to copy $filename";
            }
        } elseif (!file_exists($current_path) && !file_exists($v1_path)) {
            echo "<li style='color: orange;'>⚠️ Missing in both systems</li>";
            $missing_files[] = $filename;
        }
        echo "</ul>";
    }
}

// Check receipt images
echo "<h3>Checking Receipt Images:</h3>";
$stmt = $db->prepare("
    SELECT rn.*, e.exno 
    FROM receipt_numbers rn
    JOIN expenses e ON rn.expense_id = e.id
    WHERE rn.receipt_image IS NOT NULL AND rn.receipt_image != ''
    ORDER BY rn.id DESC
    LIMIT 100
");
$stmt->execute();
$receipts = $stmt->fetchAll();

foreach ($receipts as $receipt) {
    $filename = $receipt['receipt_image'];
    $current_path = $current_base_path . 'receipts/' . $filename;
    $v1_path = $v1_base_path . 'receipts/' . $filename;
    
    if (!file_exists($current_path) && file_exists($v1_path)) {
        // Ensure directory exists
        $target_dir = dirname($current_path);
        if (!is_dir($target_dir)) {
            mkdir($target_dir, 0755, true);
        }
        
        if (copy($v1_path, $current_path)) {
            echo "<p>✅ Copied receipt: $filename (Expense: {$receipt['exno']})</p>";
            $copied_files[] = $filename;
        } else {
            echo "<p style='color: red;'>❌ Failed to copy receipt: $filename</p>";
            $errors[] = "Failed to copy receipt $filename";
        }
    }
}

// Summary
echo "<h3>Summary:</h3>";
echo "<ul>";
echo "<li><strong>Files copied:</strong> " . count($copied_files) . "</li>";
echo "<li><strong>Files still missing:</strong> " . count($missing_files) . "</li>";
echo "<li><strong>Errors:</strong> " . count($errors) . "</li>";
echo "</ul>";

if ($copied_files) {
    echo "<h4>Copied Files:</h4>";
    echo "<ul>";
    foreach ($copied_files as $file) {
        echo "<li>$file</li>";
    }
    echo "</ul>";
}

if ($missing_files) {
    echo "<h4>Still Missing Files:</h4>";
    echo "<ul>";
    foreach ($missing_files as $file) {
        echo "<li style='color: red;'>$file</li>";
    }
    echo "</ul>";
}

if ($errors) {
    echo "<h4>Errors:</h4>";
    echo "<ul>";
    foreach ($errors as $error) {
        echo "<li style='color: red;'>$error</li>";
    }
    echo "</ul>";
}

// Test specific expense 187
echo "<h3>Testing Expense 187:</h3>";
$stmt = $db->prepare("SELECT * FROM expenses WHERE id = 187");
$stmt->execute();
$expense_187 = $stmt->fetch();

if ($expense_187 && $expense_187['transfer_slip_image']) {
    $filename = $expense_187['transfer_slip_image'];
    $current_path = $current_base_path . 'transfer_slips/' . $filename;
    $v1_path = $v1_base_path . 'transfer_slips/' . $filename;
    
    echo "<p>Transfer slip: $filename</p>";
    echo "<ul>";
    echo "<li>Current system: " . (file_exists($current_path) ? '✅ EXISTS' : '❌ MISSING') . "</li>";
    echo "<li>V1 system: " . (file_exists($v1_path) ? '✅ EXISTS' : '❌ MISSING') . "</li>";
    
    if (file_exists($current_path)) {
        $test_url = "api/view_file.php?file=" . urlencode($filename) . "&type=transfer_slip";
        echo "<li><a href='$test_url' target='_blank'>Test View File</a></li>";
    }
    echo "</ul>";
}
?>
