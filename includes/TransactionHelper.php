<?php
/**
 * Transaction Helper for MariaDB 5.5.68 Compatibility
 * 
 * This helper provides safe transaction management that works with older MariaDB versions
 * where inTransaction() method might not be available or reliable.
 */

class TransactionHelper {
    private $db;
    private $transaction_started = false;
    
    public function __construct($db) {
        $this->db = $db;
    }
    
    /**
     * Start a database transaction safely
     * 
     * @return bool True if transaction started successfully
     * @throws Exception If transaction cannot be started
     */
    public function beginTransaction() {
        if ($this->transaction_started) {
            throw new Exception('Transaction already started');
        }
        
        try {
            $result = $this->db->beginTransaction();
            if ($result) {
                $this->transaction_started = true;
                return true;
            } else {
                throw new Exception('Failed to start transaction');
            }
        } catch (Exception $e) {
            error_log('TransactionHelper: Failed to start transaction - ' . $e->getMessage());
            throw new Exception('Failed to start database transaction: ' . $e->getMessage());
        }
    }
    
    /**
     * Commit the current transaction safely
     * 
     * @return bool True if transaction committed successfully
     * @throws Exception If transaction cannot be committed
     */
    public function commit() {
        if (!$this->transaction_started) {
            throw new Exception('No active transaction to commit');
        }
        
        try {
            $result = $this->db->commit();
            if ($result) {
                $this->transaction_started = false;
                return true;
            } else {
                throw new Exception('Failed to commit transaction');
            }
        } catch (Exception $e) {
            error_log('TransactionHelper: Failed to commit transaction - ' . $e->getMessage());
            $this->transaction_started = false; // Reset state even on failure
            throw new Exception('Failed to commit database transaction: ' . $e->getMessage());
        }
    }
    
    /**
     * Rollback the current transaction safely
     * 
     * @return bool True if transaction rolled back successfully
     */
    public function rollback() {
        if (!$this->transaction_started) {
            error_log('TransactionHelper: Attempted to rollback when no transaction is active');
            return false;
        }
        
        try {
            $result = $this->db->rollback();
            $this->transaction_started = false;
            return $result;
        } catch (Exception $e) {
            error_log('TransactionHelper: Failed to rollback transaction - ' . $e->getMessage());
            $this->transaction_started = false; // Reset state even on failure
            return false;
        }
    }
    
    /**
     * Check if transaction is currently active
     * 
     * @return bool True if transaction is active
     */
    public function inTransaction() {
        return $this->transaction_started;
    }
    
    /**
     * Execute a callback within a transaction
     * Automatically handles commit/rollback
     * 
     * @param callable $callback Function to execute within transaction
     * @return mixed Result of the callback function
     * @throws Exception If transaction fails or callback throws exception
     */
    public function executeInTransaction($callback) {
        $this->beginTransaction();
        
        try {
            $result = $callback();
            $this->commit();
            return $result;
        } catch (Exception $e) {
            $this->rollback();
            throw $e;
        }
    }
    
    /**
     * Force reset transaction state
     * Use only in emergency situations
     */
    public function forceReset() {
        $this->transaction_started = false;
        error_log('TransactionHelper: Force reset transaction state');
    }
    
    /**
     * Get current transaction status for debugging
     * 
     * @return array Status information
     */
    public function getStatus() {
        return [
            'transaction_started' => $this->transaction_started,
            'pdo_in_transaction' => method_exists($this->db, 'inTransaction') ? $this->db->inTransaction() : 'unknown'
        ];
    }
}

/**
 * Global helper function to create TransactionHelper instance
 * 
 * @param PDO $db Database connection
 * @return TransactionHelper
 */
function createTransactionHelper($db) {
    return new TransactionHelper($db);
}

/**
 * Legacy compatibility function for existing code
 * Safely checks if database is in transaction
 * 
 * @param PDO $db Database connection
 * @return bool True if in transaction, false otherwise
 */
function safeInTransaction($db) {
    try {
        if (method_exists($db, 'inTransaction')) {
            return $db->inTransaction();
        }
        return false;
    } catch (Exception $e) {
        error_log('safeInTransaction: Error checking transaction state - ' . $e->getMessage());
        return false;
    }
}

/**
 * Legacy compatibility function for safe rollback
 * 
 * @param PDO $db Database connection
 * @return bool True if rollback successful or no transaction active
 */
function safeRollback($db) {
    try {
        if (safeInTransaction($db)) {
            return $db->rollback();
        }
        return true; // No transaction to rollback
    } catch (Exception $e) {
        error_log('safeRollback: Error during rollback - ' . $e->getMessage());
        return false;
    }
}
?>
