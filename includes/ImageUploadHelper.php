<?php
require_once 'ImageCompressor.php';

/**
 * Image Upload Helper Class
 * Provides convenient methods for handling image uploads with compression
 */
class ImageUploadHelper {
    
    // Upload directories
    const UPLOAD_DIRS = [
        'receipts' => '../uploads/receipts',
        'transfer_slips' => '../uploads/transfer_slips',
        'verification_slips' => '../uploads/verification_slips',
        'review_slips' => '../uploads/review_slips',
        'batch_verification' => '../uploads/batch_documents/verification',
        'batch_review' => '../uploads/batch_documents/review',
        'bulk_operations' => '../uploads/bulk_operations'
    ];
    
    /**
     * Upload and compress receipt image
     */
    public static function uploadReceiptImage($file, $receiptNo = '') {
        $prefix = $receiptNo ? 'receipt_' . $receiptNo . '_' : 'receipt_';
        return self::uploadImage($file, 'receipts', $prefix);
    }
    
    /**
     * Upload and compress transfer slip image
     */
    public static function uploadTransferSlip($file) {
        return self::uploadImage($file, 'transfer_slips', 'transfer_');
    }
    
    /**
     * Upload and compress verification slip image
     */
    public static function uploadVerificationSlip($file) {
        return self::uploadImage($file, 'verification_slips', 'verification_');
    }
    
    /**
     * Upload and compress review slip image
     */
    public static function uploadReviewSlip($file) {
        return self::uploadImage($file, 'review_slips', 'review_');
    }

    /**
     * Upload batch verification slip image
     */
    public static function uploadBatchVerificationSlip($file, $batchId = '') {
        $prefix = $batchId ? $batchId . '_verification_' : 'batch_verification_';
        return self::uploadImage($file, 'batch_verification', $prefix);
    }

    /**
     * Upload batch review slip image
     */
    public static function uploadBatchReviewSlip($file, $batchId = '') {
        $prefix = $batchId ? $batchId . '_review_' : 'batch_review_';
        return self::uploadImage($file, 'batch_review', $prefix);
    }

    /**
     * Upload bulk operation slip image
     */
    public static function uploadBulkOperationSlip($file, $batchId = '') {
        $prefix = $batchId ? $batchId . '_' : 'bulk_op_';
        return self::uploadImage($file, 'bulk_operations', $prefix);
    }
    
    /**
     * Generic image upload method
     */
    private static function uploadImage($file, $type, $prefix = '') {
        if (!array_key_exists($type, self::UPLOAD_DIRS)) {
            return ['success' => false, 'error' => 'Invalid upload type'];
        }
        
        $uploadDir = self::UPLOAD_DIRS[$type];
        
        // Ensure upload directory exists
        if (!file_exists($uploadDir)) {
            if (!mkdir($uploadDir, 0755, true)) {
                return ['success' => false, 'error' => 'Failed to create upload directory'];
            }
        }
        
        // Compress and save image
        $result = ImageCompressor::compressAndSave($file, $uploadDir, $prefix);
        
        if ($result['success']) {
            // Log compression results
            error_log("Image compressed: {$result['filename']} - " .
                     "Original: " . ImageCompressor::formatFileSize($result['original_size']) . 
                     ", Compressed: " . ImageCompressor::formatFileSize($result['compressed_size']) . 
                     " (Saved {$result['compression_ratio']}%)");
        }
        
        return $result;
    }
    
    /**
     * Upload multiple receipt images
     */
    public static function uploadMultipleReceiptImages($files, $receiptNumbers = []) {
        $results = [];
        $successCount = 0;
        $errors = [];
        
        foreach ($files['name'] as $index => $filename) {
            if (empty($filename)) continue;
            
            $file = [
                'name' => $files['name'][$index],
                'type' => $files['type'][$index],
                'tmp_name' => $files['tmp_name'][$index],
                'error' => $files['error'][$index],
                'size' => $files['size'][$index]
            ];
            
            $receiptNo = isset($receiptNumbers[$index]) ? $receiptNumbers[$index] : '';
            $result = self::uploadReceiptImage($file, $receiptNo);
            
            if ($result['success']) {
                $results[] = [
                    'filename' => $result['filename'],
                    'receipt_no' => $receiptNo,
                    'original_size' => $result['original_size'],
                    'compressed_size' => $result['compressed_size'],
                    'compression_ratio' => $result['compression_ratio']
                ];
                $successCount++;
            } else {
                $errors[] = "File {$filename}: " . $result['error'];
            }
        }
        
        return [
            'success' => $successCount > 0,
            'uploaded_count' => $successCount,
            'total_count' => count($files['name']),
            'results' => $results,
            'errors' => $errors
        ];
    }
    
    /**
     * Delete image file
     */
    public static function deleteImage($filename, $type) {
        if (!array_key_exists($type, self::UPLOAD_DIRS)) {
            return false;
        }
        
        $filepath = self::UPLOAD_DIRS[$type] . '/' . $filename;
        
        if (file_exists($filepath)) {
            return unlink($filepath);
        }
        
        return true; // File doesn't exist, consider it deleted
    }
    
    /**
     * Get image URL for display
     */
    public static function getImageUrl($filename, $type) {
        if (!array_key_exists($type, self::UPLOAD_DIRS)) {
            return null;
        }
        
        $uploadDir = str_replace('../', '', self::UPLOAD_DIRS[$type]);
        return $uploadDir . '/' . $filename;
    }
    
    /**
     * Validate image before upload
     */
    public static function validateImage($file) {
        // Check if file was uploaded
        if (!isset($file['tmp_name']) || empty($file['tmp_name'])) {
            return ['valid' => false, 'error' => 'No file uploaded'];
        }
        
        // Check for upload errors
        if ($file['error'] !== UPLOAD_ERR_OK) {
            return ['valid' => false, 'error' => 'File upload error: ' . $file['error']];
        }
        
        // Check file size (5MB limit)
        $maxSize = 5 * 1024 * 1024; // 5MB
        if ($file['size'] > $maxSize) {
            return ['valid' => false, 'error' => 'File size exceeds 5MB limit'];
        }
        
        // Check file type
        $allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/gif', 'image/webp'];
        $finfo = finfo_open(FILEINFO_MIME_TYPE);
        $mimeType = finfo_file($finfo, $file['tmp_name']);
        finfo_close($finfo);
        
        if (!in_array($mimeType, $allowedTypes)) {
            return ['valid' => false, 'error' => 'Invalid file type. Only JPG, PNG, GIF, and WebP images are allowed'];
        }
        
        // Check if it's actually an image
        $imageInfo = getimagesize($file['tmp_name']);
        if (!$imageInfo) {
            return ['valid' => false, 'error' => 'File is not a valid image'];
        }
        
        return [
            'valid' => true,
            'mime_type' => $mimeType,
            'width' => $imageInfo[0],
            'height' => $imageInfo[1],
            'size' => $file['size']
        ];
    }
    
    /**
     * Create thumbnail for existing image
     */
    public static function createThumbnail($filename, $type, $width = 150, $height = 150) {
        if (!array_key_exists($type, self::UPLOAD_DIRS)) {
            return false;
        }
        
        $uploadDir = self::UPLOAD_DIRS[$type];
        $sourcePath = $uploadDir . '/' . $filename;
        
        // Create thumbnails directory
        $thumbDir = $uploadDir . '/thumbnails';
        if (!file_exists($thumbDir)) {
            mkdir($thumbDir, 0755, true);
        }
        
        $thumbPath = $thumbDir . '/' . $filename;
        
        return ImageCompressor::createThumbnail($sourcePath, $thumbPath, $width, $height);
    }
    
    /**
     * Get compression statistics for uploaded images
     */
    public static function getCompressionStats($type = null) {
        $dirs = $type ? [$type => self::UPLOAD_DIRS[$type]] : self::UPLOAD_DIRS;
        $stats = [
            'total_files' => 0,
            'total_size' => 0,
            'by_type' => []
        ];
        
        foreach ($dirs as $dirType => $dirPath) {
            if (!file_exists($dirPath)) continue;
            
            $files = glob($dirPath . '/*');
            $typeStats = [
                'count' => 0,
                'size' => 0,
                'files' => []
            ];
            
            foreach ($files as $file) {
                if (is_file($file)) {
                    $size = filesize($file);
                    $typeStats['count']++;
                    $typeStats['size'] += $size;
                    $stats['total_files']++;
                    $stats['total_size'] += $size;
                    
                    $typeStats['files'][] = [
                        'name' => basename($file),
                        'size' => $size,
                        'size_formatted' => ImageCompressor::formatFileSize($size),
                        'modified' => filemtime($file)
                    ];
                }
            }
            
            $stats['by_type'][$dirType] = $typeStats;
        }
        
        $stats['total_size_formatted'] = ImageCompressor::formatFileSize($stats['total_size']);
        
        return $stats;
    }
    
    /**
     * Clean up old images (older than specified days)
     */
    public static function cleanupOldImages($days = 30, $type = null) {
        $dirs = $type ? [$type => self::UPLOAD_DIRS[$type]] : self::UPLOAD_DIRS;
        $cutoffTime = time() - ($days * 24 * 60 * 60);
        $deletedCount = 0;
        $deletedSize = 0;
        
        foreach ($dirs as $dirType => $dirPath) {
            if (!file_exists($dirPath)) continue;
            
            $files = glob($dirPath . '/*');
            
            foreach ($files as $file) {
                if (is_file($file) && filemtime($file) < $cutoffTime) {
                    $size = filesize($file);
                    if (unlink($file)) {
                        $deletedCount++;
                        $deletedSize += $size;
                    }
                }
            }
        }
        
        return [
            'deleted_count' => $deletedCount,
            'deleted_size' => $deletedSize,
            'deleted_size_formatted' => ImageCompressor::formatFileSize($deletedSize)
        ];
    }
}
?>
