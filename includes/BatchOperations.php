<?php
/**
 * BatchOperations Class
 * จัดการ Multi-select Batch Processing สำหรับ Verification และ Review
 */

class BatchOperations {
    private $db;
    private $performance_logs = [];

    public function __construct($database_connection) {
        $this->db = $database_connection;
    }

    /**
     * เริ่มต้น Performance Logging
     */
    private function startPerformanceLog($batch_id, $operation_step) {
        $this->performance_logs[$operation_step] = [
            'batch_id' => $batch_id,
            'operation_step' => $operation_step,
            'start_time' => microtime(true),
            'start_memory' => memory_get_usage(true),
            'items_processed' => 0,
            'success_count' => 0,
            'error_count' => 0
        ];
    }

    /**
     * Log individual expense activity during batch processing
     */
    private function logIndividualExpenseActivity($user_id, $action_type, $table_name, $record_id, $old_values, $new_values, $description) {
        try {
            // Debug: Log that function is called
            error_log("DEBUG: logIndividualExpenseActivity called - User: {$user_id}, Action: {$action_type}, Record: {$record_id}");

            // Direct database insert instead of relying on external function
            $stmt = $this->db->prepare("
                INSERT INTO activity_logs (user_id, action_type, table_name, record_id, old_values, new_values, description, ip_address, user_agent)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
            ");

            $old_json = $old_values ? json_encode($old_values) : null;
            $new_json = $new_values ? json_encode($new_values) : null;

            $result = $stmt->execute([
                $user_id,
                $action_type,
                $table_name,
                $record_id,
                $old_json,
                $new_json,
                $description,
                $_SERVER['REMOTE_ADDR'] ?? 'unknown',
                $_SERVER['HTTP_USER_AGENT'] ?? 'unknown'
            ]);

            // Debug: Log success
            if ($result) {
                error_log("DEBUG: Individual expense activity logged successfully - User {$user_id}, Action {$action_type}, Record {$record_id}");
            } else {
                error_log("DEBUG: Individual expense activity logging failed - no result");
            }

        } catch (Exception $e) {
            // Log error but don't fail the main operation
            error_log('DEBUG: Individual expense activity logging error: ' . $e->getMessage());
            error_log('DEBUG: Stack trace: ' . $e->getTraceAsString());
        }
    }

    /**
     * สิ้นสุด Performance Logging
     */
    private function endPerformanceLog($operation_step, $items_processed = 0, $success_count = 0, $error_count = 0) {
        if (!isset($this->performance_logs[$operation_step])) {
            return;
        }

        $log = &$this->performance_logs[$operation_step];
        $end_time = microtime(true);
        $end_memory = memory_get_usage(true);

        $log['end_time'] = $end_time;
        $log['duration_ms'] = round(($end_time - $log['start_time']) * 1000);
        $log['memory_usage_mb'] = round(($end_memory - $log['start_memory']) / 1024 / 1024, 2);
        $log['items_processed'] = $items_processed;
        $log['success_count'] = $success_count;
        $log['error_count'] = $error_count;

        // บันทึกลง database
        $this->savePerformanceLog($log);
    }

    /**
     * บันทึก Performance Log ลง database
     */
    private function savePerformanceLog($log) {
        try {
            $stmt = $this->db->prepare("
                INSERT INTO batch_performance_logs (
                    batch_id, operation_step, start_time, end_time,
                    duration_ms, memory_usage_mb, items_processed,
                    success_count, error_count
                ) VALUES (?, ?, FROM_UNIXTIME(?), FROM_UNIXTIME(?), ?, ?, ?, ?, ?)
            ");

            $stmt->execute([
                $log['batch_id'],
                $log['operation_step'],
                $log['start_time'],
                $log['end_time'],
                $log['duration_ms'],
                $log['memory_usage_mb'],
                $log['items_processed'],
                $log['success_count'],
                $log['error_count']
            ]);
        } catch (Exception $e) {
            error_log('Performance logging error: ' . $e->getMessage());
        }
    }
    
    /**
     * สร้าง Batch Operation ใหม่
     */
    public function createBatch($operation_type, $user_id, $expense_items, $total_amount, $notes = null) {
        try {
            // สร้าง Batch ID
            $batch_id = $this->generateBatchId($operation_type);
            $item_count = count($expense_items);

            // เริ่ม Performance Logging
            $this->startPerformanceLog($batch_id, 'batch_creation');

            // เริ่ม Transaction
            $this->db->beginTransaction();

            // สร้าง Batch Operation
            $stmt = $this->db->prepare("
                INSERT INTO batch_operations (batch_id, operation_type, user_id, total_amount, item_count, notes)
                VALUES (?, ?, ?, ?, ?, ?)
            ");
            $stmt->execute([$batch_id, $operation_type, $user_id, $total_amount, $item_count, $notes]);

            // Log batch creation
            $stmt = $this->db->prepare("CALL LogBatchOperation(?, ?, ?, ?, ?, ?, ?)");
            $stmt->execute([
                $batch_id,
                'CREATE',
                null,
                $user_id,
                json_encode(['operation_type' => $operation_type, 'item_count' => $item_count, 'total_amount' => $total_amount]),
                null,
                null
            ]);

            // เพิ่ม Batch Items
            $stmt = $this->db->prepare("
                INSERT INTO batch_items (batch_id, expense_id, individual_amount)
                VALUES (?, ?, ?)
            ");

            $success_count = 0;
            foreach ($expense_items as $item) {
                $stmt->execute([$batch_id, $item['expense_id'], $item['amount']]);
                $success_count++;
            }

            $this->db->commit();

            // สิ้นสุด Performance Logging
            $this->endPerformanceLog('batch_creation', $item_count, $success_count, 0);

            return [
                'success' => true,
                'batch_id' => $batch_id,
                'message' => 'Batch created successfully'
            ];
            
        } catch (Exception $e) {
            $this->db->rollback();
            return [
                'success' => false,
                'error' => $e->getMessage()
            ];
        }
    }
    
    /**
     * ประมวลผล Batch Verification
     */
    public function processBatchVerification($batch_id, $verification_slip_path, $user_id, $transfer_number = null, $verification_amounts = []) {
        try {
            // เริ่ม Performance Logging
            $this->startPerformanceLog($batch_id, 'batch_verification');

            $this->db->beginTransaction();

            // อัปเดตสถานะ Batch เป็น processing
            $this->updateBatchStatus($batch_id, 'processing');

            // ดึงรายการ items ใน batch
            $this->startPerformanceLog($batch_id, 'data_retrieval');
            $stmt = $this->db->prepare("
                SELECT bi.expense_id, bi.individual_amount
                FROM batch_items bi
                WHERE bi.batch_id = ? AND bi.status = 'pending'
            ");
            $stmt->execute([$batch_id]);
            $items = $stmt->fetchAll();
            $this->endPerformanceLog('data_retrieval', count($items), count($items), 0);

            $success_count = 0;
            $error_count = 0;

            // เริ่ม Performance Logging สำหรับ database updates
            $this->startPerformanceLog($batch_id, 'database_update');
            
            foreach ($items as $item) {
                try {
                    // ดึงข้อมูล expense เดิมก่อนอัปเดต สำหรับ logging
                    $stmt_old = $this->db->prepare("SELECT status, verification_amount, verification_by FROM expenses WHERE id = ?");
                    $stmt_old->execute([$item['expense_id']]);
                    $old_data = $stmt_old->fetch();

                    // อัปเดต expense ด้วยข้อมูล verification และเปลี่ยนสถานะเป็น pending
                    $stmt = $this->db->prepare("
                        UPDATE expenses SET
                            status = 'pending',
                            verification_slip_image = ?,
                            verification_amount = ?,
                            verification_date = NOW(),
                            verification_by = ?,
                            verification_transfer_no = ?,
                            batch_verification_id = ?,
                            is_batch_processed = TRUE,
                            batch_notes = 'Processed via batch verification'
                        WHERE id = ?
                    ");

                    // ใช้ verification amount ที่ user กรอก หรือ individual_amount เดิม
                    $verification_amount = isset($verification_amounts[$item['expense_id']])
                        ? floatval($verification_amounts[$item['expense_id']])
                        : $item['individual_amount'];

                    $stmt->execute([
                        $verification_slip_path,
                        $verification_amount,
                        $user_id,
                        $transfer_number,
                        $batch_id,
                        $item['expense_id']
                    ]);

                    // Log individual expense activity
                    $this->logIndividualExpenseActivity(
                        $user_id,
                        'batch_verification',
                        'expenses',
                        $item['expense_id'],
                        [
                            'status' => $old_data['status'],
                            'verification_amount' => $old_data['verification_amount'],
                            'verification_by' => $old_data['verification_by']
                        ],
                        [
                            'status' => 'pending',
                            'verification_amount' => $verification_amount,
                            'verification_by' => $user_id,
                            'batch_id' => $batch_id
                        ],
                        "Batch verification processed: Amount " . number_format($verification_amount, 2) . " บาท, Batch ID: {$batch_id}"
                    );

                    // อัปเดตสถานะ batch item
                    $this->updateBatchItemStatus($batch_id, $item['expense_id'], 'completed');
                    $success_count++;

                } catch (Exception $e) {
                    // บันทึก error สำหรับ item นี้
                    $this->updateBatchItemStatus($batch_id, $item['expense_id'], 'failed', $e->getMessage());

                    // Log failed expense activity
                    $this->logIndividualExpenseActivity(
                        $user_id,
                        'batch_verification',
                        'expenses',
                        $item['expense_id'],
                        null,
                        null,
                        "Batch verification failed: {$e->getMessage()}, Batch ID: {$batch_id}"
                    );

                    $error_count++;
                }
            }

            // สิ้นสุด Performance Logging สำหรับ database updates
            $this->endPerformanceLog('database_update', count($items), $success_count, $error_count);

            // อัปเดตสถานะ Batch และ transfer_number
            $final_status = ($error_count === 0) ? 'completed' : 'failed';
            $this->updateBatchStatus($batch_id, $final_status, true, $transfer_number);

            // Log batch completion
            $stmt = $this->db->prepare("CALL LogBatchOperation(?, ?, ?, ?, ?, ?, ?)");
            $stmt->execute([
                $batch_id,
                $final_status === 'completed' ? 'COMPLETE' : 'ERROR',
                null,
                $user_id,
                json_encode(['final_status' => $final_status, 'success_count' => $success_count, 'error_count' => $error_count]),
                null,
                null
            ]);

            $this->db->commit();

            // สิ้นสุด Performance Logging สำหรับ batch verification
            $this->endPerformanceLog('batch_verification', count($items), $success_count, $error_count);

            return [
                'success' => true,
                'batch_id' => $batch_id,
                'processed' => $success_count + $error_count,
                'success_count' => $success_count,
                'error_count' => $error_count,
                'status' => $final_status
            ];

        } catch (Exception $e) {
            $this->db->rollback();
            $this->updateBatchStatus($batch_id, 'failed');

            // Log performance error
            if (isset($this->performance_logs['batch_verification'])) {
                $this->endPerformanceLog('batch_verification', 0, 0, 1);
            }

            return [
                'success' => false,
                'error' => $e->getMessage()
            ];
        }
    }
    
    /**
     * ประมวลผล Batch Review
     */
    public function processBatchReview($batch_id, $review_slip_path, $user_id, $transfer_number = null, $review_amounts = []) {
        try {
            // เริ่ม Performance Logging
            $this->startPerformanceLog($batch_id, 'batch_review');

            $this->db->beginTransaction();

            // อัปเดตสถานะ Batch เป็น processing
            $this->updateBatchStatus($batch_id, 'processing');

            // ดึงรายการ items ใน batch
            $this->startPerformanceLog($batch_id, 'data_retrieval_review');
            $stmt = $this->db->prepare("
                SELECT bi.expense_id, bi.individual_amount,
                       e.verification_amount, e.status
                FROM batch_items bi
                JOIN expenses e ON bi.expense_id = e.id
                WHERE bi.batch_id = ? AND bi.status = 'pending'
            ");
            $stmt->execute([$batch_id]);
            $items = $stmt->fetchAll();
            $this->endPerformanceLog('data_retrieval_review', count($items), count($items), 0);

            $success_count = 0;
            $error_count = 0;

            // เริ่ม Performance Logging สำหรับ database updates
            $this->startPerformanceLog($batch_id, 'database_update_review');
            
            foreach ($items as $item) {
                try {
                    // ตรวจสอบสถานะและจำนวนเงิน
                    if ($item['status'] !== 'pending') {
                        throw new Exception("Expense must be in pending status");
                    }

                    if (abs($item['verification_amount'] - $item['individual_amount']) > 0.01) {
                        throw new Exception("Review amount must match verification amount");
                    }

                    // ดึงข้อมูล expense เดิมก่อนอัปเดต สำหรับ logging
                    $stmt_old = $this->db->prepare("SELECT status, reviewer_amount, reviewer_by FROM expenses WHERE id = ?");
                    $stmt_old->execute([$item['expense_id']]);
                    $old_data = $stmt_old->fetch();

                    // อัปเดต expense ด้วยข้อมูล review และเปลี่ยนสถานะเป็น success
                    $stmt = $this->db->prepare("
                        UPDATE expenses SET
                            status = 'success',
                            reviewer_slip_image = ?,
                            reviewer_amount = ?,
                            reviewer_date = NOW(),
                            reviewer_by = ?,
                            reviewer_transfer_no = ?,
                            batch_review_id = ?,
                            batch_notes = 'Processed via batch review'
                        WHERE id = ?
                    ");

                    // ใช้ review amount ที่ user กรอก หรือ individual_amount เดิม
                    $review_amount = isset($review_amounts[$item['expense_id']])
                        ? floatval($review_amounts[$item['expense_id']])
                        : $item['individual_amount'];

                    $stmt->execute([
                        $review_slip_path,
                        $review_amount,
                        $user_id,
                        $transfer_number,
                        $batch_id,
                        $item['expense_id']
                    ]);

                    // Log individual expense activity
                    $this->logIndividualExpenseActivity(
                        $user_id,
                        'batch_review',
                        'expenses',
                        $item['expense_id'],
                        [
                            'status' => $old_data['status'],
                            'reviewer_amount' => $old_data['reviewer_amount'],
                            'reviewer_by' => $old_data['reviewer_by']
                        ],
                        [
                            'status' => 'success',
                            'reviewer_amount' => $review_amount,
                            'reviewer_by' => $user_id,
                            'batch_id' => $batch_id
                        ],
                        "Batch review processed: Amount " . number_format($review_amount, 2) . " บาท, Batch ID: {$batch_id}"
                    );

                    // อัปเดตสถานะ batch item
                    $this->updateBatchItemStatus($batch_id, $item['expense_id'], 'completed');
                    $success_count++;

                } catch (Exception $e) {
                    // บันทึก error สำหรับ item นี้
                    $this->updateBatchItemStatus($batch_id, $item['expense_id'], 'failed', $e->getMessage());

                    // Log failed expense activity
                    $this->logIndividualExpenseActivity(
                        $user_id,
                        'batch_review',
                        'expenses',
                        $item['expense_id'],
                        null,
                        null,
                        "Batch review failed: {$e->getMessage()}, Batch ID: {$batch_id}"
                    );

                    $error_count++;
                }
            }

            // สิ้นสุด Performance Logging สำหรับ database updates
            $this->endPerformanceLog('database_update_review', count($items), $success_count, $error_count);

            // อัปเดตสถานะ Batch และ transfer_number
            $final_status = ($error_count === 0) ? 'completed' : 'failed';
            $this->updateBatchStatus($batch_id, $final_status, true, $transfer_number);

            $this->db->commit();

            // สิ้นสุด Performance Logging สำหรับ batch review
            $this->endPerformanceLog('batch_review', count($items), $success_count, $error_count);

            return [
                'success' => true,
                'batch_id' => $batch_id,
                'processed' => $success_count + $error_count,
                'success_count' => $success_count,
                'error_count' => $error_count,
                'status' => $final_status
            ];

        } catch (Exception $e) {
            $this->db->rollback();
            $this->updateBatchStatus($batch_id, 'failed');

            // Log performance error
            if (isset($this->performance_logs['batch_review'])) {
                $this->endPerformanceLog('batch_review', 0, 0, 1);
            }
            
            return [
                'success' => false,
                'error' => $e->getMessage()
            ];
        }
    }
    
    /**
     * บันทึกเอกสาร Batch
     */
    public function saveBatchDocument($batch_id, $document_type, $file_info) {
        try {
            $stmt = $this->db->prepare("
                INSERT INTO batch_documents (batch_id, document_type, file_path, original_filename, file_size, mime_type)
                VALUES (?, ?, ?, ?, ?, ?)
            ");
            
            return $stmt->execute([
                $batch_id,
                $document_type,
                $file_info['file_path'],
                $file_info['original_filename'],
                $file_info['file_size'],
                $file_info['mime_type']
            ]);
            
        } catch (Exception $e) {
            return false;
        }
    }
    
    /**
     * ดึงข้อมูล Batch
     */
    public function getBatchInfo($batch_id) {
        $stmt = $this->db->prepare("
            SELECT bo.*, u.username, u.full_name,
                   COUNT(bi.id) as total_items,
                   SUM(CASE WHEN bi.status = 'completed' THEN 1 ELSE 0 END) as completed_items,
                   SUM(CASE WHEN bi.status = 'failed' THEN 1 ELSE 0 END) as failed_items
            FROM batch_operations bo
            LEFT JOIN users u ON bo.user_id = u.id
            LEFT JOIN batch_items bi ON bo.batch_id = bi.batch_id
            WHERE bo.batch_id = ?
            GROUP BY bo.id
        ");
        $stmt->execute([$batch_id]);
        return $stmt->fetch();
    }
    
    /**
     * ดึงรายการ items ใน batch
     */
    public function getBatchItems($batch_id) {
        $stmt = $this->db->prepare("
            SELECT bi.*, e.exno, e.job_open_date as expense_date, e.transfer_amount,
                   e.receipt_images, e.additional_details, e.bookingno, e.containerno,
                   e.transfer_slip_image, e.verification_slip_image, e.reviewer_slip_image,
                   e.vehicle_plate,
                   d.name as driver_name,
                   c.name as customer_name,
                   i.name as item_name, i.description as item_description
            FROM batch_items bi
            JOIN expenses e ON bi.expense_id = e.id
            LEFT JOIN items i ON e.item_id = i.id
            LEFT JOIN drivers d ON e.driver_id = d.id
            LEFT JOIN customers c ON e.customer_id = c.id
            WHERE bi.batch_id = ?
            ORDER BY bi.id
        ");
        $stmt->execute([$batch_id]);
        return $stmt->fetchAll();
    }
    
    // Helper Methods
    private function generateBatchId($operation_type) {
        $prefix = strtoupper(substr($operation_type, 0, 3));
        return 'BATCH_' . $prefix . '_' . date('Ymd_His');
    }
    
    private function updateBatchStatus($batch_id, $status, $set_completed = false, $transfer_number = null) {
        $sql = "UPDATE batch_operations SET status = ?";
        $params = [$status];

        if ($status === 'processing') {
            $sql .= ", started_at = NOW()";
        }

        if ($set_completed) {
            $sql .= ", completed_at = NOW()";
        }

        if ($transfer_number !== null) {
            $sql .= ", transfer_number = ?";
            $params[] = $transfer_number;
        }

        $sql .= " WHERE batch_id = ?";
        $params[] = $batch_id;

        $stmt = $this->db->prepare($sql);
        return $stmt->execute($params);
    }
    
    private function updateBatchItemStatus($batch_id, $expense_id, $status, $error_message = null) {
        $stmt = $this->db->prepare("
            UPDATE batch_items 
            SET status = ?, error_message = ?, processed_at = NOW()
            WHERE batch_id = ? AND expense_id = ?
        ");
        return $stmt->execute([$status, $error_message, $batch_id, $expense_id]);
    }

    /**
     * ประมวลผล Batch Review แบบหลายไฟล์
     */
    public function processBatchReviewMultiFile($batch_id, $uploaded_files, $user_id) {
        try {
            // ตรวจสอบสถานะ batch ก่อนประมวลผล
            $batch_info = $this->getBatchInfo($batch_id);
            if (!$batch_info) {
                throw new Exception('Batch not found');
            }

            if ($batch_info['status'] !== 'pending') {
                throw new Exception('Batch is not in pending status. Current status: ' . $batch_info['status']);
            }

            // เริ่ม Performance Logging
            $this->startPerformanceLog($batch_id, 'batch_review_multi');

            $this->db->beginTransaction();

            // อัปเดตสถานะ Batch เป็น processing
            $this->updateBatchStatus($batch_id, 'processing');

            // ดึงรายการ items ใน batch
            $this->startPerformanceLog($batch_id, 'data_retrieval_review');
            $stmt = $this->db->prepare("
                SELECT bi.expense_id, bi.individual_amount,
                       e.verification_amount, e.status
                FROM batch_items bi
                JOIN expenses e ON bi.expense_id = e.id
                WHERE bi.batch_id = ? AND bi.status = 'pending'
            ");
            $stmt->execute([$batch_id]);
            $items = $stmt->fetchAll();
            $this->endPerformanceLog('data_retrieval_review', count($items), count($items), 0);

            $success_count = 0;
            $error_count = 0;
            $errors = [];

            // เริ่ม Performance Logging สำหรับ database updates
            $this->startPerformanceLog($batch_id, 'database_update_review');

            foreach ($items as $item) {
                try {
                    $expense_id = $item['expense_id'];

                    // ดึงข้อมูล expense เดิมก่อนอัปเดต สำหรับ logging
                    $stmt_old = $this->db->prepare("SELECT status, reviewer_amount, reviewer_by FROM expenses WHERE id = ?");
                    $stmt_old->execute([$expense_id]);
                    $old_data = $stmt_old->fetch();

                    // อัปเดต expense ด้วยข้อมูล review และเปลี่ยนสถานะเป็น success
                    $stmt = $this->db->prepare("
                        UPDATE expenses SET
                            status = 'success',
                            reviewer_amount = ?,
                            reviewer_date = NOW(),
                            reviewer_by = ?,
                            batch_review_id = ?,
                            batch_notes = 'Processed via batch review (multi-file)'
                        WHERE id = ?
                    ");

                    // ใช้ verification_amount หรือ individual_amount
                    $review_amount = $item['verification_amount'] ?: $item['individual_amount'];

                    $stmt->execute([
                        $review_amount,
                        $user_id,
                        $batch_id,
                        $expense_id
                    ]);

                    // Log individual expense activity
                    $this->logIndividualExpenseActivity(
                        $user_id,
                        'batch_review_multi',
                        'expenses',
                        $expense_id,
                        [
                            'status' => $old_data['status'],
                            'reviewer_amount' => $old_data['reviewer_amount'],
                            'reviewer_by' => $old_data['reviewer_by']
                        ],
                        [
                            'status' => 'success',
                            'reviewer_amount' => $review_amount,
                            'reviewer_by' => $user_id,
                            'batch_id' => $batch_id
                        ],
                        "Batch review processed (multi-file): Amount " . number_format($review_amount, 2) . " บาท, Batch ID: {$batch_id}"
                    );

                    // อัปเดตสถานะ batch item
                    $this->updateBatchItemStatus($batch_id, $expense_id, 'completed');
                    $success_count++;

                } catch (Exception $e) {
                    $error_count++;
                    $errors[] = "Expense ID {$item['expense_id']}: " . $e->getMessage();
                    error_log("Error processing expense {$item['expense_id']} in batch {$batch_id}: " . $e->getMessage());

                    // อัปเดตสถานะ batch item เป็น failed
                    $this->updateBatchItemStatus($batch_id, $item['expense_id'], 'failed');
                }
            }

            // สิ้นสุด Performance Logging สำหรับ database updates
            $this->endPerformanceLog('database_update_review', count($items), $success_count, $error_count);

            // บันทึกข้อมูลไฟล์ที่อัปโหลด
            foreach ($uploaded_files as $file_data) {
                $stmt = $this->db->prepare("
                    INSERT INTO batch_file_uploads (
                        batch_id, file_path, transfer_number, amount,
                        original_filename, file_size, mime_type, uploaded_at
                    ) VALUES (?, ?, ?, ?, ?, ?, ?, NOW())
                ");
                $stmt->execute([
                    $batch_id,
                    $file_data['file_path'],
                    $file_data['transfer_number'],
                    $file_data['amount'],
                    $file_data['original_filename'],
                    $file_data['file_size'] ?? 0,
                    $file_data['mime_type'] ?? 'application/octet-stream'
                ]);
            }

            // อัปเดตสถานะ Batch
            $final_status = ($error_count === 0) ? 'completed' : 'failed';
            $this->updateBatchStatus($batch_id, $final_status, true);

            $this->db->commit();

            // สิ้นสุด Performance Logging
            $this->endPerformanceLog('batch_review_multi', count($items), $success_count, $error_count);

            return [
                'success' => true,
                'batch_id' => $batch_id,
                'processed' => $success_count + $error_count,
                'success_count' => $success_count,
                'error_count' => $error_count,
                'errors' => $errors,
                'files_uploaded' => count($uploaded_files),
                'status' => $final_status
            ];

        } catch (Exception $e) {
            $this->db->rollback();

            // อัปเดตสถานะ Batch เป็น failed
            $this->updateBatchStatus($batch_id, 'failed');

            return [
                'success' => false,
                'error' => $e->getMessage()
            ];
        }
    }

    /**
     * ประมวลผล Batch Verification แบบหลายไฟล์
     */
    public function processBatchVerificationMultiFile($batch_id, $uploaded_files, $user_id, $verification_amounts = []) {
        try {
            // เริ่ม Performance Logging
            $this->startPerformanceLog($batch_id, 'batch_verification_multi');

            $this->db->beginTransaction();

            // อัปเดตสถานะ Batch เป็น processing
            $this->updateBatchStatus($batch_id, 'processing');

            // ดึงรายการ items ใน batch
            $this->startPerformanceLog($batch_id, 'data_retrieval');
            $stmt = $this->db->prepare("
                SELECT bi.expense_id, bi.individual_amount
                FROM batch_items bi
                WHERE bi.batch_id = ? AND bi.status = 'pending'
            ");
            $stmt->execute([$batch_id]);
            $items = $stmt->fetchAll();
            $this->endPerformanceLog('data_retrieval', count($items), count($items), 0);

            if (empty($items)) {
                throw new Exception('No pending items found in batch');
            }

            // เริ่มประมวลผล
            $this->startPerformanceLog($batch_id, 'processing');
            $processed = 0;
            $success_count = 0;
            $error_count = 0;
            $errors = [];

            foreach ($items as $item) {
                $expense_id = $item['expense_id'];
                $processed++;

                try {
                    // ใช้จำนวนเงินจาก verification_amounts หรือจำนวนเดิม
                    $verification_amount = isset($verification_amounts[$expense_id])
                        ? floatval($verification_amounts[$expense_id])
                        : floatval($item['individual_amount']);

                    // อัปเดตสถานะ expense เป็น pending
                    $stmt = $this->db->prepare("
                        UPDATE expenses
                        SET status = 'pending',
                            verification_by = ?,
                            verification_date = NOW(),
                            verification_amount = ?,
                            transfer_amount = ?
                        WHERE id = ?
                    ");
                    $stmt->execute([$user_id, $verification_amount, $verification_amount, $expense_id]);

                    // อัปเดต batch item
                    $stmt = $this->db->prepare("
                        UPDATE batch_items
                        SET status = 'completed',
                            processed_at = NOW(),
                            individual_amount = ?
                        WHERE batch_id = ? AND expense_id = ?
                    ");
                    $stmt->execute([$verification_amount, $batch_id, $expense_id]);

                    $success_count++;

                    // Log activity สำหรับแต่ละ expense
                    logActivity(
                        $this->db,
                        $user_id,
                        'verify_expense',
                        'expenses',
                        $expense_id,
                        "Expense verified via batch {$batch_id} with amount {$verification_amount}",
                        $_SERVER['REMOTE_ADDR'] ?? 'unknown',
                        $_SERVER['HTTP_USER_AGENT'] ?? 'unknown'
                    );

                } catch (Exception $e) {
                    $error_count++;
                    $errors[] = "Expense ID {$expense_id}: " . $e->getMessage();

                    // อัปเดต batch item เป็น error
                    $stmt = $this->db->prepare("
                        UPDATE batch_items
                        SET status = 'error',
                            error_message = ?,
                            processed_at = NOW()
                        WHERE batch_id = ? AND expense_id = ?
                    ");
                    $stmt->execute([$e->getMessage(), $batch_id, $expense_id]);
                }
            }

            $this->endPerformanceLog('processing', $processed, $success_count, $error_count);

            // อัปเดตสถานะ Batch
            $final_status = ($error_count === 0) ? 'completed' : 'completed_with_errors';
            $this->updateBatchStatus($batch_id, $final_status, true);

            // Log batch completion
            $stmt = $this->db->prepare("CALL LogBatchOperation(?, ?, ?, ?, ?, ?, ?)");
            $stmt->execute([
                $batch_id,
                $final_status === 'completed' ? 'COMPLETE' : 'ERROR',
                null,
                $user_id,
                json_encode(['final_status' => $final_status, 'success_count' => $success_count, 'error_count' => $error_count, 'files_count' => count($uploaded_files)]),
                null,
                null
            ]);

            // บันทึกข้อมูลไฟล์ที่อัปโหลด
            foreach ($uploaded_files as $file_data) {
                $stmt = $this->db->prepare("
                    INSERT INTO batch_file_uploads (
                        batch_id, file_path, transfer_number, amount,
                        original_filename, file_size, mime_type, uploaded_at
                    ) VALUES (?, ?, ?, ?, ?, ?, ?, NOW())
                ");
                $stmt->execute([
                    $batch_id,
                    $file_data['file_path'],
                    $file_data['transfer_number'],
                    $file_data['amount'],
                    $file_data['original_filename'],
                    $file_data['file_size'],
                    $file_data['mime_type']
                ]);
            }

            $this->db->commit();

            // สิ้นสุด Performance Logging
            $this->endPerformanceLog('batch_verification_multi', $processed, $success_count, $error_count);

            return [
                'success' => true,
                'processed' => $processed,
                'success_count' => $success_count,
                'error_count' => $error_count,
                'errors' => $errors,
                'files_uploaded' => count($uploaded_files)
            ];

        } catch (Exception $e) {
            $this->db->rollback();

            // อัปเดตสถานะ Batch เป็น failed
            $this->updateBatchStatus($batch_id, 'failed');

            return [
                'success' => false,
                'error' => $e->getMessage()
            ];
        }
    }
}
?>
