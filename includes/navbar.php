<?php
// Get current page for active menu highlighting
$current_page = basename($_SERVER['PHP_SELF']);
$current_dir = basename(dirname($_SERVER['PHP_SELF']));

// Determine base path
$base_path = '';
if ($current_dir !== 'expenses_system') {
    $base_path = '../';
}
?>

<!-- Modern Navigation Bar -->
<nav class="navbar navbar-expand-lg navbar-dark bg-gradient-primary shadow-sm">
    <div class="container-fluid">
        <!-- Brand -->
        <a class="navbar-brand fw-bold" href="<?php echo $base_path; ?>dashboard.php">
            <i class="fas fa-calculator me-2"></i>
            <span class="d-none d-md-inline">Expense Management</span>
            <span class="d-md-none">Expenses</span>
        </a>

        <!-- Mobile Toggle -->
        <button class="navbar-toggler border-0" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
            <span class="navbar-toggler-icon"></span>
        </button>
        
        <div class="collapse navbar-collapse" id="navbarNav">
            <!-- Main Menu -->
            <ul class="navbar-nav me-auto">
                <!-- Dashboard -->
                <li class="nav-item">
                    <a class="nav-link px-3 <?php echo ($current_page === 'dashboard.php') ? 'active' : ''; ?>"
                       href="<?php echo $base_path; ?>dashboard.php">
                        <i class="fas fa-tachometer-alt me-1"></i>
                        <span class="d-lg-inline d-xl-inline">Dashboard</span>
                    </a>
                </li>

                <!-- Expenses -->
                <li class="nav-item dropdown">
                    <a class="nav-link dropdown-toggle px-3 <?php echo ($current_dir === 'expenses') ? 'active' : ''; ?>"
                       href="#" id="expensesDropdown" role="button" data-bs-toggle="dropdown" aria-expanded="false">
                        <i class="fas fa-file-invoice-dollar me-1"></i>
                        <span class="d-lg-inline d-xl-inline">Expenses</span>
                    </a>
                    <ul class="dropdown-menu shadow border-0">
                        <?php if ($_SESSION['role'] !== 'report_viewer'): ?>
                        <li><a class="dropdown-item py-2" href="<?php echo $base_path; ?>expenses/create.php">
                            <i class="fas fa-plus me-2 text-success"></i>Create New
                        </a></li>
                        <?php endif; ?>
                        <li><a class="dropdown-item py-2" href="<?php echo $base_path; ?>expenses/list.php">
                            <i class="fas fa-list me-2 text-primary"></i>View All
                        </a></li>
                        <li><hr class="dropdown-divider"></li>
                        <?php if ($_SESSION['role'] === 'verification'): ?>
                        <li><h6 class="dropdown-header">Verification</h6></li>
                        <li><a class="dropdown-item py-2" href="<?php echo $base_path; ?>expenses/multi_verification.php">
                            <i class="fas fa-check-double me-2 text-warning"></i>Multi Verification
                        </a></li>
                        <li><a class="dropdown-item py-2" href="<?php echo $base_path; ?>expenses/list.php?status=open">
                            <i class="fas fa-search me-2 text-info"></i>Pending Verification
                        </a></li>
                        <li><a class="dropdown-item py-2" href="<?php echo $base_path; ?>expenses/my_batches.php?type=verification">
                            <i class="fas fa-history me-2 text-secondary"></i>My Verification Batches
                        </a></li>
                        <?php elseif ($_SESSION['role'] === 'reviewer'): ?>
                        <li><h6 class="dropdown-header">Review</h6></li>
                        <li><a class="dropdown-item py-2" href="<?php echo $base_path; ?>expenses/multi_review.php">
                            <i class="fas fa-clipboard-list me-2 text-success"></i>Multi Review
                        </a></li>
                        <li><a class="dropdown-item py-2" href="<?php echo $base_path; ?>expenses/list.php?status=pending">
                            <i class="fas fa-clipboard-check me-2 text-info"></i>Pending Review
                        </a></li>
                        <li><a class="dropdown-item py-2" href="<?php echo $base_path; ?>expenses/my_batches.php?type=review">
                            <i class="fas fa-history me-2 text-secondary"></i>My Review Batches
                        </a></li>
                        <?php endif; ?>
                        <li><hr class="dropdown-divider"></li>
                        <li><a class="dropdown-item py-2" href="<?php echo $base_path; ?>search/advanced.php">
                            <i class="fas fa-search me-2 text-dark"></i>Advanced Search
                        </a></li>
                    </ul>
                </li>

                <!-- Master Data -->
                <li class="nav-item dropdown">
                    <a class="nav-link dropdown-toggle px-3 <?php echo ($current_dir === 'master_data') ? 'active' : ''; ?>"
                       href="#" id="masterDataDropdown" role="button" data-bs-toggle="dropdown" aria-expanded="false">
                        <i class="fas fa-database me-1"></i>
                        <span class="d-lg-inline d-xl-inline">Master Data</span>
                    </a>
                    <ul class="dropdown-menu shadow border-0">
                        <li><a class="dropdown-item py-2" href="<?php echo $base_path; ?>master_data/">
                            <i class="fas fa-home me-2 text-primary"></i>Master Data Home
                        </a></li>
                        <li><hr class="dropdown-divider"></li>
                        <li><a class="dropdown-item py-2" href="<?php echo $base_path; ?>master_data/items.php">
                            <i class="fas fa-boxes me-2 text-info"></i>Items
                        </a></li>
                        <li><a class="dropdown-item py-2" href="<?php echo $base_path; ?>master_data/customers.php">
                            <i class="fas fa-building me-2 text-success"></i>Customers
                        </a></li>
                        <li><a class="dropdown-item py-2" href="<?php echo $base_path; ?>master_data/drivers.php">
                            <i class="fas fa-truck me-2 text-warning"></i>Drivers
                        </a></li>
                    </ul>
                </li>

                <!-- Reports -->
                <?php if (in_array($_SESSION['role'], ['data_entry', 'verification', 'reviewer', 'report_viewer', 'administrator'])): ?>
                <li class="nav-item dropdown">
                    <a class="nav-link dropdown-toggle px-3 <?php echo ($current_dir === 'reports') ? 'active' : ''; ?>"
                       href="#" id="reportsDropdown" role="button" data-bs-toggle="dropdown" aria-expanded="false">
                        <i class="fas fa-chart-bar me-1"></i>
                        <span class="d-lg-inline d-xl-inline">Reports</span>
                    </a>
                    <ul class="dropdown-menu shadow border-0">
                        <li><a class="dropdown-item py-2" href="<?php echo $base_path; ?>reports/">
                            <i class="fas fa-home me-2 text-primary"></i>Reports Home
                        </a></li>
                        <li><hr class="dropdown-divider"></li>
                        <li><h6 class="dropdown-header">Financial Reports</h6></li>
                        <li><a class="dropdown-item py-2" href="<?php echo $base_path; ?>reports/expense_summary.php">
                            <i class="fas fa-chart-pie me-2 text-success"></i>Expense Summary
                        </a></li>
                        <li><a class="dropdown-item py-2" href="<?php echo $base_path; ?>reports/receipt_summary.php">
                            <i class="fas fa-receipt me-2 text-info"></i>Receipt Summary
                        </a></li>
                        <li><hr class="dropdown-divider"></li>
                        <li><h6 class="dropdown-header">Analytics</h6></li>
                        <li><a class="dropdown-item py-2" href="<?php echo $base_path; ?>reports/advanced_reports.php">
                            <i class="fas fa-chart-line me-2 text-warning"></i>Advanced Reports
                        </a></li>
                        <li><a class="dropdown-item py-2" href="<?php echo $base_path; ?>reports/performance_report.php">
                            <i class="fas fa-tachometer-alt me-2 text-danger"></i>Performance Report
                        </a></li>
                        <li><a class="dropdown-item py-2" href="<?php echo $base_path; ?>reports/activity_logs.php">
                            <i class="fas fa-history me-2 text-secondary"></i>Activity Logs
                        </a></li>
                    </ul>
                </li>
                <?php endif; ?>

                <!-- Admin Menu -->
                <?php if ($_SESSION['role'] === 'administrator'): ?>
                <li class="nav-item dropdown">
                    <a class="nav-link dropdown-toggle px-3 <?php echo ($current_dir === 'admin') ? 'active' : ''; ?>"
                       href="#" id="adminDropdown" role="button" data-bs-toggle="dropdown" aria-expanded="false">
                        <i class="fas fa-cog me-1"></i>
                        <span class="d-lg-inline d-xl-inline">Admin</span>
                    </a>
                    <ul class="dropdown-menu shadow border-0">
                        <li><a class="dropdown-item py-2" href="<?php echo $base_path; ?>admin/index.php">
                            <i class="fas fa-tachometer-alt me-2 text-primary"></i>Admin Dashboard
                        </a></li>
                        <li><hr class="dropdown-divider"></li>
                        <li><h6 class="dropdown-header">Expense Operations</h6></li>
                        <li><a class="dropdown-item py-2" href="<?php echo $base_path; ?>expenses/bulk_operations.php">
                            <i class="fas fa-tasks me-2 text-warning"></i>Bulk Operations
                        </a></li>
                        <li><a class="dropdown-item py-2" href="<?php echo $base_path; ?>expenses/multi_verification.php">
                            <i class="fas fa-check-double me-2 text-info"></i>Multi Verification
                        </a></li>
                        <li><a class="dropdown-item py-2" href="<?php echo $base_path; ?>expenses/multi_review.php">
                            <i class="fas fa-clipboard-list me-2 text-success"></i>Multi Review
                        </a></li>
                        <li><hr class="dropdown-divider"></li>
                        <li><h6 class="dropdown-header">Master Data</h6></li>
                        <li><a class="dropdown-item py-2" href="<?php echo $base_path; ?>admin/items.php">
                            <i class="fas fa-boxes me-2 text-info"></i>Items
                        </a></li>
                        <li><a class="dropdown-item py-2" href="<?php echo $base_path; ?>admin/customers.php">
                            <i class="fas fa-users me-2 text-success"></i>Customers
                        </a></li>
                        <li><a class="dropdown-item py-2" href="<?php echo $base_path; ?>admin/drivers.php">
                            <i class="fas fa-id-card me-2 text-warning"></i>Drivers
                        </a></li>
                        <li><hr class="dropdown-divider"></li>
                        <li><a class="dropdown-item py-2" href="<?php echo $base_path; ?>admin/logs.php">
                            <i class="fas fa-history me-2 text-secondary"></i>Activity Logs
                        </a></li>
                    </ul>
                </li>
                <?php endif; ?>
            </ul>

            <!-- Right Side Menu -->
            <ul class="navbar-nav">
                <!-- Quick Actions -->
                <li class="nav-item dropdown d-none d-lg-block">
                    <a class="nav-link dropdown-toggle px-2" href="#" id="quickActionsDropdown"
                       role="button" data-bs-toggle="dropdown" aria-expanded="false">
                        <i class="fas fa-bolt"></i>
                    </a>
                    <ul class="dropdown-menu dropdown-menu-end shadow border-0">
                        <li><h6 class="dropdown-header">Quick Actions</h6></li>
                        <?php if ($_SESSION['role'] !== 'report_viewer'): ?>
                        <li><a class="dropdown-item py-2" href="<?php echo $base_path; ?>expenses/create.php">
                            <i class="fas fa-plus me-2 text-success"></i>Create Expense
                        </a></li>
                        <?php endif; ?>
                        <li><a class="dropdown-item py-2" href="<?php echo $base_path; ?>search/advanced.php">
                            <i class="fas fa-search me-2 text-info"></i>Advanced Search
                        </a></li>
                        <li><a class="dropdown-item py-2" href="<?php echo $base_path; ?>reports/">
                            <i class="fas fa-chart-bar me-2 text-warning"></i>Reports
                        </a></li>
                    </ul>
                </li>

                <!-- User Menu -->
                <li class="nav-item dropdown">
                    <a class="nav-link dropdown-toggle px-2" href="#" id="userDropdown"
                       role="button" data-bs-toggle="dropdown" aria-expanded="false">
                        <i class="fas fa-user-circle me-1"></i>
                        <span class="d-none d-lg-inline"><?php echo htmlspecialchars($_SESSION['full_name'] ?? $_SESSION['username']); ?></span>
                    </a>
                    <ul class="dropdown-menu dropdown-menu-end shadow border-0">
                        <li><h6 class="dropdown-header">
                            <i class="fas fa-user me-1"></i>
                            <?php echo htmlspecialchars($_SESSION['full_name'] ?? $_SESSION['username']); ?>
                        </h6></li>
                        <li><small class="dropdown-item-text text-muted">
                            <span class="badge bg-secondary"><?php echo ucfirst($_SESSION['role']); ?></span>
                        </small></li>
                        <li><hr class="dropdown-divider"></li>
                        <li><a class="dropdown-item py-2" href="<?php echo $base_path; ?>profile.php">
                            <i class="fas fa-user-edit me-2 text-primary"></i>My Profile
                        </a></li>
                        <li><a class="dropdown-item py-2" href="<?php echo $base_path; ?>reports/activity_logs.php">
                            <i class="fas fa-history me-2 text-secondary"></i>My Activities
                        </a></li>
                        <li><hr class="dropdown-divider"></li>
                        <li><a class="dropdown-item py-2 text-danger" href="<?php echo $base_path; ?>logout.php">
                            <i class="fas fa-sign-out-alt me-2"></i>Logout
                        </a></li>
                    </ul>
                </li>
            </ul>
        </div>
    </div>
</nav>

<!-- Custom CSS for Modern Navigation -->
<style>
.bg-gradient-primary {
    background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
}

.navbar-nav .nav-link {
    transition: all 0.3s ease;
    border-radius: 6px;
    margin: 0 2px;
}

.navbar-nav .nav-link:hover {
    background-color: rgba(255, 255, 255, 0.1);
    transform: translateY(-1px);
}

.navbar-nav .nav-link.active {
    background-color: rgba(255, 255, 255, 0.2);
    font-weight: 600;
}

.dropdown-menu {
    border-radius: 10px;
    padding: 8px 0;
    margin-top: 8px;
}

.dropdown-item {
    border-radius: 6px;
    margin: 2px 8px;
    transition: all 0.2s ease;
}

.dropdown-item:hover {
    background-color: #f8f9fa;
    transform: translateX(4px);
}

.dropdown-header {
    font-size: 0.75rem;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    color: #6c757d;
    font-weight: 600;
}

.navbar-toggler:focus {
    box-shadow: none;
}

/* Responsive adjustments */
@media (max-width: 991.98px) {
    .navbar-nav .nav-link {
        padding: 8px 16px;
        margin: 2px 0;
        color: #ffffff !important; /* Force white text on mobile */
    }

    .navbar-nav .nav-link span {
        color: #ffffff !important; /* Force white text for spans */
        display: inline !important; /* Show text on mobile */
    }

    .navbar-nav .nav-link i {
        color: #ffffff !important; /* Force white icons */
    }

    .dropdown-menu {
        margin-top: 0;
        border-radius: 0;
        border: none;
        box-shadow: none;
        background-color: rgba(255, 255, 255, 0.95);
    }

    .dropdown-item {
        color: #333 !important; /* Dark text for dropdown items */
    }
}

@media (max-width: 1199.98px) and (min-width: 992px) {
    .navbar-nav .nav-link span {
        display: none;
    }

    .navbar-nav .nav-link {
        padding: 8px 12px;
    }
}

/* Additional mobile fixes */
@media (max-width: 991.98px) {
    .navbar-collapse {
        background-color: rgba(0, 123, 255, 0.95);
        border-radius: 8px;
        margin-top: 8px;
        padding: 8px;
    }

    .navbar-nav .nav-link:hover {
        background-color: rgba(255, 255, 255, 0.2);
        color: #ffffff !important;
    }

    .navbar-nav .nav-link.active {
        background-color: rgba(255, 255, 255, 0.3);
        color: #ffffff !important;
        font-weight: 600;
    }
}

/* Notification badge for future use */
.notification-badge {
    position: absolute;
    top: -2px;
    right: -2px;
    background: #dc3545;
    color: white;
    border-radius: 50%;
    width: 18px;
    height: 18px;
    font-size: 10px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: bold;
}

/* Enhanced dropdown styling */
.dropdown-item-text {
    padding: 0.25rem 1rem;
}

.badge {
    font-size: 0.7em;
}
</style>
