<?php
/**
 * Common functions for the Expenses Management System
 */

/**
 * Check if user has required role
 */
function hasRole($required_role, $user_role = null) {
    if ($user_role === null) {
        $user_role = $_SESSION['role'] ?? '';
    }
    
    $roles = [
        'data_entry' => 1,
        'verification' => 2,
        'reviewer' => 3,
        'report_viewer' => 4,
        'administrator' => 5
    ];
    
    return ($roles[$user_role] ?? 0) >= ($roles[$required_role] ?? 0);
}

/**
 * Check if user can edit expense based on role and ownership
 */
function canEditExpense($expense, $user_id, $user_role) {
    // Administrators can edit anything (full access)
    if ($user_role === 'administrator') {
        return true;
    }

    // Data entry users can only edit their own open or returned records
    if ($user_role === 'data_entry') {
        return isset($expense['created_by']) && $expense['created_by'] == $user_id && in_array($expense['status'], ['open', 'returned']);
    }

    // Verification and reviewer roles can only edit open and returned records
    if ($user_role === 'verification' || $user_role === 'reviewer') {
        return in_array($expense['status'], ['open', 'returned']);
    }

    // Report viewer cannot edit anything
    if ($user_role === 'report_viewer') {
        return false;
    }

    return false;
}

/**
 * Check if user can manage status (change status, upload documents) for expenses
 */
function canManageStatus($expense, $user_id, $user_role) {
    // Administrators can manage any status
    if ($user_role === 'administrator') {
        return true;
    }

    // Verification role can manage open and pending status
    if ($user_role === 'verification' && in_array($expense['status'], ['open', 'pending'])) {
        return true;
    }

    // Reviewer role can manage open, pending and success status
    if ($user_role === 'reviewer' && in_array($expense['status'], ['open', 'pending', 'success'])) {
        return true;
    }

    // Report viewer cannot manage status
    if ($user_role === 'report_viewer') {
        return false;
    }

    return false;
}

/**
 * Check if user can change expense status
 */
function canChangeStatus($from_status, $to_status, $user_role) {
    $transitions = [
        'data_entry' => [
            'returned' => ['open'], // Can resubmit returned expenses
            'rejected' => ['open']  // Can resubmit rejected expenses
        ],
        'verification' => [
            'open' => ['pending', 'rejected', 'returned'] // Can approve, reject, or return
        ],
        'reviewer' => [
            'pending' => ['success', 'rejected', 'returned'] // Can approve, reject, or return
        ],
        'report_viewer' => [
            // No status change permissions - read only
        ],
        'administrator' => [
            'open' => ['pending', 'success', 'rejected', 'returned'],
            'pending' => ['open', 'success', 'rejected', 'returned'],
            'success' => ['open', 'pending', 'rejected', 'returned'],
            'rejected' => ['open', 'pending', 'success', 'returned'],
            'returned' => ['open', 'pending', 'success', 'rejected']
        ]
    ];

    $allowed = $transitions[$user_role] ?? [];
    return in_array($to_status, $allowed[$from_status] ?? []);
}

/**
 * Log user activity
 */
function logActivity($db, $user_id, $action_type, $table_name = null, $record_id = null, $description = '', $ip_address = '', $user_agent = '', $old_values = null, $new_values = null) {
    try {
        // Debug: Log function call
        error_log("DEBUG: logActivity called - User: {$user_id}, Action: {$action_type}, Description: {$description}");

        $stmt = $db->prepare("
            INSERT INTO activity_logs (user_id, action_type, table_name, record_id, old_values, new_values, description, ip_address, user_agent)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
        ");

        $old_json = $old_values ? json_encode($old_values) : null;
        $new_json = $new_values ? json_encode($new_values) : null;

        $result = $stmt->execute([
            $user_id,
            $action_type,
            $table_name,
            $record_id,
            $old_json,
            $new_json,
            $description,
            $ip_address,
            $user_agent
        ]);

        if ($result) {
            error_log("DEBUG: logActivity SUCCESS - Inserted log for User: {$user_id}, Action: {$action_type}");
            return true;
        } else {
            error_log("DEBUG: logActivity FAILED - No result from execute");
            return false;
        }

    } catch (Exception $e) {
        error_log('DEBUG: logActivity EXCEPTION - ' . $e->getMessage());
        error_log('DEBUG: logActivity TRACE - ' . $e->getTraceAsString());
        return false;
    }
}

/**
 * Generate next expense number
 */
function generateExpenseNumber($db) {
    $today = date('Y-m-d');
    $date_prefix = date('Ymd');

    // Try to generate unique expense number (max 100 attempts)
    for ($attempt = 1; $attempt <= 100; $attempt++) {
        // Get the last sequence number for today
        $stmt = $db->prepare("
            SELECT MAX(CAST(sequence AS UNSIGNED)) as max_sequence
            FROM expenses
            WHERE DATE(created_at) = ?
        ");
        $stmt->execute([$today]);
        $result = $stmt->fetch();

        $next_sequence = ($result['max_sequence'] ?? 0) + $attempt;
        $sequence = str_pad($next_sequence, 3, '0', STR_PAD_LEFT);
        $exno = $date_prefix . '-' . $sequence;

        // Check if this exno already exists
        $check_stmt = $db->prepare("SELECT COUNT(*) as count FROM expenses WHERE exno = ?");
        $check_stmt->execute([$exno]);
        $exists = $check_stmt->fetch()['count'] > 0;

        if (!$exists) {
            return ['sequence' => $sequence, 'exno' => $exno];
        }
    }

    // If we can't generate unique number after 100 attempts, use timestamp
    $timestamp_suffix = substr(time(), -3);
    $sequence = '999';
    $exno = $date_prefix . '-' . $sequence . '-' . $timestamp_suffix;

    return ['sequence' => $sequence, 'exno' => $exno];
}

/**
 * Sanitize input
 */
function sanitizeInput($input) {
    return htmlspecialchars(trim($input), ENT_QUOTES, 'UTF-8');
}

/**
 * Validate email
 */
function isValidEmail($email) {
    return filter_var($email, FILTER_VALIDATE_EMAIL) !== false;
}

/**
 * Format currency
 */
function formatCurrency($amount, $currency = 'USD') {
    return number_format($amount, 2) . ' ' . $currency;
}

/**
 * Format date for display
 */
function formatDate($date, $format = 'M d, Y') {
    if (empty($date)) return '';
    return date($format, strtotime($date));
}

/**
 * Format datetime for display
 */
function formatDateTime($datetime, $format = 'M d, Y H:i') {
    if (empty($datetime)) return '';
    return date($format, strtotime($datetime));
}

/**
 * Get user by ID
 */
function getUserById($user_id, $db = null) {
    // If no $db provided, create connection
    if (!$db) {
        require_once __DIR__ . '/../config/database.php';
        $database = new Database();
        $db = $database->getConnection();
    }

    try {
        $stmt = $db->prepare("SELECT * FROM users WHERE id = ?");
        $stmt->execute([$user_id]);
        return $stmt->fetch();
    } catch (Exception $e) {
        error_log("Error in getUserById: " . $e->getMessage());
        return false;
    }
}

/**
 * Get status badge HTML
 */
function getStatusBadge($status) {
    $badges = [
        'open' => '<span class="badge bg-info"><i class="fas fa-folder-open me-1"></i>Open</span>',
        'checked' => '<span class="badge bg-primary"><i class="fas fa-check me-1"></i>Checked</span>',
        'pending' => '<span class="badge bg-warning text-dark"><i class="fas fa-clock me-1"></i>Pending</span>',
        'success' => '<span class="badge bg-success"><i class="fas fa-check-circle me-1"></i>Approved</span>',
        'rejected' => '<span class="badge bg-danger"><i class="fas fa-times-circle me-1"></i>Rejected</span>',
        'returned' => '<span class="badge bg-secondary"><i class="fas fa-undo me-1"></i>Returned</span>'
    ];

    return $badges[$status] ?? '<span class="badge bg-dark">' . ucfirst($status) . '</span>';
}

/**
 * Upload file with validation
 */
function uploadFile($file, $upload_dir, $allowed_types = ['jpg', 'jpeg', 'png', 'pdf'], $max_size = 5242880) {
    if (!isset($file['tmp_name']) || empty($file['tmp_name'])) {
        return ['success' => false, 'error' => 'No file uploaded'];
    }
    
    if ($file['error'] !== UPLOAD_ERR_OK) {
        return ['success' => false, 'error' => 'Upload error: ' . $file['error']];
    }
    
    if ($file['size'] > $max_size) {
        return ['success' => false, 'error' => 'File too large. Maximum size: ' . ($max_size / 1024 / 1024) . 'MB'];
    }
    
    $file_extension = strtolower(pathinfo($file['name'], PATHINFO_EXTENSION));
    if (!in_array($file_extension, $allowed_types)) {
        return ['success' => false, 'error' => 'Invalid file type. Allowed: ' . implode(', ', $allowed_types)];
    }
    
    // Create upload directory if it doesn't exist
    if (!is_dir($upload_dir)) {
        mkdir($upload_dir, 0755, true);
    }
    
    // Generate unique filename
    $filename = uniqid() . '_' . time() . '.' . $file_extension;
    $filepath = $upload_dir . '/' . $filename;
    
    if (move_uploaded_file($file['tmp_name'], $filepath)) {
        return ['success' => true, 'filename' => $filename, 'filepath' => $filepath];
    } else {
        return ['success' => false, 'error' => 'Failed to move uploaded file'];
    }
}

/**
 * Delete file safely
 */
function deleteFile($filepath) {
    if (file_exists($filepath) && is_file($filepath)) {
        return unlink($filepath);
    }
    return false;
}

/**
 * Generate CSRF token
 */
function generateCSRFToken() {
    if (!isset($_SESSION['csrf_token'])) {
        $_SESSION['csrf_token'] = bin2hex(random_bytes(32));
    }
    return $_SESSION['csrf_token'];
}

/**
 * Verify CSRF token
 */
function verifyCSRFToken($token) {
    return isset($_SESSION['csrf_token']) && hash_equals($_SESSION['csrf_token'], $token);
}

/**
 * Check session timeout
 */
function checkSessionTimeout($timeout = 3600) { // 1 hour default
    if (isset($_SESSION['last_activity'])) {
        if (time() - $_SESSION['last_activity'] > $timeout) {
            session_destroy();
            return false;
        }
    }
    $_SESSION['last_activity'] = time();
    return true;
}

/**
 * Redirect with message
 */
function redirectWithMessage($url, $message, $type = 'success') {
    $_SESSION['flash_message'] = $message;
    $_SESSION['flash_type'] = $type;
    header('Location: ' . $url);
    exit();
}

/**
 * Get and clear flash message
 */
function getFlashMessage() {
    if (isset($_SESSION['flash_message'])) {
        $message = $_SESSION['flash_message'];
        $type = $_SESSION['flash_type'] ?? 'info';
        unset($_SESSION['flash_message'], $_SESSION['flash_type']);
        return ['message' => $message, 'type' => $type];
    }
    return null;
}

/**
 * Paginate results
 */
function paginate($total_records, $records_per_page = 20, $current_page = 1) {
    $total_pages = ceil($total_records / $records_per_page);
    $current_page = max(1, min($current_page, $total_pages));
    $offset = ($current_page - 1) * $records_per_page;
    
    return [
        'total_records' => $total_records,
        'total_pages' => $total_pages,
        'current_page' => $current_page,
        'records_per_page' => $records_per_page,
        'offset' => $offset,
        'has_previous' => $current_page > 1,
        'has_next' => $current_page < $total_pages
    ];
}

/**
 * Export data to CSV
 */
function exportToCSV($data, $filename, $headers = []) {
    header('Content-Type: text/csv');
    header('Content-Disposition: attachment; filename="' . $filename . '"');
    
    $output = fopen('php://output', 'w');
    
    if (!empty($headers)) {
        fputcsv($output, $headers);
    } elseif (!empty($data)) {
        fputcsv($output, array_keys($data[0]));
    }
    
    foreach ($data as $row) {
        fputcsv($output, $row);
    }
    
    fclose($output);
    exit();
}
?>
