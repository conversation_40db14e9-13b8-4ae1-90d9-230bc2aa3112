<?php
/**
 * Report Access Control Helper
 * Centralized access control for reports
 */

/**
 * Check if user has access to reports
 * @param string $user_role Current user role
 * @return bool True if user has access, false otherwise
 */
function hasReportAccess($user_role) {
    return in_array($user_role, ['verification', 'reviewer', 'report_viewer', 'administrator']);
}

/**
 * Enforce report access control
 * Redirects to dashboard if user doesn't have access
 */
function enforceReportAccess() {
    if (!isset($_SESSION['user_id'])) {
        header('Location: ../login.php');
        exit();
    }
    
    if (!hasReportAccess($_SESSION['role'])) {
        header('Location: ../dashboard.php');
        exit();
    }
}

/**
 * Check if user can edit/modify data (not just view)
 * @param string $user_role Current user role
 * @return bool True if user can edit, false if read-only
 */
function canEditData($user_role) {
    return in_array($user_role, ['verification', 'reviewer', 'administrator']);
}

/**
 * Check if user can export reports
 * @param string $user_role Current user role
 * @return bool True if user can export, false otherwise
 */
function canExportReports($user_role) {
    return in_array($user_role, ['verification', 'reviewer', 'report_viewer', 'administrator']);
}

/**
 * Get role-based data access filter
 * @param string $user_role Current user role
 * @param int $user_id Current user ID
 * @return array Array with 'conditions' and 'params' for SQL WHERE clause
 */
function getDataAccessFilter($user_role, $user_id) {
    $conditions = [];
    $params = [];
    
    // Data entry users can only see their own data
    if ($user_role === 'data_entry') {
        $conditions[] = 'e.created_by = ?';
        $params[] = $user_id;
    }
    // All other roles (verification, reviewer, report_viewer, administrator) can see all data
    
    return [
        'conditions' => $conditions,
        'params' => $params
    ];
}

/**
 * Get user role display information
 * @param string $role User role
 * @return array Role display information
 */
function getRoleDisplayInfo($role) {
    $roles = [
        'data_entry' => [
            'name' => 'Data Entry',
            'color' => 'secondary',
            'icon' => 'fas fa-edit'
        ],
        'verification' => [
            'name' => 'Verification',
            'color' => 'info',
            'icon' => 'fas fa-check-circle'
        ],
        'reviewer' => [
            'name' => 'Reviewer',
            'color' => 'warning',
            'icon' => 'fas fa-clipboard-check'
        ],
        'report_viewer' => [
            'name' => 'Report Viewer',
            'color' => 'primary',
            'icon' => 'fas fa-chart-bar'
        ],
        'administrator' => [
            'name' => 'Administrator',
            'color' => 'danger',
            'icon' => 'fas fa-user-shield'
        ]
    ];
    
    return $roles[$role] ?? [
        'name' => 'Unknown',
        'color' => 'secondary',
        'icon' => 'fas fa-user'
    ];
}
?>
