<?php
/**
 * Image Compression Class
 * Handles image compression and optimization for the expense system
 */
class ImageCompressor {
    
    // Configuration constants
    const MAX_WIDTH = 1200;           // Maximum width in pixels
    const MAX_HEIGHT = 1200;          // Maximum height in pixels
    const JPEG_QUALITY = 85;          // JPEG quality (0-100)
    const PNG_COMPRESSION = 6;        // PNG compression level (0-9)
    const MAX_FILE_SIZE = 5 * 1024 * 1024; // 5MB maximum file size
    
    // Allowed image types
    private static $allowedTypes = [
        'image/jpeg' => 'jpg',
        'image/jpg' => 'jpg',
        'image/png' => 'png',
        'image/gif' => 'gif',
        'image/webp' => 'webp'
    ];
    
    /**
     * Compress and save uploaded image
     * 
     * @param array $file $_FILES array element
     * @param string $uploadDir Upload directory path
     * @param string $prefix Filename prefix (optional)
     * @return array Result with success status and filename/error
     */
    public static function compressAndSave($file, $uploadDir, $prefix = '') {
        try {
            // Validate file
            $validation = self::validateFile($file);
            if (!$validation['success']) {
                return $validation;
            }
            
            // Create upload directory if it doesn't exist
            if (!file_exists($uploadDir)) {
                if (!mkdir($uploadDir, 0755, true)) {
                    return ['success' => false, 'error' => 'Failed to create upload directory'];
                }
            }
            
            // Generate unique filename
            $originalName = pathinfo($file['name'], PATHINFO_FILENAME);
            $extension = strtolower(pathinfo($file['name'], PATHINFO_EXTENSION));
            $filename = $prefix . uniqid() . '_' . time() . '.' . $extension;
            $filepath = $uploadDir . '/' . $filename;
            
            // Load and compress image
            $compressed = self::compressImage($file['tmp_name'], $filepath, $file['type']);
            
            if (!$compressed['success']) {
                return $compressed;
            }
            
            // Get file size information
            $originalSize = $file['size'];
            $compressedSize = filesize($filepath);
            $compressionRatio = round((1 - ($compressedSize / $originalSize)) * 100, 1);
            
            return [
                'success' => true,
                'filename' => $filename,
                'filepath' => $filepath,
                'original_size' => $originalSize,
                'compressed_size' => $compressedSize,
                'compression_ratio' => $compressionRatio,
                'dimensions' => $compressed['dimensions']
            ];
            
        } catch (Exception $e) {
            return ['success' => false, 'error' => 'Image compression failed: ' . $e->getMessage()];
        }
    }
    
    /**
     * Validate uploaded file
     */
    private static function validateFile($file) {
        // Check for upload errors
        if ($file['error'] !== UPLOAD_ERR_OK) {
            return ['success' => false, 'error' => 'File upload error: ' . $file['error']];
        }
        
        // Check file size
        if ($file['size'] > self::MAX_FILE_SIZE) {
            $maxSizeMB = self::MAX_FILE_SIZE / (1024 * 1024);
            return ['success' => false, 'error' => "File size exceeds {$maxSizeMB}MB limit"];
        }
        
        // Check file type
        $finfo = finfo_open(FILEINFO_MIME_TYPE);
        $mimeType = finfo_file($finfo, $file['tmp_name']);
        finfo_close($finfo);
        
        if (!array_key_exists($mimeType, self::$allowedTypes)) {
            return ['success' => false, 'error' => 'Invalid file type. Only JPG, PNG, GIF, and WebP images are allowed'];
        }
        
        return ['success' => true, 'mime_type' => $mimeType];
    }
    
    /**
     * Compress image and save to destination
     */
    private static function compressImage($sourcePath, $destinationPath, $mimeType) {
        // Create image resource from source
        $sourceImage = self::createImageFromFile($sourcePath, $mimeType);
        if (!$sourceImage) {
            return ['success' => false, 'error' => 'Failed to create image resource'];
        }
        
        // Get original dimensions
        $originalWidth = imagesx($sourceImage);
        $originalHeight = imagesy($sourceImage);
        
        // Calculate new dimensions
        $newDimensions = self::calculateNewDimensions($originalWidth, $originalHeight);
        $newWidth = $newDimensions['width'];
        $newHeight = $newDimensions['height'];
        
        // Create new image with calculated dimensions
        $newImage = imagecreatetruecolor($newWidth, $newHeight);
        
        // Preserve transparency for PNG and GIF
        if ($mimeType === 'image/png' || $mimeType === 'image/gif') {
            imagealphablending($newImage, false);
            imagesavealpha($newImage, true);
            $transparent = imagecolorallocatealpha($newImage, 255, 255, 255, 127);
            imagefill($newImage, 0, 0, $transparent);
        }
        
        // Resize image
        imagecopyresampled(
            $newImage, $sourceImage,
            0, 0, 0, 0,
            $newWidth, $newHeight,
            $originalWidth, $originalHeight
        );
        
        // Save compressed image
        $saved = self::saveCompressedImage($newImage, $destinationPath, $mimeType);
        
        // Clean up memory
        imagedestroy($sourceImage);
        imagedestroy($newImage);
        
        if (!$saved) {
            return ['success' => false, 'error' => 'Failed to save compressed image'];
        }
        
        return [
            'success' => true,
            'dimensions' => [
                'original' => ['width' => $originalWidth, 'height' => $originalHeight],
                'compressed' => ['width' => $newWidth, 'height' => $newHeight]
            ]
        ];
    }
    
    /**
     * Create image resource from file
     */
    private static function createImageFromFile($filepath, $mimeType) {
        switch ($mimeType) {
            case 'image/jpeg':
            case 'image/jpg':
                return imagecreatefromjpeg($filepath);
            case 'image/png':
                return imagecreatefrompng($filepath);
            case 'image/gif':
                return imagecreatefromgif($filepath);
            case 'image/webp':
                return imagecreatefromwebp($filepath);
            default:
                return false;
        }
    }
    
    /**
     * Calculate new dimensions while maintaining aspect ratio
     */
    private static function calculateNewDimensions($originalWidth, $originalHeight) {
        $maxWidth = self::MAX_WIDTH;
        $maxHeight = self::MAX_HEIGHT;
        
        // If image is already smaller than max dimensions, keep original size
        if ($originalWidth <= $maxWidth && $originalHeight <= $maxHeight) {
            return ['width' => $originalWidth, 'height' => $originalHeight];
        }
        
        // Calculate scaling ratio
        $widthRatio = $maxWidth / $originalWidth;
        $heightRatio = $maxHeight / $originalHeight;
        $ratio = min($widthRatio, $heightRatio);
        
        return [
            'width' => round($originalWidth * $ratio),
            'height' => round($originalHeight * $ratio)
        ];
    }
    
    /**
     * Save compressed image to file
     */
    private static function saveCompressedImage($imageResource, $destinationPath, $mimeType) {
        switch ($mimeType) {
            case 'image/jpeg':
            case 'image/jpg':
                return imagejpeg($imageResource, $destinationPath, self::JPEG_QUALITY);
            case 'image/png':
                return imagepng($imageResource, $destinationPath, self::PNG_COMPRESSION);
            case 'image/gif':
                return imagegif($imageResource, $destinationPath);
            case 'image/webp':
                return imagewebp($imageResource, $destinationPath, self::JPEG_QUALITY);
            default:
                return false;
        }
    }
    
    /**
     * Get image information
     */
    public static function getImageInfo($filepath) {
        if (!file_exists($filepath)) {
            return false;
        }
        
        $imageInfo = getimagesize($filepath);
        if (!$imageInfo) {
            return false;
        }
        
        return [
            'width' => $imageInfo[0],
            'height' => $imageInfo[1],
            'type' => $imageInfo[2],
            'mime' => $imageInfo['mime'],
            'size' => filesize($filepath),
            'size_formatted' => self::formatFileSize(filesize($filepath))
        ];
    }
    
    /**
     * Format file size in human readable format
     */
    public static function formatFileSize($bytes) {
        $units = ['B', 'KB', 'MB', 'GB'];
        $bytes = max($bytes, 0);
        $pow = floor(($bytes ? log($bytes) : 0) / log(1024));
        $pow = min($pow, count($units) - 1);
        
        $bytes /= pow(1024, $pow);
        
        return round($bytes, 2) . ' ' . $units[$pow];
    }
    
    /**
     * Create thumbnail from existing image
     */
    public static function createThumbnail($sourcePath, $destinationPath, $width = 150, $height = 150) {
        if (!file_exists($sourcePath)) {
            return false;
        }
        
        $imageInfo = getimagesize($sourcePath);
        if (!$imageInfo) {
            return false;
        }
        
        $mimeType = $imageInfo['mime'];
        $sourceImage = self::createImageFromFile($sourcePath, $mimeType);
        
        if (!$sourceImage) {
            return false;
        }
        
        $originalWidth = imagesx($sourceImage);
        $originalHeight = imagesy($sourceImage);
        
        // Create thumbnail
        $thumbnail = imagecreatetruecolor($width, $height);
        
        // Preserve transparency
        if ($mimeType === 'image/png' || $mimeType === 'image/gif') {
            imagealphablending($thumbnail, false);
            imagesavealpha($thumbnail, true);
            $transparent = imagecolorallocatealpha($thumbnail, 255, 255, 255, 127);
            imagefill($thumbnail, 0, 0, $transparent);
        }
        
        imagecopyresampled(
            $thumbnail, $sourceImage,
            0, 0, 0, 0,
            $width, $height,
            $originalWidth, $originalHeight
        );
        
        $saved = self::saveCompressedImage($thumbnail, $destinationPath, $mimeType);
        
        imagedestroy($sourceImage);
        imagedestroy($thumbnail);
        
        return $saved;
    }
}
?>
