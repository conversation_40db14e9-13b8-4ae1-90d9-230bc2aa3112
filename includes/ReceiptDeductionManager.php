<?php
/**
 * Receipt Deduction Manager
 *
 * This class handles all operations related to receipt deductions
 * including adding, updating, deleting, and calculating deductions
 */

require_once __DIR__ . '/SQLDebugger.php';

class ReceiptDeductionManager {
    private $db;
    
    public function __construct($database_connection) {
        $this->db = $database_connection;
    }
    
    /**
     * Get all deduction types
     */
    public static function getDeductionTypes() {
        return [
            'tax_vat' => 'ภาษีมูลค่าเพิ่ม (VAT)',
            'tax_withholding' => 'ภาษีหัก ณ ที่จ่าย',
            'service_fee' => 'ค่าธรรมเนียม',
            'discount' => 'ส่วนลด',
            'penalty' => 'ค่าปรับ',
            'commission' => 'ค่าคอมมิชชั่น',
            'other' => 'อื่นๆ'
        ];
    }
    
    /**
     * Add a deduction to a receipt
     */
    public function addDeduction($receiptId, $deductionData, $userId, $receiptIndex = 0) {
        try {
            // Ensure boolean conversion
            $is_percentage_based = false;
            if (isset($deductionData['is_percentage_based'])) {
                $is_percentage_based = filter_var($deductionData['is_percentage_based'], FILTER_VALIDATE_BOOLEAN);
            }



            // Try using stored procedure first, fallback to direct insert for MariaDB 5.5 compatibility
            try {
                $sql = "CALL AddReceiptDeduction(?, ?, ?, ?, ?, ?, ?, ?, ?, @deduction_id, @success, @message)";
                $stmt = $this->db->prepare($sql);

                // Prepare parameters with explicit types
                $params = [
                    $receiptId,                                    // INT
                    $receiptIndex,                                 // INT
                    $deductionData['deduction_type'],             // VARCHAR
                    floatval($deductionData['amount'] ?? 0),      // DECIMAL
                    $deductionData['percentage'] ?? null,         // DECIMAL or NULL
                    $deductionData['description'] ?? '',          // TEXT
                    $deductionData['deduction_image'] ?? null,    // VARCHAR or NULL
                    $is_percentage_based ? 1 : 0,                 // BOOLEAN as INT
                    $userId                                        // INT
                ];

                // DEBUG: Log SQL and parameters
                error_log("=== RECEIPT DEDUCTIONS SQL DEBUG (STORED PROCEDURE) ===");
                error_log("SQL: " . $sql);
                error_log("Parameters: " . json_encode($params, JSON_PRETTY_PRINT));
                error_log("Receipt ID: " . $receiptId);
                error_log("Receipt Index: " . $receiptIndex);
                error_log("Deduction Type: " . $deductionData['deduction_type']);
                error_log("Amount: " . floatval($deductionData['amount'] ?? 0));
                error_log("Percentage: " . ($deductionData['percentage'] ?? 'NULL'));
                error_log("Description: " . ($deductionData['description'] ?? ''));
                error_log("Is Percentage Based: " . ($is_percentage_based ? 'TRUE' : 'FALSE'));
                error_log("User ID: " . $userId);

                $stmt->execute($params);

                // Get the output parameters
                $result = $this->db->query("SELECT @deduction_id as deduction_id, @success as success, @message as message")->fetch(PDO::FETCH_ASSOC);

                // DEBUG: Log stored procedure result
                error_log("Stored Procedure Result: " . json_encode($result, JSON_PRETTY_PRINT));
            } catch (PDOException $e) {
                // Fallback to direct insert for MariaDB 5.5 compatibility
                error_log("Stored procedure failed, using direct insert: " . $e->getMessage());

                $this->db->beginTransaction();

                $sql = "INSERT INTO receipt_deductions (
                        receipt_number_id, receipt_index, deduction_type, amount, percentage,
                        description, deduction_image, is_percentage_based, created_by
                    ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)";

                $stmt = $this->db->prepare($sql);

                $params = [
                    $receiptId,
                    $receiptIndex,
                    $deductionData['deduction_type'],
                    floatval($deductionData['amount'] ?? 0),
                    $deductionData['percentage'] ?? null,
                    $deductionData['description'] ?? '',
                    $deductionData['deduction_image'] ?? null,
                    $is_percentage_based ? 1 : 0,
                    $userId
                ];

                // DEBUG: Log SQL and parameters for direct insert
                SQLDebugger::logSQL($sql, $params, "RECEIPT_DEDUCTIONS_DIRECT_INSERT");

                // Additional detailed logging
                error_log("=== RECEIPT DEDUCTIONS DETAILED DEBUG ===");
                error_log("Receipt ID: " . $receiptId);
                error_log("Receipt Index: " . $receiptIndex);
                error_log("Deduction Type: " . $deductionData['deduction_type']);
                error_log("Amount: " . floatval($deductionData['amount'] ?? 0));
                error_log("Percentage: " . ($deductionData['percentage'] ?? 'NULL'));
                error_log("Description: " . ($deductionData['description'] ?? ''));
                error_log("Is Percentage Based: " . ($is_percentage_based ? 'TRUE' : 'FALSE'));
                error_log("User ID: " . $userId);

                $stmt->execute($params);

                $deduction_id = $this->db->lastInsertId();

                // DEBUG: Log successful insert and check table state
                error_log("✅ DIRECT INSERT SUCCESS: Deduction ID = " . $deduction_id);

                // Log current state of receipt_deductions table for this receipt
                SQLDebugger::logTableState($this->db, 'receipt_deductions', 'receipt_number_id = ?', [$receiptId], "AFTER_INSERT_RECEIPT_{$receiptId}");

                // Update receipt calculations manually
                $this->updateReceiptCalculationsManual($receiptId);

                $this->db->commit();

                $result = [
                    'deduction_id' => $deduction_id,
                    'success' => 1,
                    'message' => 'Deduction added successfully (direct insert)'
                ];

                // DEBUG: Log final result
                error_log("Direct Insert Result: " . json_encode($result, JSON_PRETTY_PRINT));
            }

            return [
                'success' => (bool)$result['success'],
                'message' => $result['message'],
                'deduction_id' => $result['deduction_id']
            ];

        } catch (Exception $e) {
            error_log("Error adding deduction: " . $e->getMessage());
            return [
                'success' => false,
                'message' => 'เกิดข้อผิดพลาดในการเพิ่มรายการหัก: ' . $e->getMessage()
            ];
        }
    }
    
    /**
     * Get all deductions for a receipt
     */
    public function getReceiptDeductions($receiptId) {
        try {
            $stmt = $this->db->prepare("
                SELECT
                    rd.*,
                    u.name as created_by_name
                FROM receipt_deductions rd
                LEFT JOIN users u ON rd.created_by = u.id
                WHERE rd.receipt_number_id = ?
                ORDER BY rd.receipt_index ASC, rd.created_at ASC
            ");

            $stmt->execute([$receiptId]);
            return $stmt->fetchAll(PDO::FETCH_ASSOC);
            
        } catch (Exception $e) {
            error_log("Error getting receipt deductions: " . $e->getMessage());
            return [];
        }
    }
    
    /**
     * Update a deduction
     */
    public function updateDeduction($deductionId, $deductionData, $userId) {
        try {
            $this->db->beginTransaction();
            
            // Update deduction
            $stmt = $this->db->prepare("
                UPDATE receipt_deductions 
                SET 
                    deduction_type = ?,
                    amount = ?,
                    percentage = ?,
                    description = ?,
                    is_percentage_based = ?,
                    updated_by = ?,
                    updated_at = CURRENT_TIMESTAMP
                WHERE id = ?
            ");
            
            $stmt->execute([
                $deductionData['deduction_type'],
                $deductionData['amount'] ?? 0,
                $deductionData['percentage'] ?? null,
                $deductionData['description'] ?? '',
                $deductionData['is_percentage_based'] ?? false,
                $userId,
                $deductionId
            ]);
            
            $this->db->commit();
            
            return [
                'success' => true,
                'message' => 'อัพเดตรายการหักเรียบร้อยแล้ว'
            ];
            
        } catch (Exception $e) {
            $this->db->rollback();
            error_log("Error updating deduction: " . $e->getMessage());
            return [
                'success' => false,
                'message' => 'เกิดข้อผิดพลาดในการอัพเดตรายการหัก: ' . $e->getMessage()
            ];
        }
    }
    
    /**
     * Delete a deduction
     */
    public function deleteDeduction($deductionId, $userId) {
        try {
            $stmt = $this->db->prepare("
                DELETE FROM receipt_deductions 
                WHERE id = ?
            ");
            
            $stmt->execute([$deductionId]);
            
            return [
                'success' => true,
                'message' => 'ลบรายการหักเรียบร้อยแล้ว'
            ];
            
        } catch (Exception $e) {
            error_log("Error deleting deduction: " . $e->getMessage());
            return [
                'success' => false,
                'message' => 'เกิดข้อผิดพลาดในการลบรายการหัก: ' . $e->getMessage()
            ];
        }
    }
    
    /**
     * Get receipt summary with deductions
     */
    public function getReceiptSummary($receiptId) {
        try {
            $stmt = $this->db->prepare("
                SELECT * FROM receipt_summary 
                WHERE id = ?
            ");
            
            $stmt->execute([$receiptId]);
            return $stmt->fetch(PDO::FETCH_ASSOC);
            
        } catch (Exception $e) {
            error_log("Error getting receipt summary: " . $e->getMessage());
            return null;
        }
    }
    
    /**
     * Calculate deduction amount from percentage
     */
    public function calculatePercentageDeduction($grossAmount, $percentage) {
        return round(($grossAmount * $percentage / 100), 2);
    }
    
    /**
     * Update receipt gross amount and recalculate
     */
    public function updateReceiptGrossAmount($receiptId, $grossAmount, $userId) {
        try {
            $this->db->beginTransaction();
            
            // Update gross amount
            $stmt = $this->db->prepare("
                UPDATE receipt_numbers 
                SET gross_amount = ?
                WHERE id = ?
            ");
            
            $stmt->execute([$grossAmount, $receiptId]);
            
            // Recalculate all percentage-based deductions
            $stmt = $this->db->prepare("
                SELECT id, percentage 
                FROM receipt_deductions 
                WHERE receipt_number_id = ? AND is_percentage_based = 1
            ");
            
            $stmt->execute([$receiptId]);
            $percentageDeductions = $stmt->fetchAll(PDO::FETCH_ASSOC);
            
            foreach ($percentageDeductions as $deduction) {
                $newAmount = $this->calculatePercentageDeduction($grossAmount, $deduction['percentage']);
                
                $updateStmt = $this->db->prepare("
                    UPDATE receipt_deductions 
                    SET amount = ?, updated_by = ?, updated_at = CURRENT_TIMESTAMP
                    WHERE id = ?
                ");
                
                $updateStmt->execute([$newAmount, $userId, $deduction['id']]);
            }
            
            // Update calculations will be triggered by the trigger
            
            $this->db->commit();
            
            return [
                'success' => true,
                'message' => 'อัพเดตยอดก่อนหักเรียบร้อยแล้ว'
            ];
            
        } catch (Exception $e) {
            $this->db->rollback();
            error_log("Error updating gross amount: " . $e->getMessage());
            return [
                'success' => false,
                'message' => 'เกิดข้อผิดพลาดในการอัพเดตยอดก่อนหัก: ' . $e->getMessage()
            ];
        }
    }
    
    /**
     * Get deduction statistics for reporting
     */
    public function getDeductionStatistics($expenseId = null, $dateFrom = null, $dateTo = null) {
        try {
            $whereConditions = [];
            $params = [];
            
            if ($expenseId) {
                $whereConditions[] = "rn.expense_id = ?";
                $params[] = $expenseId;
            }
            
            if ($dateFrom) {
                $whereConditions[] = "DATE(rd.created_at) >= ?";
                $params[] = $dateFrom;
            }
            
            if ($dateTo) {
                $whereConditions[] = "DATE(rd.created_at) <= ?";
                $params[] = $dateTo;
            }
            
            $whereClause = !empty($whereConditions) ? 'WHERE ' . implode(' AND ', $whereConditions) : '';
            
            $stmt = $this->db->prepare("
                SELECT 
                    rd.deduction_type,
                    COUNT(*) as count,
                    SUM(rd.amount) as total_amount,
                    AVG(rd.amount) as avg_amount,
                    MIN(rd.amount) as min_amount,
                    MAX(rd.amount) as max_amount
                FROM receipt_deductions rd
                JOIN receipt_numbers rn ON rd.receipt_number_id = rn.id
                {$whereClause}
                GROUP BY rd.deduction_type
                ORDER BY total_amount DESC
            ");
            
            $stmt->execute($params);
            return $stmt->fetchAll(PDO::FETCH_ASSOC);
            
        } catch (Exception $e) {
            error_log("Error getting deduction statistics: " . $e->getMessage());
            return [];
        }
    }
    
    /**
     * Update receipt calculations manually (fallback for MariaDB 5.5)
     */
    private function updateReceiptCalculationsManual($receiptId) {
        try {
            // Get gross amount
            $stmt = $this->db->prepare("SELECT COALESCE(gross_amount, amount, 0) as gross_amount FROM receipt_numbers WHERE id = ?");
            $stmt->execute([$receiptId]);
            $receipt = $stmt->fetch(PDO::FETCH_ASSOC);
            $gross_amount = $receipt ? $receipt['gross_amount'] : 0;

            // Calculate total deductions
            $stmt = $this->db->prepare("SELECT COALESCE(SUM(amount), 0) as total_deductions FROM receipt_deductions WHERE receipt_number_id = ?");
            $stmt->execute([$receiptId]);
            $deduction_result = $stmt->fetch(PDO::FETCH_ASSOC);
            $total_deductions = $deduction_result ? $deduction_result['total_deductions'] : 0;

            // Calculate net amount
            $net_amount = $gross_amount - $total_deductions;
            $has_deductions = $total_deductions > 0;

            // Update receipt_numbers table
            $stmt = $this->db->prepare("
                UPDATE receipt_numbers
                SET has_deductions = ?, net_amount_calculated = ?
                WHERE id = ?
            ");
            $stmt->execute([$has_deductions ? 1 : 0, $net_amount, $receiptId]);

            error_log("Manual calculation update: Receipt ID={$receiptId}, Gross={$gross_amount}, Deductions={$total_deductions}, Net={$net_amount}");

        } catch (Exception $e) {
            error_log("Error in manual calculation update: " . $e->getMessage());
            throw $e;
        }
    }

    /**
     * Validate deduction data
     */
    public function validateDeductionData($data) {
        $errors = [];
        
        // Check required fields
        if (empty($data['deduction_type'])) {
            $errors[] = 'กรุณาเลือกประเภทการหัก';
        }
        
        if (!isset($data['amount']) && !isset($data['percentage'])) {
            $errors[] = 'กรุณาระบุจำนวนเงินหรือเปอร์เซ็นต์';
        }
        
        // Validate deduction type
        $validTypes = array_keys(self::getDeductionTypes());
        if (!empty($data['deduction_type']) && !in_array($data['deduction_type'], $validTypes)) {
            $errors[] = 'ประเภทการหักไม่ถูกต้อง';
        }
        
        // Validate amount
        if (isset($data['amount']) && (!is_numeric($data['amount']) || $data['amount'] < 0)) {
            $errors[] = 'จำนวนเงินต้องเป็นตัวเลขและมากกว่าหรือเท่ากับ 0';
        }
        
        // Validate percentage
        if (isset($data['percentage']) && (!is_numeric($data['percentage']) || $data['percentage'] < 0 || $data['percentage'] > 100)) {
            $errors[] = 'เปอร์เซ็นต์ต้องอยู่ระหว่าง 0-100';
        }
        
        return [
            'valid' => empty($errors),
            'errors' => $errors
        ];
    }
}
?>
