<?php
/**
 * SQL Debugger Class
 * 
 * This class helps debug SQL statements by logging them with parameters
 */

class SQLDebugger {
    
    /**
     * Log SQL statement with parameters
     */
    public static function logSQL($sql, $params = [], $context = '') {
        $timestamp = date('Y-m-d H:i:s');
        $contextStr = $context ? " [{$context}]" : '';
        
        error_log("=== SQL DEBUG{$contextStr} @ {$timestamp} ===");
        error_log("SQL: " . self::formatSQL($sql));
        
        if (!empty($params)) {
            error_log("Parameters:");
            foreach ($params as $index => $param) {
                $type = gettype($param);
                $value = $param === null ? 'NULL' : $param;
                if (is_bool($param)) {
                    $value = $param ? 'TRUE' : 'FALSE';
                }
                error_log("  [{$index}] ({$type}): {$value}");
            }
        } else {
            error_log("Parameters: None");
        }
        
        error_log("=== END SQL DEBUG ===");
    }
    
    /**
     * Format SQL for better readability
     */
    private static function formatSQL($sql) {
        // Remove extra whitespace and format for logging
        $sql = preg_replace('/\s+/', ' ', trim($sql));
        return $sql;
    }
    
    /**
     * Log SQL execution result
     */
    public static function logResult($result, $context = '') {
        $timestamp = date('Y-m-d H:i:s');
        $contextStr = $context ? " [{$context}]" : '';
        
        error_log("=== SQL RESULT{$contextStr} @ {$timestamp} ===");
        
        if (is_array($result)) {
            error_log("Result: " . json_encode($result, JSON_PRETTY_PRINT));
        } else {
            error_log("Result: " . $result);
        }
        
        error_log("=== END SQL RESULT ===");
    }
    
    /**
     * Create a debug wrapper for PDO statements
     */
    public static function executeWithDebug($stmt, $params = [], $context = '') {
        // Get the SQL from the statement (this is a bit tricky with PDO)
        $sql = $stmt->queryString ?? 'Unknown SQL';
        
        self::logSQL($sql, $params, $context);
        
        try {
            $result = $stmt->execute($params);
            
            if ($result) {
                error_log("✅ SQL executed successfully" . ($context ? " [{$context}]" : ''));
                
                // Try to get affected rows or last insert ID
                $rowCount = $stmt->rowCount();
                if ($rowCount > 0) {
                    error_log("Affected rows: {$rowCount}");
                }
                
                // If it's an INSERT, try to get last insert ID
                if (stripos($sql, 'INSERT') === 0) {
                    try {
                        $lastId = $stmt->getConnection()->lastInsertId();
                        if ($lastId) {
                            error_log("Last Insert ID: {$lastId}");
                        }
                    } catch (Exception $e) {
                        // Ignore if we can't get last insert ID
                    }
                }
            } else {
                error_log("❌ SQL execution failed" . ($context ? " [{$context}]" : ''));
            }
            
            return $result;
            
        } catch (Exception $e) {
            error_log("❌ SQL execution error" . ($context ? " [{$context}]" : '') . ": " . $e->getMessage());
            throw $e;
        }
    }
    
    /**
     * Enable SQL debugging for a PDO connection
     */
    public static function enablePDODebugging($pdo) {
        // This would require extending PDO class, which is complex
        // For now, we'll use manual logging in our methods
        error_log("SQL debugging enabled for PDO connection");
    }
    
    /**
     * Log database table state
     */
    public static function logTableState($pdo, $tableName, $whereClause = '', $params = [], $context = '') {
        try {
            $sql = "SELECT * FROM {$tableName}";
            if ($whereClause) {
                $sql .= " WHERE {$whereClause}";
            }
            $sql .= " ORDER BY id DESC LIMIT 10";
            
            $stmt = $pdo->prepare($sql);
            $stmt->execute($params);
            $rows = $stmt->fetchAll(PDO::FETCH_ASSOC);
            
            $timestamp = date('Y-m-d H:i:s');
            $contextStr = $context ? " [{$context}]" : '';
            
            error_log("=== TABLE STATE{$contextStr} @ {$timestamp} ===");
            error_log("Table: {$tableName}");
            error_log("Query: {$sql}");
            error_log("Rows found: " . count($rows));
            
            foreach ($rows as $index => $row) {
                error_log("Row {$index}: " . json_encode($row, JSON_PRETTY_PRINT));
            }
            
            error_log("=== END TABLE STATE ===");
            
        } catch (Exception $e) {
            error_log("Error logging table state for {$tableName}: " . $e->getMessage());
        }
    }
}
?>
