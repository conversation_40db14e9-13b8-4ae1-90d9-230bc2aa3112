<?php
session_start();
require_once 'config/database.php';

// Mock user session for testing
$_SESSION['user_id'] = 1;
$_SESSION['username'] = 'test_user';
$_SESSION['role'] = 'admin';

echo "<h2>Testing Per-Receipt Deductions System</h2>";

try {
    $database = new Database();
    $db = $database->getConnection();
    
    // Test data with multiple receipts and different deductions
    $test_data = [
        'driver_id' => 1,
        'customer_id' => 1,
        'item_id' => 1,
        'job_open_date' => date('Y-m-d'),
        'withdrawal_date' => date('Y-m-d'),
        'transfer_amount' => 2970.00, // Net amount after deductions
        'transfer_no' => 'TEST-PER-RECEIPT-' . time(),
        'receipts' => [
            [
                'receipt_number' => 'REC-001',
                'amount' => 1000.00,
                'description' => 'ค่าขนส่ง',
                'deductions' => [
                    [
                        'type' => 'tax_withholding',
                        'amount' => 30.00,
                        'percentage' => 3.00,
                        'description' => 'ภาษีหัก ณ ที่จ่าย 3%',
                        'isPercentageBased' => true,
                        'image' => 'test_deduction_1.jpg'
                    ]
                ]
            ],
            [
                'receipt_number' => 'REC-002',
                'amount' => 2000.00,
                'description' => 'ค่าน้ำมัน',
                'deductions' => [] // No deductions
            ]
        ]
    ];
    
    echo "<h3>Test Data:</h3>";
    echo "<pre>" . print_r($test_data, true) . "</pre>";
    
    $db->beginTransaction();
    
    // 1. Insert expense
    $sequence = '001';
    $exno = 'TEST-PER-RECEIPT-' . date('Ymd') . '-' . time();
    
    $stmt = $db->prepare("
        INSERT INTO expenses (sequence, exno, driver_id, customer_id, item_id, job_open_date, withdrawal_date, transfer_amount, transfer_no, created_by)
        VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
    ");
    
    $stmt->execute([
        $sequence,
        $exno,
        $test_data['driver_id'],
        $test_data['customer_id'], 
        $test_data['item_id'],
        $test_data['job_open_date'],
        $test_data['withdrawal_date'],
        $test_data['transfer_amount'],
        $test_data['transfer_no'],
        $_SESSION['user_id']
    ]);
    
    $expense_id = $db->lastInsertId();
    echo "<h3>✅ Expense Created: ID = $expense_id</h3>";
    
    // 2. Insert receipts
    $receipt_ids = [];
    foreach ($test_data['receipts'] as $index => $receipt) {
        $stmt = $db->prepare("
            INSERT INTO receipt_numbers (expense_id, receipt_number, amount, gross_amount, has_deductions, net_amount_calculated, description, created_by)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?)
        ");
        
        $stmt->execute([
            $expense_id,
            $receipt['receipt_number'],
            $receipt['amount'],
            $receipt['amount'], // gross_amount
            0, // has_deductions = false initially
            $receipt['amount'], // net_amount_calculated
            $receipt['description'],
            $_SESSION['user_id']
        ]);
        
        $receipt_id = $db->lastInsertId();
        $receipt_ids[$index] = $receipt_id;
        echo "<h4>✅ Receipt $index Created: ID = $receipt_id ({$receipt['receipt_number']})</h4>";
    }
    
    // 3. Add deductions
    require_once 'includes/ReceiptDeductionManager.php';
    $deductionManager = new ReceiptDeductionManager($db);
    
    foreach ($test_data['receipts'] as $receipt_index => $receipt) {
        if (!empty($receipt['deductions'])) {
            $receipt_id = $receipt_ids[$receipt_index];
            
            foreach ($receipt['deductions'] as $deduction) {
                $deduction_data = [
                    'deduction_type' => $deduction['type'],
                    'amount' => $deduction['amount'],
                    'percentage' => $deduction['percentage'],
                    'description' => $deduction['description'],
                    'deduction_image' => $deduction['image'],
                    'is_percentage_based' => $deduction['isPercentageBased']
                ];
                
                $result = $deductionManager->addDeduction($receipt_id, $deduction_data, $_SESSION['user_id'], $receipt_index);
                
                if ($result['success']) {
                    echo "<h5>✅ Deduction Added to Receipt $receipt_index: ID = " . ($result['deduction_id'] ?? 'Unknown') . "</h5>";
                } else {
                    echo "<h5>❌ Failed to add deduction: " . $result['message'] . "</h5>";
                }
            }
        }
    }
    
    $db->commit();
    
    // 4. Verify results
    echo "<h3>📊 Verification Results:</h3>";
    
    // Check receipt summary
    $stmt = $db->prepare("SELECT * FROM receipt_summary WHERE expense_id = ? ORDER BY receipt_id");
    $stmt->execute([$expense_id]);
    $summaries = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    $total_gross = 0;
    $total_deductions = 0;
    $total_net = 0;
    
    foreach ($summaries as $summary) {
        echo "<h4>Receipt: {$summary['receipt_number']}</h4>";
        echo "<ul>";
        echo "<li>Gross Amount: " . number_format($summary['gross_amount'], 2) . " บาท</li>";
        echo "<li>Total Deductions: " . number_format($summary['total_deductions'], 2) . " บาท</li>";
        echo "<li>Net Amount: " . number_format($summary['net_amount_calculated'], 2) . " บาท</li>";
        echo "<li>Has Deductions: " . ($summary['has_deductions'] ? 'Yes' : 'No') . "</li>";
        echo "<li>Deduction Count: " . $summary['deduction_count'] . "</li>";
        echo "</ul>";
        
        $total_gross += $summary['gross_amount'];
        $total_deductions += $summary['total_deductions'];
        $total_net += $summary['net_amount_calculated'];
    }
    
    echo "<h4>Summary Totals:</h4>";
    echo "<ul>";
    echo "<li><strong>Total Gross:</strong> " . number_format($total_gross, 2) . " บาท</li>";
    echo "<li><strong>Total Deductions:</strong> " . number_format($total_deductions, 2) . " บาท</li>";
    echo "<li><strong>Total Net:</strong> " . number_format($total_net, 2) . " บาท</li>";
    echo "<li><strong>Transfer Amount:</strong> " . number_format($test_data['transfer_amount'], 2) . " บาท</li>";
    echo "</ul>";
    
    // Check if net amount matches transfer amount
    $difference = abs($total_net - $test_data['transfer_amount']);
    if ($difference < 0.01) {
        echo "<h4 style='color: green;'>✅ SUCCESS: Total net amount matches transfer amount!</h4>";
    } else {
        echo "<h4 style='color: red;'>❌ ERROR: Total net amount ($total_net) does not match transfer amount ({$test_data['transfer_amount']})</h4>";
        echo "<p>Difference: " . number_format($difference, 2) . " บาท</p>";
    }
    
    // Check individual deductions
    echo "<h4>Individual Deductions:</h4>";
    $stmt = $db->prepare("
        SELECT rd.*, rn.receipt_number 
        FROM receipt_deductions rd 
        JOIN receipt_numbers rn ON rd.receipt_number_id = rn.id 
        WHERE rn.expense_id = ? 
        ORDER BY rd.receipt_index, rd.created_at
    ");
    $stmt->execute([$expense_id]);
    $deductions = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    foreach ($deductions as $deduction) {
        echo "<div style='border: 1px solid #ddd; padding: 10px; margin: 5px 0;'>";
        echo "<strong>Receipt:</strong> {$deduction['receipt_number']} (Index: {$deduction['receipt_index']})<br>";
        echo "<strong>Type:</strong> {$deduction['deduction_type']}<br>";
        echo "<strong>Amount:</strong> " . number_format($deduction['amount'], 2) . " บาท<br>";
        if ($deduction['percentage']) {
            echo "<strong>Percentage:</strong> {$deduction['percentage']}%<br>";
        }
        echo "<strong>Description:</strong> {$deduction['description']}<br>";
        if ($deduction['deduction_image']) {
            echo "<strong>Image:</strong> {$deduction['deduction_image']}<br>";
        }
        echo "<strong>Is Percentage Based:</strong> " . ($deduction['is_percentage_based'] ? 'Yes' : 'No') . "<br>";
        echo "</div>";
    }
    
    echo "<h3 style='color: green;'>🎉 Per-Receipt Deductions Test Completed Successfully!</h3>";
    echo "<p><a href='expenses/view.php?id=$expense_id' target='_blank'>View Created Expense</a></p>";
    echo "<p><a href='expenses/create.php' target='_blank'>Test Create Form</a></p>";
    
} catch (Exception $e) {
    if (isset($db) && $db->inTransaction()) {
        $db->rollback();
    }
    echo "<h3 style='color: red;'>❌ Error: " . $e->getMessage() . "</h3>";
    echo "<pre>" . $e->getTraceAsString() . "</pre>";
}
?>
