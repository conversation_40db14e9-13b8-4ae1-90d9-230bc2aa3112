<?php
session_start();
require_once 'config/database.php';

// Mock session for testing
$_SESSION['user_id'] = 1;
$_SESSION['username'] = 'test_user';
$_SESSION['role'] = 'admin';

$database = new Database();
$db = $database->getConnection();

echo "<h2>🔍 Debug Receipt Deductions</h2>";

// Check if tables exist
echo "<h3>📋 Table Structure Check:</h3>";

try {
    $stmt = $db->query("SHOW TABLES LIKE 'receipt_deductions'");
    $table_exists = $stmt->fetch();
    
    if ($table_exists) {
        echo "✅ Table 'receipt_deductions' exists<br>";
        
        // Show table structure
        $stmt = $db->query("DESCRIBE receipt_deductions");
        $columns = $stmt->fetchAll();
        echo "<h4>Table Structure:</h4>";
        echo "<table border='1'>";
        echo "<tr><th>Field</th><th>Type</th><th>Null</th><th>Key</th><th>Default</th></tr>";
        foreach ($columns as $col) {
            echo "<tr>";
            echo "<td>" . $col['Field'] . "</td>";
            echo "<td>" . $col['Type'] . "</td>";
            echo "<td>" . $col['Null'] . "</td>";
            echo "<td>" . $col['Key'] . "</td>";
            echo "<td>" . $col['Default'] . "</td>";
            echo "</tr>";
        }
        echo "</table>";
        
    } else {
        echo "❌ Table 'receipt_deductions' does not exist<br>";
    }
} catch (Exception $e) {
    echo "❌ Error checking table: " . $e->getMessage() . "<br>";
}

// Check receipt_numbers data
echo "<h3>📄 Receipt Numbers Data:</h3>";
try {
    $stmt = $db->query("SELECT id, expense_id, receipt_number, amount FROM receipt_numbers LIMIT 5");
    $receipts = $stmt->fetchAll();
    
    if ($receipts) {
        echo "<table border='1'>";
        echo "<tr><th>ID</th><th>Expense ID</th><th>Receipt Number</th><th>Amount</th></tr>";
        foreach ($receipts as $receipt) {
            echo "<tr>";
            echo "<td>" . $receipt['id'] . "</td>";
            echo "<td>" . $receipt['expense_id'] . "</td>";
            echo "<td>" . $receipt['receipt_number'] . "</td>";
            echo "<td>" . number_format($receipt['amount'], 2) . "</td>";
            echo "</tr>";
        }
        echo "</table>";
    } else {
        echo "❌ No receipt numbers found<br>";
    }
} catch (Exception $e) {
    echo "❌ Error checking receipts: " . $e->getMessage() . "<br>";
}

// Check deductions data
echo "<h3>🧮 Receipt Deductions Data:</h3>";
try {
    $stmt = $db->query("SELECT COUNT(*) as count FROM receipt_deductions");
    $count = $stmt->fetch();
    echo "Total deductions: " . $count['count'] . "<br>";
    
    if ($count['count'] > 0) {
        $stmt = $db->query("SELECT * FROM receipt_deductions LIMIT 5");
        $deductions = $stmt->fetchAll();
        
        echo "<table border='1'>";
        echo "<tr><th>ID</th><th>Receipt Number ID</th><th>Type</th><th>Amount</th><th>Description</th></tr>";
        foreach ($deductions as $deduction) {
            echo "<tr>";
            echo "<td>" . $deduction['id'] . "</td>";
            echo "<td>" . $deduction['receipt_number_id'] . "</td>";
            echo "<td>" . $deduction['deduction_type'] . "</td>";
            echo "<td>" . number_format($deduction['amount'], 2) . "</td>";
            echo "<td>" . ($deduction['description'] ?? 'N/A') . "</td>";
            echo "</tr>";
        }
        echo "</table>";
    } else {
        echo "❌ No deductions found<br>";
        echo "<br><strong>💡 This is why Receipt Deductions section is not showing!</strong><br>";
    }
} catch (Exception $e) {
    echo "❌ Error checking deductions: " . $e->getMessage() . "<br>";
}

// Test specific expense
echo "<h3>🎯 Test Expense ID 1:</h3>";
try {
    // Get receipt numbers for expense 1
    $stmt = $db->prepare("SELECT * FROM receipt_numbers WHERE expense_id = ?");
    $stmt->execute([1]);
    $receipt_numbers = $stmt->fetchAll();
    
    echo "Receipt numbers for expense 1: " . count($receipt_numbers) . "<br>";
    
    if (!empty($receipt_numbers)) {
        $receipt_ids = array_column($receipt_numbers, 'id');
        echo "Receipt IDs: " . implode(', ', $receipt_ids) . "<br>";
        
        // Get deductions for these receipts
        $placeholders = str_repeat('?,', count($receipt_ids) - 1) . '?';
        $stmt = $db->prepare("SELECT * FROM receipt_deductions WHERE receipt_number_id IN ($placeholders)");
        $stmt->execute($receipt_ids);
        $deductions = $stmt->fetchAll();
        
        echo "Deductions found: " . count($deductions) . "<br>";
        
        if (empty($deductions)) {
            echo "<br><strong>🎯 Solution: Need to add sample deductions!</strong><br>";
        }
    }
} catch (Exception $e) {
    echo "❌ Error testing expense 1: " . $e->getMessage() . "<br>";
}

echo "<h3>🛠️ Create Sample Deduction:</h3>";
try {
    // Get first receipt
    $stmt = $db->query("SELECT id FROM receipt_numbers LIMIT 1");
    $receipt = $stmt->fetch();
    
    if ($receipt) {
        $receipt_id = $receipt['id'];
        
        // Check if deduction already exists
        $stmt = $db->prepare("SELECT COUNT(*) as count FROM receipt_deductions WHERE receipt_number_id = ?");
        $stmt->execute([$receipt_id]);
        $existing = $stmt->fetch();
        
        if ($existing['count'] == 0) {
            // Insert sample deduction
            $stmt = $db->prepare("
                INSERT INTO receipt_deductions 
                (receipt_number_id, deduction_type, amount, description, created_by) 
                VALUES (?, ?, ?, ?, ?)
            ");
            
            $stmt->execute([
                $receipt_id,
                'tax_vat',
                150.00,
                'Sample VAT deduction for testing',
                1
            ]);
            
            echo "✅ Sample deduction created for receipt ID: $receipt_id<br>";
            echo "💡 Now refresh the view.php page to see the deductions!<br>";
        } else {
            echo "✅ Deduction already exists for receipt ID: $receipt_id<br>";
        }
    } else {
        echo "❌ No receipts found to add deduction<br>";
    }
} catch (Exception $e) {
    echo "❌ Error creating sample deduction: " . $e->getMessage() . "<br>";
}
?>
