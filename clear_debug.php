<?php
/**
 * Clear Debug File
 */

// Check if user is logged in (basic security)
session_start();
if (!isset($_SESSION['user_id'])) {
    http_response_code(403);
    die('Access denied');
}

// Only allow POST requests
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    die('Method not allowed');
}

// Clear the debug file
$debugFile = __DIR__ . '/debug_sql.log';

try {
    if (file_exists($debugFile)) {
        file_put_contents($debugFile, '');
        echo 'Debug file cleared successfully';
    } else {
        echo 'Debug file does not exist';
    }
} catch (Exception $e) {
    http_response_code(500);
    echo 'Error clearing debug file: ' . $e->getMessage();
}
?>
