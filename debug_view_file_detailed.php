<?php
// Enable all error reporting
error_reporting(E_ALL);
ini_set('display_errors', 1);
ini_set('log_errors', 1);

session_start();

// Simulate logged in user for testing
if (!isset($_SESSION['user_id'])) {
    $_SESSION['user_id'] = 1;
    $_SESSION['role'] = 'administrator';
    $_SESSION['username'] = 'test_user';
}

echo "<h2>Detailed Debug for view_file.php</h2>";

$test_file = $_GET['file'] ?? 'transfer_68fcafb1efdee_1761390513.jpg';
$test_type = $_GET['type'] ?? 'transfer_slip';

echo "<h3>Testing: $test_file (Type: $test_type)</h3>";

// Test the exact same logic as view_file.php
echo "<h3>Exact view_file.php Logic Test:</h3>";

// Step 1: Session check
echo "<p><strong>1. Session Check:</strong></p>";
if (!isset($_SESSION['user_id'])) {
    echo "<p style='color: red;'>❌ FAIL: No user session</p>";
    exit;
} else {
    echo "<p style='color: green;'>✅ PASS: User ID = {$_SESSION['user_id']}, Role = {$_SESSION['role']}</p>";
}

// Step 2: Parameters
echo "<p><strong>2. Parameters:</strong></p>";
$file = $_GET['file'] ?? $test_file;
$type = $_GET['type'] ?? $test_type;

echo "<p>Raw \$_GET['file']: '" . ($_GET['file'] ?? 'NOT SET') . "'</p>";
echo "<p>Raw \$_GET['type']: '" . ($_GET['type'] ?? 'NOT SET') . "'</p>";
echo "<p>Using defaults - file: '$file', type: '$type'</p>";

if (empty($file) || empty($type)) {
    echo "<p style='color: red;'>❌ FAIL: Missing parameters</p>";
    echo "<p>empty(\$file): " . (empty($file) ? 'true' : 'false') . "</p>";
    echo "<p>empty(\$type): " . (empty($type) ? 'true' : 'false') . "</p>";
    exit;
} else {
    echo "<p style='color: green;'>✅ PASS: file='$file', type='$type'</p>";
}

// Step 3: User info
echo "<p><strong>3. User Info:</strong></p>";
$user_id = $_SESSION['user_id'];
$user_role = $_SESSION['role'] ?? 'data_entry';
echo "<p>User ID: $user_id, Role: $user_role</p>";

// Step 4: Filename sanitization
echo "<p><strong>4. Filename Sanitization:</strong></p>";
if ($type === 'batch_document') {
    $file = str_replace(['../', '../', '..\\'], '', $file);
    if (!preg_match('/^[a-zA-Z0-9_\-\.\/\\\\]+$/', $file)) {
        echo "<p style='color: red;'>❌ FAIL: Invalid batch document filename</p>";
        exit;
    }
} else {
    $file = basename($file);
    if (!preg_match('/^[a-zA-Z0-9_\-\.]+$/', $file)) {
        echo "<p style='color: red;'>❌ FAIL: Invalid filename</p>";
        exit;
    }
}
echo "<p style='color: green;'>✅ PASS: Sanitized filename = '$file'</p>";

// Step 5: Upload directory
echo "<p><strong>5. Upload Directory:</strong></p>";
$upload_dir = dirname(__DIR__) . '/uploads/';
echo "<p>Upload dir: $upload_dir</p>";
echo "<p>Upload dir exists: " . (is_dir($upload_dir) ? '✅ YES' : '❌ NO') . "</p>";
echo "<p>Upload dir readable: " . (is_readable($upload_dir) ? '✅ YES' : '❌ NO') . "</p>";

// Step 6: File path determination
echo "<p><strong>6. File Path Determination:</strong></p>";
if ($type === 'transfer_slip') {
    $file_path = $upload_dir . 'transfer_slips/' . $file;
} elseif ($type === 'verification_slip') {
    $file_path = $upload_dir . 'verification_slips/' . $file;
} elseif ($type === 'reviewer_slip') {
    $file_path = $upload_dir . 'review_slips/' . $file;
} elseif ($type === 'bulk_operation_slip') {
    $file_path = $upload_dir . 'bulk_operations/' . $file;
} elseif ($type === 'batch_document') {
    $possible_paths = [
        $upload_dir . $file,
        $upload_dir . 'batch_documents/' . basename($file),
        $upload_dir . 'batch_documents/verification/' . basename($file),
        $upload_dir . 'batch_documents/review/' . basename($file),
        $upload_dir . 'bulk_operations/' . basename($file)
    ];

    $file_path = null;
    foreach ($possible_paths as $path) {
        if (file_exists($path)) {
            $file_path = $path;
            break;
        }
    }

    if (!$file_path) {
        $file_path = $upload_dir . $file;
    }
} elseif ($type === 'receipt') {
    $file_path = $upload_dir . 'receipts/' . $file;
} elseif ($type === 'deduction') {
    $file_path = $upload_dir . 'deductions/' . $file;
} else {
    $file_path = $upload_dir . 'receipts/' . $file;
}

echo "<p>Calculated file path: $file_path</p>";
echo "<p>Absolute path: " . realpath($file_path) . "</p>";

// Step 7: File existence check
echo "<p><strong>7. File Existence Check:</strong></p>";
if (!file_exists($file_path) || !is_file($file_path)) {
    echo "<p style='color: red;'>❌ FAIL: File not found or not a file</p>";
    echo "<p>file_exists(): " . (file_exists($file_path) ? 'true' : 'false') . "</p>";
    echo "<p>is_file(): " . (is_file($file_path) ? 'true' : 'false') . "</p>";
    
    // Check directory
    $dir_path = dirname($file_path);
    echo "<p>Directory: $dir_path</p>";
    echo "<p>Directory exists: " . (is_dir($dir_path) ? '✅ YES' : '❌ NO') . "</p>";
    
    if (is_dir($dir_path)) {
        echo "<p>Files in directory:</p>";
        $files = scandir($dir_path);
        echo "<ul>";
        foreach (array_slice($files, 0, 10) as $f) {
            if ($f !== '.' && $f !== '..') {
                echo "<li>$f</li>";
            }
        }
        echo "</ul>";
    }
    exit;
} else {
    echo "<p style='color: green;'>✅ PASS: File exists and is a file</p>";
    echo "<p>File size: " . filesize($file_path) . " bytes</p>";
    echo "<p>File permissions: " . substr(sprintf('%o', fileperms($file_path)), -4) . "</p>";
    echo "<p>File readable: " . (is_readable($file_path) ? '✅ YES' : '❌ NO') . "</p>";
}

// Step 8: Access control
echo "<p><strong>8. Access Control:</strong></p>";
$has_access = true;

if ($user_role === 'data_entry' && $type !== 'batch_document') {
    try {
        require_once 'config/database.php';
        $database = new Database();
        $db = $database->getConnection();
        
        echo "<p>Database connection: ✅ SUCCESS</p>";
        
        if ($type === 'receipt') {
            $stmt = $db->prepare("
                SELECT e.id, e.created_by
                FROM expenses e
                JOIN receipt_numbers rn ON e.id = rn.expense_id
                WHERE rn.receipt_image = ?
            ");
            $stmt->execute([$file]);
            $expense = $stmt->fetch();
            
            if ($expense) {
                $has_access = ($expense['created_by'] == $user_id);
                echo "<p>Found expense: ID {$expense['id']}, Created by: {$expense['created_by']}</p>";
            } else {
                echo "<p>No expense found for receipt</p>";
            }
        } else {
            $stmt = $db->prepare("
                SELECT id, created_by
                FROM expenses
                WHERE transfer_slip_image = ? OR verification_slip_image = ? OR reviewer_slip_image = ?
            ");
            $stmt->execute([$file, $file, $file]);
            $expense = $stmt->fetch();
            
            if ($expense) {
                $has_access = ($expense['created_by'] == $user_id);
                echo "<p>Found expense: ID {$expense['id']}, Created by: {$expense['created_by']}</p>";
            } else {
                echo "<p>No expense found for this file</p>";
            }
        }
    } catch (Exception $e) {
        echo "<p style='color: orange;'>⚠️ Database error: " . $e->getMessage() . "</p>";
        $has_access = true;
    }
} else {
    echo "<p>Access control bypassed (admin or batch_document)</p>";
}

echo "<p>Has access: " . ($has_access ? '✅ YES' : '❌ NO') . "</p>";

if (!$has_access) {
    echo "<p style='color: red;'>❌ FAIL: Access denied</p>";
    exit;
}

// Step 9: File info and content type
echo "<p><strong>9. File Info and Content Type:</strong></p>";
$file_info = pathinfo($file_path);
$file_extension = strtolower($file_info['extension']);

$content_types = [
    'jpg' => 'image/jpeg',
    'jpeg' => 'image/jpeg',
    'png' => 'image/png',
    'gif' => 'image/gif',
    'pdf' => 'application/pdf'
];

$content_type = $content_types[$file_extension] ?? 'application/octet-stream';

echo "<p>File extension: $file_extension</p>";
echo "<p>Content type: $content_type</p>";

// Step 10: Test file reading
echo "<p><strong>10. File Reading Test:</strong></p>";
$file_content = file_get_contents($file_path);
if ($file_content === false) {
    echo "<p style='color: red;'>❌ FAIL: Cannot read file content</p>";
    exit;
} else {
    echo "<p style='color: green;'>✅ PASS: File content readable (" . strlen($file_content) . " bytes)</p>";
}

// Step 11: Headers test
echo "<p><strong>11. Headers Test:</strong></p>";
if (headers_sent($file, $line)) {
    echo "<p style='color: red;'>❌ Headers already sent in $file at line $line</p>";
} else {
    echo "<p style='color: green;'>✅ Headers not sent yet</p>";
}

// Step 12: Test actual file output
echo "<p><strong>12. Test File Output:</strong></p>";

if (isset($_GET['output_file']) && $_GET['output_file'] === 'yes') {
    // Clear any output buffer
    while (ob_get_level()) {
        ob_end_clean();
    }
    
    // Set headers
    header('Content-Type: ' . $content_type);
    header('Content-Length: ' . filesize($file_path));
    header('Cache-Control: private, max-age=3600');
    
    // Output file
    readfile($file_path);
    exit;
}

echo "<p><a href='?file=" . urlencode($test_file) . "&type=$test_type&output_file=yes' target='_blank'>Test File Output</a></p>";

// Show image using data URL
echo "<p><strong>13. Direct Image Display (Base64):</strong></p>";
if (strpos($content_type, 'image/') === 0) {
    $base64_content = base64_encode($file_content);
    $data_url = "data:$content_type;base64,$base64_content";
    echo "<img src='$data_url' style='max-width: 300px; border: 1px solid #ccc;' alt='Direct Base64 Image'>";
} else {
    echo "<p>Not an image file</p>";
}

// Test form
echo "<h3>Test Different File:</h3>";
echo "<form method='GET' style='background: #f9f9f9; padding: 15px; border: 1px solid #ddd;'>";
echo "<label>File: <input type='text' name='file' value='$test_file' style='width: 400px;'></label><br><br>";
echo "<label>Type: <input type='text' name='type' value='$test_type' style='width: 200px;'></label><br><br>";
echo "<input type='submit' value='Debug File' style='padding: 10px 20px;'>";
echo "</form>";
?>
