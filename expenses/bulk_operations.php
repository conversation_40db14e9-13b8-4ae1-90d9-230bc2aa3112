<?php
session_start();
require_once '../config/database.php';
require_once '../includes/functions.php';

// Check if user is logged in
if (!isset($_SESSION['user_id'])) {
    header('Location: ../login.php');
    exit();
}

// Initialize database connection
$database = new Database();
$db = $database->getConnection();

// Check if user has permission for bulk operations (verification, reviewer, or administrator)
$user_role = $_SESSION['role'];
if (!in_array($user_role, ['verification', 'reviewer', 'administrator'])) {
    header('Location: ../dashboard.php?error=access_denied');
    exit();
}

$user_id = $_SESSION['user_id'];
$success = '';
$error = '';

// Handle bulk operations
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['action'])) {
    try {
        $action = $_POST['action'];
        $expense_ids = $_POST['expense_ids'] ?? [];

        if (empty($expense_ids)) {
            throw new Exception('Please select at least one expense to process.');
        }

        // Validate expense IDs
        $expense_ids = array_filter(array_map('intval', $expense_ids));
        if (empty($expense_ids)) {
            throw new Exception('Invalid expense selection.');
        }

        // Handle file upload if provided
        $bulk_slip_path = null;
        $batch_id = null;
        if (isset($_FILES['bulk_slip']) && $_FILES['bulk_slip']['error'] === UPLOAD_ERR_OK) {
            require_once '../includes/ImageUploadHelper.php';
            require_once '../includes/ImageCompressor.php';

            // Generate batch ID for this bulk operation
            $batch_id = 'BULK_' . date('Ymd_His') . '_' . $user_id;

            // Upload and compress the slip
            $upload_result = ImageUploadHelper::uploadBulkOperationSlip($_FILES['bulk_slip'], $batch_id);

            if (!$upload_result['success']) {
                throw new Exception('Failed to upload slip: ' . $upload_result['error']);
            }

            $bulk_slip_path = 'bulk_operations/' . $upload_result['filename'];

            // Log compression results
            error_log("Bulk operation slip compressed: {$upload_result['filename']} - " .
                     "Original: " . ImageCompressor::formatFileSize($upload_result['original_size']) .
                     ", Compressed: " . ImageCompressor::formatFileSize($upload_result['compressed_size']) .
                     " (Saved {$upload_result['compression_ratio']}%)");
        }
        
        $db->beginTransaction();
        
        $processed_count = 0;
        $skipped_count = 0;
        
        foreach ($expense_ids as $expense_id) {
            // Get expense details
            $stmt = $db->prepare("SELECT * FROM expenses WHERE id = ?");
            $stmt->execute([$expense_id]);
            $expense = $stmt->fetch();
            
            if (!$expense) {
                $skipped_count++;
                continue;
            }
            
            // Check permissions and valid status transitions
            $can_process = false;
            $new_status = '';
            $update_fields = [];
            
            if ($user_role === 'verification') {
                if ($action === 'verify' && $expense['status'] === 'open') {
                    $can_process = true;
                    $new_status = 'pending';
                    $update_fields = [
                        'status' => 'pending',
                        'verification_by' => $user_id,
                        'verification_date' => date('Y-m-d H:i:s'),
                        'verification_transfer_no' => $_POST['verification_transfer_no'] ?? $expense['transfer_no']
                    ];

                    // Add bulk slip if uploaded
                    if ($bulk_slip_path) {
                        $update_fields['verification_slip_image'] = $bulk_slip_path;
                        $update_fields['bulk_operation_batch_id'] = $batch_id;
                    }
                } elseif ($action === 'return' && $expense['status'] === 'pending') {
                    $can_process = true;
                    $new_status = 'open';
                    $update_fields = [
                        'status' => 'open',
                        'verification_by' => $user_id,
                        'verification_date' => date('Y-m-d H:i:s'),
                        'verification_comments' => $_POST['bulk_comments'] ?? 'Returned for corrections'
                    ];

                    // Add bulk slip if uploaded
                    if ($bulk_slip_path) {
                        $update_fields['verification_slip_image'] = $bulk_slip_path;
                        $update_fields['bulk_operation_batch_id'] = $batch_id;
                    }
                }
            } elseif ($user_role === 'reviewer' || $user_role === 'administrator') {
                if ($action === 'approve' && $expense['status'] === 'pending') {
                    $can_process = true;
                    $new_status = 'success';
                    $update_fields = [
                        'status' => 'success',
                        'reviewer_by' => $user_id,
                        'reviewer_date' => date('Y-m-d H:i:s'),
                        'reviewer_transfer_no' => $_POST['reviewer_transfer_no'] ?? $expense['transfer_no']
                    ];

                    // Add bulk slip if uploaded
                    if ($bulk_slip_path) {
                        $update_fields['reviewer_slip_image'] = $bulk_slip_path;
                        $update_fields['bulk_operation_batch_id'] = $batch_id;
                    }
                } elseif ($action === 'reject' && $expense['status'] === 'pending') {
                    $can_process = true;
                    $new_status = 'rejected';
                    $update_fields = [
                        'status' => 'rejected',
                        'reviewer_by' => $user_id,
                        'reviewer_date' => date('Y-m-d H:i:s'),
                        'reviewer_comments' => $_POST['bulk_comments'] ?? 'Rejected'
                    ];

                    // Add bulk slip if uploaded
                    if ($bulk_slip_path) {
                        $update_fields['reviewer_slip_image'] = $bulk_slip_path;
                        $update_fields['bulk_operation_batch_id'] = $batch_id;
                    }
                }

                // Administrator can also do verification actions
                if ($user_role === 'administrator') {
                    if ($action === 'verify' && $expense['status'] === 'open') {
                        $can_process = true;
                        $new_status = 'pending';
                        $update_fields = [
                            'status' => 'pending',
                            'verification_by' => $user_id,
                            'verification_date' => date('Y-m-d H:i:s'),
                            'verification_transfer_no' => $_POST['verification_transfer_no'] ?? $expense['transfer_no']
                        ];

                        // Add bulk slip if uploaded
                        if ($bulk_slip_path) {
                            $update_fields['verification_slip_image'] = $bulk_slip_path;
                            $update_fields['bulk_operation_batch_id'] = $batch_id;
                        }
                    } elseif ($action === 'return' && $expense['status'] === 'pending') {
                        $can_process = true;
                        $new_status = 'open';
                        $update_fields = [
                            'status' => 'open',
                            'verification_by' => $user_id,
                            'verification_date' => date('Y-m-d H:i:s'),
                            'verification_comments' => $_POST['bulk_comments'] ?? 'Returned for corrections'
                        ];

                        // Add bulk slip if uploaded
                        if ($bulk_slip_path) {
                            $update_fields['verification_slip_image'] = $bulk_slip_path;
                            $update_fields['bulk_operation_batch_id'] = $batch_id;
                        }
                    }
                }
            }
            
            if ($can_process) {
                // Build update query
                $set_clauses = [];
                $params = [];
                foreach ($update_fields as $field => $value) {
                    $set_clauses[] = "$field = ?";
                    $params[] = $value;
                }
                $params[] = $expense_id;
                
                $sql = "UPDATE expenses SET " . implode(', ', $set_clauses) . " WHERE id = ?";
                $stmt = $db->prepare($sql);
                $stmt->execute($params);
                
                // Log activity
                logActivity(
                    $db,
                    $user_id,
                    'update',
                    'expenses',
                    $expense_id,
                    "Bulk operation: {$action} - Status changed from {$expense['status']} to {$new_status}",
                    $_SERVER['REMOTE_ADDR'],
                    $_SERVER['HTTP_USER_AGENT']
                );
                
                $processed_count++;
            } else {
                $skipped_count++;
            }
        }
        
        $db->commit();
        
        $success = "Bulk operation completed successfully! Processed: {$processed_count} expenses";
        if ($skipped_count > 0) {
            $success .= ", Skipped: {$skipped_count} expenses (invalid status or permissions)";
        }
        
    } catch (Exception $e) {
        $db->rollback();
        $error = $e->getMessage();
    }
}

// Get expenses for bulk operations based on user role
$expenses = [];
$status_filter = '';

if ($user_role === 'verification') {
    // Verification can process 'open' expenses (verify) and 'pending' expenses (return)
    $status_filter = "WHERE e.status IN ('open', 'pending')";
} elseif ($user_role === 'reviewer') {
    // Reviewer can process 'pending' expenses (approve/reject)
    $status_filter = "WHERE e.status = 'pending'";
} elseif ($user_role === 'administrator') {
    // Administrator can process all statuses
    $status_filter = "WHERE e.status IN ('open', 'pending')";
}

$stmt = $db->prepare("
    SELECT e.*, 
           u.full_name as created_by_name,
           i.name as item_name,
           c.name as customer_name,
           d.name as driver_name
    FROM expenses e
    LEFT JOIN users u ON e.created_by = u.id
    LEFT JOIN items i ON e.item_id = i.id
    LEFT JOIN customers c ON e.customer_id = c.id
    LEFT JOIN drivers d ON e.driver_id = d.id
    {$status_filter}
    ORDER BY e.created_at ASC
    LIMIT 100
");
$stmt->execute();
$expenses = $stmt->fetchAll();
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Bulk Operations - Expense Management System</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="../assets/css/style.css" rel="stylesheet">
    <style>
        .expense-card {
            transition: all 0.2s;
            border: 2px solid transparent;
        }
        .expense-card.selected {
            border-color: #007bff;
            background-color: #f8f9fa;
        }
        .bulk-actions {
            position: sticky;
            top: 20px;
            z-index: 100;
        }
        .select-all-card {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }
    </style>
</head>
<body>
    <?php include '../includes/navbar.php'; ?>
    
    <div class="container-fluid mt-4">
        <!-- Page Header -->
        <div class="row mb-4">
            <div class="col">
                <h2>
                    <i class="fas fa-tasks me-2"></i>Bulk Operations
                    <span class="badge bg-<?php echo $user_role === 'verification' ? 'warning' : 'success'; ?> ms-2">
                        <?php echo ucfirst($user_role); ?>
                    </span>
                </h2>
                <p class="text-muted">
                    <?php if ($user_role === 'verification'): ?>
                        Process multiple expenses for verification or return them for corrections.
                    <?php else: ?>
                        Review and approve/reject multiple pending expenses.
                    <?php endif; ?>
                </p>
            </div>
        </div>

        <!-- Success/Error Messages -->
        <?php if ($success): ?>
        <div class="alert alert-success alert-dismissible fade show" role="alert">
            <i class="fas fa-check-circle me-2"></i><?php echo htmlspecialchars($success); ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
        <?php endif; ?>

        <?php if ($error): ?>
        <div class="alert alert-danger alert-dismissible fade show" role="alert">
            <i class="fas fa-exclamation-triangle me-2"></i><?php echo htmlspecialchars($error); ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
        <?php endif; ?>

        <?php if (empty($expenses)): ?>
        <div class="row">
            <div class="col-12">
                <div class="card text-center">
                    <div class="card-body py-5">
                        <i class="fas fa-inbox fa-3x text-muted mb-3"></i>
                        <h4 class="text-muted">No Expenses Available</h4>
                        <p class="text-muted">
                            <?php if ($user_role === 'verification'): ?>
                                There are no open or pending expenses available for verification.
                            <?php else: ?>
                                There are no pending expenses available for review.
                            <?php endif; ?>
                        </p>
                        <a href="../dashboard.php" class="btn btn-primary">
                            <i class="fas fa-arrow-left me-2"></i>Back to Dashboard
                        </a>
                    </div>
                </div>
            </div>
        </div>
        <?php else: ?>
        
        <form method="POST" id="bulkForm">
            <div class="row">
                <!-- Bulk Actions Panel -->
                <div class="col-md-4">
                    <div class="bulk-actions">
                        <!-- Selection Controls -->
                        <div class="card select-all-card mb-3">
                            <div class="card-body">
                                <h6 class="card-title mb-3">
                                    <i class="fas fa-check-square me-2"></i>Selection Controls
                                </h6>
                                <div class="d-grid gap-2">
                                    <button type="button" class="btn btn-light btn-sm" onclick="selectAll()">
                                        <i class="fas fa-check-double me-1"></i>Select All
                                    </button>
                                    <button type="button" class="btn btn-outline-light btn-sm" onclick="selectNone()">
                                        <i class="fas fa-times me-1"></i>Clear Selection
                                    </button>
                                </div>
                                <div class="mt-2">
                                    <small>Selected: <span id="selectedCount">0</span> expenses</small>
                                </div>
                            </div>
                        </div>

                        <!-- Action Buttons -->
                        <div class="card mb-3">
                            <div class="card-header">
                                <h6 class="mb-0">
                                    <i class="fas fa-cogs me-2"></i>Bulk Actions
                                </h6>
                            </div>
                            <div class="card-body">
                                <?php if ($user_role === 'verification'): ?>
                                <!-- Verification Actions -->
                                <div class="d-grid gap-2 mb-3">
                                    <button type="button" data-action="verify" class="btn btn-warning bulk-action-btn">
                                        <i class="fas fa-check me-2"></i>Verify Selected
                                    </button>
                                    <button type="button" data-action="return" class="btn btn-secondary bulk-action-btn">
                                        <i class="fas fa-undo me-2"></i>Return Selected
                                    </button>
                                </div>

                                <?php else: ?>
                                <!-- Reviewer Actions -->
                                <div class="d-grid gap-2 mb-3">
                                    <button type="button" data-action="approve" class="btn btn-success bulk-action-btn">
                                        <i class="fas fa-check-circle me-2"></i>Approve Selected
                                    </button>
                                    <button type="button" data-action="reject" class="btn btn-danger bulk-action-btn">
                                        <i class="fas fa-times-circle me-2"></i>Reject Selected
                                    </button>
                                </div>
                                <?php endif; ?>

                                <div class="alert alert-info">
                                    <i class="fas fa-info-circle me-2"></i>
                                    <strong>Note:</strong> Transfer number and comments will be entered in the next step.
                                </div>
                            </div>
                        </div>

                        <!-- Statistics -->
                        <div class="card">
                            <div class="card-header">
                                <h6 class="mb-0">
                                    <i class="fas fa-chart-bar me-2"></i>Statistics
                                </h6>
                            </div>
                            <div class="card-body">
                                <div class="row text-center">
                                    <div class="col-6">
                                        <div class="mb-2">
                                            <i class="fas fa-list fa-2x text-primary"></i>
                                        </div>
                                        <h6><?php echo count($expenses); ?></h6>
                                        <small class="text-muted">Available</small>
                                    </div>
                                    <div class="col-6">
                                        <div class="mb-2">
                                            <i class="fas fa-check-square fa-2x text-success"></i>
                                        </div>
                                        <h6 id="selectedCountDisplay">0</h6>
                                        <small class="text-muted">Selected</small>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Expenses List -->
                <div class="col-md-8">
                    <div class="card">
                        <div class="card-header">
                            <h6 class="mb-0">
                                <i class="fas fa-file-invoice-dollar me-2"></i>
                                Expenses for Bulk Processing
                                <span class="badge bg-secondary ms-2"><?php echo count($expenses); ?> items</span>
                            </h6>
                        </div>
                        <div class="card-body" style="max-height: 600px; overflow-y: auto;">
                            <?php foreach ($expenses as $expense): ?>
                            <div class="expense-card card mb-3" data-expense-id="<?php echo $expense['id']; ?>">
                                <div class="card-body">
                                    <div class="row align-items-center">
                                        <div class="col-md-1">
                                            <div class="form-check">
                                                <input class="form-check-input expense-checkbox" type="checkbox"
                                                       name="expense_ids[]" value="<?php echo $expense['id']; ?>"
                                                       id="expense_<?php echo $expense['id']; ?>">
                                            </div>
                                        </div>
                                        <div class="col-md-11">
                                            <div class="row">
                                                <div class="col-md-6">
                                                    <h6 class="mb-1">
                                                        <strong><?php echo htmlspecialchars($expense['exno']); ?></strong>
                                                        <?php echo getStatusBadge($expense['status']); ?>
                                                    </h6>
                                                    <p class="text-muted mb-1">
                                                        <i class="fas fa-user me-1"></i>
                                                        <?php echo htmlspecialchars($expense['created_by_name']); ?>
                                                    </p>
                                                    <p class="text-muted mb-1">
                                                        <i class="fas fa-calendar me-1"></i>
                                                        <?php echo formatDateTime($expense['created_at'], 'M d, Y H:i'); ?>
                                                    </p>
                                                </div>
                                                <div class="col-md-6">
                                                    <p class="mb-1">
                                                        <i class="fas fa-boxes me-1"></i>
                                                        <?php echo htmlspecialchars($expense['item_name'] ?? 'N/A'); ?>
                                                    </p>
                                                    <p class="mb-1">
                                                        <i class="fas fa-building me-1"></i>
                                                        <?php echo htmlspecialchars($expense['customer_name'] ?? 'N/A'); ?>
                                                    </p>
                                                    <?php if (!empty($expense['total_amount'])): ?>
                                                    <p class="mb-1">
                                                        <i class="fas fa-money-bill me-1"></i>
                                                        <strong class="text-success"><?php echo number_format($expense['total_amount'], 2); ?> บาท</strong>
                                                    </p>
                                                    <?php endif; ?>
                                                </div>
                                            </div>
                                            <div class="mt-2">
                                                <a href="view.php?id=<?php echo $expense['id']; ?>" class="btn btn-sm btn-outline-primary me-2" target="_blank">
                                                    <i class="fas fa-eye me-1"></i>View Details
                                                </a>
                                                <a href="edit.php?id=<?php echo $expense['id']; ?>" class="btn btn-sm btn-outline-secondary" target="_blank">
                                                    <i class="fas fa-edit me-1"></i>Edit
                                                </a>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <?php endforeach; ?>
                        </div>
                    </div>
                </div>
            </div>
        </form>
        <?php endif; ?>
    </div>

    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Selection functions
        function selectAll() {
            const checkboxes = document.querySelectorAll('.expense-checkbox');
            checkboxes.forEach(checkbox => {
                checkbox.checked = true;
                updateCardSelection(checkbox);
            });
            updateSelectedCount();
        }

        function selectNone() {
            const checkboxes = document.querySelectorAll('.expense-checkbox');
            checkboxes.forEach(checkbox => {
                checkbox.checked = false;
                updateCardSelection(checkbox);
            });
            updateSelectedCount();
        }

        function updateCardSelection(checkbox) {
            const card = checkbox.closest('.expense-card');
            if (checkbox.checked) {
                card.classList.add('selected');
            } else {
                card.classList.remove('selected');
            }
        }

        function updateSelectedCount() {
            const selectedCount = document.querySelectorAll('.expense-checkbox:checked').length;
            document.getElementById('selectedCount').textContent = selectedCount;
            document.getElementById('selectedCountDisplay').textContent = selectedCount;
        }

        function confirmBulkAction(action) {
            const selectedCount = document.querySelectorAll('.expense-checkbox:checked').length;

            if (selectedCount === 0) {
                alert('Please select at least one expense to process.');
                return false;
            }

            const actionText = {
                'verify': 'verify',
                'return': 'return',
                'approve': 'approve',
                'reject': 'reject'
            };

            return confirm(`Are you sure you want to ${actionText[action]} ${selectedCount} selected expense(s)?`);
        }

        // Event listeners
        document.addEventListener('DOMContentLoaded', function() {
            // Add event listeners to checkboxes
            const checkboxes = document.querySelectorAll('.expense-checkbox');
            checkboxes.forEach(checkbox => {
                checkbox.addEventListener('change', function() {
                    updateCardSelection(this);
                    updateSelectedCount();
                });
            });

            // Add click event to cards for easier selection
            const cards = document.querySelectorAll('.expense-card');
            cards.forEach(card => {
                card.addEventListener('click', function(e) {
                    // Don't trigger if clicking on links or buttons
                    if (e.target.tagName === 'A' || e.target.tagName === 'BUTTON' || e.target.closest('a') || e.target.closest('button')) {
                        return;
                    }

                    const checkbox = this.querySelector('.expense-checkbox');
                    checkbox.checked = !checkbox.checked;
                    updateCardSelection(checkbox);
                    updateSelectedCount();
                });

                // Add pointer cursor
                card.style.cursor = 'pointer';
            });

            // jQuery ready function
            $(document).ready(function() {
                console.log('jQuery ready');

                // Bulk operation buttons
                $('.bulk-action-btn').click(function() {
                console.log('Bulk action button clicked');
                const action = $(this).data('action');
                const selectedExpenses = getSelectedExpenses();

                console.log('Action:', action);
                console.log('Selected expenses:', selectedExpenses);

                if (selectedExpenses.length === 0) {
                    alert('Please select at least one expense to process.');
                    return;
                }

                showBulkUploadModal(selectedExpenses, action);
            });

            // File upload handling (using event delegation)
            $(document).on('change', '#bulkSlip', function() {
                const file = this.files[0];
                if (file) {
                    $('#fileName').text(file.name);
                    $('#fileInfo').show();
                    $('#uploadZone').addClass('border-success');
                    console.log('File selected:', file.name);
                } else {
                    $('#fileInfo').hide();
                    $('#uploadZone').removeClass('border-success');
                }
            });

            // Drag and drop (using event delegation)
            $(document).on('dragover', '#uploadZone', function(e) {
                e.preventDefault();
                e.stopPropagation();
                $(this).addClass('dragover');
            });

            $(document).on('dragleave', '#uploadZone', function(e) {
                e.preventDefault();
                e.stopPropagation();
                $(this).removeClass('dragover');
            });

            $(document).on('drop', '#uploadZone', function(e) {
                e.preventDefault();
                e.stopPropagation();
                $(this).removeClass('dragover');

                const files = e.originalEvent.dataTransfer.files;
                if (files.length > 0) {
                    const fileInput = document.getElementById('bulkSlip');
                    fileInput.files = files;
                    $(fileInput).trigger('change');
                    console.log('File dropped:', files[0].name);
                }
            });

            // Click to upload (using event delegation)
            $(document).on('click', '#uploadZone', function(e) {
                // Prevent triggering when clicking the choose file button
                if (!$(e.target).is('button') && !$(e.target).is('i')) {
                    $('#bulkSlip').click();
                }
            });

            // Form submission (using event delegation)
            $(document).on('submit', '#bulkUploadForm', function(e) {
                e.preventDefault();
                processBulkOperation();
            });
        });

        function getSelectedExpenses() {
            const selected = [];
            $('.expense-checkbox:checked').each(function() {
                const card = $(this).closest('.expense-card');
                selected.push({
                    id: $(this).val(),
                    exno: card.find('.expense-exno').text(),
                    amount: parseFloat(card.find('.expense-amount').text().replace(/[^\d.-]/g, '')) || 0,
                    date: card.find('.expense-date').text(),
                    status: card.find('.expense-status').text()
                });
            });
            return selected;
        }

        function showBulkUploadModal(expenses, action) {
            console.log('showBulkUploadModal called with:', expenses, action);

            // Update modal title and action
            const actionNames = {
                'verify': 'Verify Expenses',
                'return': 'Return Expenses',
                'approve': 'Approve Expenses',
                'reject': 'Reject Expenses'
            };

            $('#actionDisplay').val(actionNames[action] || action);
            $('#actionValue').val(action);

            // Update selected expenses summary
            const totalAmount = expenses.reduce((sum, exp) => sum + exp.amount, 0);
            $('#selectedCount').text(expenses.length);
            $('#selectedTotal').text('Total: ฿' + totalAmount.toLocaleString('th-TH', {minimumFractionDigits: 2}));

            // Set expense IDs
            $('#expenseIds').val(JSON.stringify(expenses.map(exp => exp.id)));

            // Clear previous form data
            $('#transferNumber').val('');
            $('#bulkComments').val('');
            clearFile();

            console.log('About to show modal');
            // Show modal
            $('#bulkUploadModal').modal('show');
            console.log('Modal show command executed');
        }

        function clearFile() {
            $('#bulkSlip').val('');
            $('#fileInfo').hide();
            $('#uploadZone').removeClass('border-success');
        }

        function processBulkOperation() {
            const formData = new FormData($('#bulkUploadForm')[0]);

            // Add transfer numbers based on action
            const action = $('#actionValue').val();
            const transferNumber = $('#transferNumber').val();

            if (action === 'verify' || action === 'return') {
                formData.append('verification_transfer_no', transferNumber);
            } else if (action === 'approve' || action === 'reject') {
                formData.append('reviewer_transfer_no', transferNumber);
            }

            // Show loading
            $('#processBulkBtn').prop('disabled', true).html('<i class="fas fa-spinner fa-spin me-1"></i>Processing...');

            $.ajax({
                url: window.location.href,
                method: 'POST',
                data: formData,
                processData: false,
                contentType: false,
                success: function(response) {
                    // Hide modal
                    $('#bulkUploadModal').modal('hide');

                    // Show success message and reload
                    alert('Bulk operation completed successfully!');
                    location.reload();
                },
                error: function(xhr) {
                    alert('Error: ' + (xhr.responseText || 'Unknown error occurred'));
                },
                complete: function() {
                    $('#processBulkBtn').prop('disabled', false).html('<i class="fas fa-play me-1"></i>Process Bulk Operation');
                }
            });
        }

        }); // End jQuery ready
    </script>

    <!-- Bulk Upload Modal -->
    <div class="modal fade" id="bulkUploadModal" tabindex="-1" aria-labelledby="bulkUploadModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header bg-primary text-white">
                    <h5 class="modal-title" id="bulkUploadModalLabel">
                        <i class="fas fa-upload me-2"></i>Bulk Operation with Transfer Slip
                    </h5>
                    <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <form id="bulkUploadForm" enctype="multipart/form-data">
                    <div class="modal-body">
                        <!-- Selected Expenses Summary -->
                        <div class="alert alert-info">
                            <h6><i class="fas fa-list me-2"></i>Selected Expenses</h6>
                            <div id="selectedExpensesSummary">
                                <span id="selectedCount">0</span> expenses selected
                                <span id="selectedTotal" class="float-end">Total: ฿0.00</span>
                            </div>
                        </div>

                        <!-- Action Type -->
                        <div class="mb-3">
                            <label class="form-label">
                                <i class="fas fa-cog me-1"></i>Action Type
                            </label>
                            <input type="text" id="actionDisplay" class="form-control" readonly>
                            <input type="hidden" id="actionValue" name="action">
                        </div>

                        <!-- Transfer Number -->
                        <div class="mb-3">
                            <label for="transferNumber" class="form-label">
                                <i class="fas fa-receipt me-1"></i>Transfer Number <span class="text-danger">*</span>
                            </label>
                            <input type="text" class="form-control" id="transferNumber" name="transfer_number"
                                   placeholder="Enter transfer number" required>
                            <div class="form-text">Required: Enter the transfer slip number for this bulk operation</div>
                        </div>

                        <!-- File Upload -->
                        <div class="mb-3">
                            <label for="bulkSlip" class="form-label">
                                <i class="fas fa-file-upload me-1"></i>Transfer Slip <span class="text-muted">(Optional)</span>
                            </label>
                            <div class="upload-zone" id="uploadZone">
                                <div class="upload-content">
                                    <i class="fas fa-cloud-upload-alt fa-3x text-muted mb-3"></i>
                                    <h6>Upload Transfer Slip</h6>
                                    <p class="text-muted mb-3">Drag and drop or click to select file</p>
                                    <input type="file" class="form-control" id="bulkSlip" name="bulk_slip"
                                           accept="image/*,.pdf">
                                    <button type="button" class="btn btn-outline-primary" onclick="document.getElementById('bulkSlip').click()">
                                        <i class="fas fa-folder-open me-1"></i>Choose File
                                    </button>
                                </div>
                            </div>
                            <div id="fileInfo" class="alert alert-success mt-2" style="display: none;">
                                <i class="fas fa-file me-2"></i>
                                <span id="fileName"></span>
                                <button type="button" class="btn btn-sm btn-outline-danger float-end" onclick="clearFile()">
                                    <i class="fas fa-times"></i>
                                </button>
                            </div>
                            <div class="form-text">
                                <i class="fas fa-info-circle me-1"></i>
                                Supported formats: JPG, PNG, PDF (Max size: 10MB)
                            </div>
                        </div>

                        <!-- Comments -->
                        <div class="mb-3">
                            <label for="bulkComments" class="form-label">
                                <i class="fas fa-comment me-1"></i>Comments <span class="text-muted">(Optional)</span>
                            </label>
                            <textarea class="form-control" id="bulkComments" name="bulk_comments" rows="3"
                                      placeholder="Enter comments for this bulk operation"></textarea>
                        </div>

                        <!-- Hidden fields -->
                        <input type="hidden" id="expenseIds" name="expense_ids">
                        <input type="hidden" id="verificationTransferNo" name="verification_transfer_no">
                        <input type="hidden" id="reviewerTransferNo" name="reviewer_transfer_no">
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                            <i class="fas fa-times me-1"></i>Cancel
                        </button>
                        <button type="submit" class="btn btn-success" id="processBulkBtn">
                            <i class="fas fa-play me-1"></i>Process Bulk Operation
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <style>
        .upload-zone {
            border: 2px dashed #dee2e6;
            border-radius: 8px;
            padding: 30px;
            text-align: center;
            transition: all 0.3s ease;
            cursor: pointer;
            position: relative;
        }

        .upload-zone:hover {
            border-color: #0d6efd;
            background-color: #f8f9fa;
        }

        .upload-zone.dragover {
            border-color: #0d6efd;
            background-color: #e7f3ff;
        }

        .upload-zone.border-success {
            border-color: #198754;
            background-color: #f0f9f4;
        }

        .upload-zone #bulkSlip {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            opacity: 0;
            cursor: pointer;
        }

        .upload-content {
            pointer-events: none;
        }

        .upload-content button {
            pointer-events: all;
        }
    </style>
</body>
</html>
