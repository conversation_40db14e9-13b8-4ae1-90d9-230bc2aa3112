<?php
session_start();
require_once '../config/database.php';
require_once '../includes/functions.php';
require_once '../includes/BatchOperations.php';

// Check if user is logged in
if (!isset($_SESSION['user_id'])) {
    header('Location: ../login.php');
    exit();
}

$user_id = $_SESSION['user_id'];
$user_role = $_SESSION['role'];
$username = $_SESSION['username'];

// Get batch parameters
$batch_id = $_GET['batch_id'] ?? '';
$batch_type = $_GET['type'] ?? '';



if (empty($batch_id) || !in_array($batch_type, ['verification', 'review'])) {
    $_SESSION['error'] = "Invalid batch parameters";
    header('Location: ../dashboard.php');
    exit();
}

// Check user permissions
if ($batch_type === 'verification' && !in_array($user_role, ['verification', 'administrator'])) {
    $_SESSION['error'] = "You don't have permission to access verification batches";
    header('Location: ../dashboard.php');
    exit();
}

if ($batch_type === 'review' && !in_array($user_role, ['reviewer', 'administrator'])) {
    $_SESSION['error'] = "You don't have permission to access review batches";
    header('Location: ../dashboard.php');
    exit();
}

// Get batch information
try {
    $database = new Database();
    $db = $database->getConnection();
    $batchOps = new BatchOperations($db);

    $batch_info = $batchOps->getBatchInfo($batch_id);
    if (!$batch_info) {
        throw new Exception('Batch not found');
    }

    // Check if user owns this batch (unless admin)
    if ($user_role !== 'administrator' && $batch_info['user_id'] != $user_id) {
        throw new Exception('Access denied to this batch');
    }

    // Check if user can modify this batch based on role and status
    $can_modify_batch = false;
    if ($user_role === 'administrator') {
        $can_modify_batch = true;
    } elseif ($batch_info['user_id'] == $user_id) {
        // Users can modify their own batches if status allows
        $can_modify_batch = in_array($batch_info['status'], ['pending', 'failed']);
    }

    $batch_items = $batchOps->getBatchItems($batch_id);

} catch (Exception $e) {
    $_SESSION['error'] = $e->getMessage();
    header('Location: ../dashboard.php');
    exit();
}
?>

<!DOCTYPE html>
<html lang="th">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Batch Processing | Expense Management System</title>

    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">

    <style>
        .batch-header {
            background: linear-gradient(135deg,
                <?php echo $batch_type === 'verification' ? '#667eea 0%, #764ba2 100%' : '#28a745 0%, #20c997 100%'; ?>);
            color: white;
            border-radius: 10px;
            padding: 25px;
            margin-bottom: 25px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
        }

        .batch-info-card {
            background: white;
            border-radius: 10px;
            padding: 20px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }

        /* File Item Styling */
        .file-item {
            background: #fff;
            border: 1px solid #dee2e6;
            border-radius: 8px;
            padding: 15px;
            margin-bottom: 15px;
            transition: all 0.3s ease;
            box-shadow: 0 2px 4px rgba(0,0,0,0.05);
        }

        .file-item:hover {
            box-shadow: 0 4px 12px rgba(0,0,0,0.1);
            transform: translateY(-2px);
        }

        .file-item.completed {
            border-color: #28a745;
            background: #f8fff9;
        }

        .file-item.error {
            border-color: #dc3545;
            background: #fff5f5;
        }

        .file-preview {
            width: 60px;
            height: 60px;
            border-radius: 6px;
            object-fit: cover;
            border: 1px solid #dee2e6;
        }

        .file-icon {
            width: 60px;
            height: 60px;
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 6px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 24px;
            color: #6c757d;
        }

        /* File name display improvements */
        .file-name {
            font-weight: 600;
            margin-bottom: 4px;
            word-break: break-word;
            line-height: 1.3;
            max-height: 2.6em; /* Show max 2 lines */
            overflow: hidden;
            display: -webkit-box;
            -webkit-line-clamp: 2;
            -webkit-box-orient: vertical;
        }

        .file-name-tooltip {
            cursor: help;
        }

        .file-size {
            font-size: 0.875rem;
            color: #6c757d;
        }

        .amount-input {
            border: 2px solid #dee2e6;
            transition: border-color 0.3s ease;
        }

        .amount-input:focus {
            border-color: <?php echo $batch_type === 'verification' ? '#2196f3' : '#28a745'; ?>;
            box-shadow: 0 0 0 0.2rem <?php echo $batch_type === 'verification' ? 'rgba(33, 150, 243, 0.25)' : 'rgba(40, 167, 69, 0.25)'; ?>;
        }

        .amount-input.is-valid {
            border-color: #28a745;
        }

        .amount-input.is-invalid {
            border-color: #dc3545;
        }

        .progress-bar {
            background-color: <?php echo $batch_type === 'verification' ? '#2196f3' : '#28a745'; ?>;
            transition: width 0.3s ease;
        }

        .btn-remove {
            opacity: 0.7;
            transition: opacity 0.3s ease;
        }

        .btn-remove:hover {
            opacity: 1;
        }

        /* Transfer number validation styling */
        #modalTransferNumber.is-invalid {
            border-color: #dc3545;
            background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' width='12' height='12' fill='none' stroke='%23dc3545' viewBox='0 0 12 12'%3e%3ccircle cx='6' cy='6' r='4.5'/%3e%3cpath d='m5.8 4.6 1.4 1.4M7.2 4.6l-1.4 1.4'/%3e%3c/svg%3e");
            background-repeat: no-repeat;
            background-position: right calc(0.375em + 0.1875rem) center;
            background-size: calc(0.75em + 0.375rem) calc(0.75em + 0.375rem);
        }

        #modalTransferNumber.is-valid {
            border-color: #198754;
            background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 8 8'%3e%3cpath fill='%23198754' d='m2.3 6.73.94-.94 1.93 1.93 3.53-3.53.94.94L4.16 9.66z'/%3e%3c/svg%3e");
            background-repeat: no-repeat;
            background-position: right calc(0.375em + 0.1875rem) center;
            background-size: calc(0.75em + 0.375rem) calc(0.75em + 0.375rem);
        }

        /* Transfer number checking indicator */
        #modalTransferNumber.transfer-number-checking {
            position: relative;
        }

        #modalTransferNumber.transfer-number-checking::after {
            content: '';
            position: absolute;
            right: 10px;
            top: 50%;
            transform: translateY(-50%);
            width: 16px;
            height: 16px;
            border: 2px solid #f3f3f3;
            border-top: 2px solid #007bff;
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            0% { transform: translateY(-50%) rotate(0deg); }
            100% { transform: translateY(-50%) rotate(360deg); }
        }

        .item-row {
            transition: all 0.3s ease;
        }

        .item-row:hover {
            background-color: #f8f9fa;
        }

        .status-badge {
            font-size: 0.8rem;
        }

        .progress-section {
            background: white;
            border-radius: 10px;
            padding: 20px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 20px;
            display: none;
        }

        .loading-overlay {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0,0,0,0.5);
            display: none;
            justify-content: center;
            align-items: center;
            z-index: 9999;
        }

        .loading-spinner {
            background: white;
            padding: 30px;
            border-radius: 10px;
            text-align: center;
        }

        .slip-buttons .btn {
            font-size: 0.75rem;
            padding: 0.25rem 0.5rem;
            margin-bottom: 0.25rem;
            width: 100%;
        }

        .receipt-buttons .btn {
            font-size: 0.75rem;
            padding: 0.25rem 0.5rem;
            margin-bottom: 0.25rem;
        }
    </style>
</head>
<body class="bg-light">
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark <?php echo $batch_type === 'verification' ? 'bg-primary' : 'bg-success'; ?>">
        <div class="container-fluid">
            <a class="navbar-brand" href="../dashboard.php">
                <i class="fas fa-chart-line me-2"></i>Expense System
            </a>
            <div class="navbar-nav ms-auto">
                <span class="navbar-text me-3">
                    <i class="fas fa-user me-1"></i><?php echo htmlspecialchars($username); ?> (<?php echo ucfirst($user_role); ?>)
                </span>
                <a class="nav-link" href="../dashboard.php">
                    <i class="fas fa-arrow-left me-1"></i>Back to Dashboard
                </a>
                <!-- <a class="nav-link" href="../logout.php">
                    <i class="fas fa-sign-out-alt"></i> Logout
                </a> -->
            </div>
        </div>
    </nav>

    <div class="container-fluid mt-4">
        <!-- Batch Header -->
        <div class="batch-header">
            <div class="row align-items-center">
                <div class="col-md-8">
                    <h2>
                        <i class="fas <?php echo $batch_type === 'verification' ? 'fa-check-double' : 'fa-clipboard-check'; ?> me-2"></i>
                        Batch <?php echo ucfirst($batch_type); ?> Processing
                    </h2>
                    <p class="mb-0">Batch ID: <strong><?php echo htmlspecialchars($batch_id); ?></strong></p>
                    <?php if (!empty($batch_info['transfer_number'])): ?>
                    <p class="mb-0">Transfer Number: <strong><?php echo htmlspecialchars($batch_info['transfer_number']); ?></strong></p>
                    <?php endif; ?>
                </div>
                <div class="col-md-4 text-end">
                    <div class="row">
                        <div class="col-6">
                            <div class="text-center">
                                <div class="h4 mb-0"><?php echo $batch_info['item_count']; ?></div>
                                <small>Items</small>
                            </div>
                        </div>
                        <div class="col-6">
                            <div class="text-center">
                                <div class="h4 mb-0"><?php echo number_format($batch_info['total_amount'], 2); ?></div>
                                <small>Total Amount (บาท)</small>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="row">
            <!-- Left Column: Batch Items -->
            <div class="col-md-8">
                <div class="batch-info-card">
                    <h5><i class="fas fa-list me-2"></i>Batch Items</h5>
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead class="table-light">
                                <tr>
                                    <th>Expense No.</th>
                                    <th>Date</th>
                                    <th>Amount</th>
                                    <th>Receipt</th>
                                    <th>Transfer Slip</th>
                                    <th>Details</th>
                                    <th>Status</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($batch_items as $item): ?>
                                <tr class="item-row">
                                    <td>
                                        <strong><?php echo htmlspecialchars($item['exno']); ?></strong>
                                    </td>
                                    <td><?php echo date('M d, Y', strtotime($item['expense_date'])); ?></td>
                                    <td>
                                        <strong class="text-<?php echo $batch_type === 'verification' ? 'primary' : 'success'; ?>">
                                            <?php echo number_format($item['individual_amount'], 2); ?> บาท
                                        </strong>
                                    </td>
                                    <td>
                                        <?php
                                        $receipt_images = !empty($item['receipt_images']) ? json_decode($item['receipt_images'], true) : [];
                                        if (!empty($receipt_images) && is_array($receipt_images)):
                                        ?>
                                        <div class="btn-group-vertical receipt-buttons" role="group">
                                            <?php foreach ($receipt_images as $index => $image): ?>
                                            <?php
                                            $filename = is_array($image) ? $image['filename'] : $image;
                                            $receipt_no = is_array($image) && isset($image['receipt_no']) ? $image['receipt_no'] : '';
                                            ?>
                                            <button type="button" class="btn btn-outline-info btn-sm mb-1"
                                                    onclick="viewReceipt('<?php echo htmlspecialchars($filename); ?>', '<?php echo htmlspecialchars($item['exno']); ?>', <?php echo $index + 1; ?>)"
                                                    title="<?php echo $receipt_no ? 'Receipt No: ' . htmlspecialchars($receipt_no) : 'Receipt ' . ($index + 1); ?>">
                                                <i class="fas fa-image me-1"></i>Receipt <?php echo $index + 1; ?>
                                                <?php if ($receipt_no): ?>
                                                <br><small class="text-muted"><?php echo htmlspecialchars($receipt_no); ?></small>
                                                <?php endif; ?>
                                            </button>
                                            <?php endforeach; ?>
                                        </div>
                                        <?php else: ?>
                                        <span class="text-muted">
                                            <i class="fas fa-image-slash me-1"></i>No Image
                                        </span>
                                        <?php endif; ?>
                                    </td>
                                    <td>
                                        <div class="d-flex flex-column gap-1 slip-buttons">
                                            <?php if (!empty($item['transfer_slip_image'])): ?>
                                            <button type="button" class="btn btn-outline-success btn-sm"
                                                    onclick="viewTransferSlip('<?php echo htmlspecialchars($item['transfer_slip_image']); ?>', '<?php echo htmlspecialchars($item['exno']); ?>')">
                                                <i class="fas fa-money-check-alt me-1"></i>Transfer Slip
                                            </button>
                                            <?php endif; ?>

                                            <?php if (!empty($item['verification_slip_image'])): ?>
                                            <button type="button" class="btn btn-outline-primary btn-sm"
                                                    onclick="viewTransferSlip('<?php echo htmlspecialchars($item['verification_slip_image']); ?>', '<?php echo htmlspecialchars($item['exno']); ?>', 'Verification')">
                                                <i class="fas fa-check-double me-1"></i>Verification Slip
                                            </button>
                                            <?php endif; ?>

                                            <?php if (!empty($item['reviewer_slip_image'])): ?>
                                            <button type="button" class="btn btn-outline-warning btn-sm"
                                                    onclick="viewTransferSlip('<?php echo htmlspecialchars($item['reviewer_slip_image']); ?>', '<?php echo htmlspecialchars($item['exno']); ?>', 'Review')">
                                                <i class="fas fa-clipboard-check me-1"></i>Review Slip
                                            </button>
                                            <?php endif; ?>

                                            <?php if (empty($item['transfer_slip_image']) && empty($item['verification_slip_image']) && empty($item['reviewer_slip_image'])): ?>
                                            <span class="text-muted">
                                                <i class="fas fa-file-slash me-1"></i>No Slip
                                            </span>
                                            <?php endif; ?>
                                        </div>
                                    </td>
                                    <td>
                                        <small>
                                            <?php if (!empty($item['item_name'])): ?>
                                            <strong>Item:</strong> <?php echo htmlspecialchars($item['item_name']); ?><br>
                                            <?php endif; ?>
                                            <?php if (!empty($item['bookingno'])): ?>
                                            <strong>Booking:</strong> <?php echo htmlspecialchars($item['bookingno']); ?><br>
                                            <?php endif; ?>
                                            <?php if (!empty($item['containerno'])): ?>
                                            <strong>Container:</strong> <?php echo htmlspecialchars($item['containerno']); ?><br>
                                            <?php endif; ?>
                                            <?php if (!empty($item['customer_name'])): ?>
                                            <strong>Customer:</strong> <?php echo htmlspecialchars($item['customer_name']); ?><br>
                                            <?php endif; ?>
                                            <?php if (!empty($item['driver_name'])): ?>
                                            <strong>Driver:</strong> <?php echo htmlspecialchars($item['driver_name']); ?><br>
                                            <?php endif; ?>
                                            <?php if (!empty($item['vehicle_plate'])): ?>
                                            <strong>Vehicle:</strong> <?php echo htmlspecialchars($item['vehicle_plate']); ?><br>
                                            <?php endif; ?>
                                            <?php if (!empty($item['additional_details'])): ?>
                                            <strong>Details:</strong> <?php echo htmlspecialchars(substr($item['additional_details'], 0, 50)); ?>
                                            <?php if (strlen($item['additional_details']) > 50) echo '...'; ?>
                                            <?php endif; ?>
                                        </small>
                                    </td>
                                    <td>
                                        <span class="badge status-badge
                                            <?php
                                            switch($item['status']) {
                                                case 'pending': echo 'bg-warning'; break;
                                                case 'processing': echo 'bg-info'; break;
                                                case 'completed': echo 'bg-success'; break;
                                                case 'failed': echo 'bg-danger'; break;
                                                default: echo 'bg-secondary';
                                            }
                                            ?>">
                                            <?php echo ucfirst($item['status']); ?>
                                        </span>
                                        <?php if ($item['error_message']): ?>
                                        <br><small class="text-danger"><?php echo htmlspecialchars($item['error_message']); ?></small>
                                        <?php endif; ?>
                                    </td>
                                </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>

            <!-- Right Column: Upload and Process -->
            <div class="col-md-4">
                <?php if ($batch_info['status'] === 'pending'): ?>

                <?php if ($batch_type === 'review'): ?>
                <!-- Review Batch Upload Section -->
                <div class="batch-info-card">
                    <h5><i class="fas fa-upload me-2"></i>Upload Document</h5>

                    <form id="batchProcessForm" enctype="multipart/form-data">
                        <input type="hidden" name="action" value="process_batch">
                        <input type="hidden" name="batch_id" value="<?php echo htmlspecialchars($batch_id); ?>">



                        <!-- Amount Verification Section -->
                        <div class="amount-verification-card mt-4 mb-4">
                            <h5><i class="fas fa-calculator me-2"></i>Amount Review</h5>

                            <!-- Summary Dashboard -->
                            <div class="row mb-3">
                                <div class="col-md-4">
                                    <div class="alert alert-info text-center mb-2">
                                        <div class="h6 mb-1">฿<?php echo number_format($batch_info['total_amount'], 2); ?></div>
                                        <small>Expected Total</small>
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="alert alert-secondary text-center mb-2" id="enteredTotalCard">
                                        <div class="h6 mb-1" id="enteredTotal">฿0.00</div>
                                        <small>Entered Total</small>
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="alert alert-warning text-center mb-2" id="differenceCard">
                                        <div class="h6 mb-1" id="difference">฿0.00</div>
                                        <small>Difference</small>
                                    </div>
                                </div>
                            </div>

                            <!-- Progress Bar -->
                            <div class="progress mb-3" style="height: 8px;">
                                <div class="progress-bar" id="amountProgress" style="width: 0%"></div>
                            </div>

                            <!-- Items Table -->
                            <div class="table-responsive">
                                <table class="table table-hover table-sm">
                                    <thead class="table-light">
                                        <tr>
                                            <th width="25%">Expense No</th>
                                            <th width="25%">Original Amount</th>
                                            <th width="25%"><?php echo ucfirst($batch_type); ?> Amount</th>
                                            <th width="25%">Status</th>
                                        </tr>
                                    </thead>
                                    <tbody id="amountItemsTable">
                                        <?php foreach ($batch_items as $item): ?>
                                        <tr data-expense-id="<?php echo $item['expense_id']; ?>">
                                            <td>
                                                <strong><?php echo htmlspecialchars($item['exno']); ?></strong>
                                                <br><div class="fw-bold text-primary mt-1"><?php echo htmlspecialchars($item['item_name'] ?? 'N/A'); ?></div>
                                            </td>
                                            <td>
                                                <span class="text-muted">฿<?php echo number_format($item['individual_amount'], 2); ?></span>
                                            </td>
                                            <td>
                                                <input type="number"
                                                       class="form-control form-control-sm amount-input"
                                                       name="<?php echo $batch_type; ?>_amounts[<?php echo $item['expense_id']; ?>]"
                                                       data-original="<?php echo $item['individual_amount']; ?>"
                                                       data-expense-id="<?php echo $item['expense_id']; ?>"
                                                       value="<?php echo $item['individual_amount']; ?>"
                                                       step="0.01" min="0" required>
                                            </td>
                                            <td>
                                                <span class="badge bg-secondary status-badge" id="status-<?php echo $item['expense_id']; ?>">
                                                    <i class="fas fa-clock me-1"></i>Pending
                                                </span>
                                            </td>
                                        </tr>
                                        <?php endforeach; ?>
                                    </tbody>
                                </table>
                            </div>

                            <!-- Validation Message -->
                            <div class="alert alert-danger" id="validationMessage" style="display: none;">
                                <i class="fas fa-exclamation-triangle me-2"></i>
                                <span id="validationText"></span>
                            </div>
                        </div>

                        <!-- Upload Progress -->
                        <div class="card mb-3" id="filesSummary">
                            <div class="card-header bg-light">
                                <div class="row align-items-center">
                                    <div class="col">
                                        <h6 class="mb-0">
                                            <i class="fas fa-upload me-2"></i>
                                            Upload Progress
                                        </h6>
                                    </div>
                                    <div class="col-auto">
                                        <span class="badge bg-secondary" id="filesCount">0 files</span>
                                    </div>
                                </div>
                            </div>
                            <div class="card-body">
                                <div class="row text-center">
                                    <div class="col-6">
                                        <div class="h5 mb-0" id="totalEntered">฿0.00</div>
                                        <small class="text-muted">Amount Entered</small>
                                    </div>
                                    <div class="col-6">
                                        <div class="h5 mb-0" id="remainingAmount">฿<?php echo number_format($batch_info['total_amount'], 2); ?></div>
                                        <small class="text-muted">Remaining</small>
                                    </div>
                                </div>

                                <!-- Progress Bar -->
                                <div class="mt-3">
                                    <div class="progress" style="height: 10px;">
                                        <div class="progress-bar" id="amountProgressBar" role="progressbar" style="width: 0%"></div>
                                    </div>
                                    <div class="d-flex justify-content-between mt-1">
                                        <small class="text-muted">฿0</small>
                                        <small class="text-muted">฿<?php echo number_format($batch_info['total_amount'], 2); ?></small>
                                    </div>
                                    <div class="text-center mt-2">
                                        <small class="text-muted">
                                            <span id="totalFiles">0</span> document<span id="pluralS">s</span> uploaded
                                        </small>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Add File Button -->
                        <div class="text-center mb-3">
                            <button type="button" class="btn btn-outline-<?php echo $batch_type === 'verification' ? 'primary' : 'success'; ?> btn-lg" id="addFileBtn">
                                <i class="fas fa-camera me-2"></i>Upload Document
                            </button>
                            <div class="mt-2">
                                <small class="text-muted">
                                    <i class="fas fa-info-circle me-1"></i>
                                    Upload one document at a time with transfer details
                                </small>
                            </div>
                        </div>

                        <!-- Files List -->
                        <div id="filesList" class="mb-3">
                            <!-- Dynamic file items will be added here -->
                        </div>

                        <!-- Hidden file input -->
                        <input type="file" id="hiddenFileInput" accept="image/*,.pdf" style="display: none;">

                        <!-- File Details Modal -->
                        <div class="modal fade" id="fileDetailsModal" tabindex="-1" aria-labelledby="fileDetailsModalLabel" aria-hidden="true">
                            <div class="modal-dialog modal-lg">
                                <div class="modal-content">
                                    <div class="modal-header">
                                        <h5 class="modal-title" id="fileDetailsModalLabel">
                                            <i class="fas fa-file-plus me-2"></i>Add File Details
                                        </h5>
                                        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                                    </div>
                                    <div class="modal-body">
                                        <div class="row">
                                            <div class="col-md-4">
                                                <div class="text-center">
                                                    <div id="modalFilePreview" class="mb-3">
                                                        <!-- File preview will be inserted here -->
                                                    </div>
                                                    <h6 id="modalFileName" class="text-truncate"></h6>
                                                    <small id="modalFileSize" class="text-muted"></small>
                                                </div>
                                            </div>
                                            <div class="col-md-8">
                                                <form id="fileDetailsForm">
                                                    <div class="mb-3">
                                                        <label for="modalTransferNumber" class="form-label">
                                                            <i class="fas fa-hashtag me-1"></i>Transfer Number <span class="text-danger">*</span>
                                                        </label>
                                                        <input type="text" class="form-control" id="modalTransferNumber"
                                                               placeholder="Enter transfer number" required>
                                                        <div class="invalid-feedback"></div>
                                                        <div class="form-text">Must be unique for this batch</div>
                                                    </div>
                                                    <div class="mb-3">
                                                        <label for="modalAmount" class="form-label">
                                                            <i class="fas fa-dollar-sign me-1"></i>Amount (฿) <span class="text-danger">*</span>
                                                        </label>
                                                        <input type="number" class="form-control" id="modalAmount"
                                                               placeholder="0.00" step="0.01" min="0" required>
                                                        <div class="invalid-feedback"></div>
                                                        <div class="form-text">
                                                            Remaining: <span id="modalRemainingAmount" class="fw-bold">฿0.00</span>
                                                        </div>
                                                    </div>
                                                </form>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="modal-footer">
                                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                                            <i class="fas fa-times me-1"></i>Cancel
                                        </button>
                                        <button type="button" class="btn btn-<?php echo $batch_type === 'verification' ? 'primary' : 'success'; ?>" id="addFileConfirm">
                                            <i class="fas fa-plus me-1"></i>Add File
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Process button -->
                        <div class="d-grid gap-2">
                            <button type="submit" id="submitBtn" class="btn btn-<?php echo $batch_type === 'verification' ? 'primary' : 'success'; ?> btn-lg" disabled>
                                <i class="fas fa-play me-1"></i>Process Batch
                            </button>
                        </div>
                    </form>
                </div>

                <?php endif; ?>

                <?php if ($batch_type === 'verification'): ?>
                <!-- Verification Batch Upload Section -->
                <div class="batch-info-card">
                    <h5><i class="fas fa-upload me-2"></i>Upload Document</h5>

                    <form id="batchProcessForm" enctype="multipart/form-data">
                        <input type="hidden" name="action" value="process_batch">
                        <input type="hidden" name="batch_id" value="<?php echo htmlspecialchars($batch_id); ?>">



                        <!-- Amount Verification Section -->
                        <div class="amount-verification-card mt-4 mb-4">
                            <h5><i class="fas fa-calculator me-2"></i>Amount Verification</h5>

                            <!-- Summary Dashboard -->
                            <div class="row mb-3">
                                <div class="col-md-4">
                                    <div class="alert alert-info text-center mb-2">
                                        <div class="h6 mb-1">฿<?php echo number_format($batch_info['total_amount'], 2); ?></div>
                                        <small>Expected Total</small>
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="alert alert-secondary text-center mb-2" id="enteredTotalCard">
                                        <div class="h6 mb-1" id="enteredTotal">฿0.00</div>
                                        <small>Entered Total</small>
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="alert alert-warning text-center mb-2" id="differenceCard">
                                        <div class="h6 mb-1" id="difference">฿0.00</div>
                                        <small>Difference</small>
                                    </div>
                                </div>
                            </div>

                            <!-- Progress Bar -->
                            <div class="progress mb-3" style="height: 8px;">
                                <div class="progress-bar" id="amountProgress" style="width: 0%"></div>
                            </div>

                            <!-- Items Table -->
                            <div class="table-responsive">
                                <table class="table table-hover table-sm">
                                    <thead class="table-light">
                                        <tr>
                                            <th width="25%">Expense No</th>
                                            <th width="25%">Original Amount</th>
                                            <th width="25%"><?php echo ucfirst($batch_type); ?> Amount</th>
                                            <th width="25%">Status</th>
                                        </tr>
                                    </thead>
                                    <tbody id="amountItemsTable">
                                        <?php foreach ($batch_items as $item): ?>
                                        <tr data-expense-id="<?php echo $item['expense_id']; ?>">
                                            <td>
                                                <strong><?php echo htmlspecialchars($item['exno']); ?></strong>
                                                <br><div class="fw-bold text-primary mt-1"><?php echo htmlspecialchars($item['item_name'] ?? 'N/A'); ?></div>
                                            </td>
                                            <td>
                                                <span class="text-muted">฿<?php echo number_format($item['individual_amount'], 2); ?></span>
                                            </td>
                                            <td>
                                                <input type="number"
                                                       class="form-control form-control-sm amount-input"
                                                       name="<?php echo $batch_type; ?>_amounts[<?php echo $item['expense_id']; ?>]"
                                                       data-original="<?php echo $item['individual_amount']; ?>"
                                                       data-expense-id="<?php echo $item['expense_id']; ?>"
                                                       value="<?php echo $item['individual_amount']; ?>"
                                                       step="0.01" min="0" required>
                                            </td>
                                            <td>
                                                <span class="badge bg-secondary status-badge" id="status-<?php echo $item['expense_id']; ?>">
                                                    <i class="fas fa-clock me-1"></i>Pending
                                                </span>
                                            </td>
                                        </tr>
                                        <?php endforeach; ?>
                                    </tbody>
                                </table>
                            </div>

                            <!-- Validation Message -->
                            <div class="alert alert-danger" id="validationMessage" style="display: none;">
                                <i class="fas fa-exclamation-triangle me-2"></i>
                                <span id="validationText"></span>
                            </div>
                        </div>

                        <!-- Upload Progress -->
                        <div class="card mb-3" id="filesSummary">
                            <div class="card-header bg-light">
                                <div class="row align-items-center">
                                    <div class="col">
                                        <h6 class="mb-0">
                                            <i class="fas fa-upload me-2"></i>
                                            Upload Progress
                                        </h6>
                                    </div>
                                    <div class="col-auto">
                                        <span class="badge bg-secondary" id="filesCount">0 files</span>
                                    </div>
                                </div>
                            </div>
                            <div class="card-body">
                                <div class="row text-center">
                                    <div class="col-6">
                                        <div class="h5 mb-0" id="totalEntered">฿0.00</div>
                                        <small class="text-muted">Amount Entered</small>
                                    </div>
                                    <div class="col-6">
                                        <div class="h5 mb-0" id="remainingAmount">฿<?php echo number_format($batch_info['total_amount'], 2); ?></div>
                                        <small class="text-muted">Remaining</small>
                                    </div>
                                </div>

                                <!-- Progress Bar -->
                                <div class="mt-3">
                                    <div class="progress" style="height: 10px;">
                                        <div class="progress-bar" id="amountProgressBar" role="progressbar" style="width: 0%"></div>
                                    </div>
                                    <div class="d-flex justify-content-between mt-1">
                                        <small class="text-muted">฿0</small>
                                        <small class="text-muted">฿<?php echo number_format($batch_info['total_amount'], 2); ?></small>
                                    </div>
                                    <div class="text-center mt-2">
                                        <small class="text-muted">
                                            <span id="totalFiles">0</span> document<span id="pluralS">s</span> uploaded
                                        </small>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Add File Button -->
                        <div class="text-center mb-3">
                            <button type="button" class="btn btn-outline-<?php echo $batch_type === 'verification' ? 'primary' : 'success'; ?> btn-lg" id="addFileBtn">
                                <i class="fas fa-camera me-2"></i>Upload Document
                            </button>
                            <div class="mt-2">
                                <small class="text-muted">
                                    <i class="fas fa-info-circle me-1"></i>
                                    Upload one document at a time with transfer details
                                </small>
                            </div>
                        </div>

                        <!-- Files List -->
                        <div id="filesList" class="mb-3">
                            <!-- Dynamic file items will be added here -->
                        </div>

                        <!-- Hidden file input -->
                        <input type="file" id="hiddenFileInput" accept="image/*,.pdf" style="display: none;">

                        <!-- File Details Modal -->
                        <div class="modal fade" id="fileDetailsModal" tabindex="-1" aria-labelledby="fileDetailsModalLabel" aria-hidden="true">
                            <div class="modal-dialog modal-lg">
                                <div class="modal-content">
                                    <div class="modal-header">
                                        <h5 class="modal-title" id="fileDetailsModalLabel">
                                            <i class="fas fa-file-plus me-2"></i>Add File Details
                                        </h5>
                                        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                                    </div>
                                    <div class="modal-body">
                                        <div class="row">
                                            <div class="col-md-4">
                                                <div class="text-center">
                                                    <div id="modalFilePreview" class="mb-3">
                                                        <!-- File preview will be inserted here -->
                                                    </div>
                                                    <h6 id="modalFileName" class="text-truncate"></h6>
                                                    <small id="modalFileSize" class="text-muted"></small>
                                                </div>
                                            </div>
                                            <div class="col-md-8">
                                                <form id="fileDetailsForm">
                                                    <div class="mb-3">
                                                        <label for="modalTransferNumber" class="form-label">
                                                            <i class="fas fa-hashtag me-1"></i>Transfer Number <span class="text-danger">*</span>
                                                        </label>
                                                        <input type="text" class="form-control" id="modalTransferNumber"
                                                               placeholder="Enter transfer number" required>
                                                        <div class="invalid-feedback"></div>
                                                        <div class="form-text">Must be unique for this batch</div>
                                                    </div>
                                                    <div class="mb-3">
                                                        <label for="modalAmount" class="form-label">
                                                            <i class="fas fa-dollar-sign me-1"></i>Amount (฿) <span class="text-danger">*</span>
                                                        </label>
                                                        <input type="number" class="form-control" id="modalAmount"
                                                               placeholder="0.00" step="0.01" min="0" required>
                                                        <div class="invalid-feedback"></div>
                                                        <div class="form-text">
                                                            Remaining: <span id="modalRemainingAmount" class="fw-bold">฿0.00</span>
                                                        </div>
                                                    </div>
                                                </form>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="modal-footer">
                                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                                            <i class="fas fa-times me-1"></i>Cancel
                                        </button>
                                        <button type="button" class="btn btn-<?php echo $batch_type === 'verification' ? 'primary' : 'success'; ?>" id="addFileConfirm">
                                            <i class="fas fa-plus me-1"></i>Add File
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="d-grid gap-2">
                            <button type="submit" id="submitBtn" class="btn btn-<?php echo $batch_type === 'verification' ? 'primary' : 'success'; ?> btn-lg" disabled>
                                <i class="fas fa-play me-1"></i>Process Batch
                            </button>
                            <a href="multi_<?php echo $batch_type; ?>.php" class="btn btn-outline-secondary">
                                <i class="fas fa-arrow-left me-1"></i>Back to Selection
                            </a>

                            <?php if ($can_modify_batch && in_array($batch_info['status'], ['pending', 'failed'])): ?>
                                <div class="mt-3">
                                    <h6 class="text-muted">Batch Management</h6>
                                    <div class="d-grid gap-2">
                                        <?php if ($batch_info['status'] === 'failed'): ?>
                                            <button type="button" class="btn btn-warning" onclick="retryBatch('<?php echo $batch_id; ?>')">
                                                <i class="fas fa-redo me-1"></i>Retry Batch
                                            </button>
                                        <?php endif; ?>
                                        <button type="button" class="btn btn-danger" onclick="cancelBatch('<?php echo $batch_id; ?>')">
                                            <i class="fas fa-times me-1"></i>Cancel Batch
                                        </button>
                                    </div>
                                </div>
                            <?php endif; ?>
                        </div>
                    </form>
                </div>
                <?php endif; // End verification batch ?>

                <?php else: ?>
                <!-- Batch Status -->
                <div class="batch-info-card">
                    <h5><i class="fas fa-info-circle me-2"></i>Batch Status</h5>
                    <div class="alert alert-<?php
                        switch($batch_info['status']) {
                            case 'processing': echo 'info'; break;
                            case 'completed': echo 'success'; break;
                            case 'failed': echo 'danger'; break;
                            default: echo 'secondary';
                        }
                    ?>">
                        <h6 class="alert-heading">
                            <?php echo ucfirst($batch_info['status']); ?>
                        </h6>
                        <?php if ($batch_info['status'] === 'completed'): ?>
                        <p class="mb-0">Batch processing completed successfully!</p>
                        <?php elseif ($batch_info['status'] === 'failed'): ?>
                        <p class="mb-0">Batch processing failed. Please check individual item errors.</p>
                        <?php elseif ($batch_info['status'] === 'processing'): ?>
                        <p class="mb-0">Batch is currently being processed...</p>
                        <?php endif; ?>
                    </div>

                    <div class="d-grid gap-2">
                        <a href="../dashboard.php" class="btn btn-outline-primary">
                            <i class="fas fa-home me-1"></i>Back to Dashboard
                        </a>
                        <a href="multi_<?php echo $batch_type; ?>.php" class="btn btn-outline-secondary">
                            <i class="fas fa-plus me-1"></i>Create New Batch
                        </a>

                        <?php if ($can_modify_batch && $batch_info['status'] === 'failed'): ?>
                            <div class="mt-3">
                                <h6 class="text-muted">Batch Management</h6>
                                <div class="d-grid gap-2">
                                    <button type="button" class="btn btn-warning" onclick="retryBatch('<?php echo $batch_id; ?>')">
                                        <i class="fas fa-redo me-1"></i>Retry Batch
                                    </button>
                                    <button type="button" class="btn btn-danger" onclick="cancelBatch('<?php echo $batch_id; ?>')">
                                        <i class="fas fa-times me-1"></i>Cancel Batch
                                    </button>
                                </div>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
                <?php endif; ?>
            </div>
        </div>
    </div>

    <!-- Progress Section -->
    <div class="progress-section" id="progressSection">
        <h5><i class="fas fa-cog fa-spin me-2"></i>Processing Batch...</h5>
        <div class="progress mb-3">
            <div class="progress-bar progress-bar-striped progress-bar-animated"
                 role="progressbar" style="width: 0%" id="progressBar"></div>
        </div>
        <div id="progressText">Initializing...</div>
    </div>

    <!-- Loading Overlay -->
    <div class="loading-overlay" id="loadingOverlay">
        <div class="loading-spinner">
            <div class="spinner-border text-<?php echo $batch_type === 'verification' ? 'primary' : 'success'; ?> mb-3" role="status">
                <span class="visually-hidden">Loading...</span>
            </div>
            <div>Processing batch <?php echo $batch_type; ?>...</div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <!-- jQuery -->
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>

    <script>
        const batchType = '<?php echo $batch_type; ?>';
        const batchId = '<?php echo $batch_id; ?>';
        const expectedTotal = <?php echo $batch_info['total_amount']; ?>;

        // File management system
        class BatchFileManager {
            constructor() {
                this.files = [];
                this.fileCounter = 0;
                this.init();
            }

            init() {
                this.bindEvents();
                this.updateSummary();
            }

            bindEvents() {
                const self = this;

                // Add file button
                $('#addFileBtn').click(function() {
                    $('#hiddenFileInput').click();
                });

                // File input change
                $('#hiddenFileInput').change(function(e) {
                    if (e.target.files.length > 0) {
                        self.handleFileSelect(e.target.files);
                        e.target.value = ''; // Reset input
                    }
                });

                // Form submission
                $('#batchProcessForm').submit(function(e) {
                    console.log('Form submit event triggered');
                    e.preventDefault();
                    self.validateAndSubmit();
                });

                // Submit button click (backup)
                $('#submitBtn').click(function(e) {
                    console.log('Submit button clicked');
                    if (!$(this).prop('disabled')) {
                        e.preventDefault();
                        self.validateAndSubmit();
                    }
                });

                console.log('BatchFileManager events bound');
            }

            handleFileSelect(files) {
                Array.from(files).forEach(file => {
                    if (this.validateFile(file)) {
                        this.showFileDetailsModal(file);
                    }
                });
            }

            showFileDetailsModal(file) {
                this.currentFile = file;

                // Update modal content
                $('#modalFileName').text(file.name);
                $('#modalFileSize').text(this.formatFileSize(file.size));

                // Show file preview
                const isImage = file.type.startsWith('image/');
                if (isImage) {
                    const reader = new FileReader();
                    reader.onload = (e) => {
                        $('#modalFilePreview').html(`<img src="${e.target.result}" class="img-fluid" style="max-height: 150px; border-radius: 6px;">`);
                    };
                    reader.readAsDataURL(file);
                } else {
                    $('#modalFilePreview').html(`<div class="file-icon" style="width: 100px; height: 100px; font-size: 36px;"><i class="fas fa-file-pdf"></i></div>`);
                }

                // Update remaining amount
                const totalEntered = this.files.reduce((sum, f) => sum + (f.amount || 0), 0);
                const remaining = expectedTotal - totalEntered;
                $('#modalRemainingAmount').text('฿' + remaining.toLocaleString('en-US', {minimumFractionDigits: 2}));

                // Clear form
                $('#modalTransferNumber').val('');
                $('#modalAmount').val('');
                $('#modalTransferNumber, #modalAmount').removeClass('is-valid is-invalid');
                $('.invalid-feedback').text('');

                // Show modal
                try {
                    const modalElement = document.getElementById('fileDetailsModal');
                    if (modalElement) {
                        const modal = new bootstrap.Modal(modalElement);
                        modal.show();

                        // Bind modal events
                        this.bindModalEvents();
                    } else {
                        console.error('Modal element not found');
                        alert('Error: Modal not found. Please refresh the page.');
                    }
                } catch (error) {
                    console.error('Error showing modal:', error);
                    alert('Error showing file details dialog. Please refresh the page.');
                }
            }

            bindModalEvents() {
                const self = this;

                // Remove existing event listeners to prevent duplicates
                $('#addFileConfirm').off('click.fileManager');
                $('#modalTransferNumber').off('input.fileManager blur.fileManager');
                $('#modalAmount').off('input.fileManager blur.fileManager');

                // Add file confirm
                $('#addFileConfirm').on('click.fileManager', function() {
                    self.confirmAddFile();
                });

                // Real-time validation
                $('#modalTransferNumber').on('input.fileManager blur.fileManager', function(e) {
                    self.validateModalTransferNumber(e.target.value);
                });

                $('#modalAmount').on('input.fileManager blur.fileManager', function(e) {
                    self.validateModalAmount(parseFloat(e.target.value) || 0);
                });
            }

            validateModalTransferNumber(value) {
                const input = $('#modalTransferNumber');
                const feedback = input.siblings('.invalid-feedback');

                if (!value.trim()) {
                    input.removeClass('is-valid').addClass('is-invalid');
                    feedback.text('Transfer number is required');
                    return false;
                }

                // Check for duplicates within current batch files
                const isDuplicateInBatch = this.files.some(f => f.transferNumber === value.trim());
                if (isDuplicateInBatch) {
                    input.removeClass('is-valid').addClass('is-invalid');
                    feedback.text('Transfer number already exists in this batch');
                    return false;
                }

                // Check for duplicates in database
                this.checkTransferNumberInDatabase(value.trim(), input, feedback);
                return true; // Return true for now, actual validation happens in AJAX callback
            }

            checkTransferNumberInDatabase(transferNumber, inputElement, feedbackElement) {
                // Add checking indicator
                inputElement.addClass('transfer-number-checking');
                feedbackElement.text('Checking transfer number...');

                $.ajax({
                    url: '../api/check_transfer_number.php',
                    method: 'POST',
                    data: { transfer_no: transferNumber },
                    timeout: 5000,
                    success: (response) => {
                        inputElement.removeClass('transfer-number-checking');

                        if (response.exists) {
                            inputElement.addClass('is-invalid').removeClass('is-valid');
                            feedbackElement.text(`Transfer number already exists in expense: ${response.expense_no}`);
                            console.log('Transfer number exists:', transferNumber, 'in expense:', response.expense_no);
                        } else {
                            inputElement.removeClass('is-invalid').addClass('is-valid');
                            feedbackElement.text('Transfer number is available');
                            console.log('Transfer number available:', transferNumber);
                        }
                    },
                    error: (xhr) => {
                        inputElement.removeClass('transfer-number-checking');
                        console.error('Error checking transfer number:', xhr);

                        // On error, allow the transfer number but show warning
                        inputElement.removeClass('is-invalid').addClass('is-valid');
                        feedbackElement.text('Unable to verify transfer number (network error)');
                    }
                });
            }

            validateModalAmount(value) {
                const input = $('#modalAmount');
                const feedback = input.siblings('.invalid-feedback');

                if (isNaN(value) || value <= 0) {
                    input.removeClass('is-valid').addClass('is-invalid');
                    feedback.text('Amount must be greater than 0');
                    return false;
                }

                // Check if exceeds remaining amount
                const totalEntered = this.files.reduce((sum, f) => sum + (f.amount || 0), 0);
                const remaining = expectedTotal - totalEntered;

                if (value > remaining + 0.01) {
                    input.removeClass('is-valid').addClass('is-invalid');
                    feedback.text(`Amount exceeds remaining budget (฿${remaining.toLocaleString()})`);
                    return false;
                }

                input.removeClass('is-invalid').addClass('is-valid');
                feedback.text('');
                return true;
            }

            confirmAddFile() {
                const transferNumber = $('#modalTransferNumber').val().trim();
                const amount = parseFloat($('#modalAmount').val()) || 0;
                const transferInput = $('#modalTransferNumber');

                // Check if transfer number is still being validated
                if (transferInput.hasClass('transfer-number-checking')) {
                    alert('Please wait for transfer number validation to complete.');
                    return;
                }

                // Check if transfer number validation failed
                if (transferInput.hasClass('is-invalid')) {
                    alert('Please fix the transfer number before adding the file.');
                    transferInput.focus();
                    return;
                }

                // Validate inputs
                const isTransferValid = transferInput.hasClass('is-valid') && transferNumber;
                const isAmountValid = this.validateModalAmount(amount);

                if (!isTransferValid || !isAmountValid) {
                    if (!isTransferValid) {
                        alert('Please enter a valid transfer number.');
                        transferInput.focus();
                    } else if (!isAmountValid) {
                        alert('Please enter a valid amount.');
                        $('#modalAmount').focus();
                    }
                    return;
                }

                // Add file
                this.addFileWithDetails(this.currentFile, transferNumber, amount);

                // Clear form and close modal
                $('#modalTransferNumber').val('');
                $('#modalAmount').val('');
                $('#modalTransferNumber, #modalAmount').removeClass('is-valid is-invalid transfer-number-checking');
                $('.invalid-feedback').text('');

                // Close modal
                try {
                    const modalElement = document.getElementById('fileDetailsModal');
                    const modal = bootstrap.Modal.getInstance(modalElement);
                    if (modal) {
                        modal.hide();
                    } else {
                        // Fallback: hide modal manually
                        $(modalElement).modal('hide');
                    }
                } catch (error) {
                    console.error('Error closing modal:', error);
                    // Fallback: hide modal manually
                    $('#fileDetailsModal').modal('hide');
                }
            }

            addFileWithDetails(file, transferNumber, amount) {
                const fileId = ++this.fileCounter;
                const fileData = {
                    id: fileId,
                    file: file,
                    transferNumber: transferNumber,
                    amount: amount,
                    isValid: true
                };

                this.files.push(fileData);
                this.renderFileItem(fileData);
                this.updateSummary();
            }

            validateFile(file) {
                // Check file size (5MB limit)
                if (file.size > 5 * 1024 * 1024) {
                    alert(`File ${file.name} is too large. Maximum size is 5MB.`);
                    return false;
                }

                // Check file type
                const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/gif', 'application/pdf'];
                if (!allowedTypes.includes(file.type)) {
                    alert(`File ${file.name} is not a supported format. Please use images or PDF files.`);
                    return false;
                }

                return true;
            }



            removeFile(fileId) {
                this.files = this.files.filter(f => f.id !== fileId);
                $(`#fileItem_${fileId}`).remove();
                this.updateSummary();
            }

            renderFileItem(fileData) {
                const isImage = fileData.file.type.startsWith('image/');
                const filePreview = isImage ?
                    `<img src="${URL.createObjectURL(fileData.file)}" class="file-preview" alt="Preview">` :
                    `<div class="file-icon"><i class="fas fa-file-pdf"></i></div>`;

                const fileName = fileData.file.name;
                const shortFileName = fileName.length > 50 ? fileName.substring(0, 47) + '...' : fileName;

                const html = `
                    <div class="file-item completed" id="fileItem_${fileData.id}">
                        <div class="row align-items-center">
                            <div class="col-auto">
                                ${filePreview}
                            </div>
                            <div class="col">
                                <div class="row">
                                    <div class="col-md-5">
                                        <div class="file-name file-name-tooltip" title="${fileName}">
                                            ${shortFileName}
                                        </div>
                                        <div class="file-size">${this.formatFileSize(fileData.file.size)}</div>
                                    </div>
                                    <div class="col-md-3">
                                        <label class="form-label small">Transfer Number</label>
                                        <div class="form-control-plaintext">
                                            <strong>${fileData.transferNumber}</strong>
                                        </div>
                                    </div>
                                    <div class="col-md-3">
                                        <label class="form-label small">Amount</label>
                                        <div class="form-control-plaintext">
                                            <strong class="text-success">฿${fileData.amount.toLocaleString('en-US', {minimumFractionDigits: 2})}</strong>
                                        </div>
                                    </div>
                                    <div class="col-md-1">
                                        <button type="button" class="btn btn-outline-danger btn-sm btn-remove"
                                                onclick="fileManager.removeFile(${fileData.id})"
                                                title="Remove file">
                                            <i class="fas fa-trash"></i>
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                `;

                $('#filesList').append(html);

                // Initialize tooltips for new file item
                this.initializeTooltips();
            }



            updateSummary() {
                const totalFiles = this.files.length;
                const totalEntered = this.files.reduce((sum, f) => sum + (f.amount || 0), 0);
                const remaining = expectedTotal - totalEntered;
                const percentage = Math.min((totalEntered / expectedTotal) * 100, 100);

                // Update Upload Progress (Master)
                $('#totalFiles').text(totalFiles);
                $('#filesCount').text(`${totalFiles} file${totalFiles !== 1 ? 's' : ''}`);
                $('#pluralS').text(totalFiles !== 1 ? 's' : '');
                $('#totalEntered').text('฿' + totalEntered.toLocaleString('en-US', {minimumFractionDigits: 2}));
                $('#remainingAmount').text('฿' + remaining.toLocaleString('en-US', {minimumFractionDigits: 2}));
                $('#amountProgressBar').css('width', percentage + '%');

                // Update Amount Verification section (sync with Files Summary)
                $('#enteredTotal').text('฿' + totalEntered.toLocaleString('en-US', {minimumFractionDigits: 2}));
                $('#difference').text('฿' + remaining.toLocaleString('en-US', {minimumFractionDigits: 2}));

                // Update progress bar color
                const progressBar = $('#amountProgressBar');
                if (Math.abs(remaining) < 0.01) {
                    progressBar.removeClass('bg-warning bg-danger').addClass('bg-success');
                } else if (totalEntered > expectedTotal) {
                    progressBar.removeClass('bg-warning bg-success').addClass('bg-danger');
                } else {
                    progressBar.removeClass('bg-success bg-danger').addClass('bg-warning');
                }

                // Update Amount Verification cards colors
                const enteredTotalCard = $('#enteredTotalCard');
                const differenceCard = $('#differenceCard');

                // Update entered total card
                enteredTotalCard.removeClass('alert-secondary alert-success alert-danger');
                if (totalEntered > 0) {
                    enteredTotalCard.addClass('alert-success');
                } else {
                    enteredTotalCard.addClass('alert-secondary');
                }

                // Update difference card
                differenceCard.removeClass('alert-warning alert-success alert-danger');
                if (Math.abs(remaining) < 0.01) {
                    differenceCard.addClass('alert-success');
                } else if (totalEntered > expectedTotal) {
                    differenceCard.addClass('alert-danger');
                } else {
                    differenceCard.addClass('alert-warning');
                }

                this.updateSubmitButton();
            }

            updateSubmitButton() {
                const allValid = this.files.length > 0 && this.files.every(f => f.isValid);
                const totalEntered = this.files.reduce((sum, f) => sum + (f.amount || 0), 0);
                const amountMatches = Math.abs(totalEntered - expectedTotal) < 0.01;

                const canSubmit = allValid && amountMatches;

                console.log('updateSubmitButton:', {
                    filesLength: this.files.length,
                    allValid: allValid,
                    totalEntered: totalEntered,
                    expectedTotal: expectedTotal,
                    amountMatches: amountMatches,
                    canSubmit: canSubmit
                });

                $('#submitBtn').prop('disabled', !canSubmit);

                // Update button text
                if (this.files.length === 0) {
                    $('#submitBtn').html('<i class="fas fa-plus me-1"></i>Add Files First');
                } else if (!amountMatches) {
                    $('#submitBtn').html('<i class="fas fa-exclamation-triangle me-1"></i>Amount Mismatch');
                } else if (!allValid) {
                    $('#submitBtn').html('<i class="fas fa-exclamation-triangle me-1"></i>Complete All Fields');
                } else {
                    $('#submitBtn').html('<i class="fas fa-play me-1"></i>Process Batch');
                }
            }

            validateAndSubmit() {
                console.log('validateAndSubmit called');
                console.log('Files:', this.files);

                // Final validation
                if (this.files.length === 0) {
                    alert('Please add at least one file');
                    return;
                }

                const totalEntered = this.files.reduce((sum, f) => sum + (f.amount || 0), 0);
                console.log('Total entered:', totalEntered, 'Expected:', expectedTotal);

                if (Math.abs(totalEntered - expectedTotal) >= 0.01) {
                    alert(`Amount mismatch. Expected: ฿${expectedTotal.toLocaleString()}, Entered: ฿${totalEntered.toLocaleString()}`);
                    return;
                }

                const invalidFiles = this.files.filter(f => !f.isValid);
                console.log('Invalid files:', invalidFiles);

                if (invalidFiles.length > 0) {
                    alert('Please complete all required fields for all files');
                    return;
                }

                console.log('Validation passed, calling processBatch');
                this.processBatch();
            }

            processBatch() {
                console.log('processBatch called');
                console.log('batchId:', batchId);
                console.log('batchType:', batchType);

                const formData = new FormData();

                // Add batch info
                formData.append('action', 'process_batch');
                formData.append('batch_id', batchId);

                // Add files and their data
                this.files.forEach((fileData, index) => {
                    console.log(`Adding file ${index}:`, fileData.file.name, fileData.transferNumber, fileData.amount);
                    formData.append('files[]', fileData.file);
                    formData.append('transfer_numbers[]', fileData.transferNumber);
                    formData.append('amounts[]', fileData.amount);
                });

                console.log('FormData prepared, showing loading overlay');
                $('#loadingOverlay').show();
                $('#progressSection').show();

                const ajaxUrl = `../api/batch_${batchType}.php`;
                console.log('Making AJAX request to:', ajaxUrl);

                $.ajax({
                    url: ajaxUrl,
                    method: 'POST',
                    data: formData,
                    processData: false,
                    contentType: false,
                    success: (response) => {
                        console.log('AJAX success response:', response);
                        $('#loadingOverlay').hide();

                        if (response.success) {
                            alert(`Batch ${batchType} processed successfully!\n\nProcessed: ${response.data.processed}\nSuccess: ${response.data.success_count}\nErrors: ${response.data.error_count}`);
                            location.reload();
                        } else {
                            alert('Error: ' + response.error);
                            $('#progressSection').hide();
                        }
                    },
                    error: (xhr) => {
                        console.error('AJAX error:', xhr);
                        console.error('Response text:', xhr.responseText);
                        $('#loadingOverlay').hide();
                        $('#progressSection').hide();

                        let errorMessage = 'Unknown error';
                        try {
                            const errorResponse = JSON.parse(xhr.responseText);
                            errorMessage = errorResponse.error || errorMessage;
                        } catch (e) {
                            errorMessage = xhr.responseText || errorMessage;
                        }

                        alert('Error processing batch: ' + errorMessage);
                    }
                });
            }

            formatFileSize(bytes) {
                if (bytes === 0) return '0 Bytes';
                const k = 1024;
                const sizes = ['Bytes', 'KB', 'MB', 'GB'];
                const i = Math.floor(Math.log(bytes) / Math.log(k));
                return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
            }

            // Initialize tooltips for file names
            initializeTooltips() {
                // Initialize Bootstrap tooltips for file names
                const tooltipTriggerList = [].slice.call(document.querySelectorAll('.file-name-tooltip'));
                tooltipTriggerList.map(function (tooltipTriggerEl) {
                    return new bootstrap.Tooltip(tooltipTriggerEl, {
                        placement: 'top',
                        trigger: 'hover'
                    });
                });
            }
        }

        // Initialize file manager
        let fileManager;

        $(document).ready(function() {
            fileManager = new BatchFileManager();
        });








        // Function to view receipt image
        function viewReceipt(imagePath, expenseNo, imageNumber = 1) {
            if (!imagePath) {
                alert('No receipt image available');
                return;
            }

            // Create modal HTML
            const modalHtml = `
                <div class="modal fade" id="receiptModal" tabindex="-1" aria-labelledby="receiptModalLabel" aria-hidden="true">
                    <div class="modal-dialog modal-lg">
                        <div class="modal-content">
                            <div class="modal-header">
                                <h5 class="modal-title" id="receiptModalLabel">
                                    <i class="fas fa-receipt me-2"></i>Receipt ${imageNumber} - ${expenseNo}
                                </h5>
                                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                            </div>
                            <div class="modal-body text-center">
                                <img src="../uploads/receipts/${imagePath}"
                                     class="img-fluid"
                                     alt="Receipt Image ${imageNumber}"
                                     style="max-height: 70vh; border-radius: 8px; box-shadow: 0 4px 8px rgba(0,0,0,0.1);"
                                     onerror="this.src='../assets/images/no-image.png'; this.alt='Image not found';">
                            </div>
                            <div class="modal-footer">
                                <a href="../uploads/receipts/${imagePath}"
                                   target="_blank"
                                   class="btn btn-outline-primary">
                                    <i class="fas fa-external-link-alt me-1"></i>Open in New Tab
                                </a>
                                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                            </div>
                        </div>
                    </div>
                </div>
            `;

            // Remove existing modal if any
            $('#receiptModal').remove();

            // Add modal to body
            $('body').append(modalHtml);

            // Show modal
            $('#receiptModal').modal('show');

            // Remove modal from DOM when hidden
            $('#receiptModal').on('hidden.bs.modal', function () {
                $(this).remove();
            });
        }

        // Function to view transfer slip image
        function viewTransferSlip(imagePath, expenseNo, slipType = 'Transfer') {
            if (!imagePath) {
                alert('No transfer slip image available');
                return;
            }

            // Determine upload folder and image URL based on slip type and path format
            let uploadFolder = 'transfer_slips';
            let iconClass = 'fa-money-check-alt';
            let colorClass = 'success';
            let imageUrl = '';

            if (slipType === 'Verification') {
                uploadFolder = 'verification_slips';
                iconClass = 'fa-check-double';
                colorClass = 'primary';
            } else if (slipType === 'Review') {
                uploadFolder = 'review_slips';
                iconClass = 'fa-clipboard-check';
                colorClass = 'warning';
            }

            // Check if imagePath already contains folder structure (batch documents)
            if (imagePath.includes('/')) {
                // Path already contains folder structure (e.g., batch_documents/verification/...)
                imageUrl = `../uploads/${imagePath}`;
            } else {
                // Path is just filename, use default folder structure
                imageUrl = `../uploads/${uploadFolder}/${imagePath}`;
            }

            // Create modal HTML
            const modalHtml = `
                <div class="modal fade" id="transferSlipModal" tabindex="-1" aria-labelledby="transferSlipModalLabel" aria-hidden="true">
                    <div class="modal-dialog modal-lg">
                        <div class="modal-content">
                            <div class="modal-header bg-${colorClass} text-white">
                                <h5 class="modal-title" id="transferSlipModalLabel">
                                    <i class="fas ${iconClass} me-2"></i>${slipType} Slip - ${expenseNo}
                                </h5>
                                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
                            </div>
                            <div class="modal-body text-center">
                                <img src="${imageUrl}"
                                     class="img-fluid"
                                     alt="${slipType} Slip Image"
                                     style="max-height: 70vh; border-radius: 8px; box-shadow: 0 4px 8px rgba(0,0,0,0.1);"
                                     onerror="this.src='../assets/images/no-image.png'; this.alt='Image not found';">
                            </div>
                            <div class="modal-footer">
                                <a href="${imageUrl}"
                                   target="_blank"
                                   class="btn btn-outline-${colorClass}">
                                    <i class="fas fa-external-link-alt me-1"></i>Open in New Tab
                                </a>
                                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                            </div>
                        </div>
                    </div>
                </div>
            `;

            // Remove existing modal if any
            $('#transferSlipModal').remove();

            // Add modal to body
            $('body').append(modalHtml);

            // Show modal
            $('#transferSlipModal').modal('show');

            // Remove modal from DOM when hidden
            $('#transferSlipModal').on('hidden.bs.modal', function () {
                $(this).remove();
            });
        }
        // Batch management functions
        function retryBatch(batchId) {
            if (confirm('Are you sure you want to retry this batch operation?\n\nThis will reset the batch status and allow reprocessing.')) {
                // Reset batch status to pending and reload page
                $.ajax({
                    url: '../api/batch_retry.php',
                    method: 'POST',
                    contentType: 'application/json',
                    data: JSON.stringify({
                        batch_id: batchId
                    }),
                    success: function(response) {
                        if (response.success) {
                            alert('Batch reset successfully! You can now reprocess it.');
                            location.reload();
                        } else {
                            alert('Error: ' + response.error);
                        }
                    },
                    error: function(xhr) {
                        const response = xhr.responseJSON || {};
                        alert('Error: ' + (response.error || 'Unknown error occurred'));
                    }
                });
            }
        }

        function cancelBatch(batchId) {
            if (confirm('Are you sure you want to cancel this batch operation?\n\nThis action cannot be undone.')) {
                $.ajax({
                    url: '../api/batch_cancel.php',
                    method: 'POST',
                    contentType: 'application/json',
                    data: JSON.stringify({
                        batch_id: batchId
                    }),
                    success: function(response) {
                        if (response.success) {
                            alert('Batch cancelled successfully!');
                            location.reload();
                        } else {
                            alert('Error: ' + response.error);
                        }
                    },
                    error: function(xhr) {
                        const response = xhr.responseJSON || {};
                        alert('Error: ' + (response.error || 'Unknown error occurred'));
                    }
                });
            }
        }
    </script>
</body>
</html>