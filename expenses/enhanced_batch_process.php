<?php
/**
 * Enhanced Batch Process
 * Supports multiple file uploads with individual transfer numbers
 * Improved workflow for verification batches
 */

session_start();
require_once '../config/database.php';
require_once '../includes/functions.php';
require_once '../includes/BatchOperations.php';

// Check if user is logged in
if (!isset($_SESSION['user_id'])) {
    header('Location: ../login.php');
    exit();
}

$user_id = $_SESSION['user_id'];
$user_role = $_SESSION['role'];
$username = $_SESSION['username'];

// Get batch parameters
$batch_id = $_GET['batch_id'] ?? '';
$batch_type = $_GET['type'] ?? '';

if (empty($batch_id) || !in_array($batch_type, ['verification', 'review'])) {
    header('Location: ../dashboard.php');
    exit();
}

// Check user permissions
if ($batch_type === 'verification' && !in_array($user_role, ['verification', 'administrator'])) {
    header('Location: ../dashboard.php');
    exit();
}

if ($batch_type === 'review' && !in_array($user_role, ['reviewer', 'administrator'])) {
    header('Location: ../dashboard.php');
    exit();
}

try {
    $database = new Database();
    $db = $database->getConnection();
    $batchOps = new BatchOperations($db);

    $batch_info = $batchOps->getBatchInfo($batch_id);
    if (!$batch_info) {
        throw new Exception('Batch not found');
    }

    // Check if user owns this batch (unless admin)
    if ($user_role !== 'administrator' && $batch_info['user_id'] != $user_id) {
        throw new Exception('Access denied to this batch');
    }

    $batch_items = $batchOps->getBatchItems($batch_id);
    $total_amount = array_sum(array_column($batch_items, 'total_amount'));

} catch (Exception $e) {
    header('Location: ../dashboard.php?error=' . urlencode($e->getMessage()));
    exit();
}
?>

<!DOCTYPE html>
<html lang="th">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Enhanced Batch Process | Expense Management System</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    
    <style>
        .upload-section {
            border: 2px dashed #dee2e6;
            border-radius: 10px;
            padding: 20px;
            text-align: center;
            transition: all 0.3s ease;
        }
        
        .upload-section:hover {
            border-color: #0d6efd;
            background-color: #f8f9fa;
        }
        
        .upload-section.dragover {
            border-color: #0d6efd;
            background-color: #e7f3ff;
        }
        
        .file-item {
            border: 1px solid #dee2e6;
            border-radius: 8px;
            padding: 15px;
            margin-bottom: 15px;
            background: #f8f9fa;
        }
        
        .file-preview {
            max-width: 100px;
            max-height: 100px;
            object-fit: cover;
            border-radius: 5px;
        }
        
        .amount-display {
            font-size: 1.2em;
            font-weight: bold;
        }
        
        .difference-positive {
            color: #198754;
        }
        
        .difference-negative {
            color: #dc3545;
        }
        
        .batch-summary {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 10px;
            padding: 20px;
            margin-bottom: 20px;
        }
    </style>
</head>
<body class="bg-light">
    <?php include '../includes/navbar.php'; ?>
    
    <div class="container-fluid mt-4">
        <div class="row">
            <div class="col-12">
                <!-- Batch Summary -->
                <div class="batch-summary">
                    <div class="row align-items-center">
                        <div class="col-md-8">
                            <h4 class="mb-1">
                                <i class="fas fa-layer-group me-2"></i>
                                Enhanced Batch Process: <?php echo htmlspecialchars($batch_id); ?>
                            </h4>
                            <p class="mb-0">
                                <i class="fas fa-list me-1"></i>
                                <?php echo count($batch_items); ?> รายการ | 
                                <i class="fas fa-money-bill-wave me-1"></i>
                                ยอดรวม: ฿<?php echo number_format($total_amount, 2); ?>
                            </p>
                        </div>
                        <div class="col-md-4 text-end">
                            <span class="badge bg-light text-dark fs-6">
                                <?php echo ucfirst($batch_type); ?> Batch
                            </span>
                        </div>
                    </div>
                </div>

                <!-- Main Form -->
                <form id="enhanced_batch_form" enctype="multipart/form-data">
                    <input type="hidden" name="batch_id" value="<?php echo htmlspecialchars($batch_id); ?>">
                    <input type="hidden" name="batch_type" value="<?php echo htmlspecialchars($batch_type); ?>">
                    <input type="hidden" name="total_amount" value="<?php echo $total_amount; ?>">
                    
                    <div class="row">
                        <!-- Left Column: Batch Items -->
                        <div class="col-md-6">
                            <div class="card">
                                <div class="card-header">
                                    <h5 class="mb-0">
                                        <i class="fas fa-list-ul me-2"></i>รายการในแบทช์
                                    </h5>
                                </div>
                                <div class="card-body">
                                    <div class="table-responsive">
                                        <table class="table table-sm">
                                            <thead>
                                                <tr>
                                                    <th>Expense No.</th>
                                                    <th>Amount</th>
                                                    <th>Status</th>
                                                </tr>
                                            </thead>
                                            <tbody>
                                                <?php foreach ($batch_items as $item): ?>
                                                <tr>
                                                    <td>
                                                        <a href="view.php?id=<?php echo $item['id']; ?>" 
                                                           target="_blank" class="text-decoration-none">
                                                            <?php echo htmlspecialchars($item['exno']); ?>
                                                            <i class="fas fa-external-link-alt ms-1 text-muted"></i>
                                                        </a>
                                                    </td>
                                                    <td>฿<?php echo number_format($item['total_amount'], 2); ?></td>
                                                    <td><?php echo getStatusBadge($item['status']); ?></td>
                                                </tr>
                                                <?php endforeach; ?>
                                            </tbody>
                                            <tfoot>
                                                <tr class="table-primary">
                                                    <th>รวม</th>
                                                    <th>฿<?php echo number_format($total_amount, 2); ?></th>
                                                    <th></th>
                                                </tr>
                                            </tfoot>
                                        </table>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <!-- Right Column: Upload Section -->
                        <div class="col-md-6">
                            <div class="card">
                                <div class="card-header">
                                    <h5 class="mb-0">
                                        <i class="fas fa-cloud-upload-alt me-2"></i>อัพโหลดเอกสาร
                                    </h5>
                                </div>
                                <div class="card-body">
                                    <!-- Upload Area -->
                                    <div class="upload-section" id="upload-area">
                                        <i class="fas fa-cloud-upload-alt fa-3x text-muted mb-3"></i>
                                        <h6>ลากไฟล์มาวางที่นี่ หรือคลิกเพื่อเลือกไฟล์</h6>
                                        <p class="text-muted mb-3">รองรับ JPG, PNG, PDF (สูงสุด 5MB ต่อไฟล์)</p>
                                        <input type="file" id="file-input" multiple accept="image/*,.pdf" style="display: none;">
                                        <button type="button" class="btn btn-outline-primary" onclick="document.getElementById('file-input').click()">
                                            <i class="fas fa-plus me-2"></i>เลือกไฟล์
                                        </button>
                                    </div>
                                    
                                    <!-- File List -->
                                    <div id="file-list" class="mt-3"></div>
                                    
                                    <!-- Amount Summary -->
                                    <div class="card mt-3 border-info">
                                        <div class="card-body">
                                            <div class="row text-center">
                                                <div class="col-4">
                                                    <div class="amount-display text-primary">
                                                        ฿<span id="total-amount"><?php echo number_format($total_amount, 2); ?></span>
                                                    </div>
                                                    <small class="text-muted">ยอดรวมแบทช์</small>
                                                </div>
                                                <div class="col-4">
                                                    <div class="amount-display text-success">
                                                        ฿<span id="entered-total">0.00</span>
                                                    </div>
                                                    <small class="text-muted">ยอดที่กรอก</small>
                                                </div>
                                                <div class="col-4">
                                                    <div class="amount-display" id="difference-display">
                                                        ฿<span id="difference">0.00</span>
                                                    </div>
                                                    <small class="text-muted">ส่วนต่าง</small>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    
                                    <!-- Submit Button -->
                                    <div class="d-grid mt-3">
                                        <button type="submit" class="btn btn-primary btn-lg" id="submit-btn" disabled>
                                            <i class="fas fa-check me-2"></i>Process Enhanced Batch
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>
    
    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <!-- jQuery -->
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    
    <script>
        let uploadedFiles = [];
        let totalBatchAmount = <?php echo $total_amount; ?>;
        
        // File upload handling
        const uploadArea = document.getElementById('upload-area');
        const fileInput = document.getElementById('file-input');
        const fileList = document.getElementById('file-list');
        
        // Drag and drop functionality
        uploadArea.addEventListener('dragover', (e) => {
            e.preventDefault();
            uploadArea.classList.add('dragover');
        });
        
        uploadArea.addEventListener('dragleave', () => {
            uploadArea.classList.remove('dragover');
        });
        
        uploadArea.addEventListener('drop', (e) => {
            e.preventDefault();
            uploadArea.classList.remove('dragover');
            handleFiles(e.dataTransfer.files);
        });
        
        uploadArea.addEventListener('click', () => {
            fileInput.click();
        });
        
        fileInput.addEventListener('change', (e) => {
            handleFiles(e.target.files);
        });
        
        function handleFiles(files) {
            Array.from(files).forEach(file => {
                if (file.size > 5 * 1024 * 1024) {
                    alert(`ไฟล์ ${file.name} มีขนาดใหญ่เกิน 5MB`);
                    return;
                }
                
                const fileId = Date.now() + Math.random();
                uploadedFiles.push({
                    id: fileId,
                    file: file,
                    transferNumber: '',
                    amount: 0
                });
                
                renderFileItem(fileId, file);
            });
            
            updateAmountSummary();
        }
        
        function renderFileItem(fileId, file) {
            const fileItem = document.createElement('div');
            fileItem.className = 'file-item';
            fileItem.innerHTML = `
                <div class="row align-items-center">
                    <div class="col-md-3">
                        <div class="file-preview-container">
                            ${file.type.startsWith('image/') ? 
                                `<img src="${URL.createObjectURL(file)}" class="file-preview" alt="Preview">` :
                                `<i class="fas fa-file-pdf fa-3x text-danger"></i>`
                            }
                        </div>
                        <small class="text-muted d-block mt-1">${file.name}</small>
                    </div>
                    <div class="col-md-4">
                        <label class="form-label">Transfer Number</label>
                        <input type="text" class="form-control transfer-number" 
                               placeholder="เลขที่โอน" data-file-id="${fileId}" required>
                    </div>
                    <div class="col-md-3">
                        <label class="form-label">Amount</label>
                        <input type="number" class="form-control file-amount" 
                               placeholder="0.00" step="0.01" data-file-id="${fileId}" required>
                    </div>
                    <div class="col-md-2">
                        <button type="button" class="btn btn-outline-danger btn-sm" 
                                onclick="removeFile(${fileId})">
                            <i class="fas fa-trash"></i>
                        </button>
                    </div>
                </div>
            `;
            
            fileList.appendChild(fileItem);
            
            // Add event listeners
            fileItem.querySelector('.transfer-number').addEventListener('input', (e) => {
                updateFileData(fileId, 'transferNumber', e.target.value);
            });
            
            fileItem.querySelector('.file-amount').addEventListener('input', (e) => {
                updateFileData(fileId, 'amount', parseFloat(e.target.value) || 0);
                updateAmountSummary();
            });
        }
        
        function updateFileData(fileId, field, value) {
            const fileData = uploadedFiles.find(f => f.id === fileId);
            if (fileData) {
                fileData[field] = value;
            }
        }
        
        function removeFile(fileId) {
            uploadedFiles = uploadedFiles.filter(f => f.id !== fileId);
            document.querySelector(`[data-file-id="${fileId}"]`).closest('.file-item').remove();
            updateAmountSummary();
        }
        
        function updateAmountSummary() {
            const enteredTotal = uploadedFiles.reduce((sum, file) => sum + file.amount, 0);
            const difference = enteredTotal - totalBatchAmount;
            
            document.getElementById('entered-total').textContent = enteredTotal.toFixed(2);
            document.getElementById('difference').textContent = Math.abs(difference).toFixed(2);
            
            const differenceDisplay = document.getElementById('difference-display');
            differenceDisplay.className = 'amount-display ' + 
                (difference > 0 ? 'difference-positive' : difference < 0 ? 'difference-negative' : '');
            
            // Enable submit button only if amounts match and all files have transfer numbers
            const allFilesComplete = uploadedFiles.length > 0 && 
                uploadedFiles.every(f => f.transferNumber.trim() && f.amount > 0);
            const amountsMatch = Math.abs(difference) < 0.01;
            
            document.getElementById('submit-btn').disabled = !(allFilesComplete && amountsMatch);
        }
        
        // Form submission
        document.getElementById('enhanced_batch_form').addEventListener('submit', function(e) {
            e.preventDefault();
            
            if (uploadedFiles.length === 0) {
                alert('กรุณาอัพโหลดไฟล์อย่างน้อย 1 ไฟล์');
                return;
            }
            
            const formData = new FormData(this);
            
            // Add file data
            uploadedFiles.forEach((fileData, index) => {
                formData.append(`files[${index}]`, fileData.file);
                formData.append(`transfer_numbers[${index}]`, fileData.transferNumber);
                formData.append(`amounts[${index}]`, fileData.amount);
            });
            
            const submitBtn = document.getElementById('submit-btn');
            const originalText = submitBtn.innerHTML;
            submitBtn.disabled = true;
            submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>กำลังประมวลผล...';
            
            fetch('../api/process_enhanced_batch.php', {
                method: 'POST',
                body: formData
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    alert('ประมวลผลแบทช์เรียบร้อยแล้ว!');
                    window.location.href = 'my_batches.php';
                } else {
                    alert('เกิดข้อผิดพลาด: ' + data.error);
                    submitBtn.disabled = false;
                    submitBtn.innerHTML = originalText;
                }
            })
            .catch(error => {
                alert('เกิดข้อผิดพลาด: ' + error.message);
                submitBtn.disabled = false;
                submitBtn.innerHTML = originalText;
            });
        });
    </script>
</body>
</html>
