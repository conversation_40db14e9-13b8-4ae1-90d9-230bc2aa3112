<?php
session_start();
require_once '../config/database.php';
require_once '../includes/functions.php';

// Check if user is logged in
if (!isset($_SESSION['user_id'])) {
    header('Location: ../login.php');
    exit();
}

$user_id = $_SESSION['user_id'];
$user_role = $_SESSION['role'];
$username = $_SESSION['username'];

// Get batch type from URL
$batch_type = $_GET['type'] ?? '';

// Set default batch type based on user role if no type specified
if (empty($batch_type)) {
    if ($user_role === 'administrator') {
        $batch_type = 'verification';
    } elseif ($user_role === 'reviewer') {
        $batch_type = 'review';
    } elseif ($user_role === 'verification') {
        $batch_type = 'verification';
    }
}

// Validate batch type and user permissions
if (!in_array($batch_type, ['verification', 'review'])) {
    header('Location: ../dashboard.php');
    exit();
}

// Check user permissions
if ($batch_type === 'verification' && !in_array($user_role, ['verification', 'administrator'])) {
    header('Location: ../dashboard.php');
    exit();
}

if ($batch_type === 'review' && !in_array($user_role, ['reviewer', 'administrator'])) {
    header('Location: ../dashboard.php');
    exit();
}

// Initialize variables
$batches = [];
$stats = [];
$total_records = 0;
$total_pages = 0;
$error = null;

try {
    $database = new Database();
    $db = $database->getConnection();
    
    // Get filter parameters
    $status_filter = $_GET['status'] ?? '';
    $date_from = $_GET['date_from'] ?? '';
    $date_to = $_GET['date_to'] ?? '';
    $search = $_GET['search'] ?? '';
    
    // Pagination
    $page = isset($_GET['page']) ? (int)$_GET['page'] : 1;
    $limit = 10;
    $offset = ($page - 1) * $limit;
    
    // Build WHERE conditions
    $where_conditions = ['bo.operation_type = ?'];
    $params = [$batch_type];
    
    // User filter - only show own batches unless admin
    if ($user_role !== 'administrator') {
        $where_conditions[] = 'bo.user_id = ?';
        $params[] = $user_id;
    }
    
    // Status filter
    if (!empty($status_filter)) {
        $where_conditions[] = 'bo.status = ?';
        $params[] = $status_filter;
    }
    
    // Date filters
    if (!empty($date_from)) {
        $where_conditions[] = 'DATE(bo.created_at) >= ?';
        $params[] = $date_from;
    }
    
    if (!empty($date_to)) {
        $where_conditions[] = 'DATE(bo.created_at) <= ?';
        $params[] = $date_to;
    }
    
    // Search filter
    if (!empty($search)) {
        $where_conditions[] = '(bo.batch_id LIKE ? OR bo.notes LIKE ?)';
        $search_param = '%' . $search . '%';
        $params[] = $search_param;
        $params[] = $search_param;
    }
    
    $where_clause = 'WHERE ' . implode(' AND ', $where_conditions);
    
    // Get total count for pagination
    $count_sql = "
        SELECT COUNT(*) as total
        FROM batch_operations bo
        LEFT JOIN users u ON bo.user_id = u.id
        $where_clause
    ";
    $count_stmt = $db->prepare($count_sql);
    $count_stmt->execute($params);
    $total_records = $count_stmt->fetch()['total'];
    $total_pages = ceil($total_records / $limit);
    
    // Get batches
    $sql = "
        SELECT bo.*, u.full_name, u.username,
               COUNT(bi.id) as total_items,
               SUM(CASE WHEN bi.status = 'completed' THEN 1 ELSE 0 END) as completed_items,
               SUM(CASE WHEN bi.status = 'failed' THEN 1 ELSE 0 END) as failed_items
        FROM batch_operations bo
        LEFT JOIN users u ON bo.user_id = u.id
        LEFT JOIN batch_items bi ON bo.batch_id = bi.batch_id
        $where_clause
        GROUP BY bo.id
        ORDER BY bo.created_at DESC
        LIMIT {$limit} OFFSET {$offset}
    ";

    $stmt = $db->prepare($sql);
    $stmt->execute($params);
    $batches = $stmt->fetchAll();
    
    // Get summary statistics
    $stats_sql = "
        SELECT 
            COUNT(*) as total_batches,
            SUM(CASE WHEN status = 'pending' THEN 1 ELSE 0 END) as pending_count,
            SUM(CASE WHEN status = 'processing' THEN 1 ELSE 0 END) as processing_count,
            SUM(CASE WHEN status = 'completed' THEN 1 ELSE 0 END) as completed_count,
            SUM(CASE WHEN status = 'failed' THEN 1 ELSE 0 END) as failed_count,
            SUM(CASE WHEN status = 'cancelled' THEN 1 ELSE 0 END) as cancelled_count,
            SUM(total_amount) as total_amount,
            SUM(item_count) as total_items
        FROM batch_operations bo
        WHERE bo.operation_type = ?" . 
        ($user_role !== 'administrator' ? ' AND bo.user_id = ?' : '');
    
    $stats_params = [$batch_type];
    if ($user_role !== 'administrator') {
        $stats_params[] = $user_id;
    }
    
    $stats_stmt = $db->prepare($stats_sql);
    $stats_stmt->execute($stats_params);
    $stats = $stats_stmt->fetch();

} catch (Exception $e) {
    $error = $e->getMessage();
    // Initialize default stats in case of error
    $stats = [
        'total_batches' => 0,
        'pending_count' => 0,
        'processing_count' => 0,
        'completed_count' => 0,
        'failed_count' => 0,
        'cancelled_count' => 0,
        'total_amount' => 0,
        'total_items' => 0
    ];
    $batches = [];
    $total_records = 0;
    $total_pages = 0;
}

// Status colors
$status_colors = [
    'pending' => 'warning',
    'processing' => 'info',
    'completed' => 'success',
    'failed' => 'danger',
    'cancelled' => 'secondary'
];

$page_title = ($user_role === 'administrator' ? 'All ' : 'My ') . ucfirst($batch_type) . ' Batches';
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $page_title; ?> - Expense Management</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        .batch-card {
            transition: all 0.2s;
            border-left: 4px solid #dee2e6;
        }
        .batch-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.1);
        }
        .batch-card.status-pending { border-left-color: #ffc107; }
        .batch-card.status-processing { border-left-color: #0dcaf0; }
        .batch-card.status-completed { border-left-color: #198754; }
        .batch-card.status-failed { border-left-color: #dc3545; }
        .batch-card.status-cancelled { border-left-color: #6c757d; }
        
        .stats-card {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 10px;
        }
        
        .filter-card {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 20px;
            margin-bottom: 20px;
        }
        
        .progress-ring {
            width: 60px;
            height: 60px;
        }
        
        .batch-actions .btn {
            margin: 2px;
        }
    </style>
</head>
<body>
    <?php include '../includes/navbar.php'; ?>
    
    <div class="container-fluid mt-4">
        <div class="row">
            <div class="col-12">
                <div class="d-flex justify-content-between align-items-center mb-4">
                    <h1><i class="fas fa-history me-2"></i><?php echo $page_title; ?></h1>
                    <div class="d-flex gap-2">
                        <a href="multi_<?php echo $batch_type; ?>.php" class="btn btn-primary">
                            <i class="fas fa-plus me-1"></i>Create New <?php echo ucfirst($batch_type); ?> Batch
                        </a>
                        <a href="../dashboard.php" class="btn btn-outline-secondary">
                            <i class="fas fa-arrow-left me-1"></i>Back to Dashboard
                        </a>
                    </div>
                </div>

                <?php if ($user_role === 'administrator'): ?>
                <!-- Admin Navigation Tabs -->
                <ul class="nav nav-tabs mb-4">
                    <li class="nav-item">
                        <a class="nav-link <?php echo $batch_type === 'verification' ? 'active' : ''; ?>"
                           href="my_batches.php?type=verification">
                            <i class="fas fa-search me-1"></i>Verification Batches
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link <?php echo $batch_type === 'review' ? 'active' : ''; ?>"
                           href="my_batches.php?type=review">
                            <i class="fas fa-clipboard-check me-1"></i>Review Batches
                        </a>
                    </li>
                </ul>
                <?php endif; ?>
                
                <?php if (isset($error)): ?>
                <div class="alert alert-danger">
                    <i class="fas fa-exclamation-triangle me-2"></i><?php echo htmlspecialchars($error); ?>
                </div>
                <?php endif; ?>
                
                <!-- Summary Statistics -->
                <div class="row mb-4">
                    <div class="col-12">
                        <div class="stats-card p-4">
                            <h5 class="mb-3"><i class="fas fa-chart-bar me-2"></i>Summary Statistics</h5>
                            <div class="row text-center">
                                <div class="col-md-2">
                                    <h3><?php echo number_format($stats['total_batches']); ?></h3>
                                    <small>Total Batches</small>
                                </div>
                                <div class="col-md-2">
                                    <h3><?php echo number_format($stats['total_items']); ?></h3>
                                    <small>Total Items</small>
                                </div>
                                <div class="col-md-2">
                                    <h3><?php echo number_format($stats['total_amount'], 2); ?></h3>
                                    <small>Total Amount (฿)</small>
                                </div>
                                <div class="col-md-2">
                                    <h3 class="text-warning"><?php echo number_format($stats['pending_count']); ?></h3>
                                    <small>Pending</small>
                                </div>
                                <div class="col-md-2">
                                    <h3 class="text-success"><?php echo number_format($stats['completed_count']); ?></h3>
                                    <small>Completed</small>
                                </div>
                                <div class="col-md-2">
                                    <h3 class="text-danger"><?php echo number_format($stats['failed_count']); ?></h3>
                                    <small>Failed</small>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

        <!-- Search and Filter -->
        <div class="filter-card">
            <form method="GET" class="row g-3">
                <input type="hidden" name="type" value="<?php echo htmlspecialchars($batch_type); ?>">

                <div class="col-md-3">
                    <label for="search" class="form-label">Search</label>
                    <input type="text" class="form-control" id="search" name="search"
                           placeholder="Search by Batch ID or Notes..."
                           value="<?php echo htmlspecialchars($search); ?>">
                </div>

                <div class="col-md-2">
                    <label for="status" class="form-label">Status</label>
                    <select class="form-select" id="status" name="status">
                        <option value="">All Statuses</option>
                        <option value="pending" <?php echo $status_filter === 'pending' ? 'selected' : ''; ?>>Pending</option>
                        <option value="processing" <?php echo $status_filter === 'processing' ? 'selected' : ''; ?>>Processing</option>
                        <option value="completed" <?php echo $status_filter === 'completed' ? 'selected' : ''; ?>>Completed</option>
                        <option value="failed" <?php echo $status_filter === 'failed' ? 'selected' : ''; ?>>Failed</option>
                        <option value="cancelled" <?php echo $status_filter === 'cancelled' ? 'selected' : ''; ?>>Cancelled</option>
                    </select>
                </div>

                <div class="col-md-2">
                    <label for="date_from" class="form-label">Date From</label>
                    <input type="date" class="form-control" id="date_from" name="date_from"
                           value="<?php echo htmlspecialchars($date_from); ?>">
                </div>

                <div class="col-md-2">
                    <label for="date_to" class="form-label">Date To</label>
                    <input type="date" class="form-control" id="date_to" name="date_to"
                           value="<?php echo htmlspecialchars($date_to); ?>">
                </div>

                <div class="col-md-3">
                    <label class="form-label">&nbsp;</label>
                    <div class="d-flex gap-2">
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-search me-1"></i>Search
                        </button>
                        <a href="my_batches.php?type=<?php echo htmlspecialchars($batch_type); ?>" class="btn btn-outline-secondary">
                            <i class="fas fa-times me-1"></i>Clear
                        </a>
                    </div>
                </div>
            </form>
        </div>

        <!-- Batches List -->
        <div class="row">
            <div class="col-12">
                <?php if (empty($batches)): ?>
                <div class="text-center py-5">
                    <i class="fas fa-inbox fa-3x text-muted mb-3"></i>
                    <h5 class="text-muted">No batches found</h5>
                    <p class="text-muted">
                        <?php if (empty($search) && empty($status_filter) && empty($date_from) && empty($date_to)): ?>
                            You haven't created any <?php echo $batch_type; ?> batches yet.
                        <?php else: ?>
                            No batches match your search criteria.
                        <?php endif; ?>
                    </p>
                    <a href="multi_<?php echo $batch_type; ?>.php" class="btn btn-primary">
                        <i class="fas fa-plus me-1"></i>Create Your First <?php echo ucfirst($batch_type); ?> Batch
                    </a>
                </div>
                <?php else: ?>

                <!-- Batch Cards -->
                <?php foreach ($batches as $batch): ?>
                <div class="card batch-card status-<?php echo $batch['status']; ?> mb-3">
                    <div class="card-body">
                        <div class="row align-items-center">
                            <div class="col-md-3">
                                <h6 class="mb-1">
                                    <i class="fas fa-barcode me-1"></i>
                                    <?php echo htmlspecialchars($batch['batch_id']); ?>
                                </h6>
                                <small class="text-muted">
                                    Created: <?php echo date('M d, Y H:i', strtotime($batch['created_at'])); ?>
                                </small>
                                <?php if ($user_role === 'administrator' && $batch['user_id'] != $user_id): ?>
                                <br><small class="text-info">
                                    By: <?php echo htmlspecialchars($batch['full_name']); ?>
                                </small>
                                <?php endif; ?>
                            </div>

                            <div class="col-md-2 text-center">
                                <span class="badge bg-<?php echo $status_colors[$batch['status']]; ?> fs-6">
                                    <?php echo ucfirst($batch['status']); ?>
                                </span>
                                <?php if ($batch['status'] === 'processing'): ?>
                                <br><small class="text-muted">
                                    Started: <?php echo $batch['started_at'] ? date('H:i', strtotime($batch['started_at'])) : '-'; ?>
                                </small>
                                <?php elseif ($batch['status'] === 'completed'): ?>
                                <br><small class="text-success">
                                    Completed: <?php echo $batch['completed_at'] ? date('H:i', strtotime($batch['completed_at'])) : '-'; ?>
                                </small>
                                <?php endif; ?>
                            </div>

                            <div class="col-md-2 text-center">
                                <h5 class="mb-0"><?php echo number_format($batch['item_count']); ?></h5>
                                <small class="text-muted">Items</small>
                                <?php if ($batch['completed_items'] > 0 || $batch['failed_items'] > 0): ?>
                                <br><small class="text-success"><?php echo $batch['completed_items']; ?> done</small>
                                <?php if ($batch['failed_items'] > 0): ?>
                                <small class="text-danger">, <?php echo $batch['failed_items']; ?> failed</small>
                                <?php endif; ?>
                                <?php endif; ?>
                            </div>

                            <div class="col-md-2 text-center">
                                <h5 class="mb-0">฿<?php echo number_format($batch['total_amount'], 2); ?></h5>
                                <small class="text-muted">Total Amount</small>
                            </div>

                            <div class="col-md-3">
                                <div class="batch-actions d-flex flex-wrap justify-content-end">
                                    <a href="batch_process.php?batch_id=<?php echo urlencode($batch['batch_id']); ?>&type=<?php echo $batch_type; ?>"
                                       class="btn btn-outline-primary btn-sm" title="View Details">
                                        <i class="fas fa-eye"></i> View
                                    </a>

                                    <?php if ($batch['status'] === 'failed' && ($user_role === 'administrator' || $batch['user_id'] == $user_id)): ?>
                                    <button class="btn btn-outline-warning btn-sm"
                                            onclick="retryBatch('<?php echo $batch['batch_id']; ?>')"
                                            title="Retry Failed Batch">
                                        <i class="fas fa-redo"></i> Retry
                                    </button>
                                    <?php endif; ?>

                                    <?php if (in_array($batch['status'], ['pending', 'failed']) && ($user_role === 'administrator' || $batch['user_id'] == $user_id)): ?>
                                    <button class="btn btn-outline-danger btn-sm"
                                            onclick="cancelBatch('<?php echo $batch['batch_id']; ?>')"
                                            title="Cancel Batch">
                                        <i class="fas fa-times"></i> Cancel
                                    </button>
                                    <?php endif; ?>
                                </div>

                                <?php if (!empty($batch['notes'])): ?>
                                <small class="text-muted d-block mt-2">
                                    <i class="fas fa-sticky-note me-1"></i>
                                    <?php echo htmlspecialchars($batch['notes']); ?>
                                </small>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>
                </div>
                <?php endforeach; ?>

                <!-- Pagination -->
                <?php if ($total_pages > 1): ?>
                <nav aria-label="Batch pagination">
                    <ul class="pagination justify-content-center">
                        <?php if ($page > 1): ?>
                        <li class="page-item">
                            <a class="page-link" href="?type=<?php echo urlencode($batch_type); ?>&page=<?php echo $page - 1; ?>&<?php echo http_build_query(array_filter($_GET, function($k) { return $k !== 'page' && $k !== 'type'; }, ARRAY_FILTER_USE_KEY)); ?>">
                                <i class="fas fa-chevron-left"></i> Previous
                            </a>
                        </li>
                        <?php endif; ?>

                        <?php for ($i = max(1, $page - 2); $i <= min($total_pages, $page + 2); $i++): ?>
                        <li class="page-item <?php echo $i === $page ? 'active' : ''; ?>">
                            <a class="page-link" href="?type=<?php echo urlencode($batch_type); ?>&page=<?php echo $i; ?>&<?php echo http_build_query(array_filter($_GET, function($k) { return $k !== 'page' && $k !== 'type'; }, ARRAY_FILTER_USE_KEY)); ?>">
                                <?php echo $i; ?>
                            </a>
                        </li>
                        <?php endfor; ?>

                        <?php if ($page < $total_pages): ?>
                        <li class="page-item">
                            <a class="page-link" href="?type=<?php echo urlencode($batch_type); ?>&page=<?php echo $page + 1; ?>&<?php echo http_build_query(array_filter($_GET, function($k) { return $k !== 'page' && $k !== 'type'; }, ARRAY_FILTER_USE_KEY)); ?>">
                                Next <i class="fas fa-chevron-right"></i>
                            </a>
                        </li>
                        <?php endif; ?>
                    </ul>
                </nav>

                <div class="text-center text-muted">
                    Showing <?php echo ($offset + 1); ?> to <?php echo min($offset + $limit, $total_records); ?> of <?php echo $total_records; ?> batches
                </div>
                <?php endif; ?>

                <?php endif; ?>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Retry batch function
        function retryBatch(batchId) {
            if (confirm('Are you sure you want to retry this failed batch?')) {
                fetch('../api/batch_retry.php', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        batch_id: batchId
                    })
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        alert('Batch retry initiated successfully!');
                        location.reload();
                    } else {
                        alert('Error: ' + data.error);
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    alert('An error occurred while retrying the batch.');
                });
            }
        }

        // Cancel batch function
        function cancelBatch(batchId) {
            if (confirm('Are you sure you want to cancel this batch? This action cannot be undone.')) {
                fetch('../api/batch_cancel.php', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        batch_id: batchId
                    })
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        alert('Batch cancelled successfully!');
                        location.reload();
                    } else {
                        alert('Error: ' + data.error);
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    alert('An error occurred while cancelling the batch.');
                });
            }
        }

        // Auto-refresh for processing batches
        const processingBatches = document.querySelectorAll('.status-processing');
        if (processingBatches.length > 0) {
            setTimeout(() => {
                location.reload();
            }, 30000); // Refresh every 30 seconds if there are processing batches
        }
    </script>
</body>
</html>
