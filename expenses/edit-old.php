<?php
session_start();
require_once '../config/database.php';
require_once '../includes/functions.php';
require_once '../includes/ImageUploadHelper.php';

// Check if user is logged in
if (!isset($_SESSION['user_id'])) {
    header('Location: ../login.php');
    exit();
}

$database = new Database();
$db = $database->getConnection();

// Get user info
$stmt = $db->prepare("SELECT * FROM users WHERE id = ?");
$stmt->execute([$_SESSION['user_id']]);
$user = $stmt->fetch();

if (!$user || !$user['is_active']) {
    session_destroy();
    header('Location: ../login.php');
    exit();
}

// Check if user has permission to edit expenses (report_viewer cannot edit)
if ($_SESSION['role'] === 'report_viewer') {
    header('Location: ../dashboard.php?error=access_denied');
    exit();
}

// Get expense ID from URL
$expense_id = $_GET['id'] ?? null;
if (!$expense_id) {
    header('Location: list.php');
    exit();
}

// Get expense data
$stmt = $db->prepare("
    SELECT e.*, 
           i.name as item_name,
           c.name as customer_name,
           d.name as driver_name
    FROM expenses e
    LEFT JOIN items i ON e.item_id = i.id
    LEFT JOIN customers c ON e.customer_id = c.id
    LEFT JOIN drivers d ON e.driver_id = d.id
    WHERE e.id = ?
");
$stmt->execute([$expense_id]);
$expense = $stmt->fetch();

if (!$expense) {
    header('Location: list.php');
    exit();
}

// Check if user can edit this expense
$user_id = $_SESSION['user_id'];
$user_role = $_SESSION['role'];

if (!canEditExpense($expense, $user_id, $user_role)) {
    $_SESSION['message'] = 'You do not have permission to edit this expense. Rejected expenses cannot be edited except by the creator for resubmission.';
    $_SESSION['message_type'] = 'warning';
    header('Location: view.php?id=' . $expense_id);
    exit();
}

// Get receipt numbers for this expense
$stmt = $db->prepare("SELECT * FROM receipt_numbers WHERE expense_id = ? ORDER BY id");
$stmt->execute([$expense_id]);
$receipt_numbers = $stmt->fetchAll();

$error = '';
$success = '';

// Handle form submission
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    try {
        // Validate required fields
        $required_fields = ['job_open_date', 'withdrawal_date'];
        foreach ($required_fields as $field) {
            if (empty($_POST[$field])) {
                throw new Exception("Field '$field' is required.");
            }
        }
        
        // Validate dates
        $job_open_date = $_POST['job_open_date'];
        $withdrawal_date = $_POST['withdrawal_date'];
        
        if (!strtotime($job_open_date)) {
            throw new Exception("Invalid job open date.");
        }
        
        if (!strtotime($withdrawal_date)) {
            throw new Exception("Invalid withdrawal date.");
        }
        
        // Check withdrawal date is not more than 1 month in the past
        $one_month_ago = date('Y-m-d', strtotime('-1 month'));
        if ($withdrawal_date < $one_month_ago) {
            throw new Exception("Withdrawal date cannot be more than 1 month in the past.");
        }

        // Handle new item creation
        $item_id = $_POST['item_id'] ?? null;
        if (!empty($_POST['new_item']) && trim($_POST['new_item']) !== '') {
            $stmt = $db->prepare("INSERT INTO items (name, description, created_by) VALUES (?, ?, ?)");
            $stmt->execute([trim($_POST['new_item']), 'Added from expense form', $user['id']]);
            $item_id = $db->lastInsertId();

            logActivity(
                $db,
                $user['id'],
                'create',
                'items',
                $item_id,
                'Created new item: ' . $_POST['new_item'],
                $_SERVER['REMOTE_ADDR'] ?? '',
                $_SERVER['HTTP_USER_AGENT'] ?? ''
            );
        }

        // Handle new customer creation
        $customer_id = $_POST['customer_id'] ?? null;

        // Convert empty string to null for database compatibility
        if (empty($customer_id) || $customer_id === '') {
            $customer_id = null;
        }

        if (!empty($_POST['new_customer']) && trim($_POST['new_customer']) !== '') {
            $stmt = $db->prepare("INSERT INTO customers (name, description, created_by) VALUES (?, ?, ?)");
            $stmt->execute([trim($_POST['new_customer']), 'Added from expense form', $user['id']]);
            $customer_id = $db->lastInsertId();

            logActivity(
                $db,
                $user['id'],
                'create',
                'customers',
                $customer_id,
                'Created new customer: ' . $_POST['new_customer'],
                $_SERVER['REMOTE_ADDR'] ?? '',
                $_SERVER['HTTP_USER_AGENT'] ?? ''
            );
        }

        // Handle file uploads
        $transfer_slip_image = $expense['transfer_slip_image']; // Keep existing if no new upload
        
        // Upload new transfer slip with compression if provided
        if (isset($_FILES['transfer_slip']) && $_FILES['transfer_slip']['error'] === UPLOAD_ERR_OK) {
            $upload_result = ImageUploadHelper::uploadTransferSlip($_FILES['transfer_slip']);
            if ($upload_result['success']) {
                // Delete old file if exists
                if (!empty($expense['transfer_slip_image'])) {
                    ImageUploadHelper::deleteImage($expense['transfer_slip_image'], 'transfer_slips');
                }
                $transfer_slip_image = $upload_result['filename'];

                // Log compression results
                error_log("Transfer slip compressed: {$upload_result['filename']} - " .
                         "Original: " . ImageCompressor::formatFileSize($upload_result['original_size']) .
                         ", Compressed: " . ImageCompressor::formatFileSize($upload_result['compressed_size']) .
                         " (Saved {$upload_result['compression_ratio']}%)");
            } else {
                throw new Exception("Transfer slip upload error: " . $upload_result['error']);
            }
        }

        // Handle receipt uploads and updates
        $receipt_data = [];
        if (isset($_FILES['receipts']) && is_array($_FILES['receipts']['tmp_name'])) {
            foreach ($_FILES['receipts']['tmp_name'] as $key => $tmp_name) {
                if ($_FILES['receipts']['error'][$key] === UPLOAD_ERR_OK && !empty($tmp_name)) {
                    $file = [
                        'tmp_name' => $tmp_name,
                        'name' => $_FILES['receipts']['name'][$key],
                        'size' => $_FILES['receipts']['size'][$key],
                        'error' => $_FILES['receipts']['error'][$key],
                        'type' => $_FILES['receipts']['type'][$key]
                    ];

                    $receipt_number = isset($_POST['receipt_numbers']) && isset($_POST['receipt_numbers'][$key]) ? trim($_POST['receipt_numbers'][$key]) : '';
                    $upload_result = ImageUploadHelper::uploadReceiptImage($file, $receipt_number);

                    if ($upload_result['success']) {
                        $receipt_amount = isset($_POST['receipt_amounts']) && isset($_POST['receipt_amounts'][$key]) ? floatval($_POST['receipt_amounts'][$key]) : 0.00;
                        $receipt_description = isset($_POST['receipt_descriptions']) && isset($_POST['receipt_descriptions'][$key]) ? trim($_POST['receipt_descriptions'][$key]) : '';

                        $receipt_data[] = [
                            'filename' => $upload_result['filename'],
                            'receipt_number' => $receipt_number,
                            'amount' => $receipt_amount,
                            'description' => $receipt_description
                        ];

                        // Log compression results
                        error_log("Receipt compressed: {$upload_result['filename']} - " .
                                 "Original: " . ImageCompressor::formatFileSize($upload_result['original_size']) .
                                 ", Compressed: " . ImageCompressor::formatFileSize($upload_result['compressed_size']) .
                                 " (Saved {$upload_result['compression_ratio']}%)");
                    }
                }
            }
        }

        // Calculate total amount from receipts
        $total_amount = 0;
        foreach ($receipt_data as $receipt) {
            $total_amount += $receipt['amount'];
        }

        // Add existing receipt amounts if no new receipts uploaded
        if (empty($receipt_data)) {
            foreach ($receipt_numbers as $receipt) {
                $total_amount += $receipt['amount'];
            }
        }

        // Validate transfer amount equals receipt total
        $transfer_amount = floatval($_POST['transfer_amount'] ?? 0);
        if (abs($transfer_amount - $total_amount) > 0.01) { // Allow 0.01 difference for floating point precision
            throw new Exception("Transfer Amount (" . number_format($transfer_amount, 2) . " บาท) must equal Receipt Total (" . number_format($total_amount, 2) . " บาท). Please check your amounts.");
        }

        // Prepare other fields
        $requester = trim($_POST['requester'] ?? '');
        $receiver = trim($_POST['receiver'] ?? '');
        $payment_account_no = trim($_POST['payment_account_no'] ?? '');
        $vehicle_plate = trim($_POST['vehicle_plate'] ?? '');
        
        // Update expense record
        $stmt = $db->prepare("
            UPDATE expenses SET
                bookingno = ?, job_open_date = ?, item_id = ?, customer_id = ?,
                containerno = ?, driver_id = ?, vehicle_plate = ?, payment_account_no = ?,
                additional_details = ?, requester = ?, receiver = ?, withdrawal_date = ?,
                transfer_no = ?, transfer_amount = ?, total_amount = ?, transfer_slip_image = ?
            WHERE id = ?
        ");

        $stmt->execute([
            $_POST['bookingno'] ?? '',
            $job_open_date,
            $item_id,
            $customer_id,
            $_POST['containerno'] ?? '',
            !empty($_POST['driver_id']) ? $_POST['driver_id'] : null,
            $vehicle_plate,
            $payment_account_no,
            $_POST['additional_details'] ?? '',
            $requester,
            $receiver,
            $withdrawal_date,
            $_POST['transfer_no'] ?? '',
            $transfer_amount,
            $total_amount,
            $transfer_slip_image,
            $expense_id
        ]);

        // Update receipt numbers if new ones uploaded
        if (!empty($receipt_data)) {
            // Delete old receipt numbers and files
            foreach ($receipt_numbers as $old_receipt) {
                if (!empty($old_receipt['receipt_image']) && file_exists('../uploads/receipts/' . $old_receipt['receipt_image'])) {
                    unlink('../uploads/receipts/' . $old_receipt['receipt_image']);
                }
            }
            
            $stmt = $db->prepare("DELETE FROM receipt_numbers WHERE expense_id = ?");
            $stmt->execute([$expense_id]);

            // Insert new receipt numbers
            $stmt = $db->prepare("
                INSERT INTO receipt_numbers (expense_id, receipt_number, amount, description, receipt_image, created_by)
                VALUES (?, ?, ?, ?, ?, ?)
            ");

            foreach ($receipt_data as $receipt) {
                $stmt->execute([
                    $expense_id,
                    $receipt['receipt_number'],
                    $receipt['amount'],
                    $receipt['description'],
                    $receipt['filename'],
                    $user['id']
                ]);
            }
        }

        // Log the activity
        logActivity(
            $db,
            $user['id'],
            'update',
            'expenses',
            $expense_id,
            'Updated expense: ' . $expense['exno'] . ' with total amount: ' . number_format($total_amount, 2),
            $_SERVER['REMOTE_ADDR'],
            $_SERVER['HTTP_USER_AGENT']
        );

        $success = 'Expense updated successfully!';
        
        // Refresh expense data
        $stmt = $db->prepare("
            SELECT e.*, 
                   i.name as item_name,
                   c.name as customer_name,
                   d.name as driver_name
            FROM expenses e
            LEFT JOIN items i ON e.item_id = i.id
            LEFT JOIN customers c ON e.customer_id = c.id
            LEFT JOIN drivers d ON e.driver_id = d.id
            WHERE e.id = ?
        ");
        $stmt->execute([$expense_id]);
        $expense = $stmt->fetch();
        
        // Refresh receipt numbers
        $stmt = $db->prepare("SELECT * FROM receipt_numbers WHERE expense_id = ? ORDER BY id");
        $stmt->execute([$expense_id]);
        $receipt_numbers = $stmt->fetchAll();

    } catch (Exception $e) {
        $error = $e->getMessage();
    }
}

// Get dropdown data
$stmt = $db->query("SELECT * FROM items WHERE is_active = 1 ORDER BY name");
$items = $stmt->fetchAll();

$stmt = $db->query("SELECT * FROM customers WHERE is_active = 1 ORDER BY name");
$customers = $stmt->fetchAll();

$stmt = $db->query("SELECT * FROM drivers WHERE is_active = 1 ORDER BY name");
$drivers = $stmt->fetchAll();
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Edit Expense - Expenses Management System</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="../assets/css/style.css" rel="stylesheet">
    <meta name="csrf-token" content="<?php echo generateCSRFToken(); ?>">
</head>
<body>
    <!-- Navigation -->
    <?php include '../includes/navbar.php'; ?>

    <div class="container-fluid mt-4">
        <div class="row">
            <div class="col-12">
                <div class="d-flex justify-content-between align-items-center mb-4">
                    <h1><i class="fas fa-edit me-2"></i>Edit Expense: <?php echo htmlspecialchars($expense['exno']); ?></h1>
                    <div class="d-flex gap-2">
                        <a href="view.php?id=<?php echo $expense_id; ?>" class="btn btn-info">
                            <i class="fas fa-eye me-1"></i>View
                        </a>
                        <a href="list.php" class="btn btn-secondary">
                            <i class="fas fa-arrow-left me-1"></i>Back to List
                        </a>
                    </div>
                </div>

                <?php if ($error): ?>
                    <div class="alert alert-danger alert-dismissible fade show" role="alert">
                        <i class="fas fa-exclamation-triangle me-2"></i>
                        <?php echo htmlspecialchars($error); ?>
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                <?php endif; ?>

                <?php if ($success): ?>
                    <div class="alert alert-success alert-dismissible fade show" role="alert">
                        <i class="fas fa-check-circle me-2"></i>
                        <?php echo htmlspecialchars($success); ?>
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                <?php endif; ?>

                <form method="POST" enctype="multipart/form-data" class="expense-form">
                    <input type="hidden" name="csrf_token" value="<?php echo generateCSRFToken(); ?>">

                    <!-- Basic Information -->
                    <div class="form-section">
                        <h4 class="section-title">Basic Information</h4>
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="bookingno" class="form-label">Booking NO / BL</label>
                                    <input type="text" class="form-control" id="bookingno" name="bookingno"
                                           value="<?php echo htmlspecialchars($expense['bookingno'] ?? ''); ?>">
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="job_open_date" class="form-label">Job Open Date วันที่ทำรายการ  <span class="text-danger">*</span></label>
                                    <input type="date" class="form-control" id="job_open_date" name="job_open_date"
                                           value="<?php echo $expense['job_open_date']; ?>" required>
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="item_input" class="form-label">Item รายการ</label>
                                    <div class="datalist-container">
                                        <div class="input-group">
                                            <input type="text" class="form-control" id="item_input"
                                                   list="item_list" placeholder="Search or type new item..."
                                                   autocomplete="off" value="<?php echo $expense['item_id'] ? htmlspecialchars($expense['item_name']) : ''; ?>">
                                            <button class="btn btn-outline-primary" type="button" id="add_new_item_btn">
                                                <i class="fas fa-plus"></i> Add New
                                            </button>
                                        </div>
                                        <datalist id="item_list">
                                            <?php foreach ($items as $item): ?>
                                                <option value="<?php echo htmlspecialchars($item['name']); ?>"
                                                        data-id="<?php echo $item['id']; ?>">
                                                    <?php echo htmlspecialchars($item['name']); ?>
                                                </option>
                                            <?php endforeach; ?>
                                        </datalist>
                                        <input type="hidden" id="item_id" name="item_id" value="<?php echo $expense['item_id']; ?>">
                                        <input type="hidden" id="new_item" name="new_item" value="">
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="customer_input" class="form-label">Customer ลูกค้า</label>
                                    <div class="datalist-container">
                                        <div class="input-group">
                                            <input type="text" class="form-control" id="customer_input"
                                                   list="customer_list" placeholder="Search or type new customer..."
                                                   autocomplete="off" value="<?php echo $expense['customer_id'] ? htmlspecialchars($expense['customer_name']) : ''; ?>">
                                            <button class="btn btn-outline-primary" type="button" id="add_new_customer_btn">
                                                <i class="fas fa-plus"></i> Add New
                                            </button>
                                        </div>
                                        <datalist id="customer_list">
                                            <?php foreach ($customers as $customer): ?>
                                                <option value="<?php echo htmlspecialchars($customer['name']); ?>"
                                                        data-id="<?php echo $customer['id']; ?>">
                                                    <?php echo htmlspecialchars($customer['name']); ?>
                                                </option>
                                            <?php endforeach; ?>
                                        </datalist>
                                        <input type="hidden" id="customer_id" name="customer_id" value="<?php echo $expense['customer_id']; ?>">
                                        <input type="hidden" id="new_customer" name="new_customer" value="">
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="containerno" class="form-label">Container NO</label>
                                    <input type="text" class="form-control" id="containerno" name="containerno"
                                           value="<?php echo htmlspecialchars($expense['containerno'] ?? ''); ?>">
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="driver_input" class="form-label">Driver คนขับ</label>
                                    <div class="datalist-container">
                                        <input type="text" class="form-control" id="driver_input"
                                               list="driver_list" placeholder="Search driver..."
                                               autocomplete="off" value="<?php echo $expense['driver_id'] ? htmlspecialchars($expense['driver_name']) : ''; ?>">
                                        <datalist id="driver_list">
                                            <?php foreach ($drivers as $driver): ?>
                                                <option value="<?php echo htmlspecialchars($driver['name']); ?>"
                                                        data-id="<?php echo $driver['id']; ?>"
                                                        data-vehicle="<?php echo htmlspecialchars($driver['vehicle_plate']); ?>"
                                                        data-account="<?php echo htmlspecialchars($driver['payment_account_no']); ?>">
                                                    <?php echo htmlspecialchars($driver['name']); ?>
                                                    <?php if (!empty($driver['vehicle_plate'])): ?>
                                                        - <?php echo htmlspecialchars($driver['vehicle_plate']); ?>
                                                    <?php endif; ?>
                                                </option>
                                            <?php endforeach; ?>
                                        </datalist>
                                        <input type="hidden" id="driver_id" name="driver_id" value="<?php echo $expense['driver_id']; ?>">
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="vehicle_plate" class="form-label">Vehicle Plate ทะเบียนรถ</label>
                                    <input type="text" class="form-control" id="vehicle_plate" name="vehicle_plate"
                                           value="<?php echo htmlspecialchars($expense['vehicle_plate'] ?? ''); ?>">
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="payment_account_no" class="form-label">Payment Account NO บัญชีที่จ่าย</label>
                                    <input type="text" class="form-control" id="payment_account_no" name="payment_account_no"
                                           value="<?php echo htmlspecialchars($expense['payment_account_no'] ?? ''); ?>">
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Personnel Information -->
                    <div class="form-section">
                        <h4 class="section-title">Personnel Information</h4>
                        <div class="row">
                            <div class="col-md-4">
                                <div class="mb-3">
                                    <label for="requester" class="form-label">Requester ผู้ขอ</label>
                                    <input type="text" class="form-control" id="requester" name="requester"
                                           value="<?php echo htmlspecialchars($expense['requester'] ?? ''); ?>">
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="mb-3">
                                    <label for="receiver" class="form-label">Receiver ผู้รับ</label>
                                    <input type="text" class="form-control" id="receiver" name="receiver"
                                           value="<?php echo htmlspecialchars($expense['receiver'] ?? ''); ?>">
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="mb-3">
                                    <label for="payer" class="form-label">Payer ผู้จ่าย</label>
                                    <input type="text" class="form-control" id="payer" name="payer"
                                           value="<?php echo htmlspecialchars($expense['payer'] ?? $user['full_name']); ?>" readonly>
                                    <div class="form-text">Auto-filled with logged-in user</div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Payment Information -->
                    <div class="form-section">
                        <h4 class="section-title">Payment Information</h4>
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="withdrawal_date" class="form-label">Withdrawal Date วันที่เบิก<span class="text-danger">*</span></label>
                                    <input type="date" class="form-control" id="withdrawal_date" name="withdrawal_date"
                                           value="<?php echo $expense['withdrawal_date']; ?>"
                                           min="<?php echo date('Y-m-d', strtotime('-1 month')); ?>"
                                           max="<?php echo date('Y-m-d'); ?>" required>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="transfer_no" class="form-label">Transfer Number เลขใบโอน</label>
                                    <input type="text" class="form-control" id="transfer_no" name="transfer_no"
                                           value="<?php echo htmlspecialchars($expense['transfer_no'] ?? ''); ?>">
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="transfer_amount" class="form-label">Transfer Amount ยอดโอน</label>
                                    <div class="input-group">
                                        <input type="number" class="form-control" id="transfer_amount" name="transfer_amount"
                                               step="0.01" min="0"
                                               value="<?php echo number_format($expense['transfer_amount'] ?? 0, 2, '.', ''); ?>">
                                        <span class="input-group-text">บาท</span>
                                    </div>
                                    <div class="form-text">Amount actually transferred</div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="additional_details" class="form-label">Additional Details รายละเอียดเพิ่มเติม</label>
                                    <textarea class="form-control" id="additional_details" name="additional_details" rows="3"><?php echo htmlspecialchars($expense['additional_details'] ?? ''); ?></textarea>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Current Files -->
                    <div class="form-section">
                        <h4 class="section-title">Current Files</h4>
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">Current Transfer Slip</label>
                                    <?php if (!empty($expense['transfer_slip_image'])): ?>
                                        <div class="current-file">
                                            <a href="../uploads/transfer_slips/<?php echo $expense['transfer_slip_image']; ?>"
                                               target="_blank" class="btn btn-outline-primary btn-sm">
                                                <i class="fas fa-file-image me-1"></i>View Current Transfer Slip
                                            </a>
                                        </div>
                                    <?php else: ?>
                                        <div class="text-muted">No transfer slip uploaded</div>
                                    <?php endif; ?>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">Current Receipts</label>
                                    <?php if (!empty($receipt_numbers)): ?>
                                        <div class="current-receipts">
                                            <?php foreach ($receipt_numbers as $receipt): ?>
                                                <div class="receipt-item mb-2">
                                                    <div class="d-flex justify-content-between align-items-center">
                                                        <div>
                                                            <strong><?php echo htmlspecialchars($receipt['receipt_number']); ?></strong>
                                                            <span class="text-success">(<?php echo number_format($receipt['amount'], 2); ?> บาท)</span>
                                                        </div>
                                                        <?php if (!empty($receipt['receipt_image'])): ?>
                                                            <a href="../uploads/receipts/<?php echo $receipt['receipt_image']; ?>"
                                                               target="_blank" class="btn btn-outline-primary btn-sm">
                                                                <i class="fas fa-file-image me-1"></i>View
                                                            </a>
                                                        <?php endif; ?>
                                                    </div>
                                                    <?php if (!empty($receipt['description'])): ?>
                                                        <small class="text-muted"><?php echo htmlspecialchars($receipt['description']); ?></small>
                                                    <?php endif; ?>
                                                </div>
                                            <?php endforeach; ?>
                                        </div>
                                    <?php else: ?>
                                        <div class="text-muted">No receipts uploaded</div>
                                    <?php endif; ?>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- File Uploads -->
                    <div class="form-section">
                        <h4 class="section-title">Upload New Files (Optional)</h4>
                        <div class="alert alert-info">
                            <i class="fas fa-info-circle me-2"></i>
                            <strong>Note:</strong> Uploading new files will replace the existing ones. Leave empty to keep current files.
                        </div>
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="transfer_slip" class="form-label">New Transfer Slip Image ใบโอนใหม่</label>
                                    <input type="file" class="form-control" id="transfer_slip" name="transfer_slip"
                                           accept="image/*,.pdf">
                                    <div class="form-text">Accepted formats: JPG, PNG, PDF (Max: 5MB)</div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="receipts" class="form-label">New Receipt Images ใบเสร็จใหม่</label>
                                    <input type="file" class="form-control" id="receipts" name="receipts[]"
                                           accept="image/*,.pdf" multiple>
                                    <div class="form-text">You can select multiple files. This will replace all existing receipts.</div>
                                </div>
                            </div>
                        </div>

                        <!-- Receipt Numbers Table (for new uploads) -->
                        <div id="receipt-numbers-section" class="d-none">
                            <h5>Receipt Numbers for New Uploads</h5>
                            <div class="table-responsive">
                                <table class="table table-bordered" id="receipt-numbers-table">
                                    <thead>
                                        <tr>
                                            <th>Receipt Number</th>
                                            <th>Amount (บาท)</th>
                                            <th>Description</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <!-- Dynamic rows will be added here -->
                                    </tbody>
                                </table>
                            </div>
                            <div class="mt-2">
                                <strong>Total Amount: <span id="receipt-total">0.00</span> บาท</strong>
                            </div>
                        </div>
                    </div>

                    <!-- Submit Buttons -->
                    <div class="form-section">
                        <div class="d-flex justify-content-between">
                            <button type="button" class="btn btn-outline-warning" onclick="resetForm()">
                                <i class="fas fa-undo me-1"></i>Reset Changes
                            </button>
                            <div class="d-flex gap-2">
                                <a href="view.php?id=<?php echo $expense_id; ?>" class="btn btn-secondary">
                                    <i class="fas fa-times me-1"></i>Cancel
                                </a>
                                <button type="submit" class="btn btn-success">
                                    <i class="fas fa-save me-1"></i>Update Expense
                                </button>
                            </div>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script src="../assets/js/expense-form.js"></script>

    <script>
        // Edit-specific JavaScript
        $(document).ready(function() {
            // Handle receipt file selection for new uploads
            $('#receipts').change(function() {
                const files = this.files;
                const tbody = $('#receipt-numbers-table tbody');
                const section = $('#receipt-numbers-section');

                tbody.empty();

                if (files.length > 0) {
                    section.removeClass('d-none');

                    for (let i = 0; i < files.length; i++) {
                        const row = `
                            <tr>
                                <td>
                                    <input type="text" class="form-control" name="receipt_numbers[]"
                                           placeholder="Receipt number" required>
                                </td>
                                <td>
                                    <input type="number" class="form-control receipt-amount" name="receipt_amounts[]"
                                           step="0.01" min="0" placeholder="0.00" required>
                                </td>
                                <td>
                                    <input type="text" class="form-control" name="receipt_descriptions[]"
                                           list="item_list" placeholder="เลือกจาก Item หรือพิมพ์รายละเอียด..."
                                           autocomplete="off">
                                </td>
                            </tr>
                        `;
                        tbody.append(row);
                    }

                    // Add event listeners for amount calculation
                    $('.receipt-amount').on('input', updateReceiptTotal);
                } else {
                    section.addClass('d-none');
                }
            });

            // Update receipt total
            function updateReceiptTotal() {
                let total = 0;
                $('.receipt-amount').each(function() {
                    const value = parseFloat($(this).val()) || 0;
                    total += value;
                });
                $('#receipt-total').text(total.toFixed(2));
            }

            // Custom reset function for edit form
            window.resetForm = function() {
                if (confirm('คุณแน่ใจหรือไม่ที่จะรีเซ็ตการเปลี่ยนแปลง? การกระทำนี้จะคืนค่าเป็นข้อมูลเดิม')) {
                    location.reload();
                }
            };

            // Auto-save functionality (optional)
            let autoSaveTimer;
            $('.expense-form input, .expense-form select, .expense-form textarea').on('input change', function() {
                clearTimeout(autoSaveTimer);
                autoSaveTimer = setTimeout(function() {
                    // Save form data to localStorage
                    const formData = {};
                    $('.expense-form').find('input, select, textarea').each(function() {
                        if (this.type !== 'file' && this.name !== 'csrf_token') {
                            formData[this.name] = $(this).val();
                        }
                    });
                    localStorage.setItem('expense_edit_data_' + <?php echo $expense_id; ?>, JSON.stringify(formData));
                }, 2000);
            });

            // Load auto-saved data if available
            const savedData = localStorage.getItem('expense_edit_data_' + <?php echo $expense_id; ?>);
            if (savedData) {
                try {
                    const formData = JSON.parse(savedData);
                    Object.keys(formData).forEach(function(name) {
                        const element = $('[name="' + name + '"]');
                        if (element.length && formData[name]) {
                            element.val(formData[name]);
                        }
                    });
                } catch (e) {
                    console.log('Error loading auto-saved data:', e);
                }
            }

            // Clear auto-saved data on successful submit
            $('.expense-form').on('submit', function() {
                localStorage.removeItem('expense_edit_data_' + <?php echo $expense_id; ?>);
            });
        });
    </script>
</body>
</html>
