<?php
session_start();
require_once '../config/database.php';
require_once '../includes/functions.php';
require_once '../includes/ImageUploadHelper.php';

// Check if user is logged in
if (!isset($_SESSION['user_id'])) {
    header('Location: ../login.php');
    exit();
}

// Check if user has permission to create expenses (report_viewer cannot create)
if ($_SESSION['role'] === 'report_viewer') {
    header('Location: ../dashboard.php?error=access_denied');
    exit();
}

$database = new Database();
$db = $database->getConnection();

// Get user info
$stmt = $db->prepare("SELECT * FROM users WHERE id = ?");
$stmt->execute([$_SESSION['user_id']]);
$user = $stmt->fetch();

if (!$user || !$user['is_active']) {
    session_destroy();
    header('Location: ../login.php');
    exit();
}

$error = '';
$success = '';

// Handle form submission
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    try {
        // Generate expense number
        $expense_number = generateExpenseNumber($db);
        
        // Validate required fields
        $required_fields = ['job_open_date', 'withdrawal_date'];
        foreach ($required_fields as $field) {
            if (empty($_POST[$field])) {
                throw new Exception("Field '$field' is required.");
            }
        }
        
        // Validate dates
        $job_open_date = $_POST['job_open_date'];
        $withdrawal_date = $_POST['withdrawal_date'];
        
        if (!strtotime($job_open_date)) {
            throw new Exception("Invalid job open date.");
        }
        
        if (!strtotime($withdrawal_date)) {
            throw new Exception("Invalid withdrawal date.");
        }
        
        // Check withdrawal date is not more than 1 month in the past
        $one_month_ago = date('Y-m-d', strtotime('-1 month'));
        if ($withdrawal_date < $one_month_ago) {
            throw new Exception("Withdrawal date cannot be more than 1 month in the past.");
        }

        // Handle new item creation
        $item_id = $_POST['item_id'] ?? null;
        if (!empty($_POST['new_item']) && trim($_POST['new_item']) !== '') {
            $stmt = $db->prepare("INSERT INTO items (name, description, created_by) VALUES (?, ?, ?)");
            $stmt->execute([trim($_POST['new_item']), 'Added from expense form', $user['id']]);
            $item_id = $db->lastInsertId();

            logActivity(
                $db,
                $user['id'],
                'create',
                'items',
                $item_id,
                'Created new item: ' . $_POST['new_item'],
                $_SERVER['REMOTE_ADDR'] ?? '',
                $_SERVER['HTTP_USER_AGENT'] ?? ''
            );
        }

        // Handle new customer creation
        $customer_id = $_POST['customer_id'] ?? null;

        // Convert empty string to null for database compatibility
        if (empty($customer_id) || $customer_id === '') {
            $customer_id = null;
        }

        if (!empty($_POST['new_customer']) && trim($_POST['new_customer']) !== '') {
            $stmt = $db->prepare("INSERT INTO customers (name, created_by) VALUES (?, ?)");
            $stmt->execute([trim($_POST['new_customer']), $user['id']]);
            $customer_id = $db->lastInsertId();

            logActivity(
                $db,
                $user['id'],
                'create',
                'customers',
                $customer_id,
                'Created new customer: ' . $_POST['new_customer'],
                $_SERVER['REMOTE_ADDR'] ?? '',
                $_SERVER['HTTP_USER_AGENT'] ?? ''
            );
        }

        // Handle file uploads
        $transfer_slip_image = '';
        $receipt_images = [];
        
        // Upload transfer slip with compression
        if (isset($_FILES['transfer_slip']) && $_FILES['transfer_slip']['error'] === UPLOAD_ERR_OK) {
            $upload_result = ImageUploadHelper::uploadTransferSlip($_FILES['transfer_slip']);
            if ($upload_result['success']) {
                $transfer_slip_image = $upload_result['filename'];

                // Log compression results
                error_log("Transfer slip compressed: {$upload_result['filename']} - " .
                         "Original: " . ImageCompressor::formatFileSize($upload_result['original_size']) .
                         ", Compressed: " . ImageCompressor::formatFileSize($upload_result['compressed_size']) .
                         " (Saved {$upload_result['compression_ratio']}%)");
            } else {
                throw new Exception("Transfer slip upload error: " . $upload_result['error']);
            }
        }
        
        // Upload receipt images and prepare receipt data
        $receipt_data = [];
        if (isset($_FILES['receipts']) && is_array($_FILES['receipts']['tmp_name'])) {
            foreach ($_FILES['receipts']['tmp_name'] as $key => $tmp_name) {
                if ($_FILES['receipts']['error'][$key] === UPLOAD_ERR_OK && !empty($tmp_name)) {
                    $file = [
                        'tmp_name' => $tmp_name,
                        'name' => $_FILES['receipts']['name'][$key],
                        'size' => $_FILES['receipts']['size'][$key],
                        'error' => $_FILES['receipts']['error'][$key],
                        'type' => $_FILES['receipts']['type'][$key]
                    ];

                    $receipt_number = isset($_POST['receipt_numbers']) && isset($_POST['receipt_numbers'][$key]) ? trim($_POST['receipt_numbers'][$key]) : '';
                    $upload_result = ImageUploadHelper::uploadReceiptImage($file, $receipt_number);

                    if ($upload_result['success']) {
                        $receipt_amount = isset($_POST['receipt_amounts']) && isset($_POST['receipt_amounts'][$key]) ? floatval($_POST['receipt_amounts'][$key]) : 0.00;
                        $receipt_description = isset($_POST['receipt_descriptions']) && isset($_POST['receipt_descriptions'][$key]) ? trim($_POST['receipt_descriptions'][$key]) : '';

                        $receipt_data[] = [
                            'filename' => $upload_result['filename'],
                            'receipt_number' => $receipt_number,
                            'amount' => $receipt_amount,
                            'description' => $receipt_description
                        ];

                        $receipt_images[] = [
                            'filename' => $upload_result['filename'],
                            'receipt_no' => $receipt_number
                        ];

                        // Log compression results
                        error_log("Receipt compressed: {$upload_result['filename']} - " .
                                 "Original: " . ImageCompressor::formatFileSize($upload_result['original_size']) .
                                 ", Compressed: " . ImageCompressor::formatFileSize($upload_result['compressed_size']) .
                                 " (Saved {$upload_result['compression_ratio']}%)");
                    } else {
                        error_log('Receipt upload error: ' . $upload_result['error']);
                    }
                }
            }
        }

        // Calculate total amount from receipts (gross amount)
        $gross_amount = array_sum(array_column($receipt_data, 'amount'));

        // Calculate total deductions if provided
        $total_deductions = 0;
        $deductions_count = 0;
        if (!empty($_POST['deductions_data'])) {
            $deductions_data = json_decode($_POST['deductions_data'], true);
            if ($deductions_data && is_array($deductions_data)) {
                foreach ($deductions_data as $deduction) {
                    $deduction_amount = floatval($deduction['amount'] ?? 0);
                    $total_deductions += $deduction_amount;
                    $deductions_count++;

                    // Log each deduction for debugging
                    error_log("PHP Deduction {$deductions_count}: Type=" . ($deduction['type'] ?? 'unknown') . ", Amount=" . $deduction_amount);
                }
            }
        }

        // Calculate net amount (gross - deductions)
        $total_amount = $gross_amount - $total_deductions;

        // Compare with JavaScript calculation if debug data is provided
        $js_php_mismatch = false;
        if (!empty($_POST['js_debug_data'])) {
            $js_debug = json_decode($_POST['js_debug_data'], true);
            if ($js_debug && is_array($js_debug)) {
                $js_gross = floatval($js_debug['js_gross_amount'] ?? 0);
                $js_deductions = floatval($js_debug['js_total_deductions'] ?? 0);
                $js_net = floatval($js_debug['js_calculated_net'] ?? 0);
                $js_transfer = floatval($js_debug['js_transfer_amount'] ?? 0);

                // Check for mismatches
                $gross_diff = abs($gross_amount - $js_gross);
                $deductions_diff = abs($total_deductions - $js_deductions);
                $net_diff = abs($total_amount - $js_net);

                error_log("=== JAVASCRIPT vs PHP COMPARISON ===");
                error_log("Gross Amount: JS={$js_gross}, PHP={$gross_amount}, Diff={$gross_diff}");
                error_log("Total Deductions: JS={$js_deductions}, PHP={$total_deductions}, Diff={$deductions_diff}");
                error_log("Net Amount: JS={$js_net}, PHP={$total_amount}, Diff={$net_diff}");
                error_log("Transfer Amount: JS={$js_transfer}, PHP=" . floatval($_POST['transfer_amount'] ?? 0));

                // Flag significant mismatches
                if ($gross_diff > 0.01 || $deductions_diff > 0.01 || $net_diff > 0.01) {
                    $js_php_mismatch = true;
                    error_log("⚠️ WARNING: Significant mismatch between JavaScript and PHP calculations!");
                }
            }
        }

        // Log PHP calculation for debugging
        error_log("=== PHP VALIDATION DEBUG ===");
        error_log("PHP Calculation:");
        error_log("  gross_amount: " . $gross_amount);
        error_log("  total_deductions: " . $total_deductions . " (from {$deductions_count} deductions)");
        error_log("  calculated_net_amount (PHP): " . $total_amount);
        error_log("User Input:");
        error_log("  transfer_amount: " . floatval($_POST['transfer_amount'] ?? 0));
        error_log("Difference: " . abs(floatval($_POST['transfer_amount'] ?? 0) - $total_amount));

        // Validate transfer amount equals receipt total (net amount)
        $transfer_amount = floatval($_POST['transfer_amount'] ?? 0);
        if (abs($transfer_amount - $total_amount) > 0.01) { // Allow 0.01 difference for floating point precision
            $error_message = "❌ PHP VALIDATION FAILED: Transfer Amount (" . number_format($transfer_amount, 2) . " บาท) must equal Receipt Total (" . number_format($total_amount, 2) . " บาท). ";
            $error_message .= "Breakdown: Gross=" . number_format($gross_amount, 2) . ", Deductions=" . number_format($total_deductions, 2) . " (from {$deductions_count} items). ";

            if ($js_php_mismatch) {
                $error_message .= "⚠️ CRITICAL: JavaScript and PHP calculations do not match! This indicates a serious calculation error. ";
            }

            $error_message .= "Please check your deductions data and try again.";

            error_log("PHP VALIDATION ERROR: " . $error_message);
            throw new Exception($error_message);
        }

        // Additional warning for JS/PHP mismatch even if validation passes
        if ($js_php_mismatch) {
            error_log("⚠️ WARNING: JavaScript and PHP calculations differ, but validation passed. Monitor for potential issues.");
        }

        error_log("✅ PHP VALIDATION PASSED: Transfer amount matches calculated net amount");
        
        // Get driver details if driver is selected
        $vehicle_plate = $_POST['vehicle_plate'] ?? '';
        $payment_account_no = $_POST['payment_account_no'] ?? '';
        $requester = $_POST['requester'] ?? '';
        $receiver = $_POST['receiver'] ?? '';
        
        if (!empty($_POST['driver_id'])) {
            $stmt = $db->prepare("SELECT vehicle_plate, payment_account_no, name FROM drivers WHERE id = ?");
            $stmt->execute([$_POST['driver_id']]);
            $driver = $stmt->fetch();
            
            if ($driver) {
                $vehicle_plate = $driver['vehicle_plate'];
                $payment_account_no = $driver['payment_account_no'];
                $requester = $driver['name'];
                $receiver = $driver['name'];
            }
        }
        
        // Insert expense record with duplicate handling
        try {
            $stmt = $db->prepare("
                INSERT INTO expenses (
                    sequence, exno, bookingno, job_open_date, item_id, customer_id,
                    containerno, driver_id, vehicle_plate, payment_account_no,
                    additional_details, requester, receiver, payer, withdrawal_date,
                    transfer_no, transfer_amount, total_amount, transfer_slip_image, receipt_images, created_by
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            ");

            $stmt->execute([
                $expense_number['sequence'],
                $expense_number['exno'],
                $_POST['bookingno'] ?? '',
                $job_open_date,
                $item_id,
                $customer_id,
                $_POST['containerno'] ?? '',
                !empty($_POST['driver_id']) ? $_POST['driver_id'] : null,
                $vehicle_plate,
                $payment_account_no,
                $_POST['additional_details'] ?? '',
                $requester,
                $receiver,
                $user['full_name'], // payer is the logged-in user
                $withdrawal_date,
                $_POST['transfer_no'] ?? '',
                $transfer_amount,
                $total_amount,
                $transfer_slip_image,
                json_encode($receipt_images),
                $user['id']
            ]);
        } catch (PDOException $e) {
            // If duplicate expense number, try to generate a new one
            if ($e->getCode() == 23000 && strpos($e->getMessage(), 'exno') !== false) {
                // Generate new expense number with timestamp
                $timestamp_suffix = substr(time(), -3);
                $new_sequence = $expense_number['sequence'] . $timestamp_suffix;
                $new_exno = date('Ymd') . '-' . $new_sequence;

                $stmt->execute([
                    $new_sequence,
                    $new_exno,
                    $_POST['bookingno'] ?? '',
                    $job_open_date,
                    $item_id,
                    $customer_id,
                    $_POST['containerno'] ?? '',
                    !empty($_POST['driver_id']) ? $_POST['driver_id'] : null,
                    $vehicle_plate,
                    $payment_account_no,
                    $_POST['additional_details'] ?? '',
                    $requester,
                    $receiver,
                    $user['full_name'], // payer is the logged-in user
                    $withdrawal_date,
                    $_POST['transfer_no'] ?? '',
                    $transfer_amount,
                    $total_amount,
                    $transfer_slip_image,
                    json_encode($receipt_images),
                    $user['id']
                ]);
            } else {
                throw $e; // Re-throw if it's not a duplicate exno error
            }
        }
        
        $expense_id = $db->lastInsertId();

        // Insert receipt numbers into separate table
        if (!empty($receipt_data)) {
            $stmt = $db->prepare("
                INSERT INTO receipt_numbers (expense_id, receipt_number, amount, gross_amount, has_deductions, net_amount_calculated, description, receipt_image, created_by)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
            ");

            foreach ($receipt_data as $receipt) {
                $stmt->execute([
                    $expense_id,
                    $receipt['receipt_number'],
                    $receipt['amount'],
                    $receipt['amount'], // gross_amount = amount initially (no deductions yet)
                    0, // has_deductions = 0 (false) initially
                    $receipt['amount'], // net_amount_calculated = amount initially
                    $receipt['description'],
                    $receipt['filename'],
                    $user['id']
                ]);
            }
        }

        // Handle per-receipt deductions data if provided
        if (!empty($_POST['deductions_data'])) {
            require_once '../includes/ReceiptDeductionManager.php';
            $deductionManager = new ReceiptDeductionManager($db);

            $deductions_data = json_decode($_POST['deductions_data'], true);

            // DEBUG: Log raw deductions data
            error_log("=== DEDUCTIONS DATA DEBUG ===");
            error_log("Raw POST deductions_data: " . ($_POST['deductions_data'] ?? 'NULL'));
            error_log("Decoded deductions_data: " . json_encode($deductions_data, JSON_PRETTY_PRINT));
            error_log("Is array: " . (is_array($deductions_data) ? 'YES' : 'NO'));
            error_log("Count: " . (is_array($deductions_data) ? count($deductions_data) : 'N/A'));

            if ($deductions_data && is_array($deductions_data)) {
                // Get receipt IDs that were just inserted
                $stmt = $db->prepare("SELECT id, receipt_number FROM receipt_numbers WHERE expense_id = ? ORDER BY id");
                $stmt->execute([$expense_id]);
                $receipts = $stmt->fetchAll(PDO::FETCH_ASSOC);

                // DEBUG: Log receipt mapping
                error_log("Receipts from database: " . json_encode($receipts, JSON_PRETTY_PRINT));

                // Create mapping: receipt_number => id and receipt_index => id
                $receipt_ids_by_number = [];
                $receipt_ids_by_index = [];
                foreach ($receipts as $index => $receipt) {
                    $receipt_ids_by_number[$receipt['receipt_number']] = $receipt['id'];
                    $receipt_ids_by_index[$index] = $receipt['id'];
                }

                error_log("Receipt IDs by index: " . json_encode($receipt_ids_by_index, JSON_PRETTY_PRINT));
                error_log("Receipt IDs by number: " . json_encode($receipt_ids_by_number, JSON_PRETTY_PRINT));

                foreach ($deductions_data as $deduction_index => $deduction) {
                    error_log("--- Processing Deduction #{$deduction_index} ---");
                    error_log("Deduction data: " . json_encode($deduction, JSON_PRETTY_PRINT));

                    $receipt_id = null;
                    $receipt_index = $deduction['receipt_index'] ?? 0;

                    // Try to get receipt ID by index first, then by receipt number
                    if (isset($receipt_ids_by_index[$receipt_index])) {
                        $receipt_id = $receipt_ids_by_index[$receipt_index];
                        error_log("Found receipt ID by index: {$receipt_index} => {$receipt_id}");
                    } elseif (isset($deduction['receipt_number']) && isset($receipt_ids_by_number[$deduction['receipt_number']])) {
                        $receipt_id = $receipt_ids_by_number[$deduction['receipt_number']];
                        error_log("Found receipt ID by number: {$deduction['receipt_number']} => {$receipt_id}");
                    } else {
                        error_log("❌ Could not find receipt ID for index {$receipt_index} or number " . ($deduction['receipt_number'] ?? 'NULL'));
                    }

                    if ($receipt_id) {
                        // Ensure boolean values are properly converted
                        $is_percentage_based = false;
                        if (isset($deduction['isPercentageBased'])) {
                            $is_percentage_based = filter_var($deduction['isPercentageBased'], FILTER_VALIDATE_BOOLEAN);
                        }

                        $deduction_data = [
                            'deduction_type' => $deduction['type'],
                            'amount' => floatval($deduction['amount'] ?? 0),
                            'percentage' => !empty($deduction['percentage']) ? floatval($deduction['percentage']) : null,
                            'description' => $deduction['description'] ?? '',
                            'deduction_image' => $deduction['image'] ?? null,
                            'is_percentage_based' => $is_percentage_based
                        ];

                        error_log("Prepared deduction data: " . json_encode($deduction_data, JSON_PRETTY_PRINT));
                        error_log("Calling addDeduction with Receipt ID: {$receipt_id}, User ID: {$user['id']}, Receipt Index: {$receipt_index}");

                        $result = $deductionManager->addDeduction($receipt_id, $deduction_data, $user['id'], $receipt_index);

                        error_log("addDeduction result: " . json_encode($result, JSON_PRETTY_PRINT));

                        if (!$result['success']) {
                            error_log("❌ Failed to add deduction: " . $result['message']);
                            // Don't throw exception, just log the error and continue
                        } else {
                            error_log("✅ Successfully added deduction: Type=" . $deduction_data['deduction_type'] . ", Amount=" . $deduction_data['amount'] . ", Receipt ID=" . $receipt_id);

                            // DEBUG: Check if deduction was actually inserted
                            $check_stmt = $db->prepare("SELECT COUNT(*) as count FROM receipt_deductions WHERE receipt_number_id = ?");
                            $check_stmt->execute([$receipt_id]);
                            $count_result = $check_stmt->fetch();
                            error_log("Deductions count for receipt {$receipt_id}: " . $count_result['count']);
                        }
                    } else {
                        error_log("❌ Skipping deduction due to missing receipt ID");
                    }
                }
            } else {
                error_log("❌ No valid deductions data found or data is not an array");
            }
        } else {
            error_log("=== NO DEDUCTIONS DATA ===");
            error_log("POST deductions_data is empty or not set");
        }

        // DEBUG: Final database state check
        error_log("=== FINAL DATABASE STATE CHECK ===");
        error_log("Expense ID: " . $expense_id);

        // Check receipt_numbers table
        $stmt = $db->prepare("SELECT id, receipt_number, amount, gross_amount, has_deductions, net_amount_calculated FROM receipt_numbers WHERE expense_id = ?");
        $stmt->execute([$expense_id]);
        $final_receipts = $stmt->fetchAll(PDO::FETCH_ASSOC);
        error_log("Final receipt_numbers: " . json_encode($final_receipts, JSON_PRETTY_PRINT));

        // Check receipt_deductions table
        $stmt = $db->prepare("
            SELECT rd.*, rn.receipt_number
            FROM receipt_deductions rd
            JOIN receipt_numbers rn ON rd.receipt_number_id = rn.id
            WHERE rn.expense_id = ?
        ");
        $stmt->execute([$expense_id]);
        $final_deductions = $stmt->fetchAll(PDO::FETCH_ASSOC);
        error_log("Final receipt_deductions: " . json_encode($final_deductions, JSON_PRETTY_PRINT));

        // Summary
        error_log("Total receipts created: " . count($final_receipts));
        error_log("Total deductions created: " . count($final_deductions));
        error_log("=== END FINAL DATABASE STATE CHECK ===");

        // Log the activity
        logActivity(
            $db,
            $user['id'],
            'create',
            'expenses',
            $expense_id,
            'Created new expense: ' . $expense_number['exno'] . ' with ' . count($receipt_data) . ' receipts, total amount: ' . number_format($total_amount, 2),
            $_SERVER['REMOTE_ADDR'],
            $_SERVER['HTTP_USER_AGENT']
        );

        // DEBUG BREAKPOINT: Stop here to check logs
        error_log("🛑 DEBUG BREAKPOINT: Expense creation completed. Check the logs above for SQL details.");
        error_log("Expense ID: {$expense_id}");
        error_log("Expense Number: " . $expense_number['exno']);
        error_log("Check your error log file for all the SQL statements and data.");

        // Uncomment the line below to stop execution and see debug output
        die("DEBUG: Expense creation completed. Check error logs for SQL details. Expense ID: {$expense_id}");

        redirectWithMessage('../expenses/view.php?id=' . $expense_id, 'Expense created successfully!', 'success');
        
    } catch (Exception $e) {
        $error = $e->getMessage();
        error_log('Expense creation error: ' . $e->getMessage());
    }
}

// Get dropdown data
$items = $db->query("SELECT id, name FROM items WHERE is_active = 1 ORDER BY name")->fetchAll();
$customers = $db->query("SELECT id, name FROM customers WHERE is_active = 1 ORDER BY name")->fetchAll();
$drivers = $db->query("SELECT id, name, vehicle_plate, payment_account_no FROM drivers WHERE is_active = 1 ORDER BY name")->fetchAll();

// Get selected names for form repopulation
$selected_item_name = '';
$selected_customer_name = '';
$selected_driver_name = '';

if (!empty($_POST['item_id'])) {
    $stmt = $db->prepare("SELECT name FROM items WHERE id = ?");
    $stmt->execute([$_POST['item_id']]);
    $item = $stmt->fetch();
    $selected_item_name = $item ? $item['name'] : '';
}

if (!empty($_POST['customer_id'])) {
    $stmt = $db->prepare("SELECT name FROM customers WHERE id = ?");
    $stmt->execute([$_POST['customer_id']]);
    $customer = $stmt->fetch();
    $selected_customer_name = $customer ? $customer['name'] : '';
}

if (!empty($_POST['driver_id'])) {
    $stmt = $db->prepare("SELECT name FROM drivers WHERE id = ?");
    $stmt->execute([$_POST['driver_id']]);
    $driver = $stmt->fetch();
    $selected_driver_name = $driver ? $driver['name'] : '';
}
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Create Expense - Expenses Management System</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="../assets/css/style.css" rel="stylesheet">
    <link href="../assets/css/checkbox-deductions.css" rel="stylesheet">
    <meta name="csrf-token" content="<?php echo generateCSRFToken(); ?>">
    <style>
        /* Receipt number and transfer number validation styling */
        .receipt-number-input.is-invalid,
        #transfer_no.is-invalid {
            border-color: #dc3545;
            background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 12 12' width='12' height='12' fill='none' stroke='%23dc3545'%3e%3ccircle cx='6' cy='6' r='4.5'/%3e%3cpath d='m5.8 3.6h.4L6 6.5z'/%3e%3ccircle cx='6' cy='8.2' r='.6' fill='%23dc3545' stroke='none'/%3e%3c/svg%3e");
            background-repeat: no-repeat;
            background-position: right calc(0.375em + 0.1875rem) center;
            background-size: calc(0.75em + 0.375rem) calc(0.75em + 0.375rem);
        }

        .receipt-number-input.is-valid,
        #transfer_no.is-valid {
            border-color: #198754;
            background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 8 8'%3e%3cpath fill='%23198754' d='m2.3 6.73.94-.94 1.93 1.93 3.53-3.53.94.94L4.16 9.66z'/%3e%3c/svg%3e");
            background-repeat: no-repeat;
            background-position: right calc(0.375em + 0.1875rem) center;
            background-size: calc(0.75em + 0.375rem) calc(0.75em + 0.375rem);
        }

        .invalid-feedback {
            display: block;
            width: 100%;
            margin-top: 0.25rem;
            font-size: 0.875em;
            color: #dc3545;
        }

        .valid-feedback {
            display: block;
            width: 100%;
            margin-top: 0.25rem;
            font-size: 0.875em;
            color: #198754;
        }

        /* Submit button disabled state */
        .btn:disabled {
            opacity: 0.65;
            cursor: not-allowed;
        }

        /* Receipt number and transfer number checking indicator */
        .receipt-number-checking,
        #transfer_no.transfer-number-checking {
            position: relative;
        }

        .receipt-number-checking::after {
            content: '';
            position: absolute;
            right: 10px;
            top: 50%;
            transform: translateY(-50%);
            width: 16px;
            height: 16px;
            border: 2px solid #f3f3f3;
            border-top: 2px solid #007bff;
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            0% { transform: translateY(-50%) rotate(0deg); }
            100% { transform: translateY(-50%) rotate(360deg); }
        }
    </style>
</head>
<body>
    <!-- Navigation -->
    <?php include '../includes/navbar.php'; ?>

    <div class="container-fluid mt-4">
        <div class="row">
            <div class="col-12">
                <div class="d-flex justify-content-between align-items-center mb-4">
                    <h1><i class="fas fa-plus me-2"></i>Create New Expense เพิ่มค่าใช้จ่าย</h1>
                    <a href="../dashboard.php" class="btn btn-secondary">
                        <i class="fas fa-arrow-left me-1"></i>Back to Dashboard
                    </a>
                </div>

                <?php if ($error): ?>
                    <div class="alert alert-danger alert-dismissible fade show" role="alert">
                        <i class="fas fa-exclamation-triangle me-2"></i>
                        <?php echo htmlspecialchars($error); ?>
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                <?php endif; ?>

                <form method="POST" enctype="multipart/form-data" class="expense-form">
                    <input type="hidden" name="csrf_token" value="<?php echo generateCSRFToken(); ?>">
                    
                    <!-- Basic Information -->
                    <div class="form-section">
                        <h4 class="section-title">Basic Information</h4>
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="bookingno" class="form-label">Booking NO / BL</label>
                                    <input type="text" class="form-control" id="bookingno" name="bookingno"
                                           value="<?php echo htmlspecialchars($_POST['bookingno'] ?? ''); ?>">
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="job_open_date" class="form-label">Job Open Date วันที่ทำรายการ  <span class="text-danger">*</span></label>
                                    <input type="date" class="form-control" id="job_open_date" name="job_open_date" 
                                           value="<?php echo $_POST['job_open_date'] ?? date('Y-m-d'); ?>" required>
                                </div>
                            </div>
                        </div>
                        
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="item_input" class="form-label">Item รายการ <span class="text-danger">*</span></label>
                                    <div class="datalist-container">
                                        <div class="input-group">
                                            <input type="text" class="form-control" id="item_input"
                                                   list="item_list" placeholder="Search or type new item..."
                                                   autocomplete="off" value="<?php echo ($_POST['item_id'] ?? '') ? htmlspecialchars($selected_item_name ?? '') : ''; ?>" required>
                                            <button class="btn btn-outline-primary" type="button" id="add_new_item_btn">
                                                <i class="fas fa-plus"></i> Add New
                                            </button>
                                        </div>
                                        <datalist id="item_list">
                                            <?php foreach ($items as $item): ?>
                                                <option value="<?php echo htmlspecialchars($item['name']); ?>"
                                                        data-id="<?php echo $item['id']; ?>">
                                                    <?php echo htmlspecialchars($item['name']); ?>
                                                </option>
                                            <?php endforeach; ?>
                                        </datalist>
                                        <input type="hidden" id="item_id" name="item_id" value="<?php echo $_POST['item_id'] ?? ''; ?>">
                                        <input type="hidden" id="new_item" name="new_item" value="">
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="customer_input" class="form-label">Customer ลูกค้า</label>
                                    <div class="datalist-container">
                                        <div class="input-group">
                                            <input type="text" class="form-control" id="customer_input"
                                                   list="customer_list" placeholder="Search or type new customer..."
                                                   autocomplete="off" value="<?php echo ($_POST['customer_id'] ?? '') ? htmlspecialchars($selected_customer_name ?? '') : ''; ?>">
                                            <button class="btn btn-outline-primary" type="button" id="add_new_customer_btn">
                                                <i class="fas fa-plus"></i> Add New
                                            </button>
                                        </div>
                                        <datalist id="customer_list">
                                            <?php foreach ($customers as $customer): ?>
                                                <option value="<?php echo htmlspecialchars($customer['name']); ?>"
                                                        data-id="<?php echo $customer['id']; ?>">
                                                    <?php echo htmlspecialchars($customer['name']); ?>
                                                </option>
                                            <?php endforeach; ?>
                                        </datalist>
                                        <input type="hidden" id="customer_id" name="customer_id" value="<?php echo $_POST['customer_id'] ?? ''; ?>">
                                        <input type="hidden" id="new_customer" name="new_customer" value="">
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <div class="mb-3">
                            <label for="containerno" class="form-label">Container NO</label>
                            <input type="text" class="form-control" id="containerno" name="containerno"
                                   value="<?php echo htmlspecialchars($_POST['containerno'] ?? ''); ?>">
                        </div>
                    </div>

                    <!-- Driver Information -->
                    <div class="form-section">
                        <h4 class="section-title">Driver & Vehicle Information</h4>
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="driver_input" class="form-label">Driver คนขับ <span class="text-danger">*</span></label>
                                    <div class="datalist-container">
                                        <input type="text" class="form-control" id="driver_input"
                                               list="driver_list" placeholder="Search driver..."
                                               autocomplete="off" value="<?php echo ($_POST['driver_id'] ?? '') ? htmlspecialchars($selected_driver_name ?? '') : ''; ?>">
                                        <datalist id="driver_list">
                                            <?php foreach ($drivers as $driver): ?>
                                                <option value="<?php echo htmlspecialchars($driver['name']); ?>"
                                                        data-id="<?php echo $driver['id']; ?>"
                                                        data-vehicle="<?php echo htmlspecialchars($driver['vehicle_plate']); ?>"
                                                        data-account="<?php echo htmlspecialchars($driver['payment_account_no']); ?>">
                                                    <?php echo htmlspecialchars($driver['name']); ?>
                                                    <?php if (!empty($driver['vehicle_plate'])): ?>
                                                        - <?php echo htmlspecialchars($driver['vehicle_plate']); ?>
                                                    <?php endif; ?>
                                                </option>
                                            <?php endforeach; ?>
                                        </datalist>
                                        <input type="hidden" id="driver_id" name="driver_id" value="<?php echo $_POST['driver_id'] ?? ''; ?>">
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="vehicle_plate" class="form-label">Vehicle Plate ทะเบียนรถ</label>
                                    <input type="text" class="form-control" id="vehicle_plate" name="vehicle_plate" 
                                           value="<?php echo htmlspecialchars($_POST['vehicle_plate'] ?? ''); ?>" readonly>
                                </div>
                            </div>
                        </div>
                        
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="payment_account_no" class="form-label">Payment Account Number เลขที่บัญชี</label>
                                    <input type="text" class="form-control" id="payment_account_no" name="payment_account_no" 
                                           value="<?php echo htmlspecialchars($_POST['payment_account_no'] ?? ''); ?>" readonly>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="additional_details" class="form-label">Additional Details รายละเอียดเพิ่มเติม</label>
                                    <input type="text" class="form-control" id="additional_details" name="additional_details"
                                           value="<?php echo htmlspecialchars($_POST['additional_details'] ?? ''); ?>">
                                </div>
                            </div>
                        </div>
                        
                        <div class="row">
                            <div class="col-md-4">
                                <div class="mb-3">
                                    <label for="requester" class="form-label">Requester ผู้ขอเบิก</label>
                                    <input type="text" class="form-control" id="requester" name="requester" 
                                           value="<?php echo htmlspecialchars($_POST['requester'] ?? ''); ?>" readonly>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="mb-3">
                                    <label for="receiver" class="form-label">Receiver ผู้รับเงิน</label>
                                    <input type="text" class="form-control" id="receiver" name="receiver" 
                                           value="<?php echo htmlspecialchars($_POST['receiver'] ?? ''); ?>" readonly>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="mb-3">
                                    <label for="payer" class="form-label">Payer ผู้จ่ายเงิน</label>
                                    <input type="text" class="form-control" id="payer" name="payer" 
                                           value="<?php echo htmlspecialchars($user['full_name']); ?>" readonly>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Payment Information -->
                    <div class="form-section">
                        <h4 class="section-title">Payment Information</h4>
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="withdrawal_date" class="form-label">Withdrawal Date วันที่เบิก<span class="text-danger">*</span></label>
                                    <input type="date" class="form-control" id="withdrawal_date" name="withdrawal_date" 
                                           value="<?php echo $_POST['withdrawal_date'] ?? date('Y-m-d'); ?>" 
                                           min="<?php echo date('Y-m-d', strtotime('-1 month')); ?>" 
                                           max="<?php echo date('Y-m-d'); ?>" required>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="transfer_no" class="form-label">Transfer Number เลขใบโอน <span class="text-danger">*</span></label>
                                    <input type="text" class="form-control" id="transfer_no" name="transfer_no"
                                           value="<?php echo htmlspecialchars($_POST['transfer_no'] ?? ''); ?>" required>
                                    <div class="invalid-feedback"></div>
                                    <div class="valid-feedback"></div>
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="transfer_amount" class="form-label">Transfer Amount ยอดเงินที่โอน (บาท) *</label>
                                    <input type="number" class="form-control" id="transfer_amount" name="transfer_amount"
                                           step="0.01" min="0" placeholder="0.00"
                                           value="<?php echo htmlspecialchars($_POST['transfer_amount'] ?? ''); ?>" required>
                                    <div class="form-text">ยอดเงินที่โอนจริง เพื่อเปรียบเทียบกับยอดรวมใบเสร็จ</div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">Receipt Total Comparison</label>
                                    <div class="card">
                                        <div class="card-body p-2">
                                            <div class="row text-center">
                                                <div class="col-6">
                                                    <small class="text-muted">Transfer Amount</small>
                                                    <div id="display-transfer-amount" class="fw-bold text-primary">0.00</div>
                                                </div>
                                                <div class="col-6">
                                                    <small class="text-muted">Receipt Total</small>
                                                    <div id="display-receipt-total" class="fw-bold text-success">0.00</div>
                                                </div>
                                            </div>
                                            <div class="mt-2">
                                                <div id="amount-comparison" class="text-center">
                                                    <small class="text-muted">Enter amounts to compare</small>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- File Uploads -->
                    <div class="form-section">
                        <h4 class="section-title">Document Uploads ใบเสร็จและใบโอน </h4>
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="transfer_slip" class="form-label">Transfer Slip Image ใบโอน <span class="text-danger">*</span></label>
                                    <input type="file" class="form-control" id="transfer_slip" name="transfer_slip"
                                           accept="image/*,.pdf" required>
                                    <div class="form-text">Accepted formats: JPG, PNG, PDF (Max: 5MB)</div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="receipts" class="form-label">Receipt Images ใบเสร็จ <span class="text-danger">*</span></label>
                                    <input type="file" class="form-control" id="receipts" name="receipts[]"
                                           accept="image/*,.pdf" multiple required>
                                    <div class="form-text">You can select multiple files เลขที่ใบโอนจะแสดงตามจำนวน</div>
                                </div>
                            </div>
                        </div>
                        
                        <div id="receipt-numbers-container" class="d-none">
                            <h6>Receipt Details</h6>
                            <div class="alert alert-info">
                                <small><i class="fas fa-info-circle"></i> กรอกรายละเอียดสำหรับแต่ละใบเสร็จ</small>
                            </div>
                            <div id="receipt-numbers"></div>
                            <div class="mt-3">
                                <strong>Total Amount: <span id="total-amount">0.00</span> บาท</strong>
                            </div>
                        </div>

                        <!-- Summary ยอดรวม (แสดงเมื่อมี deductions) -->
                        <div id="deductions-summary-container" class="d-none">
                            <hr class="my-4">
                            <div class="card border-primary">
                                <div class="card-header bg-primary text-white">
                                    <h6 class="mb-0"><i class="fas fa-calculator me-2"></i>สรุปยอดรวม</h6>
                                </div>
                                <div class="card-body">
                                    <div class="row">
                                        <div class="col-md-3">
                                            <div class="text-center">
                                                <div class="h4 text-success mb-1" id="total-gross-amount">0.00</div>
                                                <small class="text-muted">ยอดก่อนหัก (บาท)</small>
                                            </div>
                                        </div>
                                        <div class="col-md-3">
                                            <div class="text-center">
                                                <div class="h4 text-danger mb-1" id="total-deductions-amount">0.00</div>
                                                <small class="text-muted">รายการหัก (บาท)</small>
                                            </div>
                                        </div>
                                        <div class="col-md-3">
                                            <div class="text-center">
                                                <div class="h4 text-primary mb-1" id="total-net-amount">0.00</div>
                                                <small class="text-muted">ยอดสุทธิ (บาท)</small>
                                            </div>
                                        </div>
                                        <div class="col-md-3">
                                            <div class="text-center">
                                                <div class="h4 text-info mb-1" id="transfer-amount-display">0.00</div>
                                                <small class="text-muted">Transfer Amount (บาท)</small>
                                                <div id="amount-validation-status" class="mt-1"></div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Submit Buttons -->
                    <div class="form-section">
                        <div class="d-flex justify-content-between">
                            <button type="button" class="btn btn-outline-warning" onclick="resetForm()">
                                <i class="fas fa-undo me-1"></i>Reset Form
                            </button>
                            <div class="d-flex gap-2">
                                <a href="../index.php" class="btn btn-secondary">
                                    <i class="fas fa-times me-1"></i>Cancel
                                </a>
                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-save me-1"></i>Create Expense
                                </button>
                            </div>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- Deduction Modal -->
    <div class="modal fade" id="deductionModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="deductionModalTitle">เพิ่มรายการหัก</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <div id="deduction-form-errors"></div>
                    <form id="deduction-form">
                        <div class="mb-3">
                            <label for="deduction_type" class="form-label">ประเภทการหัก</label>
                            <select class="form-select" id="deduction_type" required>
                                <option value="">เลือกประเภท</option>
                                <option value="tax_vat">ภาษีมูลค่าเพิ่ม (VAT)</option>
                                <option value="tax_withholding">ภาษีหัก ณ ที่จ่าย</option>
                                <option value="service_fee">ค่าธรรมเนียม</option>
                                <option value="discount">ส่วนลด</option>
                                <option value="penalty">ค่าปรับ</option>
                                <option value="commission">ค่าคอมมิชชั่น</option>
                                <option value="other">อื่นๆ</option>
                            </select>
                        </div>

                        <div class="mb-3">
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="is_percentage_based">
                                <label class="form-check-label" for="is_percentage_based">
                                    คำนวณจากเปอร์เซ็นต์
                                </label>
                            </div>
                        </div>

                        <div class="mb-3" id="amount-field">
                            <label for="deduction_amount" class="form-label">จำนวนเงิน</label>
                            <div class="input-group">
                                <input type="number" class="form-control" id="deduction_amount" step="0.01" min="0">
                                <span class="input-group-text">บาท</span>
                            </div>
                        </div>

                        <div class="mb-3" id="percentage-field" style="display: none;">
                            <label for="deduction_percentage" class="form-label">เปอร์เซ็นต์</label>
                            <div class="input-group">
                                <input type="number" class="form-control" id="deduction_percentage" step="0.01" min="0" max="100">
                                <span class="input-group-text">%</span>
                            </div>
                            <div class="mt-2">
                                <small class="text-muted">จำนวนที่คำนวณได้: <span id="calculated-amount-display">0.00 บาท</span></small>
                            </div>
                        </div>

                        <div class="mb-3">
                            <label for="deduction_image" class="form-label">แนบรูปหลักฐานการหัก <span class="text-danger">*</span></label>
                            <input type="file" class="form-control" id="deduction_image" accept="image/*,.pdf" required>
                            <div class="form-text">ไฟล์ที่รองรับ: JPG, PNG, PDF (ขนาดไม่เกิน 5MB)</div>
                            <div id="image-upload-status" class="mt-2"></div>
                            <div id="deduction_image_preview" class="mt-2" style="display: none;">
                                <img class="img-thumbnail" style="max-width: 200px;">
                                <div class="mt-1">
                                    <small class="text-success"><i class="fas fa-check-circle me-1"></i>อัพโหลดเรียบร้อยแล้ว</small>
                                </div>
                            </div>
                        </div>

                        <div class="mb-3">
                            <label for="deduction_description" class="form-label">รายละเอียด</label>
                            <textarea class="form-control" id="deduction_description" rows="2" placeholder="เช่น ภาษีหัก ณ ที่จ่าย 3% ตามกฎหมาย"></textarea>
                        </div>

                        <!-- Hidden field for receipt index -->
                        <input type="hidden" id="target_receipt_index" value="">
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">ยกเลิก</button>
                    <button type="button" class="btn btn-primary" id="save-deduction-btn">บันทึก</button>
                </div>
            </div>
        </div>
    </div>

    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script src="../assets/js/expense-form.js"></script>
    <script src="../assets/js/per-receipt-deductions.js"></script>
    <script src="../assets/js/image-viewer.js?v=<?php echo filemtime('../assets/js/image-viewer.js'); ?>"></script>

    <script>
    // Override upload handler to use production API for create.php
    $(document).ready(function() {
        // Override modal show event to ensure clean state
        $('#deductionModal').on('show.bs.modal', function() {
            console.log('Modal opening - clearing image preview');
            // Force clear image preview
            $('#deduction_image_preview').hide();
            $('#deduction_image_preview img').attr('src', '');
            $('#image-upload-status').empty();
            $('#deduction_image').val('');
            $('#deduction_image').removeData('uploaded-filename').removeData('uploaded-url');
        });

        // Override the deduction image upload handler
        $('#deduction_image').off('change').on('change', function() {
            const file = this.files[0];
            if (!file) {
                // Clear preview if no file selected
                $('#deduction_image_preview').hide();
                $('#deduction_image_preview img').attr('src', '');
                return;
            }

            // Show preview
            const reader = new FileReader();
            reader.onload = function(e) {
                $('#deduction_image_preview img').attr('src', e.target.result);
                $('#deduction_image_preview').show();
            };
            reader.readAsDataURL(file);

            // Upload file using test API
            const formData = new FormData();
            formData.append('deduction_image', file);

            // Show uploading status
            $('#image-upload-status').html(`
                <div class="alert alert-info">
                    <i class="fas fa-spinner fa-spin"></i> กำลังอัพโหลด...
                </div>
            `);

            $.ajax({
                url: '../api/upload_deduction_image.php', // Use production API
                type: 'POST',
                data: formData,
                processData: false,
                contentType: false,
                success: function(response) {
                    console.log('Upload API Response:', response);
                    if (response.success) {
                        // Store filename for later use
                        $('#deduction_image').data('uploaded-filename', response.filename);
                        $('#deduction_image').data('uploaded-url', response.url);

                        // Show success message
                        $('#image-upload-status').html(`
                            <div class="alert alert-success">
                                <i class="fas fa-check"></i> อัพโหลดสำเร็จ: ${response.original_name}
                                <br><small>ไฟล์: ${response.filename}</small>
                            </div>
                        `);
                    } else {
                        console.log('Upload failed:', response.error);
                        $('#image-upload-status').html(`
                            <div class="alert alert-danger">
                                <i class="fas fa-times"></i> อัพโหลดล้มเหลว: ${response.error}
                            </div>
                        `);
                    }
                },
                error: function(xhr, status, error) {
                    console.log('Upload API Error:', xhr, status, error);
                    console.log('Response Text:', xhr.responseText);
                    let errorMessage = 'เกิดข้อผิดพลาดในการอัพโหลด';

                    if (xhr.responseJSON && xhr.responseJSON.error) {
                        errorMessage = xhr.responseJSON.error;
                    } else if (xhr.responseText) {
                        try {
                            const response = JSON.parse(xhr.responseText);
                            errorMessage = response.error || errorMessage;
                        } catch (e) {
                            errorMessage = `HTTP ${xhr.status}: ${error}`;
                        }
                    }

                    $('#image-upload-status').html(`
                        <div class="alert alert-danger">
                            <i class="fas fa-times"></i> ${errorMessage}
                            <br><small>Status: ${xhr.status} ${error}</small>
                        </div>
                    `);
                }
            });
        });

        // Initialize Image Viewer
        window.imageViewer = new ImageViewer();

        // Handle image viewer triggers for deduction images
        $(document).on('click', '.image-viewer-trigger', function() {
            const imageSrc = $(this).data('image-src');
            const imageTitle = $(this).data('image-title');

            console.log('Image viewer trigger clicked:', imageSrc, imageTitle);

            if (imageSrc) {
                // Show single image
                window.imageViewer.showImage(imageSrc, [], 0);
            } else {
                console.error('No image source found for image viewer trigger');
            }
        });
    });
    </script>
</body>
</html>
