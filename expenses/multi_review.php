<?php
session_start();
require_once '../config/database.php';
require_once '../includes/functions.php';

// Check if user is logged in
if (!isset($_SESSION['user_id'])) {
    header('Location: ../login.php');
    exit();
}

// Check if user has reviewer role
if (!in_array($_SESSION['role'], ['reviewer', 'administrator'])) {
    header('Location: ../dashboard.php');
    exit();
}

$user_id = $_SESSION['user_id'];
$user_role = $_SESSION['role'];
$username = $_SESSION['username'];
?>

<!DOCTYPE html>
<html lang="th">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Multi-select Review | Expense Management System</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    
    <style>
        .selected-summary {
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
            color: white;
            border-radius: 10px;
            padding: 20px;
            margin-bottom: 20px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
        }
        
        .expense-row {
            transition: all 0.3s ease;
        }
        
        .expense-row:hover {
            background-color: #f8f9fa;
        }
        
        .expense-row.selected {
            background-color: #e8f5e8;
            border-left: 4px solid #28a745;
        }
        
        .batch-controls {
            background: white;
            border-radius: 10px;
            padding: 20px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        
        .filter-section {
            background: #f8f9fa;
            border-radius: 8px;
            padding: 15px;
            margin-bottom: 20px;
        }
        
        .summary-card {
            background: white;
            border-radius: 8px;
            padding: 15px;
            text-align: center;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
        }
        
        .summary-number {
            font-size: 1.5rem;
            font-weight: bold;
            color: #28a745;
        }
        
        .table-container {
            background: white;
            border-radius: 10px;
            overflow: hidden;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .loading-overlay {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0,0,0,0.5);
            display: none;
            justify-content: center;
            align-items: center;
            z-index: 9999;
        }
        
        .loading-spinner {
            background: white;
            padding: 30px;
            border-radius: 10px;
            text-align: center;
        }
        
        .verification-info {
            background: #e3f2fd;
            border-left: 4px solid #2196f3;
            padding: 10px;
            margin: 5px 0;
            border-radius: 4px;
        }
    </style>
</head>
<body class="bg-light">
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-success">
        <div class="container-fluid">
            <a class="navbar-brand" href="../dashboard.php">
                <i class="fas fa-chart-line me-2"></i>Expense System
            </a>
            <div class="navbar-nav ms-auto">
                <span class="navbar-text me-3">
                    <i class="fas fa-user me-1"></i><?php echo htmlspecialchars($username); ?> (<?php echo ucfirst($user_role); ?>)
                </span>
                <a class="nav-link" href="../logout.php">
                    <i class="fas fa-sign-out-alt"></i> Logout
                </a>
            </div>
        </div>
    </nav>

    <div class="container-fluid mt-4">
        <!-- Page Header -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h2><i class="fas fa-clipboard-check me-2"></i>Reviewer Batch Management</h2>
                        <p class="text-muted">Review completed verification batches</p>
                    </div>
                    <div>
                        <a href="../dashboard.php" class="btn btn-outline-secondary">
                            <i class="fas fa-arrow-left me-1"></i>Back to Dashboard
                        </a>
                        <a href="index.php" class="btn btn-outline-success">
                            <i class="fas fa-list me-1"></i>Single Review
                        </a>
                    </div>
                </div>
            </div>
        </div>

        <!-- Selected Summary -->
        <div class="selected-summary" id="selectedSummary" style="display: none;">
            <div class="row">
                <div class="col-md-3">
                    <div class="summary-card">
                        <div class="summary-number" id="selectedCount">0</div>
                        <div>Items Selected</div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="summary-card">
                        <div class="summary-number" id="selectedAmount">0.00</div>
                        <div>Total Amount (บาท)</div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="summary-card">
                        <div class="summary-number" id="dateRange">-</div>
                        <div>Date Range</div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="d-grid">
                        <button class="btn btn-success btn-lg" id="createBatchBtn">
                            <i class="fas fa-plus me-1"></i>Create Review Batch
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <!-- Filters -->
        <div class="filter-section">
            <div class="row">
                <div class="col-md-3">
                    <label class="form-label">Search</label>
                    <input type="text" class="form-control" id="searchInput" placeholder="Expense No, Description, Customer...">
                </div>
                <div class="col-md-2">
                    <label class="form-label">Date From</label>
                    <input type="date" class="form-control" id="dateFrom">
                </div>
                <div class="col-md-2">
                    <label class="form-label">Date To</label>
                    <input type="date" class="form-control" id="dateTo">
                </div>
                <div class="col-md-2">
                    <label class="form-label">Amount Min</label>
                    <input type="number" class="form-control" id="amountMin" step="0.01" placeholder="0.00">
                </div>
                <div class="col-md-2">
                    <label class="form-label">Amount Max</label>
                    <input type="number" class="form-control" id="amountMax" step="0.01" placeholder="0.00">
                </div>
                <div class="col-md-1">
                    <label class="form-label">&nbsp;</label>
                    <div class="d-grid">
                        <button class="btn btn-success" id="searchBtn">
                            <i class="fas fa-search"></i>
                        </button>
                    </div>
                </div>
            </div>

            <!-- Info Alert -->
            <div class="row mb-3">
                <div class="col-12">
                    <div class="alert alert-info mb-0">
                        <small>
                            <i class="fas fa-info-circle me-1"></i>
                            <strong>หมายเหตุ:</strong> แสดงรายการแบทช์ที่ผ่านการ Verification แล้ว พร้อมสำหรับการ Review
                        </small>
                    </div>
                </div>
            </div>
        </div>

        <!-- Expenses Table -->
        <div class="table-container">
            <div class="table-responsive">
                <table class="table table-hover mb-0">
                    <thead class="table-dark">
                        <tr>
                            <th width="50">
                                <input type="checkbox" id="selectAll" class="form-check-input">
                            </th>
                            <th>Batch ID</th>
                            <th>Completed Date</th>
                            <th>Verified By</th>
                            <th>Batch Details</th>
                            <th>Total Amount</th>
                            <th>Created By</th>
                            <th>Status</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody id="expensesTableBody">
                        <!-- Dynamic content -->
                    </tbody>
                </table>
            </div>
            
            <!-- Pagination -->
            <div class="d-flex justify-content-between align-items-center p-3 border-top">
                <div id="paginationInfo">
                    <!-- Pagination info -->
                </div>
                <nav>
                    <ul class="pagination mb-0" id="paginationNav">
                        <!-- Pagination buttons -->
                    </ul>
                </nav>
            </div>
        </div>
    </div>

    <!-- Loading Overlay -->
    <div class="loading-overlay" id="loadingOverlay">
        <div class="loading-spinner">
            <div class="spinner-border text-success mb-3" role="status">
                <span class="visually-hidden">Loading...</span>
            </div>
            <div>Processing batch review...</div>
        </div>
    </div>



    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <!-- jQuery -->
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    
    <script>
        // Global variables
        let selectedExpenses = new Map();
        let currentPage = 1;
        let totalPages = 1;
        
        // Initialize page
        $(document).ready(function() {
            loadExpenses();
            
            // Event listeners
            $('#searchBtn').click(function() {
                currentPage = 1;
                loadExpenses();
            });


            
            $('#selectAll').change(function() {
                const isChecked = $(this).is(':checked');
                $('.expense-checkbox').prop('checked', isChecked);

                if (isChecked) {
                    $('.expense-checkbox').each(function() {
                        const batchData = $(this).data('batch');
                        selectedExpenses.set(batchData.batch_id, batchData);
                    });
                } else {
                    selectedExpenses.clear();
                }

                updateSelectedSummary();
            });
            
            $('#createBatchBtn').click(function() {
                if (selectedExpenses.size === 0) {
                    alert('Please select at least one batch');
                    return;
                }
                
                createBatch();
            });
            
            // Search on Enter key
            $('#searchInput').keypress(function(e) {
                if (e.which === 13) {
                    $('#searchBtn').click();
                }
            });
        });
        
        // Load reviewer batches data
        function loadExpenses() {
            const params = new URLSearchParams({
                page: currentPage,
                limit: 20,
                search: $('#searchInput').val(),
                date_from: $('#dateFrom').val(),
                date_to: $('#dateTo').val(),
                amount_min: $('#amountMin').val(),
                amount_max: $('#amountMax').val()
            });

            $.ajax({
                url: '../api/get_reviewer_batches.php?' + params.toString(),
                method: 'GET',
                success: function(response) {
                    if (response.success) {
                        renderBatchesTable(response.data.batches);
                        renderPagination(response.data.pagination);
                        updateSummaryInfo(response.data.summary);
                    } else {
                        alert('Error: ' + response.error);
                    }
                },
                error: function(xhr) {
                    alert('Error loading expenses: ' + (xhr.responseJSON?.error || 'Unknown error'));
                }
            });
        }
        
        // Render batches table
        function renderBatchesTable(batches) {
            const tbody = $('#expensesTableBody');
            tbody.empty();

            if (batches.length === 0) {
                tbody.append(`
                    <tr>
                        <td colspan="9" class="text-center py-4">
                            <i class="fas fa-inbox fa-2x text-muted mb-2"></i>
                            <div>No completed verification batches found for review</div>
                        </td>
                    </tr>
                `);
                return;
            }

            batches.forEach(batch => {
                const isSelected = selectedExpenses.has(batch.batch_id);
                const processingTime = batch.processing_time ? Math.round(batch.processing_time / 60) + ' min' : '-';
                const row = $(`
                    <tr class="expense-row ${isSelected ? 'selected' : ''}" data-id="${batch.batch_id}">
                        <td>
                            <input type="checkbox" class="form-check-input expense-checkbox"
                                   ${isSelected ? 'checked' : ''} data-batch='${JSON.stringify(batch)}'>
                        </td>
                        <td>
                            <strong>${batch.batch_id}</strong>
                        </td>
                        <td>${new Date(batch.completed_at).toLocaleDateString('th-TH')}</td>
                        <td>${batch.created_by_name || '-'}</td>
                        <td>
                            <div class="text-truncate" style="max-width: 200px;">
                                <strong class="text-primary">${batch.total_items} รายการ</strong><br>
                                <small class="text-muted">Processing: ${processingTime}</small>
                            </div>
                            <div class="verification-info">
                                <small><i class="fas fa-check-circle text-primary"></i> Verified: ${parseFloat(batch.total_amount).toLocaleString('th-TH', {minimumFractionDigits: 2})} บาท</small>
                            </div>
                        </td>
                        <td>
                            <strong class="text-success">${parseFloat(batch.total_amount).toLocaleString('th-TH', {minimumFractionDigits: 2})} บาท</strong>
                        </td>
                        <td>
                            <small>${batch.created_by_name || '-'}</small>
                        </td>
                        <td>
                            <span class="badge bg-success">${batch.status}</span>
                            <br><small class="text-muted"><i class="fas fa-layer-group"></i> ${batch.operation_type}</small>
                        </td>
                        <td>
                            <a href="reviewer_batch_process.php?batch_id=${batch.batch_id}" class="btn btn-sm btn-outline-primary" title="Review Batch">
                                <i class="fas fa-gavel"></i>
                            </a>
                        </td>
                    </tr>
                `);

                tbody.append(row);
            });

            // Add event listeners for checkboxes
            $('.expense-checkbox').change(function() {
                const batchData = $(this).data('batch');
                const row = $(this).closest('tr');

                if ($(this).is(':checked')) {
                    selectedExpenses.set(batchData.batch_id, batchData);
                    row.addClass('selected');
                } else {
                    selectedExpenses.delete(batchData.batch_id);
                    row.removeClass('selected');
                }

                updateSelectedSummary();
                updateSelectAllCheckbox();
            });
        }

        // Update selected summary
        function updateSelectedSummary() {
            const count = selectedExpenses.size;
            let totalAmount = 0;
            let minDate = null;
            let maxDate = null;

            selectedExpenses.forEach(batch => {
                totalAmount += parseFloat(batch.total_amount || 0);

                // ใช้ completed_at สำหรับ date range
                if (batch.completed_at) {
                    const batchDate = new Date(batch.completed_at);
                    if (!minDate || batchDate < minDate) minDate = batchDate;
                    if (!maxDate || batchDate > maxDate) maxDate = batchDate;
                }
            });

            $('#selectedCount').text(count);
            $('#selectedAmount').text(totalAmount.toLocaleString('th-TH', {minimumFractionDigits: 2}));

            if (count > 0) {
                const dateRangeText = minDate && maxDate ?
                    (minDate.getTime() === maxDate.getTime() ?
                        minDate.toLocaleDateString('th-TH') :
                        `${minDate.toLocaleDateString('th-TH')} - ${maxDate.toLocaleDateString('th-TH')}`) : '-';
                $('#dateRange').text(dateRangeText);
                $('#selectedSummary').show();
            } else {
                $('#selectedSummary').hide();
            }
        }

        // Update select all checkbox
        function updateSelectAllCheckbox() {
            const totalCheckboxes = $('.expense-checkbox').length;
            const checkedCheckboxes = $('.expense-checkbox:checked').length;

            $('#selectAll').prop('indeterminate', checkedCheckboxes > 0 && checkedCheckboxes < totalCheckboxes);
            $('#selectAll').prop('checked', checkedCheckboxes === totalCheckboxes && totalCheckboxes > 0);
        }

        // Render pagination
        function renderPagination(pagination) {
            currentPage = pagination.current_page;
            totalPages = pagination.total_pages;

            $('#paginationInfo').html(`
                Showing ${((currentPage - 1) * pagination.limit) + 1} to ${Math.min(currentPage * pagination.limit, pagination.total_records)}
                of ${pagination.total_records} entries
            `);

            const nav = $('#paginationNav');
            nav.empty();

            // Previous button
            nav.append(`
                <li class="page-item ${!pagination.has_prev ? 'disabled' : ''}">
                    <a class="page-link" href="#" data-page="${currentPage - 1}">Previous</a>
                </li>
            `);

            // Page numbers
            const startPage = Math.max(1, currentPage - 2);
            const endPage = Math.min(totalPages, currentPage + 2);

            for (let i = startPage; i <= endPage; i++) {
                nav.append(`
                    <li class="page-item ${i === currentPage ? 'active' : ''}">
                        <a class="page-link" href="#" data-page="${i}">${i}</a>
                    </li>
                `);
            }

            // Next button
            nav.append(`
                <li class="page-item ${!pagination.has_next ? 'disabled' : ''}">
                    <a class="page-link" href="#" data-page="${currentPage + 1}">Next</a>
                </li>
            `);

            // Add click handlers
            $('.page-link').click(function(e) {
                e.preventDefault();
                const page = parseInt($(this).data('page'));
                if (page && page !== currentPage && page >= 1 && page <= totalPages) {
                    currentPage = page;
                    loadExpenses();
                }
            });
        }

        // Update summary info
        function updateSummaryInfo(summary) {
            // You can add summary display here if needed
        }

        // Create batch
        function createBatch() {
            console.log('createBatch() called');
            console.log('selectedExpenses:', selectedExpenses);

            if (selectedExpenses.size === 0) {
                alert('Please select at least one batch');
                return;
            }

            const batchIds = Array.from(selectedExpenses.keys());
            const totalAmount = Array.from(selectedExpenses.values())
                .reduce((sum, batch) => sum + parseFloat(batch.total_amount || 0), 0);

            // ตรวจสอบว่ามีข้อมูลครบถ้วน
            if (isNaN(totalAmount) || totalAmount <= 0) {
                alert('Invalid total amount: ' + totalAmount);
                return;
            }

            console.log('FormData prepared, sending AJAX request...');
            $('#loadingOverlay').show();

            const formData = new FormData();
            formData.append('action', 'create_batch');
            formData.append('batch_ids', JSON.stringify(batchIds));
            formData.append('total_amount', totalAmount.toFixed(2));
            formData.append('notes', `Batch review for ${batchIds.length} verification batches`);

            $.ajax({
                url: '../api/batch_review.php',
                method: 'POST',
                data: formData,
                processData: false,
                contentType: false,
                success: function(response) {
                    $('#loadingOverlay').hide();
                    console.log('Batch creation response:', response);

                    if (response.success) {
                        console.log('Batch created successfully, batch_id:', response.batch_id);

                        // Redirect to batch_process.php for processing
                        window.location.href = `batch_process.php?batch_id=${response.batch_id}&type=review`;
                    } else {
                        alert('Error: ' + response.error);
                    }
                },
                error: function(xhr) {
                    $('#loadingOverlay').hide();
                    console.log('AJAX Error:', xhr);
                    console.log('Response Text:', xhr.responseText);
                    console.log('Status:', xhr.status);
                    alert('Error creating batch: ' + (xhr.responseJSON?.error || xhr.responseText || 'Unknown error'));
                }
            });
        }





        $(document).ready(function() {
            loadBatches();
        });
    </script>
</body>
</html>
