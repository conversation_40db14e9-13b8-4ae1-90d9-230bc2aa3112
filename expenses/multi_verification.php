<?php
session_start();
require_once '../config/database.php';
require_once '../includes/functions.php';

// Check if user is logged in
if (!isset($_SESSION['user_id'])) {
    header('Location: ../login.php');
    exit();
}

// Check if user has verification role
if (!in_array($_SESSION['role'], ['verification', 'administrator'])) {
    header('Location: ../dashboard.php');
    exit();
}

$user_id = $_SESSION['user_id'];
$user_role = $_SESSION['role'];
$username = $_SESSION['username'];
?>

<!DOCTYPE html>
<html lang="th">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Multi-select Verification | Expense Management System</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    
    <style>
        .selected-summary {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 10px;
            padding: 20px;
            margin-bottom: 20px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
        }
        
        .expense-row {
            transition: all 0.3s ease;
        }
        
        .expense-row:hover {
            background-color: #f8f9fa;
        }
        
        .expense-row.selected {
            background-color: #e3f2fd;
            border-left: 4px solid #2196f3;
        }
        
        .batch-controls {
            background: white;
            border-radius: 10px;
            padding: 20px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        
        .filter-section {
            background: #f8f9fa;
            border-radius: 8px;
            padding: 15px;
            margin-bottom: 20px;
        }
        
        .summary-card {
            background: white;
            border-radius: 8px;
            padding: 15px;
            text-align: center;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
        }
        
        .summary-number {
            font-size: 1.5rem;
            font-weight: bold;
            color: #2196f3;
        }
        
        .table-container {
            background: white;
            border-radius: 10px;
            overflow: hidden;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .loading-overlay {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0,0,0,0.5);
            display: none;
            justify-content: center;
            align-items: center;
            z-index: 9999;
        }
        
        .loading-spinner {
            background: white;
            padding: 30px;
            border-radius: 10px;
            text-align: center;
        }
    </style>
</head>
<body class="bg-light">
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container-fluid">
            <a class="navbar-brand" href="../dashboard.php">
                <i class="fas fa-chart-line me-2"></i>Expense System
            </a>
            <div class="navbar-nav ms-auto">
                <span class="navbar-text me-3">
                    <i class="fas fa-user me-1"></i><?php echo htmlspecialchars($username); ?> (<?php echo ucfirst($user_role); ?>)
                </span>
                <a class="nav-link" href="../logout.php">
                    <i class="fas fa-sign-out-alt"></i> Logout
                </a>
            </div>
        </div>
    </nav>

    <div class="container-fluid mt-4">
        <!-- Page Header -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h2><i class="fas fa-check-double me-2"></i>Multi-select Verification</h2>
                        <p class="text-muted">Select multiple expenses for batch verification processing</p>
                    </div>
                    <div>
                        <a href="../dashboard.php" class="btn btn-outline-secondary">
                            <i class="fas fa-arrow-left me-1"></i>Back to Dashboard
                        </a>
                        <a href="index.php" class="btn btn-outline-primary">
                            <i class="fas fa-list me-1"></i>Single Verification
                        </a>
                    </div>
                </div>
            </div>
        </div>

        <!-- Selected Summary -->
        <div class="selected-summary" id="selectedSummary" style="display: none;">
            <div class="row">
                <div class="col-md-3">
                    <div class="summary-card">
                        <div class="summary-number" id="selectedCount">0</div>
                        <div>Items Selected</div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="summary-card">
                        <div class="summary-number" id="selectedAmount">0.00</div>
                        <div>Total Amount (บาท)</div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="summary-card">
                        <div class="summary-number" id="dateRange">-</div>
                        <div>Date Range</div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="d-grid">
                        <button class="btn btn-success btn-lg" id="createBatchBtn">
                            <i class="fas fa-plus me-1"></i>Create Batch
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <!-- Filters -->
        <div class="filter-section">
            <div class="row">
                <div class="col-md-3">
                    <label class="form-label">Search</label>
                    <input type="text" class="form-control" id="searchInput" placeholder="Expense No, Description, Customer...">
                </div>
                <div class="col-md-2">
                    <label class="form-label">Date From</label>
                    <input type="date" class="form-control" id="dateFrom">
                </div>
                <div class="col-md-2">
                    <label class="form-label">Date To</label>
                    <input type="date" class="form-control" id="dateTo">
                </div>
                <div class="col-md-2">
                    <label class="form-label">Amount Min</label>
                    <input type="number" class="form-control" id="amountMin" step="0.01" placeholder="0.00">
                </div>
                <div class="col-md-2">
                    <label class="form-label">Amount Max</label>
                    <input type="number" class="form-control" id="amountMax" step="0.01" placeholder="0.00">
                </div>
                <div class="col-md-1">
                    <label class="form-label">&nbsp;</label>
                    <div class="d-grid">
                        <button class="btn btn-primary" id="searchBtn">
                            <i class="fas fa-search"></i>
                        </button>
                    </div>
                </div>
            </div>

            <!-- Status and Batch Filter -->
            <div class="row mb-3">
                <div class="col-md-3">
                    <label for="statusFilter" class="form-label">
                        <i class="fas fa-check-circle me-1"></i>Status Filter
                    </label>
                    <select class="form-select" id="statusFilter">
                        <option value="">🔍 All Status</option>
                        <option value="open">📂 Open</option>
                        <option value="checked" selected>✅ Checked (Recommended)</option>
                    </select>
                </div>
                <div class="col-md-3">
                    <label for="batchFilter" class="form-label">
                        <i class="fas fa-layer-group me-1"></i>Batch Status Filter
                    </label>
                    <select class="form-select" id="batchFilter">
                        <option value="">🔍 All Items</option>
                        <option value="no_batch">📝 Individual Items (Not in Batch)</option>
                        <option value="has_batch">📦 Already in Batch</option>
                    </select>
                </div>
                <div class="col-md-8">
                    <div class="alert alert-info mb-0">
                        <small>
                            <i class="fas fa-info-circle me-1"></i>
                            <strong>หมายเหตุ:</strong> ระบบจะแสดงรายการที่มีสถานะ "Open" และ "Checked" เท่านั้น<br>
                            <strong>แนะนำ:</strong> ควรเลือกรายการที่มีสถานะ "Checked" เพื่อความถูกต้อง
                        </small>
                    </div>
                </div>
            </div>
        </div>

        <!-- Expenses Table -->
        <div class="table-container">
            <div class="table-responsive">
                <table class="table table-hover mb-0">
                    <thead class="table-dark">
                        <tr>
                            <th width="50">
                                <input type="checkbox" id="selectAll" class="form-check-input">
                            </th>
                            <th>Expense No.</th>
                            <th>Date</th>
                            <th>Customer</th>
                            <th>Description</th>
                            <th>Amount</th>
                            <th>Status</th>
                            <th>Created By</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody id="expensesTableBody">
                        <!-- Dynamic content -->
                    </tbody>
                </table>
            </div>
            
            <!-- Pagination -->
            <div class="d-flex justify-content-between align-items-center p-3 border-top">
                <div id="paginationInfo">
                    <!-- Pagination info -->
                </div>
                <nav>
                    <ul class="pagination mb-0" id="paginationNav">
                        <!-- Pagination buttons -->
                    </ul>
                </nav>
            </div>
        </div>
    </div>

    <!-- Loading Overlay -->
    <div class="loading-overlay" id="loadingOverlay">
        <div class="loading-spinner">
            <div class="spinner-border text-primary mb-3" role="status">
                <span class="visually-hidden">Loading...</span>
            </div>
            <div>Processing batch verification...</div>
        </div>
    </div>



    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <!-- jQuery -->
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    
    <script>
        // Global variables
        let selectedExpenses = new Map();
        let currentPage = 1;
        let totalPages = 1;
        
        // Initialize page
        $(document).ready(function() {
            loadExpenses();
            
            // Event listeners
            $('#searchBtn').click(function() {
                currentPage = 1;
                loadExpenses();
            });

            // Batch filter change
            $('#batchFilter').change(function() {
                currentPage = 1;
                loadExpenses();
            });
            
            $('#selectAll').change(function() {
                const isChecked = $(this).is(':checked');
                $('.expense-checkbox').prop('checked', isChecked);
                
                if (isChecked) {
                    $('.expense-checkbox').each(function() {
                        const expenseData = $(this).data('expense');
                        selectedExpenses.set(expenseData.id, expenseData);
                    });
                } else {
                    selectedExpenses.clear();
                }
                
                updateSelectedSummary();
            });
            
            $('#createBatchBtn').click(function() {
                if (selectedExpenses.size === 0) {
                    alert('Please select at least one expense');
                    return;
                }
                
                createBatch();
            });
            
            // Search on Enter key
            $('#searchInput').keypress(function(e) {
                if (e.which === 13) {
                    $('#searchBtn').click();
                }
            });

            // Status filter change
            $('#statusFilter').change(function() {
                currentPage = 1;
                selectedExpenses.clear();
                updateSelectedSummary();
                loadExpenses();
            });
        });
        
        // Load expenses data
        function loadExpenses() {
            const params = new URLSearchParams({
                operation_type: 'verification',
                page: currentPage,
                limit: 20,
                search: $('#searchInput').val(),
                date_from: $('#dateFrom').val(),
                date_to: $('#dateTo').val(),
                amount_min: $('#amountMin').val(),
                amount_max: $('#amountMax').val(),
                batch_filter: $('#batchFilter').val(),
                status_filter: $('#statusFilter').val()
            });
            
            $.ajax({
                url: '../api/get_expenses_for_batch.php?' + params.toString(),
                method: 'GET',
                success: function(response) {
                    if (response.success) {
                        renderExpensesTable(response.data.expenses);
                        renderPagination(response.data.pagination);
                        updateSummaryInfo(response.data.summary);
                    } else {
                        alert('Error: ' + response.error);
                    }
                },
                error: function(xhr) {
                    alert('Error loading expenses: ' + (xhr.responseJSON?.error || 'Unknown error'));
                }
            });
        }
        
        // Render expenses table
        function renderExpensesTable(expenses) {
            const tbody = $('#expensesTableBody');
            tbody.empty();

            if (expenses.length === 0) {
                tbody.append(`
                    <tr>
                        <td colspan="9" class="text-center py-4">
                            <i class="fas fa-inbox fa-2x text-muted mb-2"></i>
                            <div>No expenses found for verification</div>
                        </td>
                    </tr>
                `);
                return;
            }

            expenses.forEach(expense => {
                const isSelected = selectedExpenses.has(expense.id);
                const row = $(`
                    <tr class="expense-row ${isSelected ? 'selected' : ''}" data-id="${expense.id}">
                        <td>
                            <input type="checkbox" class="form-check-input expense-checkbox"
                                   ${isSelected ? 'checked' : ''} data-expense='${JSON.stringify(expense)}'>
                        </td>
                        <td>
                            <strong>${expense.exno}</strong>
                        </td>
                        <td>${new Date(expense.expense_date).toLocaleDateString('th-TH')}</td>
                        <td>${expense.customer_name || '-'}</td>
                        <td>
                            <div class="text-truncate" style="max-width: 200px;" title="${expense.item_name || 'N/A'} - ${expense.description}">
                                <strong class="text-primary">${expense.item_name || 'N/A'}</strong><br>
                                <small class="text-muted">${expense.description}</small>
                            </div>
                        </td>
                        <td>
                            <strong class="text-primary">${parseFloat(expense.transfer_amount).toLocaleString('th-TH', {minimumFractionDigits: 2})} บาท</strong>
                        </td>
                        <td>
                            <span class="badge bg-warning">${expense.status}</span>
                            ${expense.batch_verification_id ? '<br><small class="text-info"><i class="fas fa-layer-group"></i> Batch: ' + expense.batch_verification_id + '</small>' : ''}
                        </td>
                        <td>${expense.created_by_name || '-'}</td>
                        <td>
                            <a href="view.php?id=${expense.id}" class="btn btn-sm btn-outline-primary" title="View Details">
                                <i class="fas fa-eye"></i>
                            </a>
                        </td>
                    </tr>
                `);

                tbody.append(row);
            });

            // Add event listeners for checkboxes
            $('.expense-checkbox').change(function() {
                const expenseData = $(this).data('expense');
                const row = $(this).closest('tr');

                if ($(this).is(':checked')) {
                    selectedExpenses.set(expenseData.id, expenseData);
                    row.addClass('selected');
                } else {
                    selectedExpenses.delete(expenseData.id);
                    row.removeClass('selected');
                }

                updateSelectedSummary();
                updateSelectAllCheckbox();
            });
        }

        // Update selected summary
        function updateSelectedSummary() {
            const count = selectedExpenses.size;
            let totalAmount = 0;
            let minDate = null;
            let maxDate = null;

            selectedExpenses.forEach(expense => {
                totalAmount += parseFloat(expense.transfer_amount);
                const expenseDate = new Date(expense.expense_date);

                if (!minDate || expenseDate < minDate) minDate = expenseDate;
                if (!maxDate || expenseDate > maxDate) maxDate = expenseDate;
            });

            $('#selectedCount').text(count);
            $('#selectedAmount').text(totalAmount.toLocaleString('th-TH', {minimumFractionDigits: 2}));

            if (count > 0) {
                const dateRangeText = minDate && maxDate ?
                    (minDate.getTime() === maxDate.getTime() ?
                        minDate.toLocaleDateString('th-TH') :
                        `${minDate.toLocaleDateString('th-TH')} - ${maxDate.toLocaleDateString('th-TH')}`) : '-';
                $('#dateRange').text(dateRangeText);
                $('#selectedSummary').show();
            } else {
                $('#selectedSummary').hide();
            }
        }

        // Update select all checkbox
        function updateSelectAllCheckbox() {
            const totalCheckboxes = $('.expense-checkbox').length;
            const checkedCheckboxes = $('.expense-checkbox:checked').length;

            $('#selectAll').prop('indeterminate', checkedCheckboxes > 0 && checkedCheckboxes < totalCheckboxes);
            $('#selectAll').prop('checked', checkedCheckboxes === totalCheckboxes && totalCheckboxes > 0);
        }

        // Render pagination
        function renderPagination(pagination) {
            currentPage = pagination.current_page;
            totalPages = pagination.total_pages;

            $('#paginationInfo').html(`
                Showing ${((currentPage - 1) * pagination.limit) + 1} to ${Math.min(currentPage * pagination.limit, pagination.total_records)}
                of ${pagination.total_records} entries
            `);

            const nav = $('#paginationNav');
            nav.empty();

            // Previous button
            nav.append(`
                <li class="page-item ${!pagination.has_prev ? 'disabled' : ''}">
                    <a class="page-link" href="#" data-page="${currentPage - 1}">Previous</a>
                </li>
            `);

            // Page numbers
            const startPage = Math.max(1, currentPage - 2);
            const endPage = Math.min(totalPages, currentPage + 2);

            for (let i = startPage; i <= endPage; i++) {
                nav.append(`
                    <li class="page-item ${i === currentPage ? 'active' : ''}">
                        <a class="page-link" href="#" data-page="${i}">${i}</a>
                    </li>
                `);
            }

            // Next button
            nav.append(`
                <li class="page-item ${!pagination.has_next ? 'disabled' : ''}">
                    <a class="page-link" href="#" data-page="${currentPage + 1}">Next</a>
                </li>
            `);

            // Add click handlers
            $('.page-link').click(function(e) {
                e.preventDefault();
                const page = parseInt($(this).data('page'));
                if (page && page !== currentPage && page >= 1 && page <= totalPages) {
                    currentPage = page;
                    loadExpenses();
                }
            });
        }

        // Update summary info
        function updateSummaryInfo(summary) {
            // You can add summary display here if needed
        }

        // Create batch
        function createBatch() {
            console.log('createBatch() called');
            console.log('selectedExpenses:', selectedExpenses);

            if (selectedExpenses.size === 0) {
                alert('Please select at least one expense');
                return;
            }

            const expenseIds = Array.from(selectedExpenses.keys());
            const totalAmount = Array.from(selectedExpenses.values())
                .reduce((sum, expense) => sum + parseFloat(expense.transfer_amount), 0);

            console.log('expenseIds:', expenseIds);
            console.log('totalAmount:', totalAmount);

            const formData = new FormData();
            formData.append('action', 'create_batch');
            formData.append('expense_ids', JSON.stringify(expenseIds));
            formData.append('total_amount', totalAmount.toFixed(2));
            formData.append('notes', `Batch verification for ${expenseIds.length} expenses`);

            console.log('FormData prepared, sending AJAX request...');
            $('#loadingOverlay').show();

            $.ajax({
                url: '../api/batch_verification.php',
                method: 'POST',
                data: formData,
                processData: false,
                contentType: false,
                success: function(response) {
                    $('#loadingOverlay').hide();
                    console.log('Batch creation response:', response);

                    if (response.success) {
                        console.log('Batch created successfully, batch_id:', response.batch_id);

                        const redirectUrl = `batch_process.php?batch_id=${response.batch_id}&type=verification`;
                        console.log('Redirecting to:', redirectUrl);

                        // Show success message and redirect
                        alert(`Verification batch created successfully!\nBatch ID: ${response.batch_id}\n\nYou will be redirected to the batch processing page.`);

                        // Force redirect using multiple methods
                        console.log('Attempting redirect...');
                        try {
                            window.location.assign(redirectUrl);
                        } catch (e) {
                            console.log('assign failed, trying replace...');
                            window.location.replace(redirectUrl);
                        }
                    } else {
                        alert('Error: ' + response.error);
                    }
                },
                error: function(xhr) {
                    $('#loadingOverlay').hide();
                    console.log('AJAX Error:', xhr);
                    console.log('Response Text:', xhr.responseText);
                    console.log('Status:', xhr.status);
                    alert('Error creating batch: ' + (xhr.responseJSON?.error || xhr.responseText || 'Unknown error'));
                }
            });
        }



        $(document).ready(function() {
            loadExpenses();
        });
    </script>
</body>
</html>
