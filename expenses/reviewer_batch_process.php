<?php
/**
 * Reviewer Batch Process
 * Allows reviewers to process completed verification batches
 */

session_start();
require_once '../config/database.php';
require_once '../includes/functions.php';
require_once '../includes/BatchOperations.php';

// Check if user is logged in
if (!isset($_SESSION['user_id'])) {
    header('Location: ../login.php');
    exit();
}

$user_id = $_SESSION['user_id'];
$user_role = $_SESSION['role'];
$username = $_SESSION['username'];

// Check permissions
if (!in_array($user_role, ['reviewer', 'administrator'])) {
    header('Location: ../dashboard.php');
    exit();
}

// Get batch parameters
$batch_id = $_GET['batch_id'] ?? '';

if (empty($batch_id)) {
    header('Location: multi_review.php?error=missing_batch_id');
    exit();
}

try {
    $database = new Database();
    $db = $database->getConnection();
    $batchOps = new BatchOperations($db);

    $batch_info = $batchOps->getBatchInfo($batch_id);
    if (!$batch_info) {
        throw new Exception('Batch not found');
    }

    // Verify this is a completed verification batch
    if ($batch_info['operation_type'] !== 'verification' || $batch_info['status'] !== 'completed') {
        throw new Exception('Invalid batch for review');
    }

    $batch_items = $batchOps->getBatchItems($batch_id);
    $total_amount = array_sum(array_column($batch_items, 'transfer_amount'));

} catch (Exception $e) {
    header('Location: multi_review.php?error=' . urlencode($e->getMessage()));
    exit();
}
?>

<!DOCTYPE html>
<html lang="th">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Reviewer Batch Process | Expense Management System</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    
    <style>
        .batch-summary {
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
            color: white;
            border-radius: 10px;
            padding: 20px;
            margin-bottom: 20px;
        }
        
        .file-preview {
            max-width: 150px;
            max-height: 150px;
            object-fit: cover;
            border-radius: 8px;
            border: 2px solid #dee2e6;
        }
        
        .batch-files {
            background: #f8f9fa;
            border-radius: 8px;
            padding: 15px;
            margin-bottom: 20px;
        }
        
        .decision-section {
            background: #fff;
            border: 2px solid #dee2e6;
            border-radius: 10px;
            padding: 20px;
        }
        
        .approve-section {
            border-color: #28a745;
        }
        
        .reject-section {
            border-color: #dc3545;
        }
    </style>
</head>
<body class="bg-light">
    <?php include '../includes/navbar.php'; ?>
    
    <div class="container-fluid mt-4">
        <div class="row">
            <div class="col-12">
                <!-- Batch Summary -->
                <div class="batch-summary">
                    <div class="row align-items-center">
                        <div class="col-md-8">
                            <h4 class="mb-1">
                                <i class="fas fa-clipboard-check me-2"></i>
                                Reviewer Batch Process: <?php echo htmlspecialchars($batch_id); ?>
                            </h4>
                            <p class="mb-0">
                                <i class="fas fa-list me-1"></i>
                                <?php echo count($batch_items); ?> รายการ | 
                                <i class="fas fa-money-bill-wave me-1"></i>
                                ยอดรวม: ฿<?php echo number_format($total_amount, 2); ?> |
                                <i class="fas fa-user me-1"></i>
                                Verified by: <?php echo htmlspecialchars($batch_info['full_name'] ?? $batch_info['username'] ?? 'Unknown'); ?>
                            </p>
                        </div>
                        <div class="col-md-4 text-end">
                            <span class="badge bg-light text-dark fs-6">
                                <i class="fas fa-check-circle me-1"></i>Verification Completed
                            </span>
                        </div>
                    </div>
                </div>

                <div class="row">
                    <!-- Left Column: Batch Items -->
                    <div class="col-md-6">
                        <div class="card">
                            <div class="card-header">
                                <h5 class="mb-0">
                                    <i class="fas fa-list-ul me-2"></i>รายการในแบทช์
                                </h5>
                            </div>
                            <div class="card-body">
                                <div class="table-responsive">
                                    <table class="table table-sm">
                                        <thead>
                                            <tr>
                                                <th>Expense No.</th>
                                                <th>Amount</th>
                                                <th>Receipt</th>
                                                <th>Transfer Slip</th>
                                                <th>Details</th>
                                                <th>Status</th>
                                                <th>Action</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <?php foreach ($batch_items as $item): ?>
                                            <tr>
                                                <td>
                                                    <?php echo htmlspecialchars($item['exno']); ?>
                                                </td>
                                                <td>฿<?php echo number_format($item['transfer_amount'], 2); ?></td>
                                                <td>
                                                    <button type="button" class="btn btn-sm btn-outline-info receipt-btn"
                                                            data-expense-id="<?php echo $item['expense_id']; ?>"
                                                            data-exno="<?php echo htmlspecialchars($item['exno']); ?>">
                                                        <i class="fas fa-receipt"></i>
                                                    </button>
                                                </td>
                                                <td>
                                                    <?php if (!empty($item['transfer_slip_image'])): ?>
                                                        <button type="button" class="btn btn-sm btn-outline-success transfer-btn"
                                                                data-expense-id="<?php echo $item['expense_id']; ?>"
                                                                data-exno="<?php echo htmlspecialchars($item['exno']); ?>"
                                                                data-transfer-image="<?php echo htmlspecialchars($item['transfer_slip_image']); ?>">
                                                            <i class="fas fa-file-invoice"></i>
                                                        </button>
                                                    <?php else: ?>
                                                        <span class="text-muted">-</span>
                                                    <?php endif; ?>
                                                </td>
                                                <td>
                                                    <small>
                                                        <?php if (!empty($item['item_name'])): ?>
                                                        <strong>รายการ:</strong> <?php echo htmlspecialchars($item['item_name']); ?><br>
                                                        <?php endif; ?>
                                                        <?php if (!empty($item['bookingno'])): ?>
                                                        <strong>BookingNO:</strong> <?php echo htmlspecialchars($item['bookingno']); ?><br>
                                                        <?php endif; ?>
                                                        <?php if (!empty($item['containerno'])): ?>
                                                        <strong>ContainerNo:</strong> <?php echo htmlspecialchars($item['containerno']); ?><br>
                                                        <?php endif; ?>
                                                        <?php if (!empty($item['customer_name'])): ?>
                                                        <strong>Customer:</strong> <?php echo htmlspecialchars($item['customer_name']); ?><br>
                                                        <?php endif; ?>
                                                        <?php if (!empty($item['driver_name'])): ?>
                                                        <strong>พขร:</strong> <?php echo htmlspecialchars($item['driver_name']); ?><br>
                                                        <?php endif; ?>
                                                        <?php if (!empty($item['vehicle_plate'])): ?>
                                                        <strong>ทะเบียนรถ:</strong> <?php echo htmlspecialchars($item['vehicle_plate']); ?><br>
                                                        <?php endif; ?>
                                                        <?php if (!empty($item['additional_details'])): ?>
                                                        <strong>เพิ่มเติม:</strong> <?php echo htmlspecialchars(substr($item['additional_details'], 0, 50)); ?>
                                                        <?php if (strlen($item['additional_details']) > 50) echo '...'; ?>
                                                        <?php endif; ?>
                                                    </small>
                                                </td>
                                                <td><?php echo getStatusBadge($item['status']); ?></td>
                                                <td>
                                                    <a href="view.php?id=<?php echo $item['expense_id']; ?>"
                                                       target="_blank" class="btn btn-sm btn-outline-primary">
                                                        <i class="fas fa-eye"></i>
                                                    </a>
                                                </td>
                                            </tr>
                                            <?php endforeach; ?>
                                        </tbody>
                                        <tfoot>
                                            <tr class="table-success">
                                                <th>รวม</th>
                                                <th>฿<?php echo number_format($total_amount, 2); ?></th>
                                                <th></th>
                                                <th></th>
                                                <th></th>
                                                <th></th>
                                                <th></th>
                                            </tr>
                                        </tfoot>
                                    </table>
                                </div>
                            </div>
                        </div>
                        
                        <!-- Batch Files -->
                        <?php if (!empty($batch_info['file_data'])): ?>
                        <?php $files = json_decode($batch_info['file_data'], true); ?>
                        <div class="card mt-3">
                            <div class="card-header">
                                <h5 class="mb-0">
                                    <i class="fas fa-paperclip me-2"></i>เอกสารแนบ
                                </h5>
                            </div>
                            <div class="card-body">
                                <div class="row">
                                    <?php foreach ($files as $file): ?>
                                    <div class="col-md-6 mb-3">
                                        <div class="batch-files">
                                            <div class="text-center mb-2">
                                                <?php if (in_array(strtolower(pathinfo($file['filename'], PATHINFO_EXTENSION)), ['jpg', 'jpeg', 'png'])): ?>
                                                    <img src="../uploads/batch_slips/<?php echo $batch_id; ?>/<?php echo $file['filename']; ?>" 
                                                         class="file-preview" alt="Slip">
                                                <?php else: ?>
                                                    <i class="fas fa-file-pdf fa-4x text-danger"></i>
                                                <?php endif; ?>
                                            </div>
                                            <div class="text-center">
                                                <small class="text-muted d-block"><?php echo htmlspecialchars($file['original_name']); ?></small>
                                                <strong>Transfer: <?php echo htmlspecialchars($file['transfer_number']); ?></strong><br>
                                                <span class="text-success">฿<?php echo number_format($file['amount'], 2); ?></span>
                                            </div>
                                        </div>
                                    </div>
                                    <?php endforeach; ?>
                                </div>
                            </div>
                        </div>
                        <?php endif; ?>
                    </div>
                    
                    <!-- Right Column: Review Decision -->
                    <div class="col-md-6">
                        <div class="card">
                            <div class="card-header">
                                <h5 class="mb-0">
                                    <i class="fas fa-times-circle me-2"></i>ปฏิเสธแบทช์
                                </h5>
                            </div>
                            <div class="card-body">
                                <div class="alert alert-info">
                                    <i class="fas fa-info-circle me-2"></i>
                                    <strong>หมายเหตุ:</strong> แบทช์นี้ผ่านการตรวจสอบแล้วใน batch_process
                                    คุณสามารถปฏิเสธแบทช์เพื่อส่งกลับไปแก้ไขได้เท่านั้น
                                </div>

                                <!-- Reject Section -->
                                <div class="decision-section reject-section">
                                    <h6 class="text-danger">
                                        <i class="fas fa-times-circle me-2"></i>ปฏิเสธแบทช์
                                    </h6>
                                    <p class="text-muted mb-3">
                                        ปฏิเสธแบทช์นี้และส่งกลับไปให้ Verification แก้ไข
                                    </p>
                                    
                                    <form id="reject_form">
                                        <input type="hidden" name="batch_id" value="<?php echo htmlspecialchars($batch_id); ?>">
                                        <input type="hidden" name="action" value="reject">
                                        
                                        <div class="mb-3">
                                            <label for="reject_reason" class="form-label">
                                                เหตุผลการปฏิเสธ <span class="text-danger">*</span>
                                            </label>
                                            <textarea class="form-control" id="reject_reason" name="reason" 
                                                      rows="3" placeholder="ระบุเหตุผลการปฏิเสธ" required></textarea>
                                        </div>
                                        
                                        <div class="d-grid">
                                            <button type="submit" class="btn btn-danger btn-lg">
                                                <i class="fas fa-times-circle me-2"></i>ปฏิเสธแบทช์
                                            </button>
                                        </div>
                                    </form>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <!-- jQuery -->
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    
    <script>
        // Handle receipt image modal
        $('.receipt-btn').click(function() {
            const expenseId = $(this).data('expense-id');
            const exno = $(this).data('exno');

            $('#imageModalTitle').text('Receipt Images - ' + exno);
            $('#imageModalBody').html('<div class="text-center"><i class="fas fa-spinner fa-spin"></i> Loading...</div>');
            $('#imageModal').modal('show');

            // Load receipt images from receipt_numbers table
            $.ajax({
                url: '../api/get_receipt_images.php',
                method: 'GET',
                data: { expense_id: expenseId },
                success: function(response) {
                    let modalContent = '';
                    if (response.success && response.data && response.data.length > 0) {
                        response.data.forEach(function(receipt) {
                            if (receipt.receipt_image) {
                                modalContent += '<div class="mb-3">';
                                modalContent += '<div class="d-flex justify-content-between align-items-center mb-2">';
                                modalContent += '<small class="text-muted">Receipt: ' + (receipt.receipt_number || 'N/A') + '</small>';
                                modalContent += '<small class="text-muted">Amount: ฿' + parseFloat(receipt.amount || 0).toLocaleString() + '</small>';
                                modalContent += '</div>';
                                modalContent += '<img src="../api/view_file.php?file=' + encodeURIComponent(receipt.receipt_image) + '&type=receipt" class="img-fluid" alt="Receipt Image">';
                                modalContent += '</div>';
                            }
                        });
                    }

                    if (modalContent === '') {
                        modalContent = '<p class="text-muted">No receipt images available</p>';
                    }

                    $('#imageModalBody').html(modalContent);
                },
                error: function() {
                    $('#imageModalBody').html('<p class="text-danger">Error loading receipt images</p>');
                }
            });
        });

        // Handle transfer slip modal
        $('.transfer-btn').click(function() {
            const expenseId = $(this).data('expense-id');
            const exno = $(this).data('exno');
            const transferImage = $(this).data('transfer-image');

            $('#imageModalTitle').text('Transfer Slip - ' + exno);

            const modalContent = '<img src="../uploads/transfer_slips/' +
                               encodeURIComponent(transferImage) + '" class="img-fluid" alt="Transfer Slip">';

            $('#imageModalBody').html(modalContent);
            $('#imageModal').modal('show');
        });

        // Handle reject form
        $('#reject_form').submit(function(e) {
            e.preventDefault();
            
            const reason = $('#reject_reason').val().trim();
            if (!reason) {
                alert('กรุณาระบุเหตุผลการปฏิเสธ');
                return;
            }
            
            if (!confirm('ยืนยันการปฏิเสธแบทช์นี้?\n\nแบทช์จะถูกส่งกลับไปให้ Verification แก้ไข')) {
                return;
            }
            
            processReviewDecision(this, 'ปฏิเสธ');
        });
        
        function processReviewDecision(form, actionText) {
            const $btn = $(form).find('button[type="submit"]');
            const originalText = $btn.html();
            const formData = new FormData(form);
            
            $btn.prop('disabled', true).html(`<i class="fas fa-spinner fa-spin me-2"></i>กำลัง${actionText}...`);
            
            fetch('../api/process_reviewer_batch.php', {
                method: 'POST',
                body: formData
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    alert(`${actionText}แบทช์เรียบร้อยแล้ว!`);
                    window.location.href = 'multi_review.php';
                } else {
                    alert('เกิดข้อผิดพลาด: ' + data.error);
                    $btn.prop('disabled', false).html(originalText);
                }
            })
            .catch(error => {
                alert('เกิดข้อผิดพลาด: ' + error.message);
                $btn.prop('disabled', false).html(originalText);
            });
        }
    </script>

    <!-- Image Modal -->
    <div class="modal fade" id="imageModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="imageModalTitle">Image</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body" id="imageModalBody">
                    <!-- Content will be loaded dynamically -->
                </div>
            </div>
        </div>
    </div>

</body>
</html>
