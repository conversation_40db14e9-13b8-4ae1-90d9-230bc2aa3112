<?php
session_start();
require_once '../config/database.php';
require_once '../includes/functions.php';

// Check if user is logged in
if (!isset($_SESSION['user_id'])) {
    header('Location: ../login.php');
    exit();
}

$expense_id = (int)($_GET['id'] ?? 0);
if (empty($expense_id)) {
    header('Location: ../index.php');
    exit();
}

$database = new Database();
$db = $database->getConnection();

$user_id = $_SESSION['user_id'];
$user_role = $_SESSION['role'];

// Get navigation parameters from URL
$search = $_GET['search'] ?? '';
$status_filter = $_GET['status'] ?? '';
$current_page = (int)($_GET['page'] ?? 1);

// Get expense with related data including verification and review info
$stmt = $db->prepare("
    SELECT
        e.*,
        i.name as item_name,
        c.name as customer_name,
        d.name as driver_name,
        u.full_name as created_by_name,
        v.full_name as verification_user_name,
        r.full_name as reviewer_user_name
    FROM expenses e
    LEFT JOIN items i ON e.item_id = i.id
    LEFT JOIN customers c ON e.customer_id = c.id
    LEFT JOIN drivers d ON e.driver_id = d.id
    LEFT JOIN users u ON e.created_by = u.id
    LEFT JOIN users v ON e.verification_by = v.id
    LEFT JOIN users r ON e.reviewer_by = r.id
    WHERE e.id = ?
");
$stmt->execute([$expense_id]);
$expense = $stmt->fetch();

if (!$expense) {
    header('Location: list.php');
    exit();
}

// Build navigation: Get filtered expense IDs for Previous/Next functionality
$nav_where_conditions = [];
$nav_params = [];

// Role-based access (same as list.php)
if ($user_role === 'data_entry') {
    $nav_where_conditions[] = 'e.created_by = ?';
    $nav_params[] = $user_id;
}

// Search filter (same as list.php)
if (!empty($search)) {
    $nav_where_conditions[] = '(e.exno LIKE ? OR e.bookingno LIKE ? OR i.name LIKE ? OR c.name LIKE ? OR d.name LIKE ?)';
    $search_param = '%' . $search . '%';
    $nav_params = array_merge($nav_params, [$search_param, $search_param, $search_param, $search_param, $search_param]);
}

// Status filter (same as list.php)
if (!empty($status_filter)) {
    $nav_where_conditions[] = 'e.status = ?';
    $nav_params[] = $status_filter;
}

$nav_where_clause = !empty($nav_where_conditions) ? 'WHERE ' . implode(' AND ', $nav_where_conditions) : '';

// Get all filtered expense IDs in the same order as list.php
$nav_stmt = $db->prepare("
    SELECT e.id, e.exno, d.name as driver_name, c.name as customer_name
    FROM expenses e
    LEFT JOIN items i ON e.item_id = i.id
    LEFT JOIN customers c ON e.customer_id = c.id
    LEFT JOIN drivers d ON e.driver_id = d.id
    LEFT JOIN users u ON e.created_by = u.id
    {$nav_where_clause}
    ORDER BY e.id DESC
");
$nav_stmt->execute($nav_params);
$filtered_expenses = $nav_stmt->fetchAll();

// Find current position and get previous/next
$current_position = -1;
$prev_expense = null;
$next_expense = null;
$total_filtered = count($filtered_expenses);

for ($i = 0; $i < $total_filtered; $i++) {
    if ($filtered_expenses[$i]['id'] == $expense_id) {
        $current_position = $i;
        $prev_expense = ($i > 0) ? $filtered_expenses[$i - 1] : null;
        $next_expense = ($i < $total_filtered - 1) ? $filtered_expenses[$i + 1] : null;
        break;
    }
}

// Build navigation URLs
function buildNavUrl($id, $search, $status_filter, $current_page) {
    $params = array_filter([
        'id' => $id,
        'search' => $search,
        'status' => $status_filter,
        'page' => $current_page
    ]);
    return 'view.php?' . http_build_query($params);
}

function buildListUrl($search, $status_filter, $current_page) {
    $params = array_filter([
        'search' => $search,
        'status' => $status_filter,
        'page' => $current_page
    ]);
    return 'list.php' . (!empty($params) ? '?' . http_build_query($params) : '');
}

// Navigation component function
function renderNavigationBar($prev_expense, $next_expense, $current_position, $total_filtered, $search, $status_filter, $current_page, $expense) {
    $list_url = buildListUrl($search, $status_filter, $current_page);
    $search_info = '';

    if (!empty($search)) {
        $search_info = '<span class="text-muted">Search: <strong>' . htmlspecialchars($search) . '</strong></span>';
    }

    if (!empty($status_filter)) {
        $status_info = '<span class="text-muted">Status: <strong>' . htmlspecialchars($status_filter) . '</strong></span>';
        $search_info .= !empty($search_info) ? ' | ' . $status_info : $status_info;
    }

    echo '<div class="navigation-bar bg-light p-3 rounded mb-3">';
    echo '<div class="row align-items-center">';

    // Left side: Back to list button
    echo '<div class="col-md-4">';
    echo '<a href="' . htmlspecialchars($list_url) . '" class="btn btn-secondary">';
    echo '<i class="fas fa-arrow-left me-1"></i>Back to List';
    echo '</a>';
    echo '</div>';

    // Center: Position info and search criteria
    echo '<div class="col-md-4 text-center">';
    if ($current_position >= 0) {
        echo '<div class="fw-bold">' . ($current_position + 1) . ' of ' . $total_filtered . ' results</div>';
    }
    if (!empty($search_info)) {
        echo '<div class="small">' . $search_info . '</div>';
    }
    echo '</div>';

    // Right side: Previous/Next buttons
    echo '<div class="col-md-4 text-end">';
    if ($prev_expense) {
        $prev_url = buildNavUrl($prev_expense['id'], $search, $status_filter, $current_page);
        echo '<a href="' . htmlspecialchars($prev_url) . '" class="btn btn-outline-primary me-2" id="prev-btn" title="Previous: ' . htmlspecialchars($prev_expense['exno']) . '">';
        echo '<i class="fas fa-chevron-left me-1"></i>Previous';
        echo '</a>';
    }

    if ($next_expense) {
        $next_url = buildNavUrl($next_expense['id'], $search, $status_filter, $current_page);
        echo '<a href="' . htmlspecialchars($next_url) . '" class="btn btn-outline-primary" id="next-btn" title="Next: ' . htmlspecialchars($next_expense['exno']) . '">';
        echo 'Next<i class="fas fa-chevron-right ms-1"></i>';
        echo '</a>';
    }
    echo '</div>';

    echo '</div>';

    // Additional info row
    if ($prev_expense || $next_expense) {
        echo '<div class="row mt-2">';
        echo '<div class="col-12">';
        echo '<div class="d-flex justify-content-between small text-muted">';

        if ($prev_expense) {
            echo '<div>';
            echo '<i class="fas fa-chevron-left me-1"></i>';
            echo 'Previous: <strong>' . htmlspecialchars($prev_expense['exno']) . '</strong>';
            if (!empty($prev_expense['driver_name'])) {
                echo ' - ' . htmlspecialchars($prev_expense['driver_name']);
            }
            if (!empty($prev_expense['customer_name'])) {
                echo ' (' . htmlspecialchars($prev_expense['customer_name']) . ')';
            }
            echo '</div>';
        } else {
            echo '<div></div>';
        }

        if ($next_expense) {
            echo '<div class="text-end">';
            echo 'Next: <strong>' . htmlspecialchars($next_expense['exno']) . '</strong>';
            if (!empty($next_expense['driver_name'])) {
                echo ' - ' . htmlspecialchars($next_expense['driver_name']);
            }
            if (!empty($next_expense['customer_name'])) {
                echo ' (' . htmlspecialchars($next_expense['customer_name']) . ')';
            }
            echo '<i class="fas fa-chevron-right ms-1"></i>';
            echo '</div>';
        } else {
            echo '<div></div>';
        }

        echo '</div>';
        echo '</div>';
        echo '</div>';
    }

    echo '</div>';
}

// Get batch documents if this expense was processed via batch
$batch_verification_slip = null;
$batch_review_slip = null;
$batch_verification_expenses = [];
$batch_review_expenses = [];

if (!empty($expense['batch_verification_id'])) {
    $stmt = $db->prepare("
        SELECT file_path, original_filename
        FROM batch_documents
        WHERE batch_id = ? AND document_type = 'verification_slip'
        LIMIT 1
    ");
    $stmt->execute([$expense['batch_verification_id']]);
    $batch_verification_slip = $stmt->fetch();

    // Get other expenses in the same verification batch
    $stmt = $db->prepare("
        SELECT e.id, e.exno, c.name as customer_name, i.name as item_name
        FROM expenses e
        LEFT JOIN customers c ON e.customer_id = c.id
        LEFT JOIN items i ON e.item_id = i.id
        WHERE e.batch_verification_id = ? AND e.id != ?
        ORDER BY e.exno
    ");
    $stmt->execute([$expense['batch_verification_id'], $expense['id']]);
    $batch_verification_expenses = $stmt->fetchAll();
}

if (!empty($expense['batch_review_id'])) {
    $stmt = $db->prepare("
        SELECT file_path, original_filename
        FROM batch_documents
        WHERE batch_id = ? AND document_type = 'review_slip'
        LIMIT 1
    ");
    $stmt->execute([$expense['batch_review_id']]);
    $batch_review_slip = $stmt->fetch();

    // Get other expenses in the same review batch
    $stmt = $db->prepare("
        SELECT e.id, e.exno, c.name as customer_name, i.name as item_name
        FROM expenses e
        LEFT JOIN customers c ON e.customer_id = c.id
        LEFT JOIN items i ON e.item_id = i.id
        WHERE e.batch_review_id = ? AND e.id != ?
        ORDER BY e.exno
    ");
    $stmt->execute([$expense['batch_review_id'], $expense['id']]);
    $batch_review_expenses = $stmt->fetchAll();
}

if (!$expense) {
    header('Location: ../index.php');
    exit();
}

// Check access permissions
$has_access = false;
if ($user_role === 'administrator') {
    $has_access = true;
} elseif ($user_role === 'data_entry') {
    $has_access = ($expense['created_by'] == $user_id);
} else {
    // Verification and reviewer roles can access all expenses
    $has_access = true;
}

if (!$has_access) {
    header('Location: ../index.php');
    exit();
}

// Parse receipt images
$receipt_images = [];
if (!empty($expense['receipt_images'])) {
    $receipt_images = json_decode($expense['receipt_images'], true) ?: [];
}

// Get receipt numbers with deductions (fallback to direct table query if view doesn't exist)
try {
    $stmt = $db->prepare("SELECT * FROM receipt_summary WHERE expense_id = ? ORDER BY id");
    $stmt->execute([$expense_id]);
    $receipt_numbers = $stmt->fetchAll();
} catch (PDOException $e) {
    // Fallback to direct table query if receipt_summary view doesn't exist
    $stmt = $db->prepare("
        SELECT
            id,
            expense_id,
            receipt_number,
            amount as gross_amount,
            amount as original_amount,
            amount as net_amount_calculated,
            0 as has_deductions,
            description,
            receipt_image,
            0 as total_deductions,
            0 as deduction_count,
            amount as calculated_net_amount
        FROM receipt_numbers
        WHERE expense_id = ?
        ORDER BY id
    ");
    $stmt->execute([$expense_id]);
    $receipt_numbers = $stmt->fetchAll();
}

// Get individual deductions for each receipt
$receipt_deductions = [];
if (!empty($receipt_numbers)) {
    $receipt_ids = array_column($receipt_numbers, 'id');
    $placeholders = str_repeat('?,', count($receipt_ids) - 1) . '?';

    try {
        $stmt = $db->prepare("
            SELECT rd.*, rn.receipt_number
            FROM receipt_deductions rd
            JOIN receipt_numbers rn ON rd.receipt_number_id = rn.id
            WHERE rd.receipt_number_id IN ($placeholders)
            ORDER BY rd.receipt_index, rd.created_at
        ");
        $stmt->execute($receipt_ids);
        $deductions = $stmt->fetchAll();

        // Group deductions by receipt_id
        foreach ($deductions as $deduction) {
            $receipt_deductions[$deduction['receipt_number_id']][] = $deduction;
        }
    } catch (PDOException $e) {
        // If receipt_deductions table doesn't exist, just continue without deductions
        $receipt_deductions = [];
    }
}

// Get activity logs for this expense (filtered by role)
if ($user_role === 'administrator') {
    // Admin can see all activities
    $stmt = $db->prepare("
        SELECT
            al.*,
            u.full_name as user_name
        FROM activity_logs al
        LEFT JOIN users u ON al.user_id = u.id
        WHERE al.table_name = 'expenses' AND al.record_id = ?
        ORDER BY al.created_at DESC
    ");
    $stmt->execute([$expense_id]);
} else {
    // Other users can only see their own activities
    $stmt = $db->prepare("
        SELECT
            al.*,
            u.full_name as user_name
        FROM activity_logs al
        LEFT JOIN users u ON al.user_id = u.id
        WHERE al.table_name = 'expenses' AND al.record_id = ? AND al.user_id = ?
        ORDER BY al.created_at DESC
    ");
    $stmt->execute([$expense_id, $user_id]);
}
$activity_logs = $stmt->fetchAll();
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>View Expense <?php echo htmlspecialchars($expense['exno']); ?> - Expenses Management System</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="../assets/css/style.css" rel="stylesheet">
    <meta name="csrf-token" content="<?php echo generateCSRFToken(); ?>">
    <style>
        .navigation-bar {
            border: 1px solid #dee2e6;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }

        .navigation-bar .btn {
            min-width: 100px;
        }

        .navigation-bar .position-info {
            font-weight: 600;
            color: #495057;
        }

        .navigation-bar .small {
            font-size: 0.875rem;
        }

        /* Enhanced Documents & Deductions Styling */
        .bg-gradient-primary {
            background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
        }

        .bg-gradient-light {
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
        }

        .deduction-item {
            transition: all 0.3s ease;
            border-left: 4px solid #dc3545;
            max-width: 100%;
            word-wrap: break-word;
        }

        .deduction-item:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0,0,0,0.15);
        }

        /* Collapsible deductions styling */
        .collapse.show {
            animation: slideDown 0.3s ease-out;
        }

        .collapse:not(.show) {
            animation: slideUp 0.3s ease-out;
        }

        @keyframes slideDown {
            from {
                opacity: 0;
                transform: translateY(-10px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        @keyframes slideUp {
            from {
                opacity: 1;
                transform: translateY(0);
            }
            to {
                opacity: 0;
                transform: translateY(-10px);
            }
        }

        /* Toggle button styling */
        .btn-outline-secondary:hover {
            background-color: #6c757d;
            border-color: #6c757d;
            color: white;
        }

        /* Deduction card responsive fix */
        .card.border-danger {
            max-width: 100%;
            overflow: hidden;
        }

        .deductions-list {
            max-height: none;
            overflow: visible;
        }

        .deduction-icon {
            width: 30px;
            height: 30px;
            display: flex;
            align-items: center;
            justify-content: center;
            background: rgba(220, 53, 69, 0.1);
            border-radius: 50%;
        }

        .receipt-amounts {
            background: rgba(0, 123, 255, 0.05);
            border-radius: 8px;
            padding: 15px;
            border-left: 4px solid #007bff;
        }

        .card {
            transition: all 0.3s ease;
        }

        .card:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(0,0,0,0.15);
        }

        .image-thumbnail {
            transition: all 0.3s ease;
        }

        .image-thumbnail:hover {
            transform: scale(1.05);
        }

        .badge {
            font-size: 0.75rem;
        }

        .alert-info {
            background: linear-gradient(135deg, #d1ecf1 0%, #bee5eb 100%);
            border: none;
        }





        /* Status Management specific styling */
        .status-management-section {
            position: sticky;
            top: 20px;
        }

        .status-management-section .card {
            border: 1px solid #dee2e6;
            box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
        }

        .status-management-section .card-header {
            background-color: #f8f9fa;
            border-bottom: 1px solid #dee2e6;
        }



        /* Responsive adjustments */
        @media (max-width: 768px) {
            .navigation-bar .col-md-4 {
                text-align: center !important;
                margin-bottom: 10px;
            }

            .navigation-bar .btn {
                min-width: auto;
                margin: 2px;
            }
        }
    </style>
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container-fluid">
            <a class="navbar-brand" href="../index.php">
                <i class="fas fa-receipt me-2"></i>Expenses System
            </a>
            
            <div class="navbar-nav ms-auto">
                <a class="nav-link" href="../dashboard.php">
                    <i class="fas fa-arrow-left me-1"></i>Back to Dashboard
                </a>
            </div>
        </div>
    </nav>

    <div class="container-fluid mt-4 main-content-container">
        <div class="row">
            <div class="col-12">
                <!-- Top Navigation Bar -->
                <?php renderNavigationBar($prev_expense, $next_expense, $current_position, $total_filtered, $search, $status_filter, $current_page, $expense); ?>

                <div class="d-flex justify-content-between align-items-center mb-4">
                    <div>
                        <h1><i class="fas fa-eye me-2"></i>Expense Details</h1>
                        <p class="text-muted mb-0">Expense No: <strong><?php echo htmlspecialchars($expense['exno']); ?></strong></p>
                    </div>
                    <div class="d-flex gap-2">
                        <?php echo getStatusBadge($expense['status']); ?>
                        <?php if (canEditExpense($expense, $user_id, $user_role)): ?>
                            <a href="edit.php?id=<?php echo $expense['id']; ?>" class="btn btn-outline-primary">
                                <i class="fas fa-edit me-1"></i>Edit
                            </a>
                        <?php endif; ?>
                        <button class="btn btn-outline-secondary" onclick="window.print()">
                            <i class="fas fa-print me-1"></i>Print
                        </button>
                    </div>
                </div>

                <!-- Main Content Row -->



                <style>
                /* Only apply to main content row - very specific selector */
                .main-content-container > .row {
                    display: flex !important;
                    flex-wrap: nowrap !important;
                    align-items: flex-start !important;
                }
                .main-content-container > .row > .col-lg-8 {
                    flex: 0 0 66.666667% !important;
                    max-width: 66.666667% !important;
                    margin-right: 20px !important;
                }
                .main-content-container > .row > .col-lg-4 {
                    flex: 0 0 calc(33.333333% - 20px) !important;
                    max-width: calc(33.333333% - 20px) !important;
                }
                /* Ensure bottom navigation is NOT affected */
                .container-fluid.mt-4.mb-4 {
                    display: block !important;
                    width: 100% !important;
                }
                .container-fluid.mt-4.mb-4 .row {
                    display: flex !important;
                    flex-wrap: wrap !important;
                }
                .container-fluid.mt-4.mb-4 .col-12 {
                    flex: 0 0 100% !important;
                    max-width: 100% !important;
                }
                @media (max-width: 991px) {
                    .main-content-container > .row {
                        flex-direction: column !important;
                    }
                    .main-content-container > .row > .col-lg-8,
                    .main-content-container > .row > .col-lg-4 {
                        flex: 0 0 100% !important;
                        max-width: 100% !important;
                        margin-right: 0 !important;
                    }
                }
                </style>
                <script>
                document.addEventListener('DOMContentLoaded', function() {
                    // Force layout after page load
                    setTimeout(function() {
                        const mainContainer = document.querySelector('.main-content-container');
                        const row = mainContainer ? mainContainer.querySelector('.row') : null;
                        const col8 = row ? row.querySelector('.col-lg-8') : null;
                        const col4 = row ? row.querySelector('.col-lg-4') : null;

                        if (row && col8 && col4) {
                            // Force flexbox layout
                            row.style.display = 'flex';
                            row.style.flexWrap = 'nowrap';
                            row.style.alignItems = 'flex-start';

                            col8.style.flex = '0 0 66.666667%';
                            col8.style.maxWidth = '66.666667%';
                            col8.style.marginRight = '20px';

                            col4.style.flex = '0 0 calc(33.333333% - 20px)';
                            col4.style.maxWidth = 'calc(33.333333% - 20px)';

                            console.log('Layout forced successfully');
                        } else {
                            console.log('Layout elements not found');
                        }
                    }, 100);
                });
                </script>
                <div class="row">
                    <div class="col-lg-8">
                        <!-- Basic Information -->
                        <div class="card mb-4">
                            <div class="card-header">
                                <h5 class="card-title mb-0">Basic Information</h5>
                            </div>
                            <div class="card-body">
                                <div style="display: flex; flex-wrap: wrap; gap: 20px;">
                                    <div style="flex: 1; min-width: 300px;">
                                        <table class="table table-borderless">
                                            <tr>
                                                <td><strong>Expense No:</strong></td>
                                                <td><?php echo htmlspecialchars($expense['exno']); ?></td>
                                            </tr>
                                            <tr>
                                                <td><strong>Booking No:</strong></td>
                                                <td><?php echo htmlspecialchars($expense['bookingno'] ?: '-'); ?></td>
                                            </tr>
                                            <tr>
                                                <td><strong>Job Open Date:</strong></td>
                                                <td><?php echo formatDate($expense['job_open_date']); ?></td>
                                            </tr>
                                            <tr>
                                                <td><strong>Item:</strong></td>
                                                <td><?php echo htmlspecialchars($expense['item_name'] ?: '-'); ?></td>
                                            </tr>
                                            <tr>
                                                <td><strong>Customer:</strong></td>
                                                <td><?php echo htmlspecialchars($expense['customer_name'] ?: '-'); ?></td>
                                            </tr>
                                        </table>
                                    </div>
                                    <div class="col-md-6">
                                        <table class="table table-borderless">
                                            <tr>
                                                <td><strong>Container No:</strong></td>
                                                <td><?php echo htmlspecialchars($expense['containerno'] ?: '-'); ?></td>
                                            </tr>
                                            <tr>
                                                <td><strong>Driver:</strong></td>
                                                <td><?php echo htmlspecialchars($expense['driver_name'] ?: '-'); ?></td>
                                            </tr>
                                            <tr>
                                                <td><strong>Vehicle Plate:</strong></td>
                                                <td><?php echo htmlspecialchars($expense['vehicle_plate'] ?: '-'); ?></td>
                                            </tr>
                                            <tr>
                                                <td><strong>Payment Account:</strong></td>
                                                <td><?php echo htmlspecialchars($expense['payment_account_no'] ?: '-'); ?></td>
                                            </tr>
                                            <tr>
                                                <td><strong>Additional Details:</strong></td>
                                                <td><?php echo htmlspecialchars($expense['additional_details'] ?: '-'); ?></td>
                                            </tr>
                                        </table>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Personnel Information -->
                        <div class="card mb-4">
                            <div class="card-header">
                                <h5 class="card-title mb-0">Personnel Information</h5>
                            </div>
                            <div class="card-body">
                                <div style="display: flex; flex-wrap: wrap; gap: 20px;">
                                    <div style="flex: 1; min-width: 200px;">
                                        <strong>Requester:</strong><br>
                                        <?php echo htmlspecialchars($expense['requester'] ?: '-'); ?>
                                    </div>
                                    <div class="col-md-4">
                                        <strong>Receiver:</strong><br>
                                        <?php echo htmlspecialchars($expense['receiver'] ?: '-'); ?>
                                    </div>
                                    <div class="col-md-4">
                                        <strong>Payer:</strong><br>
                                        <?php echo htmlspecialchars($expense['payer'] ?: '-'); ?>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Payment Information -->
                        <div class="card mb-4">
                            <div class="card-header">
                                <h5 class="card-title mb-0">Payment Information</h5>
                            </div>
                            <div class="card-body">
                                <div style="display: flex; flex-wrap: wrap; gap: 15px;">
                                    <div style="flex: 1; min-width: 150px;">
                                        <strong>Withdrawal Date:</strong><br>
                                        <?php echo formatDate($expense['withdrawal_date']); ?>
                                    </div>
                                    <div class="col-md-2">
                                        <strong>Transfer No:</strong><br>
                                        <?php echo htmlspecialchars($expense['transfer_no'] ?: '-'); ?>
                                    </div>
                                    <div class="col-md-2">
                                        <strong>Verification Transfer:</strong><br>
                                        <span class="text-info"><?php echo htmlspecialchars($expense['verification_transfer_no'] ?: '-'); ?></span>
                                    </div>
                                    <div class="col-md-2">
                                        <strong>Review Transfer:</strong><br>
                                        <span class="text-warning"><?php echo htmlspecialchars($expense['reviewer_transfer_no'] ?: '-'); ?></span>
                                    </div>
                                    <div class="col-md-3">
                                        <strong>Transfer Amount:</strong><br>
                                        <span class="text-primary h6"><?php echo number_format($expense['transfer_amount'] ?? 0, 2); ?> บาท</span>
                                    </div>
                                    <div class="col-md-3">
                                        <strong>Receipt Total:</strong><br>
                                        <span class="text-success h6"><?php echo number_format($expense['total_amount'] ?? 0, 2); ?> บาท</span>
                                    </div>
                                </div>

                                <!-- Amount Comparison -->
                                <?php
                                $transfer_amount = $expense['transfer_amount'] ?? 0;
                                $receipt_total = $expense['total_amount'] ?? 0;
                                if ($transfer_amount > 0 || $receipt_total > 0):
                                ?>
                                <div class="row mt-3">
                                    <div class="col-12">
                                        <div class="alert <?php
                                            if ($transfer_amount == $receipt_total) {
                                                echo 'alert-success';
                                            } elseif (abs($transfer_amount - $receipt_total) < 0.01) {
                                                echo 'alert-success';
                                            } else {
                                                echo 'alert-warning';
                                            }
                                        ?> mb-0">
                                            <div class="d-flex align-items-center">
                                                <?php if (abs($transfer_amount - $receipt_total) < 0.01): ?>
                                                    <i class="fas fa-check-circle me-2"></i>
                                                    <strong>Amount Verification: MATCH</strong>
                                                <?php else: ?>
                                                    <i class="fas fa-exclamation-triangle me-2"></i>
                                                    <strong>Amount Verification: MISMATCH</strong>
                                                    <span class="ms-2">
                                                        Difference: <?php echo number_format(abs($transfer_amount - $receipt_total), 2); ?> บาท
                                                        <?php if ($transfer_amount > $receipt_total): ?>
                                                            (Transfer higher)
                                                        <?php else: ?>
                                                            (Receipts higher)
                                                        <?php endif; ?>
                                                    </span>
                                                <?php endif; ?>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <?php endif; ?>
                            </div>
                        </div>

                        <!-- Documents -->
                        <div class="card mb-4">
                            <div class="card-header bg-gradient-primary text-white">
                                <h5 class="card-title mb-0">
                                    <i class="fas fa-folder-open me-2"></i>Documents & Receipt Details
                                </h5>
                            </div>
                            <div class="card-body">
                                <!-- Transfer Slip Section -->
                                        <div class="card border-info">
                                            <div class="card-header bg-info text-white">
                                                <h6 class="mb-0">
                                                    <i class="fas fa-money-check-alt me-2"></i>Transfer Slip
                                                </h6>
                                            </div>
                                            <div class="card-body">
                                                <?php if (!empty($expense['transfer_slip_image'])): ?>
                                                    <div class="images-container">
                                                        <a href="../api/view_file.php?file=<?php echo urlencode($expense['transfer_slip_image']); ?>&type=transfer_slip"
                                                           class="image-thumbnail" data-src="../api/view_file.php?file=<?php echo urlencode($expense['transfer_slip_image']); ?>&type=transfer_slip">
                                                            <img src="../api/view_file.php?file=<?php echo urlencode($expense['transfer_slip_image']); ?>&type=transfer_slip"
                                                                 class="img-thumbnail shadow-sm" style="max-width: 250px; max-height: 180px; border-radius: 8px;"
                                                                 alt="Transfer Slip">
                                                        </a>
                                                        <div class="mt-2">
                                                            <small class="text-muted">
                                                                <i class="fas fa-info-circle me-1"></i>
                                                                Click to view full size
                                                            </small>
                                                        </div>
                                                    </div>
                                                <?php else: ?>
                                                    <div class="text-center py-3">
                                                        <i class="fas fa-file-upload text-muted" style="font-size: 2rem;"></i>
                                                        <p class="text-muted mt-2 mb-0">No transfer slip uploaded</p>
                                                    </div>
                                                <?php endif; ?>
                                            </div>
                                        </div>

                                <!-- Receipt Details Section -->
                                        <div class="card border-success">
                                            <div class="card-header bg-success text-white">
                                                <h6 class="mb-0">
                                                    <i class="fas fa-receipt me-2"></i>Receipt Details & Deductions
                                                </h6>
                                            </div>
                                            <div class="card-body">
                                                <?php if (!empty($receipt_numbers)): ?>
                                                    <div class="receipt-details" style="display: flex; flex-direction: column;">
                                                        <?php
                                                        $total_gross = 0;
                                                        $total_deductions = 0;
                                                        $total_net = 0;
                                                        $total_deduction_count = 0;
                                                        foreach ($receipt_numbers as $receipt):
                                                            $total_gross += $receipt['gross_amount'];
                                                            $receipt_id = $receipt['id'];

                                                            // Calculate actual deductions for this receipt
                                                            $receipt_deduction_total = 0;
                                                            if (isset($receipt_deductions[$receipt_id])) {
                                                                $receipt_deduction_total = array_sum(array_column($receipt_deductions[$receipt_id], 'amount'));
                                                                $total_deduction_count += count($receipt_deductions[$receipt_id]);
                                                            }

                                                            $total_deductions += $receipt_deduction_total;
                                                            $total_net += ($receipt['gross_amount'] - $receipt_deduction_total);
                                                        ?>
                                                            <?php
                                                            // Check if this receipt has deductions for styling
                                                            $receipt_has_deductions = isset($receipt_deductions[$receipt_id]) && count($receipt_deductions[$receipt_id]) > 0;
                                                            ?>
                                                            <div class="card mb-4 shadow-sm border-<?php echo $receipt_has_deductions ? 'warning' : 'primary'; ?>">
                                                                <div class="card-header bg-<?php echo $receipt_has_deductions ? 'warning' : 'primary'; ?> text-<?php echo $receipt_has_deductions ? 'dark' : 'white'; ?>">
                                                                    <div class="row align-items-center">
                                                                        <div class="col-md-8">
                                                                            <h6 class="mb-0">
                                                                                <i class="fas fa-receipt me-2"></i>
                                                                                Receipt #: <?php echo htmlspecialchars($receipt['receipt_number']); ?>
                                                                                <?php if ($receipt_has_deductions): ?>
                                                                                    <span class="badge bg-dark ms-2">
                                                                                        <i class="fas fa-minus-circle me-1"></i>มีรายการหัก
                                                                                    </span>
                                                                                <?php else: ?>
                                                                                    <span class="badge bg-light text-dark ms-2">
                                                                                        <i class="fas fa-check-circle me-1"></i>ไม่มีรายการหัก
                                                                                    </span>
                                                                                <?php endif; ?>
                                                                            </h6>
                                                                </div>
                                                                <div class="col-md-4 text-end">
                                                                    <?php if (!empty($receipt['receipt_image'])): ?>
                                                                        <?php
                                                                        $receipt_image_path = '../uploads/receipts/' . $receipt['receipt_image'];
                                                                        $image_exists = file_exists($receipt_image_path);
                                                                        ?>
                                                                        <a href="../api/view_file.php?file=<?php echo urlencode($receipt['receipt_image']); ?>&type=receipt"
                                                                           class="btn btn-sm <?php echo $image_exists ? 'btn-outline-primary' : 'btn-outline-danger'; ?> image-thumbnail"
                                                                           data-src="../api/view_file.php?file=<?php echo urlencode($receipt['receipt_image']); ?>&type=receipt"
                                                                           <?php if (!$image_exists): ?>title="ไฟล์รูปภาพไม่พบ"<?php endif; ?>>
                                                                            <i class="fas fa-image me-1"></i>
                                                                            <?php echo $image_exists ? 'ดูรูปใบเสร็จ' : 'รูปไม่พบ'; ?>
                                                                        </a>
                                                                        <?php if (!$image_exists): ?>
                                                                            <small class="text-danger d-block mt-1">
                                                                                <i class="fas fa-exclamation-triangle me-1"></i>
                                                                                ไฟล์: <?php echo htmlspecialchars($receipt['receipt_image']); ?>
                                                                            </small>
                                                                        <?php endif; ?>
                                                                    <?php else: ?>
                                                                        <span class="text-muted">
                                                                            <i class="fas fa-image me-1"></i>ไม่มีรูปใบเสร็จ
                                                                        </span>
                                                                    <?php endif; ?>
                                                                </div>
                                                            </div>
                                                        </div>
                                                        <div class="card-body">
                                                            <div class="row align-items-start">
                                                                <div class="col-md-6">
                                                                    <div class="receipt-amounts">
                                                                        <div class="d-flex justify-content-between mb-1">
                                                                            <span>ยอดก่อนหัก:</span>
                                                                            <span class="text-primary fw-bold"><?php echo number_format($receipt['gross_amount'], 2); ?> บาท</span>
                                                                        </div>
                                                                        <?php
                                                                        // Calculate actual deductions for display
                                                                        $receipt_deduction_total = 0;
                                                                        $has_deductions = false;
                                                                        if (isset($receipt_deductions[$receipt_id])) {
                                                                            $receipt_deduction_total = array_sum(array_column($receipt_deductions[$receipt_id], 'amount'));
                                                                            $has_deductions = $receipt_deduction_total > 0;
                                                                        }
                                                                        $receipt_net_amount = $receipt['gross_amount'] - $receipt_deduction_total;
                                                                        ?>
                                                                        <?php if ($has_deductions): ?>
                                                                            <div class="d-flex justify-content-between mb-1">
                                                                                <span class="text-danger">รายการหัก:</span>
                                                                                <span class="text-danger fw-bold">-<?php echo number_format($receipt_deduction_total, 2); ?> บาท</span>
                                                                            </div>
                                                                            <hr class="my-2">
                                                                        <?php endif; ?>
                                                                        <div class="d-flex justify-content-between">
                                                                            <span class="fw-bold">ยอดสุทธิ:</span>
                                                                            <span class="text-success fw-bold"><?php echo number_format($receipt_net_amount, 2); ?> บาท</span>
                                                                        </div>
                                                                    </div>
                                                                    <?php if (!empty($receipt['description'])): ?>
                                                                        <div class="mt-2">
                                                                            <small class="text-muted">
                                                                                <i class="fas fa-comment me-1"></i>
                                                                                <?php echo htmlspecialchars($receipt['description']); ?>
                                                                            </small>
                                                                        </div>
                                                                    <?php endif; ?>
                                                                </div>

                                                                <div class="col-md-6">
                                                                    <?php if ($receipt_has_deductions && isset($receipt_deductions[$receipt_id])): ?>
                                                                        <div class="mt-3">
                                                                            <div class="card border-danger">
                                                                                <div class="card-header bg-danger text-white py-2">
                                                                                    <h6 class="mb-0">
                                                                                        <i class="fas fa-minus-circle me-2"></i>รายการหัก
                                                                                        <span class="badge bg-light text-dark ms-2">
                                                                                            <?php echo count($receipt_deductions[$receipt_id]); ?> รายการ
                                                                                        </span>
                                                                                    </h6>
                                                                                </div>
                                                                                <div class="card-body p-2">
                                                                                    <?php
                                                                                    $deduction_count = count($receipt_deductions[$receipt_id]);
                                                                                    $show_first = min(2, $deduction_count); // Show first 2 items
                                                                                    $remaining_count = $deduction_count - $show_first;

                                                                                    // Define type mappings
                                                                                    $type_names = [
                                                                                        'tax_vat' => 'ภาษีมูลค่าเพิ่ม (VAT)',
                                                                                        'tax_withholding' => 'ภาษีหัก ณ ที่จ่าย',
                                                                                        'service_fee' => 'ค่าธรรมเนียม',
                                                                                        'discount' => 'ส่วนลด',
                                                                                        'penalty' => 'ค่าปรับ',
                                                                                        'commission' => 'ค่าคอมมิชชั่น',
                                                                                        'other' => 'อื่นๆ'
                                                                                    ];

                                                                                    $type_icons = [
                                                                                        'tax_vat' => 'fas fa-percentage',
                                                                                        'tax_withholding' => 'fas fa-hand-holding-usd',
                                                                                        'service_fee' => 'fas fa-credit-card',
                                                                                        'discount' => 'fas fa-tag',
                                                                                        'penalty' => 'fas fa-exclamation-triangle',
                                                                                        'commission' => 'fas fa-handshake',
                                                                                        'other' => 'fas fa-ellipsis-h'
                                                                                    ];

                                                                                    // Show first few deductions
                                                                                    for ($i = 0; $i < $show_first; $i++):
                                                                                        $deduction = $receipt_deductions[$receipt_id][$i];
                                                                                        $type_name = $type_names[$deduction['deduction_type']] ?? $deduction['deduction_type'];
                                                                                        $type_icon = $type_icons[$deduction['deduction_type']] ?? 'fas fa-minus';
                                                                                    ?>
                                                                                        <div class="deduction-item border rounded p-2 mb-2 bg-light">
                                                                                            <div class="d-flex justify-content-between align-items-center">
                                                                                                <div class="d-flex align-items-center">
                                                                                                    <div class="deduction-icon bg-danger text-white rounded-circle me-2">
                                                                                                        <i class="<?php echo $type_icon; ?>"></i>
                                                                                                    </div>
                                                                                                    <div>
                                                                                                        <div class="fw-bold"><?php echo $type_name; ?></div>
                                                                                                        <?php if (!empty($deduction['description'])): ?>
                                                                                                            <small class="text-muted"><?php echo htmlspecialchars($deduction['description']); ?></small>
                                                                                                        <?php endif; ?>
                                                                                                    </div>
                                                                                                </div>
                                                                                                <div class="text-end">
                                                                                                    <span class="text-danger fw-bold">-<?php echo number_format($deduction['amount'], 2); ?> บาท</span>
                                                                                                    <?php if (!empty($deduction['deduction_image'])): ?>
                                                                                                        <div class="mt-1">
                                                                                                            <a href="../api/view_file.php?file=<?php echo urlencode($deduction['deduction_image']); ?>&type=deduction"
                                                                                                               class="btn btn-sm btn-outline-primary image-thumbnail"
                                                                                                               data-src="../api/view_file.php?file=<?php echo urlencode($deduction['deduction_image']); ?>&type=deduction"
                                                                                                               title="หลักฐานการหัก - <?php echo $type_name; ?>">
                                                                                                                <i class="fas fa-image me-1"></i>ดูหลักฐาน
                                                                                                            </a>
                                                                                                        </div>
                                                                                                    <?php endif; ?>
                                                                                                </div>
                                                                                            </div>
                                                                                        </div>
                                                                                    <?php endfor; ?>

                                                                                    <?php if ($remaining_count > 0): ?>
                                                                                        <div class="text-center">
                                                                                            <button class="btn btn-sm btn-outline-secondary" type="button" data-bs-toggle="collapse" data-bs-target="#moreDeductions<?php echo $receipt_id; ?>">
                                                                                                <i class="fas fa-chevron-down me-1"></i>ดูเพิ่มเติม (<?php echo $remaining_count; ?> รายการ)
                                                                                            </button>
                                                                                        </div>
                                                                                        <div class="collapse mt-2" id="moreDeductions<?php echo $receipt_id; ?>">
                                                                                            <?php for ($i = $show_first; $i < $deduction_count; $i++):
                                                                                                $deduction = $receipt_deductions[$receipt_id][$i];
                                                                                                $type_name = $type_names[$deduction['deduction_type']] ?? $deduction['deduction_type'];
                                                                                                $type_icon = $type_icons[$deduction['deduction_type']] ?? 'fas fa-minus';
                                                                                            ?>
                                                                                                <div class="deduction-item border rounded p-2 mb-2 bg-light">
                                                                                                    <div class="d-flex justify-content-between align-items-center">
                                                                                                        <div class="d-flex align-items-center">
                                                                                                            <div class="deduction-icon bg-danger text-white rounded-circle me-2">
                                                                                                                <i class="<?php echo $type_icon; ?>"></i>
                                                                                                            </div>
                                                                                                            <div>
                                                                                                                <div class="fw-bold"><?php echo $type_name; ?></div>
                                                                                                                <?php if (!empty($deduction['description'])): ?>
                                                                                                                    <small class="text-muted"><?php echo htmlspecialchars($deduction['description']); ?></small>
                                                                                                                <?php endif; ?>
                                                                                                            </div>
                                                                                                        </div>
                                                                                                        <div class="text-end">
                                                                                                            <span class="text-danger fw-bold">-<?php echo number_format($deduction['amount'], 2); ?> บาท</span>
                                                                                                            <?php if (!empty($deduction['deduction_image'])): ?>
                                                                                                                <div class="mt-1">
                                                                                                                    <a href="../api/view_file.php?file=<?php echo urlencode($deduction['deduction_image']); ?>&type=deduction"
                                                                                                                       class="btn btn-sm btn-outline-primary image-thumbnail"
                                                                                                                       data-src="../api/view_file.php?file=<?php echo urlencode($deduction['deduction_image']); ?>&type=deduction"
                                                                                                                       title="หลักฐานการหัก - <?php echo $type_name; ?>">
                                                                                                                        <i class="fas fa-image me-1"></i>ดูหลักฐาน
                                                                                                                    </a>
                                                                                                                </div>
                                                                                                            <?php endif; ?>
                                                                                                        </div>
                                                                                                    </div>
                                                                                                </div>
                                                                                            <?php endfor; ?>
                                                                                        </div>
                                                                                    <?php endif; ?>
                                                                                </div>
                                                                            </div>
                                                                        </div>
                                                                    <?php endif; ?>
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </div>

                                                <?php endforeach; ?>


                                                <!-- Enhanced Summary Card -->
                                                <div class="card border-primary shadow-sm mt-4">
                                                    <div class="card-header bg-gradient-primary text-white">
                                                        <h6 class="mb-0">
                                                            <i class="fas fa-calculator me-2"></i>สรุปยอดรวม
                                                            <span class="badge bg-light text-dark ms-2"><?php echo count($receipt_numbers); ?> ใบเสร็จ</span>
                                                            <?php if ($total_deduction_count > 0): ?>
                                                            <span class="badge bg-warning text-dark ms-1"><?php echo $total_deduction_count; ?> รายการหัก</span>
                                                            <?php endif; ?>
                                                        </h6>
                                                    </div>
                                                    <div class="card-body">
                                                        <div class="row g-3">
                                                            <div class="col-md-3">
                                                                <div class="text-center p-3 bg-light rounded">
                                                                    <div class="mb-2">
                                                                        <i class="fas fa-money-bill-wave text-primary" style="font-size: 1.5rem;"></i>
                                                                    </div>
                                                                    <div class="h3 text-primary mb-1"><?php echo number_format($total_gross, 2); ?></div>
                                                                    <small class="text-muted fw-bold">ยอดก่อนหัก (บาท)</small>
                                                                </div>
                                                            </div>
                                                            <div class="col-md-3">
                                                                <div class="text-center p-3 bg-light rounded">
                                                                    <div class="mb-2">
                                                                        <i class="fas fa-minus-circle text-danger" style="font-size: 1.5rem;"></i>
                                                                    </div>
                                                                    <div class="h3 text-danger mb-1">-<?php echo number_format($total_deductions, 2); ?></div>
                                                                    <small class="text-muted fw-bold">รายการหัก (บาท)</small>
                                                                    <?php if ($total_deductions > 0): ?>
                                                                        <div class="mt-1">
                                                                            <small class="text-muted">
                                                                                <?php echo $total_deduction_count; ?> รายการ
                                                                            </small>
                                                                        </div>
                                                                    <?php endif; ?>
                                                                </div>
                                                            </div>
                                                            <div class="col-md-3">
                                                                <div class="text-center p-3 bg-light rounded">
                                                                    <div class="mb-2">
                                                                        <i class="fas fa-check-circle text-success" style="font-size: 1.5rem;"></i>
                                                                    </div>
                                                                    <div class="h3 text-success mb-1"><?php echo number_format($total_net, 2); ?></div>
                                                                    <small class="text-muted fw-bold">ยอดสุทธิ (บาท)</small>
                                                                </div>
                                                            </div>
                                                            <div class="col-md-3">
                                                                <div class="text-center p-3 bg-light rounded">
                                                                    <div class="mb-2">
                                                                        <i class="fas fa-exchange-alt text-info" style="font-size: 1.5rem;"></i>
                                                                    </div>
                                                                    <div class="h3 text-info mb-1"><?php echo number_format($expense['transfer_amount'] ?? 0, 2); ?></div>
                                                                    <small class="text-muted fw-bold">Transfer Amount (บาท)</small>
                                                                    <?php
                                                                    $transfer_amount = $expense['transfer_amount'] ?? 0;
                                                                    $difference = abs($total_net - $transfer_amount);
                                                                    if ($transfer_amount > 0):
                                                                        if ($difference < 0.01): ?>
                                                                            <div class="mt-2">
                                                                                <span class="badge bg-success">
                                                                                    <i class="fas fa-check me-1"></i>ยอดตรงต้อง
                                                                                </span>
                                                                            </div>
                                                                        <?php else: ?>
                                                                            <div class="mt-2">
                                                                                <span class="badge bg-danger">
                                                                                    <i class="fas fa-exclamation-triangle me-1"></i>ต่างกัน <?php echo number_format($difference, 2); ?> บาท
                                                                                </span>
                                                                            </div>
                                                                        <?php endif; ?>
                                                                    <?php endif; ?>
                                                                </div>
                                                            </div>
                                                        </div>

                                                        <!-- Calculation Formula Display -->
                                                        <div class="row mt-4">
                                                            <div class="col-12">
                                                                <div class="alert alert-info mb-0">
                                                                    <div class="text-center">
                                                                        <strong>สูตรการคำนวณ:</strong>
                                                                        <span class="text-primary"><?php echo number_format($total_gross, 2); ?></span>
                                                                        <span class="text-muted">-</span>
                                                                        <span class="text-danger"><?php echo number_format($total_deductions, 2); ?></span>
                                                                        <span class="text-muted">=</span>
                                                                        <span class="text-success fw-bold"><?php echo number_format($total_net, 2); ?> บาท</span>
                                                                    </div>
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        <?php else: ?>
                                            <div class="text-center py-5">
                                                <i class="fas fa-receipt text-muted" style="font-size: 3rem;"></i>
                                                <p class="text-muted mt-3 mb-0">ไม่พบข้อมูลใบเสร็จ</p>
                                            </div>
                                        <?php endif; ?>
                                    </div>
                                </div>

                                <!-- Workflow Documents (Show for Success status) -->
                                <?php if ($expense['status'] === 'success'): ?>
                                <div class="row mt-4">
                                    <?php
                                    // Use batch verification slip if available, otherwise use individual slip
                                    $verification_slip_path = $batch_verification_slip ? $batch_verification_slip['file_path'] : $expense['verification_slip_image'];
                                    $verification_slip_name = $batch_verification_slip ? $batch_verification_slip['original_filename'] : 'Verification Slip';
                                    $is_batch_verification = !empty($batch_verification_slip);
                                    ?>
                                    <?php if (!empty($verification_slip_path)): ?>
                                    <div class="col-md-6">
                                        <h6>
                                            Verification Slip
                                            <?php if ($is_batch_verification): ?>
                                            <span class="badge bg-primary ms-2">Batch Operation</span>
                                            <?php endif; ?>
                                        </h6>
                                        <div class="images-container">
                                            <a href="../api/view_file.php?file=<?php echo urlencode($verification_slip_path); ?>&type=<?php echo $is_batch_verification ? 'batch_document' : 'verification_slip'; ?>"
                                               class="image-thumbnail" data-src="../api/view_file.php?file=<?php echo urlencode($verification_slip_path); ?>&type=<?php echo $is_batch_verification ? 'batch_document' : 'verification_slip'; ?>">
                                                <img src="../api/view_file.php?file=<?php echo urlencode($verification_slip_path); ?>&type=<?php echo $is_batch_verification ? 'batch_document' : 'verification_slip'; ?>"
                                                     class="img-thumbnail" style="max-width: 200px; max-height: 150px;"
                                                     alt="<?php echo htmlspecialchars($verification_slip_name); ?>">
                                            </a>
                                        </div>
                                        <small class="text-muted">
                                            Amount: <?php echo number_format($expense['verification_amount'] ?? 0, 2); ?> บาท<br>
                                            By: <?php echo htmlspecialchars($expense['verification_user_name'] ?? 'Unknown'); ?>
                                            <?php if ($is_batch_verification): ?>
                                            <br><span class="badge bg-info">Batch ID: <?php echo htmlspecialchars($expense['batch_verification_id']); ?></span>
                                            <br><small class="text-info">File: <?php echo htmlspecialchars($verification_slip_name); ?></small>
                                            <?php if (count($batch_verification_expenses) > 0): ?>
                                            <br><strong class="text-primary">Processed with <?php echo count($batch_verification_expenses); ?> other item(s):</strong>
                                            <div class="mt-1">
                                                <?php foreach ($batch_verification_expenses as $batch_exp): ?>
                                                <div class="d-flex justify-content-between align-items-center mb-1">
                                                    <small>
                                                        <a href="view.php?id=<?php echo $batch_exp['id']; ?>" class="text-decoration-none">
                                                            <?php echo htmlspecialchars($batch_exp['exno']); ?>
                                                        </a>
                                                        - <?php echo htmlspecialchars($batch_exp['item_name'] ?? 'N/A'); ?>
                                                    </small>
                                                    <small class="text-muted"><?php echo htmlspecialchars($batch_exp['customer_name'] ?? 'N/A'); ?></small>
                                                </div>
                                                <?php endforeach; ?>
                                            </div>
                                            <?php endif; ?>
                                            <?php endif; ?>
                                        </small>
                                    </div>
                                    <?php endif; ?>

                                    <?php
                                    // Use batch review slip if available, otherwise use individual slip
                                    $review_slip_path = $batch_review_slip ? $batch_review_slip['file_path'] : $expense['reviewer_slip_image'];
                                    $review_slip_name = $batch_review_slip ? $batch_review_slip['original_filename'] : 'Review Slip';
                                    $is_batch_review = !empty($batch_review_slip);
                                    ?>
                                    <?php if (!empty($review_slip_path)): ?>
                                    <div class="col-md-6">
                                        <h6>
                                            Review Slip
                                            <?php if ($is_batch_review): ?>
                                            <span class="badge bg-success ms-2">Batch Operation</span>
                                            <?php endif; ?>
                                        </h6>
                                        <div class="images-container">
                                            <a href="../api/view_file.php?file=<?php echo urlencode($review_slip_path); ?>&type=<?php echo $is_batch_review ? 'batch_document' : 'reviewer_slip'; ?>"
                                               class="image-thumbnail" data-src="../api/view_file.php?file=<?php echo urlencode($review_slip_path); ?>&type=<?php echo $is_batch_review ? 'batch_document' : 'reviewer_slip'; ?>">
                                                <img src="../api/view_file.php?file=<?php echo urlencode($review_slip_path); ?>&type=<?php echo $is_batch_review ? 'batch_document' : 'reviewer_slip'; ?>"
                                                     class="img-thumbnail" style="max-width: 200px; max-height: 150px;"
                                                     alt="<?php echo htmlspecialchars($review_slip_name); ?>">
                                            </a>
                                        </div>
                                        <small class="text-muted">
                                            Amount: <?php echo number_format($expense['reviewer_amount'] ?? 0, 2); ?> บาท<br>
                                            By: <?php echo htmlspecialchars($expense['reviewer_user_name'] ?? 'Unknown'); ?>
                                            <?php if ($is_batch_review): ?>
                                            <br><span class="badge bg-info">Batch ID: <?php echo htmlspecialchars($expense['batch_review_id']); ?></span>
                                            <br><small class="text-info">File: <?php echo htmlspecialchars($review_slip_name); ?></small>
                                            <?php if (count($batch_review_expenses) > 0): ?>
                                            <br><strong class="text-success">Processed with <?php echo count($batch_review_expenses); ?> other item(s):</strong>
                                            <div class="mt-1">
                                                <?php foreach ($batch_review_expenses as $batch_exp): ?>
                                                <div class="d-flex justify-content-between align-items-center mb-1">
                                                    <small>
                                                        <a href="view.php?id=<?php echo $batch_exp['id']; ?>" class="text-decoration-none">
                                                            <?php echo htmlspecialchars($batch_exp['exno']); ?>
                                                        </a>
                                                        - <?php echo htmlspecialchars($batch_exp['item_name'] ?? 'N/A'); ?>
                                                    </small>
                                                    <small class="text-muted"><?php echo htmlspecialchars($batch_exp['customer_name'] ?? 'N/A'); ?></small>
                                                </div>
                                                <?php endforeach; ?>
                                            </div>
                                            <?php endif; ?>
                                            <?php endif; ?>
                                        </small>
                                    </div>
                                    <?php endif; ?>
                                </div>
                                <?php endif; ?>


                            </div>
                        </div> <!-- End Documents Card -->

                    </div> <!-- End col-lg-8 (Main Content) -->

                    <div class="col-lg-4">
                        <!-- Status Management -->
                        <div class="status-management-section">
                        <div class="card mb-4">
                            <div class="card-header">
                                <h5 class="card-title mb-0">Status Management</h5>
                            </div>
                            <div class="card-body">
                                <div class="mb-3">
                                    <strong>Current Status:</strong><br>
                                    <?php echo getStatusBadge($expense['status']); ?>
                                </div>

                                <!-- Rejection Information -->
                                <?php if ($expense['status'] === 'rejected' && !empty($expense['rejection_reason'])): ?>
                                <div class="alert alert-danger">
                                    <h6 class="alert-heading">
                                        <i class="fas fa-times-circle me-1"></i>Rejection Information
                                    </h6>
                                    <p class="mb-2"><strong>Reason:</strong> <?php echo htmlspecialchars($expense['rejection_reason']); ?></p>
                                    <small class="text-muted">
                                        Rejected on <?php echo date('M j, Y \a\t g:i A', strtotime($expense['rejection_date'])); ?>
                                        <?php if (!empty($expense['rejected_by'])): ?>
                                            <?php
                                            $stmt = $db->prepare("SELECT full_name FROM users WHERE id = ?");
                                            $stmt->execute([$expense['rejected_by']]);
                                            $rejected_by_user = $stmt->fetch();
                                            ?>
                                            by <?php echo htmlspecialchars($rejected_by_user['full_name'] ?? 'Unknown User'); ?>
                                        <?php endif; ?>
                                    </small>
                                </div>
                                <?php endif; ?>

                                <!-- Return Information -->
                                <?php if ($expense['status'] === 'returned' && !empty($expense['return_reason'])): ?>
                                <div class="alert alert-warning">
                                    <h6 class="alert-heading">
                                        <i class="fas fa-undo me-1"></i>Return Information
                                    </h6>
                                    <p class="mb-2"><strong>Reason:</strong> <?php echo htmlspecialchars($expense['return_reason']); ?></p>
                                    <small class="text-muted">
                                        Returned on <?php echo date('M j, Y \a\t g:i A', strtotime($expense['return_date'])); ?>
                                        <?php if (!empty($expense['returned_by'])): ?>
                                            <?php
                                            $stmt = $db->prepare("SELECT full_name FROM users WHERE id = ?");
                                            $stmt->execute([$expense['returned_by']]);
                                            $returned_by_user = $stmt->fetch();
                                            ?>
                                            by <?php echo htmlspecialchars($returned_by_user['full_name'] ?? 'Unknown User'); ?>
                                        <?php endif; ?>
                                    </small>
                                </div>
                                <?php endif; ?>
                                
                                <?php
                                // Check if user can manage status (for pending/success) or edit expense (for open/returned)
                                $can_manage_status = canManageStatus($expense, $user_id, $user_role);
                                $can_edit_expense = canEditExpense($expense, $user_id, $user_role);

                                // Status change validation
                                $can_change_status = true;
                                $validation_errors = [];

                                // Check requirements for status changes
                                if ($user_role === 'administrator') {
                                    // Administrator can always change status (full override)
                                    $can_change_status = true;
                                    $validation_errors = []; // No validation errors for admin
                                } elseif ($user_role === 'verification' && $expense['status'] === 'open') {
                                    if (empty($expense['verification_transfer_no'])) {
                                        $validation_errors[] = 'Verification transfer number is required';
                                        $can_change_status = false;
                                    }
                                    // Check if verification slip exists (either individual or batch)
                                    $has_verification_slip = !empty($expense['verification_slip_image']) || !empty($batch_verification_slip);
                                    if (!$has_verification_slip) {
                                        $validation_errors[] = 'Please submit verification documents first';
                                        $can_change_status = false;
                                    }
                                    if (empty($expense['verification_amount'])) {
                                        $validation_errors[] = 'Verification amount is required';
                                        $can_change_status = false;
                                    }
                                } elseif ($user_role === 'reviewer' && $expense['status'] === 'pending') {
                                    if (empty($expense['reviewer_transfer_no'])) {
                                        $validation_errors[] = 'Review transfer number is required';
                                        $can_change_status = false;
                                    }
                                    // Check if review slip exists (either individual or batch)
                                    $has_review_slip = !empty($expense['reviewer_slip_image']) || !empty($batch_review_slip);
                                    if (!$has_review_slip) {
                                        $validation_errors[] = 'Please submit review documents first';
                                        $can_change_status = false;
                                    }
                                    if (empty($expense['reviewer_amount'])) {
                                        $validation_errors[] = 'Review amount is required';
                                        $can_change_status = false;
                                    }

                                    // Check consistency between verification and review amounts
                                    if (!empty($expense['verification_amount']) && !empty($expense['reviewer_amount'])) {
                                        if (abs($expense['verification_amount'] - $expense['reviewer_amount']) > 0.01) {
                                            $validation_errors[] = 'Review amount must match verification amount (Verification: ' . number_format($expense['verification_amount'], 2) . ' บาท)';
                                            $can_change_status = false;
                                        }
                                    }
                                }

                                $possible_statuses = [];
                                $show_reset_pending = false;

                                if ($user_role === 'verification' && $expense['status'] === 'open') {
                                    $possible_statuses[] = 'pending';
                                } elseif ($user_role === 'verification' && $expense['status'] === 'pending') {
                                    // Verification can reset pending back to open for corrections
                                    $show_reset_pending = true;
                                    // Remove the ability to move to success directly
                                    // $possible_statuses[] = 'success';
                                } elseif ($user_role === 'reviewer' && $expense['status'] === 'open') {
                                    $possible_statuses[] = 'pending'; // Allow reviewer to move open to pending
                                } elseif ($user_role === 'reviewer' && $expense['status'] === 'pending') {
                                    $possible_statuses[] = 'success';
                                } elseif ($user_role === 'administrator') {
                                    $all_statuses = ['open', 'pending', 'success'];
                                    $possible_statuses = array_filter($all_statuses, function($status) use ($expense) {
                                        return $status !== $expense['status'];
                                    });
                                    $can_change_status = true; // Admin can always change status
                                }
                                ?>

                                <?php if ($can_manage_status && (!empty($possible_statuses) || $show_reset_pending || $user_role === 'administrator')): ?>

                                    <!-- Administrator Control Panel -->
                                    <?php if ($user_role === 'administrator'): ?>
                                    <div class="card mb-3 border-warning">
                                        <div class="card-header bg-warning">
                                            <h6 class="mb-0"><i class="fas fa-user-shield me-1"></i>Administrator Control Panel</h6>
                                        </div>
                                        <div class="card-body">
                                            <div class="alert alert-warning mb-3">
                                                <i class="fas fa-exclamation-triangle me-1"></i>
                                                <strong>Administrator Override:</strong> You can bypass all workflow validation and change status directly.
                                            </div>

                                            <div class="row">
                                                <div class="col-md-6">
                                                    <h6>Quick Actions:</h6>
                                                    <button class="btn btn-sm btn-outline-primary mb-2" onclick="resetWorkflow()">
                                                        <i class="fas fa-undo me-1"></i>Reset to Open
                                                    </button>
                                                    <button class="btn btn-sm btn-outline-success mb-2" onclick="forceApprove()">
                                                        <i class="fas fa-check-double me-1"></i>Force Approve
                                                    </button>
                                                    <button class="btn btn-sm btn-outline-info mb-2" onclick="showAdminForms()">
                                                        <i class="fas fa-edit me-1"></i>Show Admin Forms
                                                    </button>
                                                </div>
                                                <div class="col-md-6">
                                                    <h6>Override Options:</h6>
                                                    <div class="form-check">
                                                        <input class="form-check-input" type="checkbox" id="bypass_validation" checked>
                                                        <label class="form-check-label" for="bypass_validation">
                                                            Bypass all validation
                                                        </label>
                                                    </div>
                                                    <div class="form-check">
                                                        <input class="form-check-input" type="checkbox" id="allow_amount_mismatch" checked>
                                                        <label class="form-check-label" for="allow_amount_mismatch">
                                                            Allow amount mismatch
                                                        </label>
                                                    </div>
                                                    <div class="form-check">
                                                        <input class="form-check-input" type="checkbox" id="log_admin_action" checked>
                                                        <label class="form-check-label" for="log_admin_action">
                                                            Log admin actions
                                                        </label>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>

                                    <!-- Admin Verification Form (always visible for admin) -->
                                    <div class="card mb-3 admin-form" style="display: none;">
                                        <div class="card-header bg-info">
                                            <h6 class="mb-0"><i class="fas fa-user-shield me-1"></i>Admin: Verification Override</h6>
                                        </div>
                                        <div class="card-body">
                                            <div class="alert alert-info">
                                                <strong>Current Verification:</strong>
                                                <?php if (!empty($expense['verification_by'])): ?>
                                                    Verified by <?php echo htmlspecialchars($expense['verification_user_name']); ?>
                                                    on <?php echo date('M d, Y H:i', strtotime($expense['verification_date'])); ?>
                                                    (Amount: <?php echo number_format($expense['verification_amount'], 2); ?> บาท)
                                                <?php else: ?>
                                                    Not verified yet
                                                <?php endif; ?>
                                            </div>

                                            <form id="admin_verification_form" enctype="multipart/form-data">
                                                <input type="hidden" name="expense_id" value="<?php echo $expense['id']; ?>">
                                                <input type="hidden" name="admin_override" value="1">
                                                <div class="row">
                                                    <div class="col-md-6">
                                                        <div class="mb-3">
                                                            <label for="admin_verification_slip" class="form-label">
                                                                Verification Slip <span class="text-warning">(Admin Override)</span>
                                                            </label>
                                                            <input type="file" class="form-control" id="admin_verification_slip"
                                                                   name="verification_slip" accept="image/*,.pdf">
                                                            <div class="form-text">Upload new verification slip (optional for admin)</div>
                                                        </div>
                                                    </div>
                                                    <div class="col-md-6">
                                                        <div class="mb-3">
                                                            <label for="admin_verification_amount" class="form-label">
                                                                Verification Amount <span class="text-warning">(Admin Override)</span>
                                                            </label>
                                                            <input type="number" class="form-control" id="admin_verification_amount"
                                                                   name="verification_amount" step="0.01" min="0"
                                                                   value="<?php echo $expense['verification_amount'] ?? ''; ?>"
                                                                   placeholder="Enter verification amount">
                                                            <div class="form-text">Admin can modify verification amount</div>
                                                        </div>
                                                    </div>
                                                </div>
                                                <button type="submit" class="btn btn-warning">
                                                    <i class="fas fa-user-shield me-1"></i>Admin: Update Verification
                                                </button>
                                            </form>
                                        </div>
                                    </div>

                                    <!-- Admin Review Form (always visible for admin) -->
                                    <div class="card mb-3 admin-form" style="display: none;">
                                        <div class="card-header bg-success">
                                            <h6 class="mb-0"><i class="fas fa-user-shield me-1"></i>Admin: Review Override</h6>
                                        </div>
                                        <div class="card-body">
                                            <div class="alert alert-success">
                                                <strong>Current Review:</strong>
                                                <?php if (!empty($expense['reviewer_by'])): ?>
                                                    Reviewed by <?php echo htmlspecialchars($expense['reviewer_user_name']); ?>
                                                    on <?php echo date('M d, Y H:i', strtotime($expense['reviewer_date'])); ?>
                                                    (Amount: <?php echo number_format($expense['reviewer_amount'], 2); ?> บาท)
                                                <?php else: ?>
                                                    Not reviewed yet
                                                <?php endif; ?>
                                            </div>

                                            <form id="admin_review_form" enctype="multipart/form-data">
                                                <input type="hidden" name="expense_id" value="<?php echo $expense['id']; ?>">
                                                <input type="hidden" name="admin_override" value="1">
                                                <div class="row">
                                                    <div class="col-md-6">
                                                        <div class="mb-3">
                                                            <label for="admin_reviewer_slip" class="form-label">
                                                                Review Slip <span class="text-warning">(Admin Override)</span>
                                                            </label>
                                                            <input type="file" class="form-control" id="admin_reviewer_slip"
                                                                   name="reviewer_slip" accept="image/*,.pdf">
                                                            <div class="form-text">Upload new review slip (optional for admin)</div>
                                                        </div>
                                                    </div>
                                                    <div class="col-md-6">
                                                        <div class="mb-3">
                                                            <label for="admin_reviewer_amount" class="form-label">
                                                                Review Amount <span class="text-warning">(Admin Override)</span>
                                                            </label>
                                                            <input type="number" class="form-control" id="admin_reviewer_amount"
                                                                   name="reviewer_amount" step="0.01" min="0"
                                                                   value="<?php echo $expense['reviewer_amount'] ?? $expense['verification_amount'] ?? ''; ?>"
                                                                   placeholder="Enter review amount">
                                                            <div class="form-text">Admin can set any review amount</div>
                                                        </div>
                                                    </div>
                                                </div>
                                                <button type="submit" class="btn btn-success">
                                                    <i class="fas fa-user-shield me-1"></i>Admin: Update Review
                                                </button>
                                            </form>
                                        </div>
                                    </div>
                                    <?php endif; ?>

                                    <!-- Check Button (for verification role) -->
                                    <?php if ($user_role === 'verification' && $expense['status'] === 'open'): ?>
                                    <div class="card mb-3 border-info">
                                        <div class="card-header bg-info text-white">
                                            <h6 class="mb-0"><i class="fas fa-check-circle me-1"></i>Check Expense</h6>
                                        </div>
                                        <div class="card-body">
                                            <p class="mb-3">
                                                <i class="fas fa-info-circle text-info me-2"></i>
                                                ตรวจสอบความถูกต้องของรายการก่อนดำเนินการต่อ
                                            </p>

                                            <form id="check_form">
                                                <input type="hidden" name="expense_id" value="<?php echo $expense['id']; ?>">

                                                <div class="mb-3">
                                                    <label for="check_comment" class="form-label">
                                                        <i class="fas fa-comment me-1"></i>หมายเหตุการตรวจสอบ
                                                    </label>
                                                    <textarea class="form-control" id="check_comment" name="check_comment"
                                                              rows="3" placeholder="ระบุรายละเอียดการตรวจสอบ (ไม่บังคับ)"></textarea>
                                                </div>

                                                <div class="d-grid">
                                                    <button type="submit" class="btn btn-info btn-lg">
                                                        <i class="fas fa-check-circle me-2"></i>Check - ตรวจสอบแล้ว
                                                    </button>
                                                </div>
                                            </form>
                                        </div>
                                    </div>
                                    <?php endif; ?>

                                    <!-- Verification Form (for verification role) -->
                                    <?php if ($user_role === 'verification' && $expense['status'] === 'checked' && empty($expense['verification_by'])): ?>
                                    <div class="card mb-3">
                                        <div class="card-header">
                                            <h6 class="mb-0"><i class="fas fa-upload me-1"></i>Verification Documents</h6>
                                        </div>
                                        <div class="card-body">
                                            <form id="verification_form" enctype="multipart/form-data">
                                                <input type="hidden" name="expense_id" value="<?php echo $expense['id']; ?>">

                                                <!-- Transfer Number Field -->
                                                <div class="row mb-3">
                                                    <div class="col-md-12">
                                                        <div class="mb-3">
                                                            <label for="verification_transfer_number" class="form-label">
                                                                <i class="fas fa-receipt me-1"></i>Transfer Number <span class="text-danger">*</span>
                                                            </label>
                                                            <input type="text" class="form-control" id="verification_transfer_number"
                                                                   name="verification_transfer_no" required
                                                                   placeholder="Enter verification transfer slip number"
                                                                   value="">
                                                            <div class="form-text">Required: Enter the verification transfer slip number</div>
                                                        </div>
                                                    </div>
                                                </div>

                                                <div class="row">
                                                    <div class="col-md-6">
                                                        <div class="mb-3">
                                                            <label for="verification_slip" class="form-label">
                                                                Verification Slip <span class="text-danger">*</span>
                                                            </label>
                                                            <input type="file" class="form-control" id="verification_slip"
                                                                   name="verification_slip" accept="image/*,.pdf" required>
                                                            <div class="form-text">Upload verification transfer slip (JPG, PNG, PDF - Max 5MB)</div>
                                                        </div>
                                                    </div>
                                                    <div class="col-md-6">
                                                        <div class="mb-3">
                                                            <label for="verification_amount" class="form-label">
                                                                Verification Amount <span class="text-danger">*</span>
                                                            </label>
                                                            <input type="number" class="form-control" id="verification_amount"
                                                                   name="verification_amount" step="0.01" min="0" required
                                                                   placeholder="Enter verified amount">
                                                            <div class="form-text">Enter verified transfer amount in บาท</div>
                                                        </div>
                                                    </div>
                                                </div>
                                                <button type="submit" class="btn btn-success">
                                                    <i class="fas fa-check me-1"></i>Submit Verification
                                                </button>
                                            </form>
                                        </div>
                                    </div>
                                    <?php endif; ?>

                                    <!-- Review Form (for reviewer role) -->
                                    <?php if ($user_role === 'reviewer' && $expense['status'] === 'pending' && empty($expense['reviewer_by'])): ?>
                                    <div class="card mb-3">
                                        <div class="card-header">
                                            <h6 class="mb-0"><i class="fas fa-clipboard-check me-1"></i>Review Documents</h6>
                                        </div>
                                        <div class="card-body">
                                            <!-- Show verification data if exists -->
                                            <?php
                                            $verification_slip_display = $batch_verification_slip ? $batch_verification_slip['file_path'] : $expense['verification_slip_image'];
                                            $verification_slip_display_name = $batch_verification_slip ? $batch_verification_slip['original_filename'] : 'Verification Slip';
                                            $is_batch_verification_display = !empty($batch_verification_slip);
                                            ?>
                                            <?php if (!empty($verification_slip_display)): ?>
                                            <div class="alert alert-info mb-3">
                                                <h6>
                                                    <i class="fas fa-info-circle me-1"></i>Verification Data:
                                                    <?php if ($is_batch_verification_display): ?>
                                                    <span class="badge bg-primary ms-2">Batch Operation</span>
                                                    <?php endif; ?>
                                                </h6>
                                                <div class="row">
                                                    <div class="col-md-3">
                                                        <strong>Verification Amount:</strong><br>
                                                        <span class="text-primary"><?php echo number_format($expense['verification_amount'], 2); ?> บาท</span>
                                                    </div>
                                                    <div class="col-md-3">
                                                        <strong>Verified by:</strong><br>
                                                        <?php echo htmlspecialchars($expense['verification_user_name'] ?? 'Unknown'); ?>
                                                        <?php if ($is_batch_verification_display): ?>
                                                        <br><small class="text-info">Batch ID: <?php echo htmlspecialchars($expense['batch_verification_id']); ?></small>
                                                        <?php endif; ?>
                                                    </div>
                                                    <div class="col-md-3">
                                                        <strong>Verification Date:</strong><br>
                                                        <?php echo $expense['verification_date'] ? date('M d, Y H:i', strtotime($expense['verification_date'])) : 'N/A'; ?>
                                                    </div>
                                                    <div class="col-md-3">
                                                        <strong>Verification Slip:</strong><br>
                                                        <a href="../api/view_file.php?file=<?php echo urlencode($verification_slip_display); ?>&type=<?php echo $is_batch_verification_display ? 'batch_document' : 'verification_slip'; ?>"
                                                           class="image-thumbnail d-inline-block"
                                                           data-src="../api/view_file.php?file=<?php echo urlencode($verification_slip_display); ?>&type=<?php echo $is_batch_verification_display ? 'batch_document' : 'verification_slip'; ?>">
                                                            <img src="../api/view_file.php?file=<?php echo urlencode($verification_slip_display); ?>&type=<?php echo $is_batch_verification_display ? 'batch_document' : 'verification_slip'; ?>"
                                                                 alt="<?php echo htmlspecialchars($verification_slip_display_name); ?>"
                                                                 class="img-thumbnail"
                                                                 style="width: 60px; height: 60px; object-fit: cover; cursor: pointer;"
                                                                 title="Click to view <?php echo htmlspecialchars($verification_slip_display_name); ?>">
                                                        </a>
                                                        <br>
                                                        <small class="text-muted">
                                                            <?php echo $is_batch_verification_display ? htmlspecialchars($verification_slip_display_name) : 'Click to view'; ?>
                                                        </small>
                                                    </div>
                                                </div>
                                            </div>
                                            <?php endif; ?>

                                            <form id="reviewer_form" enctype="multipart/form-data">
                                                <input type="hidden" name="expense_id" value="<?php echo $expense['id']; ?>">

                                                <!-- Transfer Number Field -->
                                                <div class="row mb-3">
                                                    <div class="col-md-12">
                                                        <div class="mb-3">
                                                            <label for="reviewer_transfer_number" class="form-label">
                                                                <i class="fas fa-receipt me-1"></i>Transfer Number <span class="text-danger">*</span>
                                                            </label>
                                                            <input type="text" class="form-control" id="reviewer_transfer_number"
                                                                   name="reviewer_transfer_no" required
                                                                   placeholder="Enter review transfer slip number"
                                                                   value="">
                                                            <div class="form-text">Required: Enter the review transfer slip number</div>
                                                        </div>
                                                    </div>
                                                </div>

                                                <div class="row">
                                                    <div class="col-md-6">
                                                        <div class="mb-3">
                                                            <label for="reviewer_slip" class="form-label">
                                                                Review Slip <span class="text-danger">*</span>
                                                            </label>
                                                            <input type="file" class="form-control" id="reviewer_slip"
                                                                   name="reviewer_slip" accept="image/*,.pdf" required>
                                                            <div class="form-text">Upload review confirmation slip (JPG, PNG, PDF - Max 5MB)</div>
                                                        </div>
                                                    </div>
                                                    <div class="col-md-6">
                                                        <div class="mb-3">
                                                            <label for="reviewer_amount" class="form-label">
                                                                Review Amount <span class="text-danger">*</span>
                                                            </label>
                                                            <input type="number" class="form-control" id="reviewer_amount"
                                                                   name="reviewer_amount" step="0.01" min="0" required
                                                                   value="<?php echo $expense['verification_amount'] ?? ''; ?>"
                                                                   placeholder="Enter final approved amount">
                                                            <div class="form-text">Enter final approved amount in บาท (should match verification amount)</div>
                                                        </div>
                                                    </div>
                                                </div>
                                                <button type="submit" class="btn btn-primary">
                                                    <i class="fas fa-check-double me-1"></i>Submit Review
                                                </button>
                                            </form>
                                        </div>
                                    </div>
                                    <?php endif; ?>

                                    <!-- Status Change Requirements (Only for verification and reviewer) -->
                                    <?php if ($user_role === 'verification' || $user_role === 'reviewer'): ?>
                                    <div class="card mb-3">
                                        <div class="card-header">
                                            <h6 class="mb-0">
                                                <i class="fas fa-clipboard-check me-1"></i>Status Change Requirements
                                            </h6>
                                        </div>
                                        <div class="card-body">

                                            <div class="checklist">
                                                <?php if ($user_role === 'verification'): ?>
                                                <div class="form-check mb-2">
                                                    <input class="form-check-input" type="checkbox" disabled
                                                           <?php echo !empty($expense['verification_transfer_no']) ? 'checked' : ''; ?>>
                                                    <label class="form-check-label <?php echo !empty($expense['verification_transfer_no']) ? 'text-success' : 'text-danger'; ?>">
                                                        <i class="fas fa-<?php echo !empty($expense['verification_transfer_no']) ? 'check' : 'times'; ?> me-1"></i>
                                                        Verification Transfer Number Entered
                                                    </label>
                                                </div>
                                                <div class="form-check mb-2">
                                                    <?php $has_verification_slip_check = !empty($expense['verification_slip_image']) || !empty($batch_verification_slip); ?>
                                                    <input class="form-check-input" type="checkbox" disabled
                                                           <?php echo $has_verification_slip_check ? 'checked' : ''; ?>>
                                                    <label class="form-check-label <?php echo $has_verification_slip_check ? 'text-success' : 'text-danger'; ?>">
                                                        <i class="fas fa-<?php echo $has_verification_slip_check ? 'check' : 'times'; ?> me-1"></i>
                                                        Verification Slip Uploaded
                                                        <?php if (!empty($batch_verification_slip)): ?>
                                                        <span class="badge bg-primary ms-1">Batch</span>
                                                        <?php endif; ?>
                                                    </label>
                                                </div>
                                                <div class="form-check mb-2">
                                                    <input class="form-check-input" type="checkbox" disabled
                                                           <?php echo !empty($expense['verification_amount']) && $expense['verification_amount'] > 0 ? 'checked' : ''; ?>>
                                                    <label class="form-check-label <?php echo !empty($expense['verification_amount']) && $expense['verification_amount'] > 0 ? 'text-success' : 'text-danger'; ?>">
                                                        <i class="fas fa-<?php echo !empty($expense['verification_amount']) && $expense['verification_amount'] > 0 ? 'check' : 'times'; ?> me-1"></i>
                                                        Verification Amount Entered
                                                    </label>
                                                </div>
                                                <?php endif; ?>

                                                <?php if ($user_role === 'reviewer'): ?>
                                                <div class="form-check mb-2">
                                                    <input class="form-check-input" type="checkbox" disabled
                                                           <?php echo !empty($expense['reviewer_transfer_no']) ? 'checked' : ''; ?>>
                                                    <label class="form-check-label <?php echo !empty($expense['reviewer_transfer_no']) ? 'text-success' : 'text-danger'; ?>">
                                                        <i class="fas fa-<?php echo !empty($expense['reviewer_transfer_no']) ? 'check' : 'times'; ?> me-1"></i>
                                                        Review Transfer Number Entered
                                                    </label>
                                                </div>
                                                <div class="form-check mb-2">
                                                    <?php $has_review_slip_check = !empty($expense['reviewer_slip_image']) || !empty($batch_review_slip); ?>
                                                    <input class="form-check-input" type="checkbox" disabled
                                                           <?php echo $has_review_slip_check ? 'checked' : ''; ?>>
                                                    <label class="form-check-label <?php echo $has_review_slip_check ? 'text-success' : 'text-danger'; ?>">
                                                        <i class="fas fa-<?php echo $has_review_slip_check ? 'check' : 'times'; ?> me-1"></i>
                                                        Review Slip Uploaded
                                                        <?php if (!empty($batch_review_slip)): ?>
                                                        <span class="badge bg-success ms-1">Batch</span>
                                                        <?php endif; ?>
                                                    </label>
                                                </div>
                                                <div class="form-check mb-2">
                                                    <input class="form-check-input" type="checkbox" disabled
                                                           <?php echo !empty($expense['reviewer_amount']) && $expense['reviewer_amount'] > 0 ? 'checked' : ''; ?>>
                                                    <label class="form-check-label <?php echo !empty($expense['reviewer_amount']) && $expense['reviewer_amount'] > 0 ? 'text-success' : 'text-danger'; ?>">
                                                        <i class="fas fa-<?php echo !empty($expense['reviewer_amount']) && $expense['reviewer_amount'] > 0 ? 'check' : 'times'; ?> me-1"></i>
                                                        Review Amount Entered
                                                    </label>
                                                </div>
                                                <div class="form-check mb-2">
                                                    <input class="form-check-input" type="checkbox" disabled
                                                           <?php echo !empty($expense['verification_amount']) && !empty($expense['reviewer_amount']) && abs($expense['verification_amount'] - $expense['reviewer_amount']) < 0.01 ? 'checked' : ''; ?>>
                                                    <label class="form-check-label <?php echo !empty($expense['verification_amount']) && !empty($expense['reviewer_amount']) && abs($expense['verification_amount'] - $expense['reviewer_amount']) < 0.01 ? 'text-success' : 'text-danger'; ?>">
                                                        <i class="fas fa-<?php echo !empty($expense['verification_amount']) && !empty($expense['reviewer_amount']) && abs($expense['verification_amount'] - $expense['reviewer_amount']) < 0.01 ? 'check' : 'times'; ?> me-1"></i>
                                                        Amounts Match
                                                        <?php if (!empty($expense['verification_amount']) && !empty($expense['reviewer_amount']) && abs($expense['verification_amount'] - $expense['reviewer_amount']) >= 0.01): ?>
                                                            <small class="text-muted">
                                                                (Diff: <?php echo number_format(abs($expense['verification_amount'] - $expense['reviewer_amount']), 2); ?> บาท)
                                                            </small>
                                                        <?php endif; ?>
                                                    </label>
                                                </div>
                                                <?php endif; ?>
                                            </div>
                                        </div>
                                    </div>
                                    <?php endif; ?>

                                    <!-- Document Status Overview (Only for Administrator) -->
                                    <?php if ($user_role === 'administrator'): ?>
                                    <div class="card mb-3">
                                        <div class="card-header bg-info">
                                            <h6 class="mb-0"><i class="fas fa-file-alt me-1"></i>Document Status Overview (Admin View)</h6>
                                        </div>
                                        <div class="card-body">
                                            <div class="row">
                                                <div class="col-md-4">
                                                    <h6 class="text-muted">Original Documents</h6>
                                                    <div class="status-item mb-2">
                                                        <i class="fas fa-<?php echo !empty($expense['transfer_slip_image']) ? 'check text-success' : 'times text-danger'; ?> me-1"></i>
                                                        <small>Transfer Slip</small>
                                                    </div>
                                                    <div class="status-item mb-2">
                                                        <i class="fas fa-<?php echo !empty($expense['transfer_amount']) && $expense['transfer_amount'] > 0 ? 'check text-success' : 'times text-danger'; ?> me-1"></i>
                                                        <small>Transfer Amount: <?php echo number_format($expense['transfer_amount'] ?? 0, 2); ?> บาท</small>
                                                    </div>
                                                    <div class="status-item">
                                                        <i class="fas fa-<?php echo !empty($receipt_total) && $receipt_total > 0 ? 'check text-success' : 'times text-danger'; ?> me-1"></i>
                                                        <small>Receipt Total: <?php echo number_format($receipt_total, 2); ?> บาท</small>
                                                    </div>
                                                </div>

                                                <div class="col-md-4">
                                                    <h6 class="text-muted">Verification</h6>
                                                    <div class="status-item mb-2">
                                                        <?php $has_verification_slip_status = !empty($expense['verification_slip_image']) || !empty($batch_verification_slip); ?>
                                                        <i class="fas fa-<?php echo $has_verification_slip_status ? 'check text-success' : 'times text-danger'; ?> me-1"></i>
                                                        <small>
                                                            Verification Slip
                                                            <?php if (!empty($batch_verification_slip)): ?>
                                                            <span class="badge bg-primary ms-1">Batch</span>
                                                            <?php endif; ?>
                                                        </small>
                                                    </div>
                                                    <div class="status-item mb-2">
                                                        <i class="fas fa-<?php echo !empty($expense['verification_amount']) && $expense['verification_amount'] > 0 ? 'check text-success' : 'times text-danger'; ?> me-1"></i>
                                                        <small>Verification Amount: <?php echo number_format($expense['verification_amount'] ?? 0, 2); ?> บาท</small>
                                                    </div>
                                                    <div class="status-item">
                                                        <?php if (!empty($expense['verification_by'])): ?>
                                                            <i class="fas fa-user text-info me-1"></i>
                                                            <small>By: <?php echo htmlspecialchars($expense['verification_user_name'] ?? 'Unknown'); ?></small>
                                                        <?php else: ?>
                                                            <i class="fas fa-times text-danger me-1"></i>
                                                            <small>Not verified</small>
                                                        <?php endif; ?>
                                                    </div>
                                                </div>

                                                <div class="col-md-4">
                                                    <h6 class="text-muted">Review</h6>
                                                    <div class="status-item mb-2">
                                                        <?php $has_review_slip_status = !empty($expense['reviewer_slip_image']) || !empty($batch_review_slip); ?>
                                                        <i class="fas fa-<?php echo $has_review_slip_status ? 'check text-success' : 'times text-danger'; ?> me-1"></i>
                                                        <small>
                                                            Review Slip
                                                            <?php if (!empty($batch_review_slip)): ?>
                                                            <span class="badge bg-success ms-1">Batch</span>
                                                            <?php endif; ?>
                                                        </small>
                                                    </div>
                                                    <div class="status-item mb-2">
                                                        <i class="fas fa-<?php echo !empty($expense['reviewer_amount']) && $expense['reviewer_amount'] > 0 ? 'check text-success' : 'times text-danger'; ?> me-1"></i>
                                                        <small>Review Amount: <?php echo number_format($expense['reviewer_amount'] ?? 0, 2); ?> บาท</small>
                                                    </div>
                                                    <div class="status-item">
                                                        <?php if (!empty($expense['reviewer_by'])): ?>
                                                            <i class="fas fa-user text-info me-1"></i>
                                                            <small>By: <?php echo htmlspecialchars($expense['reviewer_user_name'] ?? 'Unknown'); ?></small>
                                                        <?php else: ?>
                                                            <i class="fas fa-times text-danger me-1"></i>
                                                            <small>Not reviewed</small>
                                                        <?php endif; ?>
                                                    </div>
                                                </div>
                                            </div>

                                            <!-- Amount Consistency Check -->
                                            <?php if (!empty($expense['verification_amount']) && !empty($expense['reviewer_amount'])): ?>
                                            <hr>
                                            <div class="text-center">
                                                <?php if (abs($expense['verification_amount'] - $expense['reviewer_amount']) < 0.01): ?>
                                                    <span class="badge bg-success">
                                                        <i class="fas fa-check me-1"></i>Verification & Review Amounts Match
                                                    </span>
                                                <?php else: ?>
                                                    <span class="badge bg-warning">
                                                        <i class="fas fa-exclamation-triangle me-1"></i>
                                                        Amount Mismatch: <?php echo number_format(abs($expense['verification_amount'] - $expense['reviewer_amount']), 2); ?> บาท
                                                    </span>
                                                <?php endif; ?>
                                            </div>
                                            <?php endif; ?>
                                        </div>
                                    </div>
                                    <?php endif; ?>

                                    <!-- Validation Errors -->
                                    <?php if (!empty($validation_errors)): ?>
                                        <div class="alert alert-warning mb-3">
                                            <h6><i class="fas fa-exclamation-triangle me-1"></i>Requirements Not Met:</h6>
                                            <ul class="mb-0">
                                                <?php foreach ($validation_errors as $error): ?>
                                                    <li><?php echo htmlspecialchars($error); ?></li>
                                                <?php endforeach; ?>
                                            </ul>
                                        </div>
                                    <?php endif; ?>

                                    <!-- Reject/Return Actions -->
                                    <?php if (($user_role === 'verification' && $expense['status'] === 'open') ||
                                              ($user_role === 'reviewer' && $expense['status'] === 'pending') ||
                                              $user_role === 'administrator'): ?>
                                    <div class="mb-3">
                                        <button type="button" class="btn btn-danger w-100" id="reject_btn">
                                            <i class="fas fa-times-circle me-1"></i>Reject Expense
                                        </button>
                                    </div>
                                    <?php endif; ?>

                                    <!-- Resubmit Action (for returned/rejected expenses) -->
                                    <?php if (($expense['status'] === 'returned' || $expense['status'] === 'rejected') &&
                                              ($expense['created_by'] == $user_id || $user_role === 'administrator')): ?>
                                    <div class="mb-3">
                                        <button type="button" class="btn btn-success w-100" id="resubmit_btn">
                                            <i class="fas fa-paper-plane me-1"></i>Resubmit for Review
                                        </button>
                                    </div>
                                    <?php endif; ?>

                                    <!-- Reset Pending Button for Verification Role -->
                                    <?php if ($show_reset_pending): ?>
                                    <div class="alert alert-info mb-3">
                                        <div class="d-flex align-items-center">
                                            <div class="flex-grow-1">
                                                <h6 class="alert-heading mb-1">
                                                    <i class="fas fa-info-circle me-1"></i>Pending Status Management
                                                </h6>
                                                <p class="mb-0">
                                                    This expense is currently pending. You can reset it back to "Open" status to make corrections or upload new documents.
                                                </p>
                                            </div>
                                            <div class="ms-3">
                                                <button type="button" class="btn btn-outline-warning" id="reset_pending_btn">
                                                    <i class="fas fa-undo me-1"></i>Reset to Open
                                                </button>
                                            </div>
                                        </div>
                                    </div>
                                    <?php endif; ?>

                                    <?php if (!empty($possible_statuses)): ?>
                                    <div class="mb-3">
                                        <label for="new_status" class="form-label">
                                            Change Status To:
                                            <?php if ($user_role === 'administrator'): ?>
                                                <span class="badge bg-warning ms-2">
                                                    <i class="fas fa-user-shield me-1"></i>Admin Override
                                                </span>
                                            <?php endif; ?>
                                        </label>
                                        <select class="form-select" id="new_status" <?php echo !$can_change_status && $user_role !== 'administrator' ? 'disabled' : ''; ?>>
                                            <option value="">Select new status...</option>
                                            <?php if ($user_role === 'administrator'): ?>
                                                <!-- Admin can change to any status -->
                                                <option value="open">Open</option>
                                                <option value="pending">Pending</option>
                                                <option value="success">Success</option>
                                                <option value="rejected">Rejected</option>
                                                <option value="returned">Returned</option>
                                            <?php else: ?>
                                                <!-- Regular users see only allowed transitions -->
                                                <?php foreach ($possible_statuses as $status): ?>
                                                    <option value="<?php echo $status; ?>"><?php echo ucfirst($status); ?></option>
                                                <?php endforeach; ?>
                                            <?php endif; ?>
                                        </select>
                                        <?php if ($user_role === 'administrator'): ?>
                                        <div class="form-text text-warning">
                                            <i class="fas fa-exclamation-triangle me-1"></i>
                                            Administrator can change to any status without validation
                                        </div>
                                        <?php endif; ?>
                                    </div>

                                    <div class="mb-3">
                                        <label for="status_comment" class="form-label">Comment (Optional):</label>
                                        <textarea class="form-control" id="status_comment" rows="3"
                                                  placeholder="Add a comment about this status change..."
                                                  <?php echo !$can_change_status ? 'disabled' : ''; ?>></textarea>
                                    </div>

                                    <button type="button" class="btn btn-primary" id="change_status_btn"
                                            <?php echo !$can_change_status ? 'disabled' : ''; ?>>
                                        <i class="fas fa-exchange-alt me-1"></i>Change Status
                                    </button>

                                    <?php if (!$can_change_status): ?>
                                        <small class="text-muted d-block mt-2">
                                            <i class="fas fa-info-circle me-1"></i>
                                            Please complete all requirements above before changing status.
                                        </small>
                                    <?php endif; ?>
                                <?php endif; ?>

                                <?php endif; ?>
                            </div>
                        </div>

                        <!-- Record Information -->
                        <div class="card mb-4">
                            <div class="card-header">
                                <h5 class="card-title mb-0">Record Information</h5>
                            </div>
                            <div class="card-body">
                                <table class="table table-borderless table-sm">
                                    <tr>
                                        <td><strong>Created By:</strong></td>
                                        <td><?php echo htmlspecialchars($expense['created_by_name']); ?></td>
                                    </tr>
                                    <tr>
                                        <td><strong>Created At:</strong></td>
                                        <td><?php echo formatDateTime($expense['created_at']); ?></td>
                                    </tr>
                                    <tr>
                                        <td><strong>Last Updated:</strong></td>
                                        <td><?php echo formatDateTime($expense['updated_at']); ?></td>
                                    </tr>
                                </table>
                            </div>
                        </div>

                        <!-- Activity Log -->
                        <div class="card">
                            <div class="card-header">
                                <?php if ($user_role === 'administrator'): ?>
                                    <h5 class="card-title mb-0">
                                        <i class="fas fa-history me-1"></i>Activity History (All Users)
                                    </h5>
                                <?php else: ?>
                                    <h5 class="card-title mb-0">
                                        <i class="fas fa-history me-1"></i>My Activity History
                                    </h5>
                                <?php endif; ?>
                            </div>
                            <div class="card-body">
                                <?php if (!empty($activity_logs)): ?>
                                    <div class="timeline">
                                        <?php foreach ($activity_logs as $log): ?>
                                            <div class="activity-log-item">
                                                <div class="d-flex justify-content-between">
                                                    <strong><?php echo htmlspecialchars($log['user_name']); ?></strong>
                                                    <small class="text-muted"><?php echo formatDateTime($log['created_at']); ?></small>
                                                </div>
                                                <div class="activity-description">
                                                    <?php echo htmlspecialchars($log['description']); ?>
                                                </div>
                                                <?php if ($log['action_type'] === 'status_change' && !empty($log['new_values'])): ?>
                                                    <?php $new_values = json_decode($log['new_values'], true); ?>
                                                    <?php if (!empty($new_values['comment'])): ?>
                                                        <div class="mt-1">
                                                            <small class="text-muted">Comment: <?php echo htmlspecialchars($new_values['comment']); ?></small>
                                                        </div>
                                                    <?php endif; ?>
                                                <?php endif; ?>
                                            </div>
                                        <?php endforeach; ?>
                                    </div>
                                <?php else: ?>
                                    <?php if ($user_role === 'administrator'): ?>
                                        <p class="text-muted">No activity recorded for this expense yet.</p>
                                    <?php else: ?>
                                        <p class="text-muted">You haven't performed any actions on this expense yet.</p>
                                    <?php endif; ?>
                                <?php endif; ?>
                            </div>
                        </div>
                        </div> <!-- End status-management-section -->
                    </div> <!-- End col-lg-4 (Status Management) -->
                </div> <!-- End Main Content Row -->
            </div> <!-- End col-12 -->
        </div> <!-- End container row -->
    </div> <!-- End container-fluid -->
    <!-- Reject Modal -->
    <div class="modal fade" id="rejectModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header bg-danger text-white">
                    <h5 class="modal-title">
                        <i class="fas fa-times-circle me-1"></i>Reject Expense
                    </h5>
                    <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <form id="rejectForm">
                        <input type="hidden" name="expense_id" value="<?php echo $expense['id']; ?>">
                        <?php if ($user_role === 'administrator'): ?>
                        <input type="hidden" name="admin_override" value="1">
                        <?php endif; ?>

                        <div class="mb-3">
                            <label for="rejection_reason" class="form-label">
                                <strong>Reason for Rejection:</strong>
                            </label>
                            <textarea class="form-control" id="rejection_reason" name="rejection_reason"
                                      rows="4" placeholder="Please provide a detailed reason for rejecting this expense..." required></textarea>
                        </div>

                        <div class="alert alert-warning">
                            <i class="fas fa-exclamation-triangle me-1"></i>
                            <strong>Warning:</strong> Rejecting this expense will mark it as rejected and notify the creator.
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="button" class="btn btn-danger" id="confirmReject">
                        <i class="fas fa-times-circle me-1"></i>Reject Expense
                    </button>
                </div>
            </div>
        </div>
    </div>



    <!-- Resubmit Modal -->
    <div class="modal fade" id="resubmitModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header bg-success text-white">
                    <h5 class="modal-title">
                        <i class="fas fa-paper-plane me-1"></i>Resubmit for Review
                    </h5>
                    <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <form id="resubmitForm">
                        <input type="hidden" name="expense_id" value="<?php echo $expense['id']; ?>">
                        <?php if ($user_role === 'administrator'): ?>
                        <input type="hidden" name="admin_override" value="1">
                        <?php endif; ?>

                        <!-- Show rejection/return reason if available -->
                        <?php if (!empty($expense['rejection_reason'])): ?>
                        <div class="alert alert-danger">
                            <h6><i class="fas fa-times-circle me-1"></i>Previous Rejection Reason:</h6>
                            <p class="mb-0"><?php echo htmlspecialchars($expense['rejection_reason']); ?></p>
                        </div>
                        <?php endif; ?>

                        <?php if (!empty($expense['return_reason'])): ?>
                        <div class="alert alert-warning">
                            <h6><i class="fas fa-undo me-1"></i>Previous Return Reason:</h6>
                            <p class="mb-0"><?php echo htmlspecialchars($expense['return_reason']); ?></p>
                        </div>
                        <?php endif; ?>

                        <div class="mb-3">
                            <label for="resubmit_comment" class="form-label">
                                <strong>Resubmission Comment (Optional):</strong>
                            </label>
                            <textarea class="form-control" id="resubmit_comment" name="resubmit_comment"
                                      rows="3" placeholder="Describe what changes you made (optional)..."></textarea>
                        </div>

                        <div class="alert alert-success">
                            <i class="fas fa-info-circle me-1"></i>
                            <strong>Note:</strong> This will reset the expense status to "Open" and make it available for review again.
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="button" class="btn btn-success" id="confirmResubmit">
                        <i class="fas fa-paper-plane me-1"></i>Resubmit for Review
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Bottom Navigation Bar -->
    <div class="container-fluid mt-4 mb-4">
        <div class="row">
            <div class="col-12">
                <?php renderNavigationBar($prev_expense, $next_expense, $current_position, $total_filtered, $search, $status_filter, $current_page, $expense); ?>
            </div>
        </div>
    </div>



    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script src="../assets/js/image-viewer.js?v=<?php echo filemtime('../assets/js/image-viewer.js'); ?>"></script>
    <script>
        // Navigation data for JavaScript
        const navigationData = {
            currentPage: <?php echo $current_page; ?>,
            search: '<?php echo addslashes($search); ?>',
            statusFilter: '<?php echo addslashes($status_filter); ?>',
            nextExpense: <?php echo $next_expense ? json_encode(['id' => $next_expense['id'], 'exno' => $next_expense['exno']]) : 'null'; ?>,
            prevExpense: <?php echo $prev_expense ? json_encode(['id' => $prev_expense['id'], 'exno' => $prev_expense['exno']]) : 'null'; ?>
        };

        // Helper function to navigate to next expense or back to list
        function navigateAfterAction() {
            if (navigationData.nextExpense) {
                let nextUrl = 'view.php?id=' + navigationData.nextExpense.id;
                if (navigationData.search) nextUrl += '&search=' + encodeURIComponent(navigationData.search);
                if (navigationData.statusFilter) nextUrl += '&status=' + encodeURIComponent(navigationData.statusFilter);
                nextUrl += '&page=' + navigationData.currentPage;

                window.location.href = nextUrl;
            } else {
                // If no next expense, go back to list with current page
                let listUrl = 'list.php?page=' + navigationData.currentPage;
                if (navigationData.search) listUrl += '&search=' + encodeURIComponent(navigationData.search);
                if (navigationData.statusFilter) listUrl += '&status=' + encodeURIComponent(navigationData.statusFilter);

                window.location.href = listUrl;
            }
        }

        $(document).ready(function() {


            // Status change validation data
            const hasTransferSlip = <?php echo !empty($expense['transfer_slip_image']) ? 'true' : 'false'; ?>;
            const hasTransferAmount = <?php echo !empty($expense['transfer_amount']) && $expense['transfer_amount'] > 0 ? 'true' : 'false'; ?>;
            const transferAmount = <?php echo $transfer_amount; ?>;
            const receiptTotal = <?php echo $receipt_total; ?>;
            const amountsMatch = Math.abs(transferAmount - receiptTotal) < 0.01;
            const userRole = '<?php echo $user_role; ?>';
            const currentStatus = '<?php echo $expense['status']; ?>';
            const canChangeStatus = <?php echo $can_change_status ? 'true' : 'false'; ?>;

            $('#change_status_btn').click(function() {
                const newStatus = $('#new_status').val();
                const comment = $('#status_comment').val();

                if (!newStatus) {
                    alert('Please select a new status');
                    return;
                }

                // Double-check validation on client side
                if (!canChangeStatus) {
                    alert('Cannot change status. Please complete all requirements first.');
                    return;
                }

                // Build validation summary for confirmation
                let validationSummary = '';
                if (userRole === 'verification' || userRole === 'reviewer') {
                    validationSummary = '\n\nValidation Status:\n' +
                                      '• Transfer Slip: ' + (hasTransferSlip ? '✓ Uploaded' : '✗ Missing') + '\n' +
                                      '• Transfer Amount: ' + (hasTransferAmount ? '✓ Entered (' + transferAmount.toFixed(2) + ' บาท)' : '✗ Missing') + '\n' +
                                      '• Amount Match: ' + (amountsMatch ? '✓ Amounts Match' : '✗ Mismatch (Diff: ' + Math.abs(transferAmount - receiptTotal).toFixed(2) + ' บาท)');
                }

                const confirmMessage = 'Are you sure you want to change status from "' + currentStatus + '" to "' + newStatus + '"?' + validationSummary;

                if (confirm(confirmMessage)) {
                    // Show loading state
                    const $btn = $(this);
                    const originalText = $btn.html();
                    $btn.prop('disabled', true).html('<i class="fas fa-spinner fa-spin me-1"></i>Changing...');

                    $.ajax({
                        url: '../api/change_status.php',
                        method: 'POST',
                        contentType: 'application/json',
                        data: JSON.stringify({
                            expense_id: <?php echo $expense['id']; ?>,
                            status: newStatus,
                            comment: comment
                        }),
                        success: function(response) {
                            if (response.success) {
                                // Show success message briefly before reload
                                $btn.removeClass('btn-primary').addClass('btn-success')
                                    .html('<i class="fas fa-check me-1"></i>Status Changed!');

                                setTimeout(function() {
                                    location.reload();
                                }, 1000);
                            } else {
                                alert('Error: ' + response.error);
                                $btn.prop('disabled', false).html(originalText);
                            }
                        },
                        error: function(xhr) {
                            const response = xhr.responseJSON || {};
                            alert('Error: ' + (response.error || 'Unknown error occurred'));
                            $btn.prop('disabled', false).html(originalText);
                        }
                    });
                }
            });

            // Real-time validation feedback
            $('#new_status').change(function() {
                const selectedStatus = $(this).val();
                const $btn = $('#change_status_btn');

                if (!selectedStatus) {
                    $btn.prop('disabled', true);
                    return;
                }

                // Enable/disable button based on validation
                $btn.prop('disabled', !canChangeStatus);

                // Update button text based on validation
                if (!canChangeStatus) {
                    $btn.html('<i class="fas fa-times me-1"></i>Requirements Not Met');
                } else {
                    $btn.html('<i class="fas fa-exchange-alt me-1"></i>Change Status');
                }
            });

            // Verification form submission
            $('#verification_form').submit(function(e) {
                e.preventDefault();

                const formData = new FormData(this);
                const $btn = $(this).find('button[type="submit"]');
                const originalText = $btn.html();

                // Show loading state
                $btn.prop('disabled', true).html('<i class="fas fa-spinner fa-spin me-1"></i>Submitting...');

                $.ajax({
                    url: '../api/submit_verification.php',
                    method: 'POST',
                    data: formData,
                    processData: false,
                    contentType: false,
                    success: function(response) {
                        console.log('Verification response:', response);
                        if (response.success) {
                            $btn.removeClass('btn-success').addClass('btn-success')
                                .html('<i class="fas fa-check me-1"></i>Verification Submitted!');

                            // Show success message
                            alert('Verification submitted successfully!');

                            // Reload page to show updated data
                            setTimeout(function() {
                                location.reload();
                            }, 1000);
                        } else {
                            alert('Error: ' + response.error);
                            $btn.prop('disabled', false).html(originalText);
                        }
                    },
                    error: function(xhr) {
                        console.log('Verification error:', xhr);
                        console.log('Response text:', xhr.responseText);
                        const response = xhr.responseJSON || {};
                        alert('Error: ' + (response.error || 'Unknown error occurred'));
                        $btn.prop('disabled', false).html(originalText);
                    }
                });
            });

            // Review form submission
            $('#reviewer_form').submit(function(e) {
                e.preventDefault();

                const formData = new FormData(this);
                const $btn = $(this).find('button[type="submit"]');
                const originalText = $btn.html();

                // Show loading state
                $btn.prop('disabled', true).html('<i class="fas fa-spinner fa-spin me-1"></i>Submitting...');

                $.ajax({
                    url: '../api/submit_review.php',
                    method: 'POST',
                    data: formData,
                    processData: false,
                    contentType: false,
                    success: function(response) {
                        console.log('Review response:', response);
                        if (response.success) {
                            $btn.removeClass('btn-primary').addClass('btn-success')
                                .html('<i class="fas fa-check me-1"></i>Review Submitted!');

                            // Show success message
                            alert('Review submitted successfully!');

                            // Reload page to show updated data
                            setTimeout(function() {
                                location.reload();
                            }, 1000);
                        } else {
                            alert('Error: ' + response.error);
                            $btn.prop('disabled', false).html(originalText);
                        }
                    },
                    error: function(xhr) {
                        console.log('Review error:', xhr);
                        console.log('Response text:', xhr.responseText);
                        const response = xhr.responseJSON || {};
                        alert('Error: ' + (response.error || 'Unknown error occurred'));
                        $btn.prop('disabled', false).html(originalText);
                    }
                });
            });

            // Real-time amount validation for reviewer
            $('#reviewer_amount').on('input', function() {
                const reviewAmount = parseFloat($(this).val()) || 0;
                const verificationAmount = <?php echo $expense['verification_amount'] ?? 0; ?>;

                if (verificationAmount > 0 && Math.abs(reviewAmount - verificationAmount) > 0.01) {
                    $(this).addClass('is-invalid');
                    if (!$(this).next('.invalid-feedback').length) {
                        $(this).after('<div class="invalid-feedback">Amount must match verification amount (' +
                                    verificationAmount.toFixed(2) + ' บาท)</div>');
                    }
                } else {
                    $(this).removeClass('is-invalid');
                    $(this).next('.invalid-feedback').remove();
                }
            });

            // Admin verification form submission
            $('#admin_verification_form').submit(function(e) {
                e.preventDefault();

                if (!confirmAdminAction('Update Verification', 'This will override existing verification data.')) {
                    return;
                }

                const formData = new FormData(this);
                const $btn = $(this).find('button[type="submit"]');
                const originalText = $btn.html();

                $btn.prop('disabled', true).html('<i class="fas fa-spinner fa-spin me-1"></i>Processing...');

                $.ajax({
                    url: '../api/submit_verification.php',
                    method: 'POST',
                    data: formData,
                    processData: false,
                    contentType: false,
                    success: function(response) {
                        if (response.success) {
                            $btn.removeClass('btn-warning').addClass('btn-success')
                                .html('<i class="fas fa-check me-1"></i>Admin: Verification Updated!');
                            alert('Admin: Verification updated successfully!');
                            setTimeout(function() {
                                location.reload();
                            }, 1000);
                        } else {
                            alert('Error: ' + response.error);
                            $btn.prop('disabled', false).html(originalText);
                        }
                    },
                    error: function(xhr) {
                        const response = xhr.responseJSON || {};
                        alert('Error: ' + (response.error || 'Unknown error occurred'));
                        $btn.prop('disabled', false).html(originalText);
                    }
                });
            });

            // Admin review form submission
            $('#admin_review_form').submit(function(e) {
                e.preventDefault();

                if (!confirmAdminAction('Update Review', 'This will override existing review data.')) {
                    return;
                }

                const formData = new FormData(this);
                const $btn = $(this).find('button[type="submit"]');
                const originalText = $btn.html();

                $btn.prop('disabled', true).html('<i class="fas fa-spinner fa-spin me-1"></i>Processing...');

                $.ajax({
                    url: '../api/submit_review.php',
                    method: 'POST',
                    data: formData,
                    processData: false,
                    contentType: false,
                    success: function(response) {
                        if (response.success) {
                            $btn.removeClass('btn-success').addClass('btn-success')
                                .html('<i class="fas fa-check me-1"></i>Admin: Review Updated!');
                            alert('Admin: Review updated successfully!');
                            setTimeout(function() {
                                location.reload();
                            }, 1000);
                        } else {
                            alert('Error: ' + response.error);
                            $btn.prop('disabled', false).html(originalText);
                        }
                    },
                    error: function(xhr) {
                        const response = xhr.responseJSON || {};
                        alert('Error: ' + (response.error || 'Unknown error occurred'));
                        $btn.prop('disabled', false).html(originalText);
                    }
                });
            });

            // Admin functions
            window.resetWorkflow = function() {
                if (confirmAdminAction('Reset Workflow', 'This will reset the expense status to Open and clear verification/review data.')) {
                    changeStatusWithAdmin('open', 'Admin: Reset workflow to Open status');
                }
            };

            window.forceApprove = function() {
                if (confirmAdminAction('Force Approve', 'This will change the expense status to Success regardless of validation.')) {
                    changeStatusWithAdmin('success', 'Admin: Force approved without validation');
                }
            };

            window.showAdminForms = function() {
                $('.admin-form').toggle();
                const $btn = $('button[onclick="showAdminForms()"]');
                const isVisible = $('.admin-form').is(':visible');
                $btn.html(isVisible ?
                    '<i class="fas fa-eye-slash me-1"></i>Hide Admin Forms' :
                    '<i class="fas fa-edit me-1"></i>Show Admin Forms'
                );
            };

            // Enhanced confirmation for admin actions
            window.confirmAdminAction = function(action, message) {
                return confirm(
                    `⚠️ ADMINISTRATOR OVERRIDE ⚠️\n\n` +
                    `Action: ${action}\n` +
                    `${message}\n\n` +
                    `This will bypass normal workflow validation.\n` +
                    `Are you sure you want to proceed?`
                );
            };

            // Admin status change function
            window.changeStatusWithAdmin = function(newStatus, comment) {
                const formData = new FormData();
                formData.append('expense_id', <?php echo $expense['id']; ?>);
                formData.append('new_status', newStatus);
                formData.append('comment', comment);
                formData.append('admin_override', '1');

                $.ajax({
                    url: '../api/change_status.php',
                    method: 'POST',
                    data: formData,
                    processData: false,
                    contentType: false,
                    success: function(response) {
                        if (response.success) {
                            alert('Status changed successfully!');
                            location.reload();
                        } else {
                            alert('Error: ' + response.error);
                        }
                    },
                    error: function(xhr) {
                        const response = xhr.responseJSON || {};
                        alert('Error: ' + (response.error || 'Unknown error occurred'));
                    }
                });
            };

            // Override status change validation for admin
            <?php if ($user_role === 'administrator'): ?>
            $('#change_status_btn').click(function(e) {
                const newStatus = $('#new_status').val();
                const comment = $('#status_comment').val();

                if (!newStatus) {
                    alert('Please select a status to change to.');
                    return;
                }

                if ($('#bypass_validation').is(':checked')) {
                    e.preventDefault();
                    if (confirmAdminAction('Change Status', `Change status to ${newStatus} with admin override.`)) {
                        changeStatusWithAdmin(newStatus, comment || 'Admin override: Status changed without validation');
                    }
                }
            });
            <?php endif; ?>

            // Reject/Resubmit functionality
            $('#reject_btn').click(function() {
                $('#rejectModal').modal('show');
            });



            $('#resubmit_btn').click(function() {
                $('#resubmitModal').modal('show');
            });

            // Handle reject confirmation
            $('#confirmReject').click(function() {
                const $btn = $(this);
                const originalText = $btn.html();
                const formData = new FormData(document.getElementById('rejectForm'));

                if (!formData.get('rejection_reason').trim()) {
                    alert('Please provide a reason for rejection');
                    return;
                }

                $btn.prop('disabled', true).html('<i class="fas fa-spinner fa-spin me-1"></i>Rejecting...');

                $.ajax({
                    url: '../api/reject_expense.php',
                    method: 'POST',
                    data: formData,
                    processData: false,
                    contentType: false,
                    success: function(response) {
                        if (response.success) {
                            alert('Expense rejected successfully!');
                            location.reload();
                        } else {
                            alert('Error: ' + response.error);
                            $btn.prop('disabled', false).html(originalText);
                        }
                    },
                    error: function(xhr) {
                        alert('Error: ' + (xhr.responseJSON?.error || 'Failed to reject expense'));
                        $btn.prop('disabled', false).html(originalText);
                    }
                });
            });



            // Handle resubmit confirmation
            $('#confirmResubmit').click(function() {
                const $btn = $(this);
                const originalText = $btn.html();
                const formData = new FormData(document.getElementById('resubmitForm'));

                $btn.prop('disabled', true).html('<i class="fas fa-spinner fa-spin me-1"></i>Resubmitting...');

                $.ajax({
                    url: '../api/resubmit_expense.php',
                    method: 'POST',
                    data: formData,
                    processData: false,
                    contentType: false,
                    success: function(response) {
                        if (response.success) {
                            alert('Expense resubmitted successfully!');
                            location.reload();
                        } else {
                            alert('Error: ' + response.error);
                            $btn.prop('disabled', false).html(originalText);
                        }
                    },
                    error: function(xhr) {
                        alert('Error: ' + (xhr.responseJSON?.error || 'Failed to resubmit expense'));
                        $btn.prop('disabled', false).html(originalText);
                    }
                });
            });

            // Handle Check form submission
            $('#check_form').submit(function(e) {
                e.preventDefault();

                const $btn = $(this).find('button[type="submit"]');
                const originalText = $btn.html();
                const formData = new FormData(this);

                if (!confirm('ยืนยันการตรวจสอบรายการนี้?\n\nรายการจะเปลี่ยนสถานะเป็น "Checked" และพร้อมสำหรับขั้นตอนต่อไป')) {
                    return;
                }

                $btn.prop('disabled', true).html('<i class="fas fa-spinner fa-spin me-2"></i>กำลังตรวจสอบ...');

                $.ajax({
                    url: '../api/check_expense.php',
                    method: 'POST',
                    data: formData,
                    processData: false,
                    contentType: false,
                    success: function(response) {
                        if (response.success) {
                            alert('ตรวจสอบรายการเรียบร้อยแล้ว!');
                            navigateAfterAction();
                        } else {
                            alert('เกิดข้อผิดพลาด: ' + response.error);
                            $btn.prop('disabled', false).html(originalText);
                        }
                    },
                    error: function(xhr) {
                        alert('เกิดข้อผิดพลาด: ' + (xhr.responseJSON?.error || 'ไม่สามารถตรวจสอบรายการได้'));
                        $btn.prop('disabled', false).html(originalText);
                    }
                });
            });

            // Handle Reset Pending button
            $('#reset_pending_btn').click(function() {
                const $btn = $(this);
                const originalText = $btn.html();

                if (confirm('Are you sure you want to reset this expense back to "Open" status?\n\nThis will allow you to make corrections and upload new documents.')) {
                    $btn.prop('disabled', true).html('<i class="fas fa-spinner fa-spin me-1"></i>Resetting...');

                    $.ajax({
                        url: '../api/change_status.php',
                        method: 'POST',
                        data: {
                            expense_id: <?php echo $expense['id']; ?>,
                            new_status: 'open',
                            comment: 'Reset from pending to open for corrections (Verification)'
                        },
                        success: function(response) {
                            if (response.success) {
                                alert('Status reset to Open successfully!\n\nYou can now make corrections and upload new documents.');
                                location.reload();
                            } else {
                                alert('Error: ' + response.error);
                                $btn.prop('disabled', false).html(originalText);
                            }
                        },
                        error: function(xhr) {
                            alert('Error: ' + (xhr.responseJSON?.error || 'Failed to reset status'));
                            $btn.prop('disabled', false).html(originalText);
                        }
                    });
                }
            });

            // Function to toggle deduction collapse text and icon
            window.toggleDeductionText = function(button, remainingCount) {
                const $btn = $(button);
                const $icon = $btn.find('i');
                const $text = $btn.find('.toggle-text');

                // Check if collapsed or expanded
                const isExpanded = $btn.attr('aria-expanded') === 'true';

                if (isExpanded) {
                    // Currently expanded, will collapse
                    $icon.removeClass('fa-chevron-up').addClass('fa-chevron-down');
                    $text.text('ดูอีก ' + remainingCount + ' รายการ');
                } else {
                    // Currently collapsed, will expand
                    $icon.removeClass('fa-chevron-down').addClass('fa-chevron-up');
                    $text.text('ซ่อนรายการ');
                }
            };
        });
    </script>
</body>
</html>
