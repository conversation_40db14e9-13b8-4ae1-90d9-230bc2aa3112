<?php
/**
 * Edit Functionality Disabled
 * 
 * This file replaces the original edit.php to prevent direct editing of expenses.
 * As part of the new workflow, expenses cannot be edited directly.
 * Instead, use the Check → Reject → Create New workflow.
 */

session_start();
require_once '../config/database.php';
require_once '../includes/functions.php';

// Check if user is logged in
if (!isset($_SESSION['user_id'])) {
    header('Location: ../login.php');
    exit();
}

$user_role = $_SESSION['role'];
$expense_id = $_GET['id'] ?? '';

?>
<!DOCTYPE html>
<html lang="th">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Edit Disabled | Expense Management System</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
</head>
<body class="bg-light">
    <?php include '../includes/navbar.php'; ?>
    
    <div class="container mt-4">
        <div class="row justify-content-center">
            <div class="col-md-8">
                <div class="card border-warning">
                    <div class="card-header bg-warning text-dark">
                        <h4 class="mb-0">
                            <i class="fas fa-exclamation-triangle me-2"></i>
                            การแก้ไขรายการถูกปิดใช้งาน
                        </h4>
                    </div>
                    <div class="card-body">
                        <div class="alert alert-info">
                            <h5><i class="fas fa-info-circle me-2"></i>ระบบได้เปลี่ยนแปลงขั้นตอนการทำงาน</h5>
                            <p class="mb-0">
                                เพื่อความถูกต้องและการควบคุมที่ดีขึ้น ระบบไม่อนุญาตให้แก้ไขรายการ expenses โดยตรงอีกต่อไป
                            </p>
                        </div>
                        
                        <div class="row">
                            <div class="col-md-6">
                                <div class="card border-primary h-100">
                                    <div class="card-header bg-primary text-white">
                                        <h6 class="mb-0">
                                            <i class="fas fa-user-check me-2"></i>
                                            สำหรับสิทธิ์ Verification
                                        </h6>
                                    </div>
                                    <div class="card-body">
                                        <ol class="mb-0">
                                            <li><strong>Check</strong> - ตรวจสอบความถูกต้อง</li>
                                            <li><strong>Verify</strong> - อนุมัติรายการ</li>
                                            <li><strong>Reject</strong> - ปฏิเสธหากผิดพลาด</li>
                                        </ol>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="col-md-6">
                                <div class="card border-success h-100">
                                    <div class="card-header bg-success text-white">
                                        <h6 class="mb-0">
                                            <i class="fas fa-user-edit me-2"></i>
                                            สำหรับสิทธิ์ Data Entry
                                        </h6>
                                    </div>
                                    <div class="card-body">
                                        <p class="mb-2">หากรายการถูก Reject:</p>
                                        <ol class="mb-0">
                                            <li><strong>สร้างรายการใหม่</strong></li>
                                            <li>ใช้ข้อมูลเดิมได้ (transfer_no, receipt_numbers)</li>
                                            <li>แก้ไขข้อผิดพลาดให้ถูกต้อง</li>
                                        </ol>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <div class="mt-4 text-center">
                            <div class="btn-group" role="group">
                                <?php if (!empty($expense_id)): ?>
                                <a href="view.php?id=<?= htmlspecialchars($expense_id) ?>" class="btn btn-primary">
                                    <i class="fas fa-eye me-2"></i>ดูรายการนี้
                                </a>
                                <?php endif; ?>
                                
                                <a href="list.php" class="btn btn-secondary">
                                    <i class="fas fa-list me-2"></i>รายการทั้งหมด
                                </a>
                                
                                <?php if (in_array($user_role, ['data_entry', 'administrator'])): ?>
                                <a href="create.php" class="btn btn-success">
                                    <i class="fas fa-plus me-2"></i>สร้างรายการใหม่
                                </a>
                                <?php endif; ?>
                                
                                <a href="../dashboard.php" class="btn btn-outline-primary">
                                    <i class="fas fa-home me-2"></i>หน้าหลัก
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="card mt-4 border-info">
                    <div class="card-header bg-info text-white">
                        <h6 class="mb-0">
                            <i class="fas fa-lightbulb me-2"></i>
                            ข้อดีของระบบใหม่
                        </h6>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-4">
                                <div class="text-center">
                                    <i class="fas fa-shield-alt fa-2x text-primary mb-2"></i>
                                    <h6>ความปลอดภัย</h6>
                                    <small class="text-muted">ป้องกันการแก้ไขข้อมูลโดยไม่ได้รับอนุญาต</small>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="text-center">
                                    <i class="fas fa-history fa-2x text-success mb-2"></i>
                                    <h6>ตรวจสอบได้</h6>
                                    <small class="text-muted">บันทึกประวัติการเปลี่ยนแปลงอย่างชัดเจน</small>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="text-center">
                                    <i class="fas fa-users fa-2x text-warning mb-2"></i>
                                    <h6>การทำงานร่วมกัน</h6>
                                    <small class="text-muted">แบ่งหน้าที่ความรับผิดชอบอย่างชัดเจน</small>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
