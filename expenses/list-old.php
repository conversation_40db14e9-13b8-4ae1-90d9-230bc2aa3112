<?php
session_start();
require_once '../config/database.php';
require_once '../includes/functions.php';

// Check if user is logged in
if (!isset($_SESSION['user_id'])) {
    header('Location: ../login.php');
    exit();
}

$database = new Database();
$db = $database->getConnection();

$user_id = $_SESSION['user_id'];
$user_role = $_SESSION['role'];

// Get filter parameters
$search = $_GET['search'] ?? '';
$status_filter = $_GET['status'] ?? '';
$page = max(1, (int)($_GET['page'] ?? 1));
$per_page = 20;

// Build query based on user role and filters
$where_conditions = [];
$params = [];

// Role-based access
if ($user_role === 'data_entry') {
    $where_conditions[] = 'e.created_by = ?';
    $params[] = $user_id;
}
// report_viewer, verification, reviewer, and administrator can see all data

// Search filter
if (!empty($search)) {
    $where_conditions[] = '(e.exno LIKE ? OR e.bookingno LIKE ? OR i.name LIKE ? OR c.name LIKE ? OR d.name LIKE ?)';
    $search_param = '%' . $search . '%';
    $params = array_merge($params, [$search_param, $search_param, $search_param, $search_param, $search_param]);
}

// Status filter
if (!empty($status_filter)) {
    $where_conditions[] = 'e.status = ?';
    $params[] = $status_filter;
}

$where_clause = !empty($where_conditions) ? 'WHERE ' . implode(' AND ', $where_conditions) : '';

// Get total count for pagination
$count_stmt = $db->prepare("
    SELECT COUNT(*) as total
    FROM expenses e
    LEFT JOIN items i ON e.item_id = i.id
    LEFT JOIN customers c ON e.customer_id = c.id
    LEFT JOIN drivers d ON e.driver_id = d.id
    $where_clause
");
$count_stmt->execute($params);
$total_records = $count_stmt->fetch()['total'];

$pagination = paginate($total_records, $per_page, $page);

// Get expenses
$stmt = $db->prepare("
    SELECT 
        e.*,
        i.name as item_name,
        c.name as customer_name,
        d.name as driver_name,
        u.full_name as created_by_name
    FROM expenses e
    LEFT JOIN items i ON e.item_id = i.id
    LEFT JOIN customers c ON e.customer_id = c.id
    LEFT JOIN drivers d ON e.driver_id = d.id
    LEFT JOIN users u ON e.created_by = u.id
    $where_clause
    ORDER BY e.created_at DESC
    LIMIT {$per_page} OFFSET {$pagination['offset']}
");

$stmt->execute($params);
$expenses = $stmt->fetchAll();
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Expenses List - Expenses Management System</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="../assets/css/style.css" rel="stylesheet">
</head>
<body>
    <!-- Navigation -->
    <?php include '../includes/navbar.php'; ?>

    <div class="container-fluid mt-4">
        <div class="row">
            <div class="col-12">
                <div class="d-flex justify-content-between align-items-center mb-4">
                    <h1><i class="fas fa-list me-2"></i>Expenses List</h1>
                    <a href="create.php" class="btn btn-primary">
                        <i class="fas fa-plus me-1"></i>New Expense
                    </a>
                </div>

                <!-- Search and Filter -->
                <div class="search-filter-bar">
                    <form method="GET" class="row g-3">
                        <div class="col-md-4">
                            <label for="search" class="form-label">Search</label>
                            <input type="text" class="form-control" id="search" name="search" 
                                   placeholder="Search by expense no, booking no, item, customer, or driver..."
                                   value="<?php echo htmlspecialchars($search); ?>">
                        </div>
                        <div class="col-md-3">
                            <label for="status" class="form-label">Status</label>
                            <select class="form-select" id="status" name="status">
                                <option value="">All Statuses</option>
                                <option value="open" <?php echo $status_filter === 'open' ? 'selected' : ''; ?>>Open</option>
                                <option value="pending" <?php echo $status_filter === 'pending' ? 'selected' : ''; ?>>Pending</option>
                                <option value="success" <?php echo $status_filter === 'success' ? 'selected' : ''; ?>>Approved</option>
                            </select>
                        </div>
                        <div class="col-md-2">
                            <label class="form-label">&nbsp;</label>
                            <div class="d-grid">
                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-search me-1"></i>Search
                                </button>
                            </div>
                        </div>
                        <div class="col-md-2">
                            <label class="form-label">&nbsp;</label>
                            <div class="d-grid">
                                <a href="list.php" class="btn btn-outline-warning">
                                    <i class="fas fa-undo me-1"></i>Reset
                                </a>
                            </div>
                        </div>
                        <div class="col-md-1">
                            <label class="form-label">&nbsp;</label>
                            <div class="d-grid">
                                <a href="../reports/index.php" class="btn btn-outline-info" title="Reports">
                                    <i class="fas fa-chart-bar"></i>
                                </a>
                            </div>
                        </div>
                    </form>
                </div>

                <!-- Results Info -->
                <div class="d-flex justify-content-between align-items-center mb-3">
                    <h5>
                        <?php echo number_format($total_records); ?> expense(s) found
                        <?php if (!empty($search) || !empty($status_filter)): ?>
                            <small class="text-muted">
                                (filtered
                                <?php if (!empty($search)): ?>
                                    by "<?php echo htmlspecialchars($search); ?>"
                                <?php endif; ?>
                                <?php if (!empty($status_filter)): ?>
                                    status: <?php echo ucfirst($status_filter); ?>
                                <?php endif; ?>
                                )
                            </small>
                        <?php endif; ?>
                    </h5>
                    <?php if ($total_records > 0): ?>
                        <div class="d-flex align-items-center gap-3">
                            <div>
                                Showing <?php echo number_format($pagination['offset'] + 1); ?> -
                                <?php echo number_format(min($pagination['offset'] + $per_page, $total_records)); ?>
                                of <?php echo number_format($total_records); ?>
                            </div>
                            <a href="../api/export_csv.php?<?php echo http_build_query($_GET); ?>"
                               class="btn btn-outline-success btn-sm">
                                <i class="fas fa-download me-1"></i>Export CSV
                            </a>
                        </div>
                    <?php endif; ?>
                </div>

                <!-- Expenses Table -->
                <div class="card">
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table table-striped table-hover">
                                <thead>
                                    <tr>
                                        <th>Expense No.</th>
                                        <th>Job Date</th>
                                        <th>Item</th>
                                        <th>Driver</th>
                                        <th>Transfer Amount</th>
                                        <th>Receipt Numbers</th>
                                        <th>Withdrawal Date</th>
                                        <th>Status</th>
                                        <th>Created By</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php if (empty($expenses)): ?>
                                        <tr>
                                            <td colspan="10" class="text-center py-4">
                                                <div class="text-muted">
                                                    <i class="fas fa-inbox fa-3x mb-3"></i>
                                                    <p>No expenses found</p>
                                                    <?php if (empty($search) && empty($status_filter)): ?>
                                                        <a href="create.php" class="btn btn-primary">
                                                            <i class="fas fa-plus me-1"></i>Create First Expense
                                                        </a>
                                                    <?php else: ?>
                                                        <a href="list.php" class="btn btn-outline-secondary">
                                                            <i class="fas fa-times me-1"></i>Clear Filters
                                                        </a>
                                                    <?php endif; ?>
                                                </div>
                                            </td>
                                        </tr>
                                    <?php else: ?>
                                        <?php foreach ($expenses as $expense): ?>
                                            <tr>
                                                <td>
                                                    <strong><?php echo htmlspecialchars($expense['exno']); ?></strong>
                                                    <?php if (!empty($expense['bookingno'])): ?>
                                                        <br><small class="text-muted">Booking: <?php echo htmlspecialchars($expense['bookingno']); ?></small>
                                                    <?php endif; ?>
                                                </td>
                                                <td><?php echo formatDate($expense['job_open_date']); ?></td>
                                                <td><?php echo htmlspecialchars($expense['item_name'] ?: '-'); ?></td>
                                                <td>
                                                    <?php echo htmlspecialchars($expense['driver_name'] ?: '-'); ?>
                                                    <?php if (!empty($expense['vehicle_plate'])): ?>
                                                        <br><small class="text-muted"><?php echo htmlspecialchars($expense['vehicle_plate']); ?></small>
                                                    <?php endif; ?>
                                                </td>
                                                <td class="text-end">
                                                    <?php if (isset($expense['transfer_amount']) && $expense['transfer_amount'] > 0): ?>
                                                        <span class="text-primary fw-bold"><?php echo number_format($expense['transfer_amount'], 2); ?></span>
                                                        <small class="text-muted d-block">บาท</small>
                                                    <?php else: ?>
                                                        <span class="text-muted">-</span>
                                                    <?php endif; ?>
                                                </td>
                                                <td style="max-width: 200px;">
                                                    <?php
                                                    // Get receipt numbers for this expense
                                                    $stmt = $db->prepare("SELECT receipt_number, amount FROM receipt_numbers WHERE expense_id = ? ORDER BY id");
                                                    $stmt->execute([$expense['id']]);
                                                    $receipts = $stmt->fetchAll();

                                                    if (!empty($receipts)):
                                                        $total_receipt_amount = 0;
                                                        foreach ($receipts as $receipt):
                                                            $total_receipt_amount += $receipt['amount'];
                                                    ?>
                                                        <div class="badge bg-light text-dark me-1 mb-1">
                                                            <?php echo htmlspecialchars($receipt['receipt_number']); ?>
                                                            <small class="text-success">(<?php echo number_format($receipt['amount'], 2); ?>)</small>
                                                        </div>
                                                    <?php endforeach; ?>
                                                        <div class="mt-1">
                                                            <small class="text-muted">
                                                                Total: <span class="text-success fw-bold"><?php echo number_format($total_receipt_amount, 2); ?> บาท</span>
                                                                <!-- <?php //if (isset($expense['transfer_amount']) && abs($expense['transfer_amount'] - $total_receipt_amount) > 0.01): ?>
                                                                    <span class="text-warning">
                                                                        <i class="fas fa-exclamation-triangle"></i> Mismatch
                                                                    </span>
                                                                <?php //endif; ?> -->
                                                            </small>
                                                        </div>
                                                    <?php else: ?>
                                                        <span class="text-muted">No receipts</span>
                                                    <?php endif; ?>
                                                </td>
                                                <td>
                                                    <?php if (!empty($expense['withdrawal_date'])): ?>
                                                        <?php echo formatDate($expense['withdrawal_date']); ?>
                                                    <?php else: ?>
                                                        <span class="text-muted">-</span>
                                                    <?php endif; ?>
                                                </td>
                                                <td><?php echo getStatusBadge($expense['status']); ?></td>
                                                <td><?php echo htmlspecialchars($expense['created_by_name']); ?></td>
                                                <td>
                                                    <div class="btn-group btn-group-sm">
                                                        <a href="view.php?id=<?php echo $expense['id']; ?>" 
                                                           class="btn btn-outline-primary" title="View">
                                                            <i class="fas fa-eye"></i>
                                                        </a>
                                                        <?php if (canEditExpense($expense, $user_id, $user_role)): ?>
                                                            <a href="edit.php?id=<?php echo $expense['id']; ?>"
                                                               class="btn btn-outline-secondary" title="Edit">
                                                                <i class="fas fa-edit"></i>
                                                            </a>
                                                        <?php endif; ?>
                                                        <?php if ($user_role === 'administrator'): ?>
                                                            <button class="btn btn-outline-danger" 
                                                                    onclick="deleteExpense(<?php echo $expense['id']; ?>)" 
                                                                    title="Delete">
                                                                <i class="fas fa-trash"></i>
                                                            </button>
                                                        <?php endif; ?>
                                                    </div>
                                                </td>
                                            </tr>
                                        <?php endforeach; ?>
                                    <?php endif; ?>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>

                <!-- Pagination -->
                <?php if ($pagination['total_pages'] > 1): ?>
                    <nav aria-label="Expenses pagination" class="mt-4">
                        <ul class="pagination justify-content-center">
                            <?php if ($pagination['has_previous']): ?>
                                <li class="page-item">
                                    <a class="page-link" href="?<?php echo http_build_query(array_merge($_GET, ['page' => $pagination['current_page'] - 1])); ?>">
                                        <i class="fas fa-chevron-left"></i>
                                    </a>
                                </li>
                            <?php endif; ?>
                            
                            <?php
                            $start_page = max(1, $pagination['current_page'] - 2);
                            $end_page = min($pagination['total_pages'], $pagination['current_page'] + 2);
                            ?>
                            
                            <?php for ($i = $start_page; $i <= $end_page; $i++): ?>
                                <li class="page-item <?php echo $i === $pagination['current_page'] ? 'active' : ''; ?>">
                                    <a class="page-link" href="?<?php echo http_build_query(array_merge($_GET, ['page' => $i])); ?>">
                                        <?php echo $i; ?>
                                    </a>
                                </li>
                            <?php endfor; ?>
                            
                            <?php if ($pagination['has_next']): ?>
                                <li class="page-item">
                                    <a class="page-link" href="?<?php echo http_build_query(array_merge($_GET, ['page' => $pagination['current_page'] + 1])); ?>">
                                        <i class="fas fa-chevron-right"></i>
                                    </a>
                                </li>
                            <?php endif; ?>
                        </ul>
                    </nav>
                <?php endif; ?>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script>
        function deleteExpense(id) {
            if (confirm('Are you sure you want to delete this expense? This action cannot be undone.')) {
                $.ajax({
                    url: '../api/delete_expense.php',
                    method: 'POST',
                    contentType: 'application/json',
                    data: JSON.stringify({ expense_id: id }),
                    success: function(response) {
                        if (response.success) {
                            location.reload();
                        } else {
                            alert('Error: ' + response.error);
                        }
                    },
                    error: function(xhr) {
                        const response = xhr.responseJSON || {};
                        alert('Error: ' + (response.error || 'Unknown error occurred'));
                    }
                });
            }
        }
    </script>
</body>
</html>
