<?php
require_once 'config/database.php';

try {
    $database = new Database();
    $db = $database->getConnection();
    
    echo "<h2>Database Structure Check</h2>";
    
    // Check expenses table structure
    echo "<h3>Expenses Table Columns:</h3>";
    $stmt = $db->prepare('DESCRIBE expenses');
    $stmt->execute();
    $columns = $stmt->fetchAll();
    
    echo "<table border='1'>";
    echo "<tr><th>Field</th><th>Type</th><th>Null</th><th>Key</th><th>Default</th></tr>";
    foreach ($columns as $column) {
        echo "<tr>";
        echo "<td>" . htmlspecialchars($column['Field']) . "</td>";
        echo "<td>" . htmlspecialchars($column['Type']) . "</td>";
        echo "<td>" . htmlspecialchars($column['Null']) . "</td>";
        echo "<td>" . htmlspecialchars($column['Key']) . "</td>";
        echo "<td>" . htmlspecialchars($column['Default'] ?? 'NULL') . "</td>";
        echo "</tr>";
    }
    echo "</table>";
    
    // Check if transfer_amount exists
    $transfer_amount_exists = false;
    foreach ($columns as $column) {
        if ($column['Field'] === 'transfer_amount') {
            $transfer_amount_exists = true;
            break;
        }
    }
    
    echo "<h3>Transfer Amount Column Status:</h3>";
    if ($transfer_amount_exists) {
        echo "<p style='color: green;'>✓ transfer_amount column EXISTS</p>";
    } else {
        echo "<p style='color: red;'>✗ transfer_amount column MISSING</p>";
        echo "<p><strong>Need to run:</strong> <a href='update_transfer_amount.php'>update_transfer_amount.php</a></p>";
    }
    
    // Check sample data
    echo "<h3>Sample Expense Data:</h3>";
    $stmt = $db->prepare('SELECT id, exno, transfer_no, transfer_amount, total_amount FROM expenses LIMIT 5');
    $stmt->execute();
    $expenses = $stmt->fetchAll();
    
    if (empty($expenses)) {
        echo "<p>No expenses found in database.</p>";
    } else {
        echo "<table border='1'>";
        echo "<tr><th>ID</th><th>EXNO</th><th>Transfer No</th><th>Transfer Amount</th><th>Total Amount</th></tr>";
        foreach ($expenses as $expense) {
            echo "<tr>";
            echo "<td>" . htmlspecialchars($expense['id']) . "</td>";
            echo "<td>" . htmlspecialchars($expense['exno']) . "</td>";
            echo "<td>" . htmlspecialchars($expense['transfer_no'] ?? 'NULL') . "</td>";
            echo "<td>" . htmlspecialchars($expense['transfer_amount'] ?? 'NULL') . "</td>";
            echo "<td>" . htmlspecialchars($expense['total_amount'] ?? 'NULL') . "</td>";
            echo "</tr>";
        }
        echo "</table>";
    }
    
    // Check receipt_numbers table
    echo "<h3>Receipt Numbers Table:</h3>";
    $stmt = $db->prepare('SELECT COUNT(*) as count FROM receipt_numbers');
    $stmt->execute();
    $result = $stmt->fetch();
    echo "<p>Total receipt numbers: " . $result['count'] . "</p>";
    
    if ($result['count'] > 0) {
        $stmt = $db->prepare('SELECT * FROM receipt_numbers LIMIT 5');
        $stmt->execute();
        $receipts = $stmt->fetchAll();
        
        echo "<table border='1'>";
        echo "<tr><th>ID</th><th>Expense ID</th><th>Receipt Number</th><th>Amount</th><th>Description</th></tr>";
        foreach ($receipts as $receipt) {
            echo "<tr>";
            echo "<td>" . htmlspecialchars($receipt['id']) . "</td>";
            echo "<td>" . htmlspecialchars($receipt['expense_id']) . "</td>";
            echo "<td>" . htmlspecialchars($receipt['receipt_number']) . "</td>";
            echo "<td>" . htmlspecialchars($receipt['amount']) . "</td>";
            echo "<td>" . htmlspecialchars($receipt['description'] ?? '') . "</td>";
            echo "</tr>";
        }
        echo "</table>";
    }
    
} catch (Exception $e) {
    echo "<h3 style='color: red;'>Error:</h3>";
    echo "<p>" . htmlspecialchars($e->getMessage()) . "</p>";
}
?>
<!DOCTYPE html>
<html>
<head>
    <title>Database Debug</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        table { border-collapse: collapse; margin: 10px 0; }
        th, td { padding: 8px; text-align: left; }
        th { background-color: #f2f2f2; }
    </style>
</head>
<body>
    <a href="reports/receipt_summary.php">← Back to Receipt Summary</a>
</body>
</html>
