<?php
/**
 * Production Readiness Test
 * Final comprehensive test before deployment
 */

session_start();
require_once 'config/database.php';
require_once 'includes/TransactionHelper.php';
require_once 'includes/functions.php';

// Mock admin session
$_SESSION['user_id'] = 1;
$_SESSION['role'] = 'administrator';

echo "<h1>🎯 Production Readiness Test</h1>";
echo "<p><strong>Target:</strong> PHP 8.1.33 + MariaDB 5.5.68</p>";

$test_results = [];
$all_passed = true;

try {
    $database = new Database();
    $db = $database->getConnection();
    
    // Test 1: Core Database Operations
    echo "<h2>1. 🗄️ Core Database Operations</h2>";
    try {
        // Test basic CRUD
        $stmt = $db->prepare("SELECT COUNT(*) FROM users WHERE role = ?");
        $stmt->execute(['administrator']);
        $admin_count = $stmt->fetchColumn();
        
        if ($admin_count > 0) {
            echo "<p>✅ Admin users exist ({$admin_count})</p>";
            $test_results['admin_users'] = true;
        } else {
            echo "<p>❌ No admin users found</p>";
            $test_results['admin_users'] = false;
            $all_passed = false;
        }
        
        // Test transactions
        $transaction = createTransactionHelper($db);
        $transaction->beginTransaction();
        $transaction->rollback();
        echo "<p>✅ Transaction system working</p>";
        $test_results['transactions'] = true;
        
    } catch (Exception $e) {
        echo "<p>❌ Database operations failed: " . $e->getMessage() . "</p>";
        $test_results['database'] = false;
        $all_passed = false;
    }
    
    // Test 2: File System Operations
    echo "<h2>2. 📁 File System Operations</h2>";
    try {
        $upload_dirs = [
            'uploads/receipts',
            'uploads/transfer_slips',
            'uploads/verification_slips',
            'uploads/review_slips',
            'uploads/batch_documents',
            'uploads/bulk_operations'
        ];
        
        $missing_dirs = [];
        foreach ($upload_dirs as $dir) {
            if (!is_dir($dir)) {
                $missing_dirs[] = $dir;
            }
        }
        
        if (empty($missing_dirs)) {
            echo "<p>✅ All upload directories exist</p>";
            $test_results['upload_dirs'] = true;
        } else {
            echo "<p>❌ Missing directories: " . implode(', ', $missing_dirs) . "</p>";
            $test_results['upload_dirs'] = false;
            $all_passed = false;
        }
        
        // Test file permissions
        if (is_writable('uploads/')) {
            echo "<p>✅ Upload directory is writable</p>";
            $test_results['file_permissions'] = true;
        } else {
            echo "<p>❌ Upload directory is not writable</p>";
            $test_results['file_permissions'] = false;
            $all_passed = false;
        }
        
    } catch (Exception $e) {
        echo "<p>❌ File system test failed: " . $e->getMessage() . "</p>";
        $test_results['filesystem'] = false;
        $all_passed = false;
    }
    
    // Test 3: Expense Workflow
    echo "<h2>3. 💼 Expense Workflow</h2>";
    try {
        // Test expense creation
        $transaction = createTransactionHelper($db);
        $transaction->beginTransaction();
        
        $stmt = $db->prepare("
            INSERT INTO expenses (exno, customer_id, item_id, transfer_amount, status, created_by, created_at) 
            VALUES (?, 1, 1, 1000.00, 'open', 1, NOW())
        ");
        $test_exno = 'PT' . date('His'); // Shorter test expense number
        $stmt->execute([$test_exno]);
        $test_expense_id = $db->lastInsertId();
        
        // Test status update
        $stmt = $db->prepare("UPDATE expenses SET status = 'pending' WHERE id = ?");
        $stmt->execute([$test_expense_id]);
        
        // Test cleanup
        $stmt = $db->prepare("DELETE FROM expenses WHERE id = ?");
        $stmt->execute([$test_expense_id]);
        
        $transaction->commit();
        
        echo "<p>✅ Expense workflow operations working</p>";
        $test_results['expense_workflow'] = true;
        
    } catch (Exception $e) {
        if (isset($transaction) && $transaction->inTransaction()) {
            $transaction->rollback();
        }
        echo "<p>❌ Expense workflow failed: " . $e->getMessage() . "</p>";
        $test_results['expense_workflow'] = false;
        $all_passed = false;
    }
    
    // Test 4: Batch Operations
    echo "<h2>4. 📦 Batch Operations</h2>";
    try {
        // Test batch table exists and is accessible
        $stmt = $db->query("SELECT COUNT(*) FROM batch_operations");
        $batch_count = $stmt->fetchColumn();
        
        $stmt = $db->query("SELECT COUNT(*) FROM batch_documents");
        $doc_count = $stmt->fetchColumn();
        
        echo "<p>✅ Batch operations table accessible ({$batch_count} operations)</p>";
        echo "<p>✅ Batch documents table accessible ({$doc_count} documents)</p>";
        $test_results['batch_operations'] = true;
        
    } catch (Exception $e) {
        echo "<p>❌ Batch operations failed: " . $e->getMessage() . "</p>";
        $test_results['batch_operations'] = false;
        $all_passed = false;
    }
    
    // Test 5: User Authentication & Authorization
    echo "<h2>5. 🔐 Authentication & Authorization</h2>";
    try {
        // Test role checking
        $roles = ['data_entry', 'verification', 'reviewer', 'report_viewer', 'administrator'];
        $role_counts = [];
        
        foreach ($roles as $role) {
            $stmt = $db->prepare("SELECT COUNT(*) FROM users WHERE role = ?");
            $stmt->execute([$role]);
            $count = $stmt->fetchColumn();
            $role_counts[$role] = $count;
        }
        
        if ($role_counts['administrator'] > 0) {
            echo "<p>✅ Admin users available</p>";
            $test_results['admin_auth'] = true;
        } else {
            echo "<p>❌ No admin users available</p>";
            $test_results['admin_auth'] = false;
            $all_passed = false;
        }
        
        // Test session handling
        if (isset($_SESSION['user_id'])) {
            echo "<p>✅ Session handling working</p>";
            $test_results['sessions'] = true;
        } else {
            echo "<p>⚠️ Session test inconclusive</p>";
            $test_results['sessions'] = true; // Not critical for this test
        }
        
    } catch (Exception $e) {
        echo "<p>❌ Authentication test failed: " . $e->getMessage() . "</p>";
        $test_results['authentication'] = false;
        $all_passed = false;
    }
    
    // Test 6: Image Processing
    echo "<h2>6. 🖼️ Image Processing</h2>";
    try {
        if (extension_loaded('gd')) {
            echo "<p>✅ GD extension loaded</p>";
            
            // Test image functions
            $functions = ['imagecreatefromjpeg', 'imagecreatefrompng', 'imagejpeg', 'imagepng'];
            $missing_functions = [];
            
            foreach ($functions as $func) {
                if (!function_exists($func)) {
                    $missing_functions[] = $func;
                }
            }
            
            if (empty($missing_functions)) {
                echo "<p>✅ All image functions available</p>";
                $test_results['image_processing'] = true;
            } else {
                echo "<p>⚠️ Missing image functions: " . implode(', ', $missing_functions) . "</p>";
                $test_results['image_processing'] = false;
            }
        } else {
            echo "<p>⚠️ GD extension not loaded (image compression disabled)</p>";
            $test_results['image_processing'] = false;
        }
        
    } catch (Exception $e) {
        echo "<p>❌ Image processing test failed: " . $e->getMessage() . "</p>";
        $test_results['image_processing'] = false;
    }
    
    // Test 7: CSV Export
    echo "<h2>7. 📊 CSV Export</h2>";
    try {
        if (function_exists('fputcsv')) {
            // Test CSV generation
            $temp_file = tempnam(sys_get_temp_dir(), 'csv_test');
            $handle = fopen($temp_file, 'w');
            
            if ($handle) {
                fputcsv($handle, ['Test', 'CSV', 'Export']);
                fclose($handle);
                unlink($temp_file);
                
                echo "<p>✅ CSV export functionality working</p>";
                $test_results['csv_export'] = true;
            } else {
                echo "<p>❌ Cannot create temporary CSV file</p>";
                $test_results['csv_export'] = false;
                $all_passed = false;
            }
        } else {
            echo "<p>❌ fputcsv function not available</p>";
            $test_results['csv_export'] = false;
            $all_passed = false;
        }
        
    } catch (Exception $e) {
        echo "<p>❌ CSV export test failed: " . $e->getMessage() . "</p>";
        $test_results['csv_export'] = false;
        $all_passed = false;
    }
    
    // Test 8: MariaDB 5.5.68 Compatibility
    echo "<h2>8. 🔧 MariaDB 5.5.68 Compatibility</h2>";
    try {
        // Test that we're not using JSON functions
        $json_test_passed = true;
        
        try {
            $stmt = $db->query("SELECT JSON_LENGTH('[1,2,3]')");
            echo "<p>⚠️ JSON functions available (will need fallback in production)</p>";
        } catch (Exception $e) {
            echo "<p>✅ JSON functions not available (compatible with MariaDB 5.5.68)</p>";
        }
        
        // Test TEXT-based JSON storage
        $stmt = $db->query("SHOW COLUMNS FROM expenses LIKE 'workflow_history'");
        $column = $stmt->fetch();
        
        if ($column && strpos($column['Type'], 'text') !== false) {
            echo "<p>✅ workflow_history uses TEXT (MariaDB 5.5.68 compatible)</p>";
            $test_results['mariadb_compatibility'] = true;
        } else {
            echo "<p>⚠️ workflow_history column type may need adjustment</p>";
            $test_results['mariadb_compatibility'] = false;
        }
        
    } catch (Exception $e) {
        echo "<p>❌ MariaDB compatibility test failed: " . $e->getMessage() . "</p>";
        $test_results['mariadb_compatibility'] = false;
        $all_passed = false;
    }
    
} catch (Exception $e) {
    echo "<p>❌ <strong>Critical Error:</strong> " . $e->getMessage() . "</p>";
    $all_passed = false;
}

// Final Results
echo "<h2>📋 Test Results Summary</h2>";

$passed_tests = array_sum($test_results);
$total_tests = count($test_results);

echo "<p><strong>Tests Passed:</strong> {$passed_tests}/{$total_tests}</p>";

echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
echo "<tr><th>Test</th><th>Status</th></tr>";
foreach ($test_results as $test => $result) {
    $status = $result ? '✅ PASS' : '❌ FAIL';
    $color = $result ? 'green' : 'red';
    echo "<tr><td>{$test}</td><td style='color: {$color};'>{$status}</td></tr>";
}
echo "</table>";

// Final Verdict
echo "<h2>🎯 Final Verdict</h2>";

if ($all_passed && $passed_tests >= 7) {
    echo "<div style='background: #d4edda; border: 1px solid #c3e6cb; padding: 20px; border-radius: 5px; margin: 20px 0;'>";
    echo "<h3 style='color: #155724; margin: 0;'>🚀 PRODUCTION READY!</h3>";
    echo "<p style='color: #155724; margin: 10px 0 0 0;'>All critical tests passed. System is ready for deployment to PHP 8.1.33 + MariaDB 5.5.68 environment.</p>";
    echo "</div>";
} elseif ($passed_tests >= 6) {
    echo "<div style='background: #fff3cd; border: 1px solid #ffeaa7; padding: 20px; border-radius: 5px; margin: 20px 0;'>";
    echo "<h3 style='color: #856404; margin: 0;'>⚠️ READY WITH MINOR ISSUES</h3>";
    echo "<p style='color: #856404; margin: 10px 0 0 0;'>Most tests passed. Minor issues should be addressed but system is functional.</p>";
    echo "</div>";
} else {
    echo "<div style='background: #f8d7da; border: 1px solid #f5c6cb; padding: 20px; border-radius: 5px; margin: 20px 0;'>";
    echo "<h3 style='color: #721c24; margin: 0;'>❌ NOT READY</h3>";
    echo "<p style='color: #721c24; margin: 10px 0 0 0;'>Critical issues found. Please resolve before deployment.</p>";
    echo "</div>";
}

echo "<h3>📚 Next Steps:</h3>";
echo "<ol>";
echo "<li>Review <a href='DEPLOYMENT_CHECKLIST.md' target='_blank'>Deployment Checklist</a></li>";
echo "<li>Follow <a href='PRODUCTION_DEPLOYMENT.md' target='_blank'>Production Deployment Guide</a></li>";
echo "<li>Run <a href='setup_production.php' target='_blank'>Production Setup</a> on target server</li>";
echo "<li>Remove test files after deployment</li>";
echo "</ol>";

echo "<p><small>Test completed on: " . date('Y-m-d H:i:s') . "</small></p>";
?>
