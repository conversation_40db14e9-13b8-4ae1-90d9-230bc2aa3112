<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Receipt Deductions System Test</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        .deduction-item {
            transition: all 0.3s ease;
        }
        
        .deduction-item:hover {
            box-shadow: 0 4px 8px rgba(0,0,0,0.1);
        }
        
        .summary-card {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }
        
        .summary-card .card-body {
            padding: 1.5rem;
        }
        
        .amount-display {
            font-size: 1.2rem;
            font-weight: bold;
        }
        
        .progress-custom {
            height: 8px;
            background-color: rgba(255,255,255,0.3);
        }
        
        .progress-custom .progress-bar {
            background-color: #ffc107;
        }
        
        .demo-section {
            margin-bottom: 30px;
            padding: 20px;
            border: 2px dashed #dee2e6;
            border-radius: 10px;
        }
        
        .feature-highlight {
            background: #f8f9fa;
            border-left: 4px solid #007bff;
            padding: 15px;
            margin: 15px 0;
        }
    </style>
</head>
<body>
    <div class="container mt-4">
        <h1><i class="fas fa-calculator me-2"></i>Receipt Deductions System Test</h1>
        <p class="text-muted">Testing the new receipt deductions functionality with database integration</p>
        
        <!-- System Overview -->
        <div class="demo-section">
            <h4><i class="fas fa-info-circle me-2"></i>System Overview</h4>
            <div class="row">
                <div class="col-md-6">
                    <div class="feature-highlight">
                        <h6><i class="fas fa-database me-1"></i>Database Structure</h6>
                        <ul class="mb-0">
                            <li><strong>receipt_numbers</strong> - Updated with gross_amount, has_deductions</li>
                            <li><strong>receipt_deductions</strong> - New table for deduction details</li>
                            <li><strong>receipt_summary</strong> - View for easy querying</li>
                            <li><strong>Stored Procedures</strong> - AddReceiptDeduction, UpdateReceiptCalculations</li>
                            <li><strong>Triggers</strong> - Auto-update calculations</li>
                        </ul>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="feature-highlight">
                        <h6><i class="fas fa-cogs me-1"></i>Features</h6>
                        <ul class="mb-0">
                            <li><strong>Multiple Deduction Types</strong> - VAT, Withholding Tax, Fees, etc.</li>
                            <li><strong>Percentage & Fixed Amount</strong> - Flexible calculation methods</li>
                            <li><strong>Auto Calculation</strong> - Real-time updates</li>
                            <li><strong>Audit Trail</strong> - Track who created/modified</li>
                            <li><strong>Data Validation</strong> - Server & client-side validation</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Receipt Summary -->
        <div class="demo-section">
            <h4><i class="fas fa-receipt me-2"></i>Receipt Summary</h4>
            <div class="row">
                <div class="col-md-8">
                    <div class="card summary-card">
                        <div class="card-body">
                            <div class="row text-center">
                                <div class="col-md-4">
                                    <div class="mb-2">
                                        <i class="fas fa-money-bill-wave fa-2x mb-2"></i>
                                        <h6>ยอดก่อนหัก</h6>
                                        <div class="amount-display" id="gross-amount-display">10,000.00</div>
                                        <small>บาท</small>
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="mb-2">
                                        <i class="fas fa-minus-circle fa-2x mb-2"></i>
                                        <h6>รายการหัก</h6>
                                        <div class="amount-display" id="total-deductions-display">300.00</div>
                                        <small>บาท</small>
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="mb-2">
                                        <i class="fas fa-equals fa-2x mb-2"></i>
                                        <h6>ยอดสุทธิ</h6>
                                        <div class="amount-display" id="net-amount-display">9,700.00</div>
                                        <small>บาท</small>
                                    </div>
                                </div>
                            </div>
                            <div class="mt-3">
                                <div class="d-flex justify-content-between align-items-center mb-2">
                                    <small>การหัก</small>
                                    <small id="deduction-percentage">3.0%</small>
                                </div>
                                <div class="progress progress-custom">
                                    <div class="progress-bar" id="deduction-progress" style="width: 3%"></div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="card">
                        <div class="card-body">
                            <h6><i class="fas fa-edit me-1"></i>แก้ไขยอดก่อนหัก</h6>
                            <div class="input-group">
                                <input type="number" class="form-control" id="gross_amount" value="10000" step="0.01">
                                <span class="input-group-text">บาท</span>
                            </div>
                            <small class="text-muted">เปลี่ยนยอดเพื่อทดสอบการคำนวณ</small>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Deductions List -->
        <div class="demo-section">
            <div class="d-flex justify-content-between align-items-center mb-3">
                <h4><i class="fas fa-list me-2"></i>รายการหัก</h4>
                <button type="button" class="btn btn-primary add-deduction-btn">
                    <i class="fas fa-plus me-1"></i>เพิ่มรายการหัก
                </button>
            </div>
            
            <div id="deductions-container">
                <!-- Sample deductions for demo -->
                <div class="deduction-item card mb-2">
                    <div class="card-body p-3">
                        <div class="row align-items-center">
                            <div class="col-md-3">
                                <strong>ภาษีหัก ณ ที่จ่าย</strong>
                                <br><small class="text-muted">3%</small>
                            </div>
                            <div class="col-md-3">
                                <span class="text-success fw-bold">300.00 บาท</span>
                            </div>
                            <div class="col-md-4">
                                <small class="text-muted">ภาษีหัก ณ ที่จ่าย 3% ตามกฎหมาย</small>
                            </div>
                            <div class="col-md-2 text-end">
                                <div class="btn-group btn-group-sm">
                                    <button type="button" class="btn btn-outline-primary edit-deduction-btn" 
                                            data-deduction-id="1" title="แก้ไข">
                                        <i class="fas fa-edit"></i>
                                    </button>
                                    <button type="button" class="btn btn-outline-danger delete-deduction-btn" 
                                            data-deduction-id="1" title="ลบ">
                                        <i class="fas fa-trash"></i>
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- API Testing -->
        <div class="demo-section">
            <h4><i class="fas fa-code me-2"></i>API Testing</h4>
            <div class="row">
                <div class="col-md-6">
                    <h6>Available Endpoints:</h6>
                    <ul class="list-group">
                        <li class="list-group-item">
                            <strong>GET</strong> <code>/api/receipt_deductions.php?receipt_id=1</code>
                            <br><small class="text-muted">Get deductions for receipt</small>
                        </li>
                        <li class="list-group-item">
                            <strong>POST</strong> <code>/api/receipt_deductions.php</code>
                            <br><small class="text-muted">Add new deduction</small>
                        </li>
                        <li class="list-group-item">
                            <strong>PUT</strong> <code>/api/receipt_deductions.php</code>
                            <br><small class="text-muted">Update deduction</small>
                        </li>
                        <li class="list-group-item">
                            <strong>DELETE</strong> <code>/api/receipt_deductions.php?deduction_id=1</code>
                            <br><small class="text-muted">Delete deduction</small>
                        </li>
                    </ul>
                </div>
                <div class="col-md-6">
                    <h6>Test Buttons:</h6>
                    <div class="d-grid gap-2">
                        <button type="button" class="btn btn-outline-info" onclick="testGetDeductions()">
                            <i class="fas fa-download me-1"></i>Test GET Deductions
                        </button>
                        <button type="button" class="btn btn-outline-success" onclick="testAddDeduction()">
                            <i class="fas fa-plus me-1"></i>Test ADD Deduction
                        </button>
                        <button type="button" class="btn btn-outline-warning" onclick="testUpdateDeduction()">
                            <i class="fas fa-edit me-1"></i>Test UPDATE Deduction
                        </button>
                        <button type="button" class="btn btn-outline-danger" onclick="testDeleteDeduction()">
                            <i class="fas fa-trash me-1"></i>Test DELETE Deduction
                        </button>
                    </div>
                </div>
            </div>
            
            <div class="mt-3">
                <h6>API Response:</h6>
                <pre id="api-response" class="bg-light p-3 rounded" style="max-height: 300px; overflow-y: auto;">
Ready for testing...
                </pre>
            </div>
        </div>
        
        <!-- Implementation Guide -->
        <div class="demo-section">
            <h4><i class="fas fa-book me-2"></i>Implementation Guide</h4>
            <div class="row">
                <div class="col-md-6">
                    <h6>1. Database Migration:</h6>
                    <pre class="bg-light p-3 rounded"><code>mysql -u root -p expenses_system < database/migrations/add_receipt_deductions_system.sql</code></pre>
                    
                    <h6 class="mt-3">2. PHP Integration:</h6>
                    <pre class="bg-light p-3 rounded"><code>require_once 'includes/ReceiptDeductionManager.php';
$manager = new ReceiptDeductionManager($db);
$deductions = $manager->getReceiptDeductions($receiptId);</code></pre>
                </div>
                <div class="col-md-6">
                    <h6>3. JavaScript Integration:</h6>
                    <pre class="bg-light p-3 rounded"><code>&lt;script src="assets/js/receipt-deductions.js"&gt;&lt;/script&gt;
&lt;script&gt;
// Load deductions for receipt
receiptDeductionManager.loadReceiptDeductions(receiptId);
&lt;/script&gt;</code></pre>
                    
                    <h6 class="mt-3">4. HTML Structure:</h6>
                    <pre class="bg-light p-3 rounded"><code>&lt;div id="deductions-container"&gt;&lt;/div&gt;
&lt;button class="add-deduction-btn"&gt;Add Deduction&lt;/button&gt;</code></pre>
                </div>
            </div>
        </div>
    </div>

    <!-- Deduction Modal -->
    <div class="modal fade" id="deductionModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="deductionModalTitle">เพิ่มรายการหัก</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <div id="form-errors"></div>
                    <form id="deduction-form">
                        <div class="mb-3">
                            <label for="deduction_type" class="form-label">ประเภทการหัก</label>
                            <select class="form-select" id="deduction_type" required>
                                <option value="">เลือกประเภท</option>
                                <option value="tax_vat">ภาษีมูลค่าเพิ่ม (VAT)</option>
                                <option value="tax_withholding">ภาษีหัก ณ ที่จ่าย</option>
                                <option value="service_fee">ค่าธรรมเนียม</option>
                                <option value="discount">ส่วนลด</option>
                                <option value="penalty">ค่าปรับ</option>
                                <option value="commission">ค่าคอมมิชชั่น</option>
                                <option value="other">อื่นๆ</option>
                            </select>
                        </div>
                        
                        <div class="mb-3">
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="is_percentage_based">
                                <label class="form-check-label" for="is_percentage_based">
                                    คำนวณจากเปอร์เซ็นต์
                                </label>
                            </div>
                        </div>
                        
                        <div class="mb-3" id="amount-field">
                            <label for="deduction_amount" class="form-label">จำนวนเงิน</label>
                            <div class="input-group">
                                <input type="number" class="form-control" id="deduction_amount" step="0.01" min="0">
                                <span class="input-group-text">บาท</span>
                            </div>
                        </div>
                        
                        <div class="mb-3" id="percentage-field" style="display: none;">
                            <label for="deduction_percentage" class="form-label">เปอร์เซ็นต์</label>
                            <div class="input-group">
                                <input type="number" class="form-control" id="deduction_percentage" step="0.01" min="0" max="100">
                                <span class="input-group-text">%</span>
                            </div>
                            <div class="mt-2">
                                <small class="text-muted">จำนวนที่คำนวณได้: <span id="calculated-amount-display">0.00 บาท</span></small>
                            </div>
                        </div>
                        
                        <div class="mb-3">
                            <label for="deduction_description" class="form-label">รายละเอียด</label>
                            <textarea class="form-control" id="deduction_description" rows="2"></textarea>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">ยกเลิก</button>
                    <button type="button" class="btn btn-primary" id="save-deduction-btn">บันทึก</button>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script src="assets/js/receipt-deductions.js"></script>
    
    <script>
        // Demo functions for testing
        function testGetDeductions() {
            fetch('api/receipt_deductions.php?receipt_id=1')
                .then(response => response.json())
                .then(data => {
                    document.getElementById('api-response').textContent = JSON.stringify(data, null, 2);
                })
                .catch(error => {
                    document.getElementById('api-response').textContent = 'Error: ' + error.message;
                });
        }
        
        function testAddDeduction() {
            const testData = {
                receipt_id: 1,
                deduction_data: {
                    deduction_type: 'tax_withholding',
                    amount: 300,
                    percentage: 3,
                    description: 'Test deduction',
                    is_percentage_based: true
                }
            };
            
            fetch('api/receipt_deductions.php', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(testData)
            })
            .then(response => response.json())
            .then(data => {
                document.getElementById('api-response').textContent = JSON.stringify(data, null, 2);
            })
            .catch(error => {
                document.getElementById('api-response').textContent = 'Error: ' + error.message;
            });
        }
        
        function testUpdateDeduction() {
            alert('Update test requires existing deduction ID');
        }
        
        function testDeleteDeduction() {
            alert('Delete test requires existing deduction ID');
        }
        
        // Initialize demo
        $(document).ready(function() {
            // Set current receipt ID for demo
            if (window.receiptDeductionManager) {
                receiptDeductionManager.currentReceiptId = 1;
            }
            
            // Show success message
            setTimeout(function() {
                const toast = `
                    <div class="toast-container position-fixed bottom-0 end-0 p-3">
                        <div class="toast show" role="alert">
                            <div class="toast-header">
                                <i class="fas fa-check-circle text-success me-2"></i>
                                <strong class="me-auto">System Ready</strong>
                                <button type="button" class="btn-close" data-bs-dismiss="toast"></button>
                            </div>
                            <div class="toast-body">
                                Receipt Deductions System is loaded and ready for testing!
                            </div>
                        </div>
                    </div>
                `;
                $('body').append(toast);
                
                setTimeout(function() {
                    $('.toast').toast('hide');
                }, 3000);
            }, 1000);
        });
    </script>
</body>
</html>
