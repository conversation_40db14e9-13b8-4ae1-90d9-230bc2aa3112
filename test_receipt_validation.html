<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Receipt Number Validation</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        /* Receipt number validation styling */
        .receipt-number-input.is-invalid {
            border-color: #dc3545;
            background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 12 12' width='12' height='12' fill='none' stroke='%23dc3545'%3e%3ccircle cx='6' cy='6' r='4.5'/%3e%3cpath d='m5.8 3.6h.4L6 6.5z'/%3e%3ccircle cx='6' cy='8.2' r='.6' fill='%23dc3545' stroke='none'/%3e%3c/svg%3e");
            background-repeat: no-repeat;
            background-position: right calc(0.375em + 0.1875rem) center;
            background-size: calc(0.75em + 0.375rem) calc(0.75em + 0.375rem);
        }
        
        .receipt-number-input.is-valid {
            border-color: #198754;
            background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 8 8'%3e%3cpath fill='%23198754' d='m2.3 6.73.94-.94 1.93 1.93 3.53-3.53.94.94L4.16 9.66z'/%3e%3c/svg%3e");
            background-repeat: no-repeat;
            background-position: right calc(0.375em + 0.1875rem) center;
            background-size: calc(0.75em + 0.375rem) calc(0.75em + 0.375rem);
        }
        
        .invalid-feedback {
            display: block;
            width: 100%;
            margin-top: 0.25rem;
            font-size: 0.875em;
            color: #dc3545;
        }
        
        .valid-feedback {
            display: block;
            width: 100%;
            margin-top: 0.25rem;
            font-size: 0.875em;
            color: #198754;
        }
        
        .receipt-number-checking {
            position: relative;
        }
        
        .receipt-number-checking::after {
            content: '';
            position: absolute;
            right: 10px;
            top: 50%;
            transform: translateY(-50%);
            width: 16px;
            height: 16px;
            border: 2px solid #f3f3f3;
            border-top: 2px solid #007bff;
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }
        
        @keyframes spin {
            0% { transform: translateY(-50%) rotate(0deg); }
            100% { transform: translateY(-50%) rotate(360deg); }
        }
    </style>
</head>
<body>
    <div class="container mt-4">
        <div class="row">
            <div class="col-md-8 mx-auto">
                <div class="card">
                    <div class="card-header">
                        <h4><i class="fas fa-test-tube me-2"></i>Test Receipt Number Validation</h4>
                    </div>
                    <div class="card-body">
                        <div class="alert alert-info">
                            <h6><i class="fas fa-info-circle me-2"></i>Test Instructions:</h6>
                            <ul class="mb-0">
                                <li><strong>Existing Receipt Numbers:</strong> Try "1111" or "2222" (should show error)</li>
                                <li><strong>New Receipt Numbers:</strong> Try "TEST123" or any new number (should show success)</li>
                                <li><strong>Duplicate in Form:</strong> Enter same number in multiple fields (should show error)</li>
                                <li><strong>Empty Fields:</strong> Leave fields empty (should show required error)</li>
                            </ul>
                        </div>
                        
                        <form id="test-form">
                            <div id="receipt-numbers">
                                <!-- Receipt 1 -->
                                <div class="card mb-3">
                                    <div class="card-header">
                                        <h6 class="mb-0"><i class="fas fa-receipt"></i> Receipt 1</h6>
                                    </div>
                                    <div class="card-body">
                                        <div class="row">
                                            <div class="col-md-6">
                                                <label class="form-label">Receipt Number *</label>
                                                <input type="text" class="form-control receipt-number-input" name="receipt_numbers[]"
                                                       placeholder="เลขที่ใบเสร็จ" required>
                                                <div class="invalid-feedback"></div>
                                                <div class="valid-feedback"></div>
                                            </div>
                                            <div class="col-md-6">
                                                <label class="form-label">Amount (บาท) *</label>
                                                <input type="number" class="form-control" name="receipt_amounts[]"
                                                       placeholder="0.00" step="0.01" min="0" required>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                
                                <!-- Receipt 2 -->
                                <div class="card mb-3">
                                    <div class="card-header">
                                        <h6 class="mb-0"><i class="fas fa-receipt"></i> Receipt 2</h6>
                                    </div>
                                    <div class="card-body">
                                        <div class="row">
                                            <div class="col-md-6">
                                                <label class="form-label">Receipt Number *</label>
                                                <input type="text" class="form-control receipt-number-input" name="receipt_numbers[]"
                                                       placeholder="เลขที่ใบเสร็จ" required>
                                                <div class="invalid-feedback"></div>
                                                <div class="valid-feedback"></div>
                                            </div>
                                            <div class="col-md-6">
                                                <label class="form-label">Amount (บาท) *</label>
                                                <input type="number" class="form-control" name="receipt_amounts[]"
                                                       placeholder="0.00" step="0.01" min="0" required>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                
                                <!-- Receipt 3 -->
                                <div class="card mb-3">
                                    <div class="card-header">
                                        <h6 class="mb-0"><i class="fas fa-receipt"></i> Receipt 3</h6>
                                    </div>
                                    <div class="card-body">
                                        <div class="row">
                                            <div class="col-md-6">
                                                <label class="form-label">Receipt Number *</label>
                                                <input type="text" class="form-control receipt-number-input" name="receipt_numbers[]"
                                                       placeholder="เลขที่ใบเสร็จ" required>
                                                <div class="invalid-feedback"></div>
                                                <div class="valid-feedback"></div>
                                            </div>
                                            <div class="col-md-6">
                                                <label class="form-label">Amount (บาท) *</label>
                                                <input type="number" class="form-control" name="receipt_amounts[]"
                                                       placeholder="0.00" step="0.01" min="0" required>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="d-flex justify-content-between">
                                <button type="button" class="btn btn-secondary" onclick="clearForm()">
                                    <i class="fas fa-undo me-1"></i>Clear Form
                                </button>
                                <button type="submit" class="btn btn-primary" id="submit-btn">
                                    <i class="fas fa-save me-1"></i>Test Submit
                                </button>
                            </div>
                        </form>
                        
                        <div id="validation-status" class="mt-4"></div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        $(document).ready(function() {
            // Add event listeners for receipt number validation
            $('.receipt-number-input').on('input blur', validateReceiptNumbers);
            
            // Form submission
            $('#test-form').on('submit', function(e) {
                e.preventDefault();
                
                const hasInvalidInputs = $('.receipt-number-input.is-invalid').length > 0;
                const emptyInputs = $('.receipt-number-input').filter(function() {
                    return !$(this).val().trim();
                });
                
                if (hasInvalidInputs || emptyInputs.length > 0) {
                    $('#validation-status').html(`
                        <div class="alert alert-danger">
                            <i class="fas fa-exclamation-triangle me-2"></i>
                            <strong>Form validation failed!</strong><br>
                            Please fix all receipt number errors before submitting.
                        </div>
                    `);
                } else {
                    $('#validation-status').html(`
                        <div class="alert alert-success">
                            <i class="fas fa-check-circle me-2"></i>
                            <strong>Form validation passed!</strong><br>
                            All receipt numbers are valid and unique.
                        </div>
                    `);
                }
            });
        });
        
        // Validation functions (copied from expense-form.js)
        function validateReceiptNumbers() {
            const receiptNumbers = [];
            const inputs = $('.receipt-number-input');
            let hasDuplicates = false;
            
            // Clear previous validation states
            inputs.removeClass('is-invalid is-valid').siblings('.invalid-feedback, .valid-feedback').text('');
            
            inputs.each(function() {
                const value = $(this).val().trim();
                const input = $(this);
                
                if (value) {
                    // Check if this receipt number already exists in the form
                    const duplicateIndex = receiptNumbers.indexOf(value);
                    if (duplicateIndex !== -1) {
                        // Mark both inputs as invalid
                        input.addClass('is-invalid');
                        input.siblings('.invalid-feedback').text('Receipt number already used in this form');
                        
                        // Also mark the first occurrence
                        inputs.eq(duplicateIndex).addClass('is-invalid');
                        inputs.eq(duplicateIndex).siblings('.invalid-feedback').text('Receipt number already used in this form');
                        
                        hasDuplicates = true;
                    } else {
                        // Check if receipt number exists in database
                        checkReceiptNumberInDatabase(value, input);
                    }
                    
                    receiptNumbers.push(value);
                }
            });
            
            // Update submit button state
            updateSubmitButtonState();
            
            return !hasDuplicates;
        }
        
        function checkReceiptNumberInDatabase(receiptNumber, inputElement) {
            // Add loading indicator
            inputElement.addClass('receipt-number-checking');
            
            // Clear previous timeout if exists
            const timeoutId = inputElement.data('timeout-id');
            if (timeoutId) {
                clearTimeout(timeoutId);
            }
            
            // Debounce the API call
            const newTimeoutId = setTimeout(function() {
                $.ajax({
                    url: 'api/check_receipt_number.php',
                    method: 'POST',
                    data: { receipt_number: receiptNumber },
                    dataType: 'json',
                    success: function(response) {
                        inputElement.removeClass('receipt-number-checking');
                        
                        if (response.exists) {
                            inputElement.addClass('is-invalid').removeClass('is-valid');
                            inputElement.siblings('.invalid-feedback').text(`Receipt number already exists in expense: ${response.expense_no}`);
                        } else {
                            inputElement.removeClass('is-invalid').addClass('is-valid');
                            inputElement.siblings('.invalid-feedback').text('');
                            inputElement.siblings('.valid-feedback').text('Receipt number is available');
                        }
                        updateSubmitButtonState();
                    },
                    error: function(xhr, status, error) {
                        inputElement.removeClass('receipt-number-checking');
                        console.error('Error checking receipt number in database:', error);
                        
                        // Show error but don't block submission
                        inputElement.removeClass('is-invalid is-valid');
                        inputElement.siblings('.invalid-feedback').text('Unable to verify receipt number. Please check manually.');
                        updateSubmitButtonState();
                    }
                });
            }, 500); // 500ms debounce
            
            inputElement.data('timeout-id', newTimeoutId);
        }
        
        function updateSubmitButtonState() {
            const hasInvalidInputs = $('.receipt-number-input.is-invalid').length > 0;
            const submitButton = $('#submit-btn');
            
            if (hasInvalidInputs) {
                submitButton.prop('disabled', true);
                submitButton.html('<i class="fas fa-exclamation-triangle me-1"></i>Fix Receipt Numbers');
                submitButton.removeClass('btn-primary').addClass('btn-danger');
            } else {
                submitButton.prop('disabled', false);
                submitButton.html('<i class="fas fa-save me-1"></i>Test Submit');
                submitButton.removeClass('btn-danger').addClass('btn-primary');
            }
        }
        
        function clearForm() {
            $('#test-form')[0].reset();
            $('.receipt-number-input').removeClass('is-invalid is-valid receipt-number-checking');
            $('.invalid-feedback, .valid-feedback').text('');
            $('#validation-status').empty();
            updateSubmitButtonState();
        }
    </script>
</body>
</html>
