<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Update Database - Add Verification Fields</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 40px; }
        .success { color: green; }
        .warning { color: orange; }
        .error { color: red; }
    </style>
</head>
<body>
    <h1>🔧 Database Update - Add Verification Fields</h1>
    
    <?php
    require_once 'config/database.php';
    
    try {
        $database = new Database();
        $db = $database->getConnection();
        
        echo "<h2>Adding verification and review fields to expenses table...</h2>";
        
        // Read and execute the SQL file
        $sql = file_get_contents('database/add_verification_fields.sql');
        
        // Split by semicolon and execute each statement
        $statements = array_filter(array_map('trim', explode(';', $sql)));
        
        foreach ($statements as $statement) {
            if (!empty($statement) && !preg_match('/^(SELECT|PREPARE|EXECUTE|DEALLOCATE)/', trim($statement))) {
                continue; // Skip dynamic SQL statements
            }
            
            if (!empty($statement)) {
                try {
                    $db->exec($statement);
                    echo "<p class='success'>✅ Executed: " . substr(trim($statement), 0, 80) . "...</p>";
                } catch (PDOException $e) {
                    if (strpos($e->getMessage(), 'already exists') !== false || 
                        strpos($e->getMessage(), 'Duplicate') !== false) {
                        echo "<p class='warning'>⚠️ Skipped (already exists): " . substr(trim($statement), 0, 80) . "...</p>";
                    } else {
                        echo "<p class='error'>❌ Error: " . $e->getMessage() . "</p>";
                    }
                }
            }
        }
        
        // Execute the dynamic SQL statements manually
        echo "<h3>Executing dynamic SQL statements...</h3>";
        
        // Check and add verification_slip_image
        $stmt = $db->prepare("
            SELECT COUNT(*) as count
            FROM INFORMATION_SCHEMA.COLUMNS
            WHERE table_name = 'expenses'
            AND table_schema = DATABASE()
            AND column_name = 'verification_slip_image'
        ");
        $stmt->execute();
        $result = $stmt->fetch();
        
        if ($result['count'] == 0) {
            $db->exec("ALTER TABLE expenses ADD COLUMN verification_slip_image VARCHAR(255) NULL AFTER receipt_images");
            echo "<p class='success'>✅ Added verification_slip_image column</p>";
        } else {
            echo "<p class='warning'>⚠️ verification_slip_image column already exists</p>";
        }
        
        // Check and add verification_amount
        $stmt = $db->prepare("
            SELECT COUNT(*) as count
            FROM INFORMATION_SCHEMA.COLUMNS
            WHERE table_name = 'expenses'
            AND table_schema = DATABASE()
            AND column_name = 'verification_amount'
        ");
        $stmt->execute();
        $result = $stmt->fetch();
        
        if ($result['count'] == 0) {
            $db->exec("ALTER TABLE expenses ADD COLUMN verification_amount DECIMAL(15,2) NULL AFTER verification_slip_image");
            echo "<p class='success'>✅ Added verification_amount column</p>";
        } else {
            echo "<p class='warning'>⚠️ verification_amount column already exists</p>";
        }
        
        // Check and add verification_date
        $stmt = $db->prepare("
            SELECT COUNT(*) as count
            FROM INFORMATION_SCHEMA.COLUMNS
            WHERE table_name = 'expenses'
            AND table_schema = DATABASE()
            AND column_name = 'verification_date'
        ");
        $stmt->execute();
        $result = $stmt->fetch();
        
        if ($result['count'] == 0) {
            $db->exec("ALTER TABLE expenses ADD COLUMN verification_date DATETIME NULL AFTER verification_amount");
            echo "<p class='success'>✅ Added verification_date column</p>";
        } else {
            echo "<p class='warning'>⚠️ verification_date column already exists</p>";
        }
        
        // Check and add verification_by
        $stmt = $db->prepare("
            SELECT COUNT(*) as count
            FROM INFORMATION_SCHEMA.COLUMNS
            WHERE table_name = 'expenses'
            AND table_schema = DATABASE()
            AND column_name = 'verification_by'
        ");
        $stmt->execute();
        $result = $stmt->fetch();
        
        if ($result['count'] == 0) {
            $db->exec("ALTER TABLE expenses ADD COLUMN verification_by INT NULL AFTER verification_date");
            echo "<p class='success'>✅ Added verification_by column</p>";
        } else {
            echo "<p class='warning'>⚠️ verification_by column already exists</p>";
        }
        
        // Check and add reviewer_slip_image
        $stmt = $db->prepare("
            SELECT COUNT(*) as count
            FROM INFORMATION_SCHEMA.COLUMNS
            WHERE table_name = 'expenses'
            AND table_schema = DATABASE()
            AND column_name = 'reviewer_slip_image'
        ");
        $stmt->execute();
        $result = $stmt->fetch();
        
        if ($result['count'] == 0) {
            $db->exec("ALTER TABLE expenses ADD COLUMN reviewer_slip_image VARCHAR(255) NULL AFTER verification_by");
            echo "<p class='success'>✅ Added reviewer_slip_image column</p>";
        } else {
            echo "<p class='warning'>⚠️ reviewer_slip_image column already exists</p>";
        }
        
        // Check and add reviewer_amount
        $stmt = $db->prepare("
            SELECT COUNT(*) as count
            FROM INFORMATION_SCHEMA.COLUMNS
            WHERE table_name = 'expenses'
            AND table_schema = DATABASE()
            AND column_name = 'reviewer_amount'
        ");
        $stmt->execute();
        $result = $stmt->fetch();
        
        if ($result['count'] == 0) {
            $db->exec("ALTER TABLE expenses ADD COLUMN reviewer_amount DECIMAL(15,2) NULL AFTER reviewer_slip_image");
            echo "<p class='success'>✅ Added reviewer_amount column</p>";
        } else {
            echo "<p class='warning'>⚠️ reviewer_amount column already exists</p>";
        }
        
        // Check and add reviewer_date
        $stmt = $db->prepare("
            SELECT COUNT(*) as count
            FROM INFORMATION_SCHEMA.COLUMNS
            WHERE table_name = 'expenses'
            AND table_schema = DATABASE()
            AND column_name = 'reviewer_date'
        ");
        $stmt->execute();
        $result = $stmt->fetch();
        
        if ($result['count'] == 0) {
            $db->exec("ALTER TABLE expenses ADD COLUMN reviewer_date DATETIME NULL AFTER reviewer_amount");
            echo "<p class='success'>✅ Added reviewer_date column</p>";
        } else {
            echo "<p class='warning'>⚠️ reviewer_date column already exists</p>";
        }
        
        // Check and add reviewer_by
        $stmt = $db->prepare("
            SELECT COUNT(*) as count
            FROM INFORMATION_SCHEMA.COLUMNS
            WHERE table_name = 'expenses'
            AND table_schema = DATABASE()
            AND column_name = 'reviewer_by'
        ");
        $stmt->execute();
        $result = $stmt->fetch();
        
        if ($result['count'] == 0) {
            $db->exec("ALTER TABLE expenses ADD COLUMN reviewer_by INT NULL AFTER reviewer_date");
            echo "<p class='success'>✅ Added reviewer_by column</p>";
        } else {
            echo "<p class='warning'>⚠️ reviewer_by column already exists</p>";
        }
        
        echo "<h2 class='success'>✅ Database updated successfully!</h2>";
        echo "<p>New features added:</p>";
        echo "<ul>";
        echo "<li><strong>Verification fields:</strong> verification_slip_image, verification_amount, verification_date, verification_by</li>";
        echo "<li><strong>Review fields:</strong> reviewer_slip_image, reviewer_amount, reviewer_date, reviewer_by</li>";
        echo "<li><strong>Enhanced workflow:</strong> Both verification and review roles can now upload documents and enter amounts</li>";
        echo "<li><strong>Better audit trail:</strong> Track who verified and reviewed each expense</li>";
        echo "</ul>";
        
        echo "<h3>Next Steps:</h3>";
        echo "<ol>";
        echo "<li>Update view.php to include verification and review forms</li>";
        echo "<li>Create API endpoints for submitting verification and review data</li>";
        echo "<li>Update validation logic to check verification/review requirements</li>";
        echo "<li>Delete this file (update_verification_fields.php) after use</li>";
        echo "</ol>";
        
    } catch (Exception $e) {
        echo "<p class='error'>❌ Error: " . $e->getMessage() . "</p>";
    }
    ?>
</body>
</html>
