<?php
/**
 * Test API Fix
 * ทดสอบ API ที่แก้ไขแล้ว
 * ลบไฟล์นี้หลังจากทดสอบเสร็จแล้ว
 */
session_start();
require_once 'config/database.php';

// Check if user is logged in
if (!isset($_SESSION['user_id'])) {
    header('Location: index.php');
    exit();
}

// Test URLs from the error log
$test_cases = [
    [
        'file' => 'transfer_68f562851e96c_1760912005.png',
        'type' => 'transfer_slip',
        'description' => 'Transfer Slip'
    ],
    [
        'file' => 'receipt_111_68f5628561a81_1760912005.png',
        'type' => 'receipt',
        'description' => 'Receipt Image 1'
    ],
    [
        'file' => 'receipt_222_68f56285a61eb_1760912005.jpg',
        'type' => 'receipt',
        'description' => 'Receipt Image 2'
    ],
    [
        'file' => 'batch_documents/review/BATCH_REV_20251019_225230_review_68f56bfcec82f_1760914428.jpg',
        'type' => 'batch_document',
        'description' => 'Batch Review Document'
    ],
    [
        'file' => 'batch_documents/verification/BATCH_VER_20251019_224656_verification_68f56a8acd39c_1760914058.png',
        'type' => 'batch_document',
        'description' => 'Batch Verification Document'
    ]
];

?>
<!DOCTYPE html>
<html lang="th">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test API Fix - Expenses System</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        .test-image { max-width: 200px; max-height: 150px; margin: 5px; border: 2px solid #ddd; }
        .status-ok { color: #28a745; }
        .status-error { color: #dc3545; }
        .test-result { margin: 10px 0; padding: 10px; border-radius: 5px; }
        .test-success { background-color: #d4edda; border: 1px solid #c3e6cb; }
        .test-failure { background-color: #f8d7da; border: 1px solid #f5c6cb; }
    </style>
</head>
<body class="bg-light">
    <div class="container mt-4">
        <div class="row justify-content-center">
            <div class="col-md-12">
                <div class="card shadow">
                    <div class="card-header bg-success text-white">
                        <h3><i class="fas fa-tools me-2"></i>Test API Fix</h3>
                        <p class="mb-0">ทดสอบ API ที่แก้ไขแล้ว - ควรแสดงรูปได้ทั้งหมด</p>
                    </div>
                    <div class="card-body">
                        
                        <h4><i class="fas fa-info-circle me-2"></i>API Changes</h4>
                        <div class="alert alert-info">
                            <ul class="mb-0">
                                <li>✅ ลบ try-catch ที่ซ้ำซ้อน</li>
                                <li>✅ ใช้ relative path <code>../uploads/</code> แทน absolute path</li>
                                <li>✅ ปรับปรุง batch_document path handling</li>
                                <li>✅ ลดความซับซ้อนของ access control</li>
                                <li>✅ เพิ่ม error messages ที่ชัดเจน</li>
                            </ul>
                        </div>

                        <h4><i class="fas fa-test-tube me-2"></i>Test Cases</h4>
                        
                        <div class="table-responsive">
                            <table class="table table-striped">
                                <thead>
                                    <tr>
                                        <th>Description</th>
                                        <th>File</th>
                                        <th>Type</th>
                                        <th>API Test</th>
                                        <th>Image Preview</th>
                                        <th>Status</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($test_cases as $index => $test): ?>
                                        <tr>
                                            <td><strong><?php echo htmlspecialchars($test['description']); ?></strong></td>
                                            <td>
                                                <small><code><?php echo htmlspecialchars($test['file']); ?></code></small>
                                            </td>
                                            <td>
                                                <span class="badge bg-secondary"><?php echo htmlspecialchars($test['type']); ?></span>
                                            </td>
                                            <td>
                                                <?php
                                                $api_url = "api/view_file.php?file=" . urlencode($test['file']) . "&type=" . urlencode($test['type']);
                                                ?>
                                                <a href="<?php echo $api_url; ?>" target="_blank" class="btn btn-sm btn-primary">
                                                    <i class="fas fa-external-link-alt me-1"></i>Test API
                                                </a>
                                            </td>
                                            <td>
                                                <img src="<?php echo $api_url; ?>" 
                                                     class="test-image img-thumbnail" 
                                                     alt="<?php echo htmlspecialchars($test['description']); ?>"
                                                     onload="updateStatus(<?php echo $index; ?>, true)"
                                                     onerror="updateStatus(<?php echo $index; ?>, false)">
                                            </td>
                                            <td>
                                                <div id="status-<?php echo $index; ?>" class="test-result">
                                                    <i class="fas fa-spinner fa-spin"></i> Loading...
                                                </div>
                                            </td>
                                        </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>

                        <hr>
                        <h4><i class="fas fa-chart-bar me-2"></i>Test Summary</h4>
                        <div id="summary" class="alert alert-info">
                            <i class="fas fa-clock me-2"></i>กำลังทดสอบ...
                        </div>

                        <hr>
                        <h4><i class="fas fa-folder me-2"></i>File Path Check</h4>
                        <div class="row">
                            <?php
                            $directories = [
                                'uploads/receipts/',
                                'uploads/transfer_slips/',
                                'uploads/verification_slips/',
                                'uploads/review_slips/',
                                'uploads/batch_documents/',
                                'uploads/batch_documents/verification/',
                                'uploads/batch_documents/review/',
                                'uploads/bulk_operations/'
                            ];
                            
                            foreach ($directories as $dir) {
                                echo "<div class='col-md-6 mb-2'>";
                                if (is_dir($dir)) {
                                    $files = glob($dir . '*');
                                    echo "<span class='status-ok'><i class='fas fa-check'></i></span> ";
                                    echo "<code>{$dir}</code> (" . count($files) . " files)";
                                } else {
                                    echo "<span class='status-error'><i class='fas fa-times'></i></span> ";
                                    echo "<code>{$dir}</code> (missing)";
                                }
                                echo "</div>";
                            }
                            ?>
                        </div>

                        <div class="alert alert-warning mt-3">
                            <h6><i class="fas fa-shield-alt me-2"></i>Security Notice</h6>
                            <p><strong>ลบไฟล์นี้ (test_api_fix.php) หลังจากทดสอบเสร็จแล้ว!</strong></p>
                        </div>

                        <div class="text-center mt-4">
                            <a href="expenses/" class="btn btn-primary me-3">
                                <i class="fas fa-list me-2"></i>Expenses List
                            </a>
                            <button onclick="window.location.reload()" class="btn btn-secondary">
                                <i class="fas fa-sync me-2"></i>Refresh Test
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        let testResults = {};
        let totalTests = <?php echo count($test_cases); ?>;
        let completedTests = 0;

        function updateStatus(index, success) {
            const statusDiv = document.getElementById('status-' + index);
            testResults[index] = success;
            completedTests++;

            if (success) {
                statusDiv.innerHTML = '<i class="fas fa-check status-ok"></i> ✅ Success';
                statusDiv.className = 'test-result test-success';
            } else {
                statusDiv.innerHTML = '<i class="fas fa-times status-error"></i> ❌ Failed';
                statusDiv.className = 'test-result test-failure';
            }

            updateSummary();
        }

        function updateSummary() {
            const summaryDiv = document.getElementById('summary');
            
            if (completedTests === totalTests) {
                const successCount = Object.values(testResults).filter(r => r).length;
                const failureCount = totalTests - successCount;
                
                if (successCount === totalTests) {
                    summaryDiv.innerHTML = `
                        <i class="fas fa-check-circle status-ok me-2"></i>
                        <strong>🎉 ทุกการทดสอบผ่าน!</strong> (${successCount}/${totalTests})
                        <br><small>API ทำงานได้ปกติแล้ว รูปทั้งหมดแสดงได้</small>
                    `;
                    summaryDiv.className = 'alert alert-success';
                } else {
                    summaryDiv.innerHTML = `
                        <i class="fas fa-exclamation-triangle status-error me-2"></i>
                        <strong>⚠️ มีการทดสอบที่ล้มเหลว</strong><br>
                        ✅ สำเร็จ: ${successCount}/${totalTests}<br>
                        ❌ ล้มเหลว: ${failureCount}/${totalTests}
                    `;
                    summaryDiv.className = 'alert alert-warning';
                }
            } else {
                summaryDiv.innerHTML = `
                    <i class="fas fa-spinner fa-spin me-2"></i>
                    กำลังทดสอบ... (${completedTests}/${totalTests})
                `;
            }
        }

        // Set timeout for failed loads
        setTimeout(() => {
            for (let i = 0; i < totalTests; i++) {
                if (!(i in testResults)) {
                    updateStatus(i, false);
                }
            }
        }, 10000); // 10 seconds timeout
    </script>
</body>
</html>
