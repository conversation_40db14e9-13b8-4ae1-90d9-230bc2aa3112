<?php
/**
 * Test Batch Workflow Status
 * ทดสอบการแสดงสถานะ Batch ใน Multi-Verification และ Multi-Review
 * ลบไฟล์นี้หลังจากทดสอบเสร็จแล้ว
 */
session_start();
require_once 'config/database.php';

// Check if user is logged in
if (!isset($_SESSION['user_id'])) {
    header('Location: index.php');
    exit();
}

$database = new Database();
$db = $database->getConnection();

?>
<!DOCTYPE html>
<html lang="th">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Batch Workflow Status - Expenses System</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        .status-badge { font-size: 0.8em; }
        .batch-info { font-size: 0.75em; color: #6c757d; }
        .workflow-step { padding: 15px; margin: 10px 0; border-radius: 8px; }
        .step-open { background-color: #fff3cd; border-left: 4px solid #ffc107; }
        .step-pending { background-color: #d1ecf1; border-left: 4px solid #17a2b8; }
        .step-success { background-color: #d4edda; border-left: 4px solid #28a745; }
    </style>
</head>
<body class="bg-light">
    <div class="container mt-4">
        <div class="row justify-content-center">
            <div class="col-md-12">
                <div class="card shadow">
                    <div class="card-header bg-primary text-white">
                        <h3><i class="fas fa-flow-chart me-2"></i>Test Batch Workflow Status</h3>
                        <p class="mb-0">ทดสอบการแสดงสถานะ Batch ใน Multi-Verification และ Multi-Review</p>
                    </div>
                    <div class="card-body">
                        
                        <h4><i class="fas fa-info-circle me-2"></i>Workflow Overview</h4>
                        <div class="row mb-4">
                            <div class="col-md-4">
                                <div class="workflow-step step-open">
                                    <h6><i class="fas fa-edit me-2"></i>1. Data Entry</h6>
                                    <p class="mb-1"><strong>Status:</strong> <span class="badge bg-warning">open</span></p>
                                    <p class="mb-0"><small>รายการใหม่ที่ Data Entry บันทึก</small></p>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="workflow-step step-pending">
                                    <h6><i class="fas fa-check-circle me-2"></i>2. Verification</h6>
                                    <p class="mb-1"><strong>Status:</strong> <span class="badge bg-info">pending</span></p>
                                    <p class="mb-0"><small>ผ่าน verification แล้ว รอ review</small></p>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="workflow-step step-success">
                                    <h6><i class="fas fa-thumbs-up me-2"></i>3. Review</h6>
                                    <p class="mb-1"><strong>Status:</strong> <span class="badge bg-success">success</span></p>
                                    <p class="mb-0"><small>ผ่าน review แล้ว (เสร็จสิ้น)</small></p>
                                </div>
                            </div>
                        </div>

                        <h4><i class="fas fa-database me-2"></i>Current Data Status</h4>
                        
                        <?php
                        // Get status counts
                        $status_counts = [];
                        $stmt = $db->prepare("
                            SELECT 
                                status,
                                COUNT(*) as count,
                                COUNT(CASE WHEN batch_verification_id IS NOT NULL THEN 1 END) as with_verification_batch,
                                COUNT(CASE WHEN batch_review_id IS NOT NULL THEN 1 END) as with_review_batch
                            FROM expenses 
                            GROUP BY status
                            ORDER BY 
                                CASE status 
                                    WHEN 'open' THEN 1 
                                    WHEN 'pending' THEN 2 
                                    WHEN 'success' THEN 3 
                                    ELSE 4 
                                END
                        ");
                        $stmt->execute();
                        $status_counts = $stmt->fetchAll();
                        ?>
                        
                        <div class="table-responsive mb-4">
                            <table class="table table-striped">
                                <thead>
                                    <tr>
                                        <th>Status</th>
                                        <th>Total Count</th>
                                        <th>With Verification Batch</th>
                                        <th>With Review Batch</th>
                                        <th>Individual Items</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($status_counts as $row): ?>
                                        <tr>
                                            <td>
                                                <?php
                                                $badge_class = [
                                                    'open' => 'bg-warning',
                                                    'pending' => 'bg-info', 
                                                    'success' => 'bg-success',
                                                    'rejected' => 'bg-danger',
                                                    'returned' => 'bg-secondary'
                                                ];
                                                $class = $badge_class[$row['status']] ?? 'bg-dark';
                                                ?>
                                                <span class="badge <?php echo $class; ?>"><?php echo $row['status']; ?></span>
                                            </td>
                                            <td><strong><?php echo number_format($row['count']); ?></strong></td>
                                            <td>
                                                <?php echo number_format($row['with_verification_batch']); ?>
                                                <?php if ($row['with_verification_batch'] > 0): ?>
                                                    <small class="text-info">(<i class="fas fa-layer-group"></i> Batch)</small>
                                                <?php endif; ?>
                                            </td>
                                            <td>
                                                <?php echo number_format($row['with_review_batch']); ?>
                                                <?php if ($row['with_review_batch'] > 0): ?>
                                                    <small class="text-success">(<i class="fas fa-layer-group"></i> Batch)</small>
                                                <?php endif; ?>
                                            </td>
                                            <td>
                                                <?php 
                                                $individual = $row['count'] - $row['with_verification_batch'] - $row['with_review_batch'];
                                                echo number_format($individual);
                                                ?>
                                                <?php if ($individual > 0): ?>
                                                    <small class="text-muted">(<i class="fas fa-file"></i> Individual)</small>
                                                <?php endif; ?>
                                            </td>
                                        </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>

                        <h4><i class="fas fa-tools me-2"></i>New Features Added</h4>
                        <div class="alert alert-success">
                            <h6><i class="fas fa-plus-circle me-2"></i>เพิ่มฟีเจอร์ใหม่:</h6>
                            <ul class="mb-0">
                                <li>✅ <strong>Batch Status Filter</strong> - แยกรายการที่ทำ batch แล้วและยังไม่ทำ</li>
                                <li>✅ <strong>Batch ID Display</strong> - แสดง Batch ID ในตารางรายการ</li>
                                <li>✅ <strong>Visual Indicators</strong> - ไอคอนและสีแยกประเภทรายการ</li>
                                <li>✅ <strong>Status Explanation</strong> - คำอธิบายสถานะในหน้า multi</li>
                            </ul>
                        </div>

                        <h4><i class="fas fa-question-circle me-2"></i>How to Use</h4>
                        <div class="row">
                            <div class="col-md-6">
                                <div class="card border-info">
                                    <div class="card-header bg-info text-white">
                                        <h6><i class="fas fa-check-circle me-2"></i>Multi-Verification</h6>
                                    </div>
                                    <div class="card-body">
                                        <p><strong>Filter Options:</strong></p>
                                        <ul>
                                            <li><strong>🔍 All Items</strong> - แสดงทั้งหมด</li>
                                            <li><strong>📝 Individual Items</strong> - รายการที่ยังไม่ได้ทำ batch</li>
                                            <li><strong>📦 Already in Batch</strong> - รายการที่ทำ batch แล้ว</li>
                                        </ul>
                                        <a href="expenses/multi_verification.php" class="btn btn-info btn-sm">
                                            <i class="fas fa-external-link-alt me-1"></i>Test Multi-Verification
                                        </a>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="card border-success">
                                    <div class="card-header bg-success text-white">
                                        <h6><i class="fas fa-thumbs-up me-2"></i>Multi-Review</h6>
                                    </div>
                                    <div class="card-body">
                                        <p><strong>Filter Options:</strong></p>
                                        <ul>
                                            <li><strong>🔍 All Items</strong> - แสดงทั้งหมด</li>
                                            <li><strong>📝 Individual Items</strong> - รายการที่ยังไม่ได้ทำ batch</li>
                                            <li><strong>📦 Already in Batch</strong> - รายการที่ทำ batch แล้ว</li>
                                        </ul>
                                        <a href="expenses/multi_review.php" class="btn btn-success btn-sm">
                                            <i class="fas fa-external-link-alt me-1"></i>Test Multi-Review
                                        </a>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="alert alert-warning mt-4">
                            <h6><i class="fas fa-shield-alt me-2"></i>Security Notice</h6>
                            <p><strong>ลบไฟล์นี้ (test_batch_workflow.php) หลังจากทดสอบเสร็จแล้ว!</strong></p>
                        </div>

                        <div class="text-center mt-4">
                            <a href="expenses/" class="btn btn-primary me-3">
                                <i class="fas fa-list me-2"></i>Expenses List
                            </a>
                            <a href="dashboard.php" class="btn btn-secondary">
                                <i class="fas fa-home me-2"></i>Dashboard
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</body>
</html>
