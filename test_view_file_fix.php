<?php
session_start();

// Simulate logged in user for testing
if (!isset($_SESSION['user_id'])) {
    $_SESSION['user_id'] = 1;
    $_SESSION['role'] = 'administrator';
    $_SESSION['username'] = 'test_user';
}

echo "<h2>Test view_file.php Fix</h2>";

$test_file = $_GET['file'] ?? 'transfer_68fcafb1efdee_1761390513.jpg';
$test_type = $_GET['type'] ?? 'transfer_slip';

echo "<h3>Testing: $test_file</h3>";

// Test the corrected view_file.php logic
if (isset($_GET['serve_file']) && $_GET['serve_file'] === 'yes') {
    // This is the corrected view_file.php logic
    
    // Session check
    if (!isset($_SESSION['user_id'])) {
        header('HTTP/1.0 403 Forbidden');
        exit('Access denied');
    }
    
    // Parameters
    $file = $_GET['file'] ?? '';
    $type = $_GET['type'] ?? '';
    
    if (empty($file) || empty($type)) {
        header('HTTP/1.0 400 Bad Request');
        exit('Missing parameters');
    }
    
    // User info
    $user_id = $_SESSION['user_id'];
    $user_role = $_SESSION['role'] ?? 'data_entry';
    
    // Filename sanitization
    if ($type === 'batch_document') {
        $file = str_replace(['../', '../', '..\\'], '', $file);
        if (!preg_match('/^[a-zA-Z0-9_\-\.\/\\\\]+$/', $file)) {
            header('HTTP/1.0 400 Bad Request');
            exit('Invalid filename');
        }
    } else {
        $file = basename($file);
        if (!preg_match('/^[a-zA-Z0-9_\-\.]+$/', $file)) {
            header('HTTP/1.0 400 Bad Request');
            exit('Invalid filename');
        }
    }
    
    // CORRECTED: Use relative path from api directory
    $upload_dir = '../uploads/';
    
    // File path determination
    if ($type === 'transfer_slip') {
        $file_path = $upload_dir . 'transfer_slips/' . $file;
    } elseif ($type === 'verification_slip') {
        $file_path = $upload_dir . 'verification_slips/' . $file;
    } elseif ($type === 'reviewer_slip') {
        $file_path = $upload_dir . 'review_slips/' . $file;
    } elseif ($type === 'bulk_operation_slip') {
        $file_path = $upload_dir . 'bulk_operations/' . $file;
    } elseif ($type === 'receipt') {
        $file_path = $upload_dir . 'receipts/' . $file;
    } elseif ($type === 'deduction') {
        $file_path = $upload_dir . 'deductions/' . $file;
    } else {
        $file_path = $upload_dir . 'receipts/' . $file;
    }
    
    // File existence check
    if (!file_exists($file_path) || !is_file($file_path)) {
        header('HTTP/1.0 404 Not Found');
        exit('File not found: ' . basename($file_path));
    }
    
    // Access control (simplified for admin)
    if ($user_role === 'data_entry' && $type !== 'batch_document') {
        // Database check would go here
        // For now, allow access for admin
    }
    
    // File info
    $file_info = pathinfo($file_path);
    $file_extension = strtolower($file_info['extension']);
    
    $content_types = [
        'jpg' => 'image/jpeg',
        'jpeg' => 'image/jpeg',
        'png' => 'image/png',
        'gif' => 'image/gif',
        'pdf' => 'application/pdf'
    ];
    
    $content_type = $content_types[$file_extension] ?? 'application/octet-stream';
    
    // Clear any output buffer
    while (ob_get_level()) {
        ob_end_clean();
    }
    
    // Set headers
    header('Content-Type: ' . $content_type);
    header('Content-Length: ' . filesize($file_path));
    header('Cache-Control: private, max-age=3600');
    
    // Output file
    readfile($file_path);
    exit;
}

// Show test results
echo "<h3>Path Testing Results:</h3>";

$paths_to_test = [
    'Current (wrong)' => dirname(__DIR__) . '/uploads/',
    'Corrected' => '../uploads/',
    'Alternative 1' => './uploads/',
    'Alternative 2' => 'uploads/'
];

echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
echo "<tr><th>Description</th><th>Path</th><th>File Exists</th><th>Test</th></tr>";

foreach ($paths_to_test as $desc => $path) {
    $file_path = $path . 'transfer_slips/' . $test_file;
    $exists = file_exists($file_path);
    
    echo "<tr>";
    echo "<td>$desc</td>";
    echo "<td>$path</td>";
    echo "<td style='color: " . ($exists ? 'green' : 'red') . "'>" . ($exists ? '✅ YES' : '❌ NO') . "</td>";
    echo "<td>";
    if ($exists) {
        echo "<a href='?file=" . urlencode($test_file) . "&type=$test_type&serve_file=yes' target='_blank'>Test Serve</a>";
    } else {
        echo "N/A";
    }
    echo "</td>";
    echo "</tr>";
}
echo "</table>";

echo "<h3>🛠️ Fix Instructions:</h3>";
echo "<div style='background: #f8f9fa; border: 1px solid #dee2e6; padding: 15px; border-radius: 5px;'>";
echo "<p><strong>In api/view_file.php, change line 40:</strong></p>";
echo "<p style='background: #f8d7da; padding: 10px; border-radius: 3px;'>";
echo "<code>❌ \$upload_dir = dirname(__DIR__) . '/uploads/';</code>";
echo "</p>";
echo "<p style='background: #d4edda; padding: 10px; border-radius: 3px;'>";
echo "<code>✅ \$upload_dir = '../uploads/';</code>";
echo "</p>";
echo "<p><strong>Explanation:</strong> The api/view_file.php is in the 'api' subdirectory, so it needs to go up one level (../) to reach the uploads directory.</p>";
echo "</div>";

echo "<h3>Test Links:</h3>";
echo "<ul>";
echo "<li><a href='?file=" . urlencode($test_file) . "&type=$test_type&serve_file=yes' target='_blank'>Test Fixed view_file.php Logic</a></li>";
echo "<li><a href='api/view_file.php?file=" . urlencode($test_file) . "&type=$test_type' target='_blank'>Test Current api/view_file.php</a></li>";
echo "</ul>";

// Test form
echo "<h3>Test Different File:</h3>";
echo "<form method='GET' style='background: #f9f9f9; padding: 15px; border: 1px solid #ddd;'>";
echo "<label>File: <input type='text' name='file' value='$test_file' style='width: 400px;'></label><br><br>";
echo "<label>Type: <input type='text' name='type' value='$test_type' style='width: 200px;'></label><br><br>";
echo "<input type='submit' value='Test Fix' style='padding: 10px 20px;'>";
echo "</form>";
?>
