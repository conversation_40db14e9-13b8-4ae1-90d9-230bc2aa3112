-- Add Reject/Return Workflow for MariaDB 5.5.68
-- Compatible version without <PERSON><PERSON><PERSON> functions

-- Add workflow_history column as TEXT instead of JSON
ALTER TABLE expenses ADD COLUMN workflow_history TEXT DEFAULT NULL COMMENT 'Workflow history as JSON string';

-- Add workflow status columns
ALTER TABLE expenses ADD COLUMN rejection_reason TEXT DEFAULT NULL COMMENT 'Reason for rejection';
ALTER TABLE expenses ADD COLUMN return_reason TEXT DEFAULT NULL COMMENT 'Reason for return';
ALTER TABLE expenses ADD COLUMN rejected_by INT DEFAULT NULL COMMENT 'User who rejected';
ALTER TABLE expenses ADD COLUMN returned_by INT DEFAULT NULL COMMENT 'User who returned';
ALTER TABLE expenses ADD COLUMN rejected_at TIMESTAMP NULL DEFAULT NULL COMMENT 'When rejected';
ALTER TABLE expenses ADD COLUMN returned_at TIMESTAMP NULL DEFAULT NULL COMMENT 'When returned';

-- Add foreign key constraints
ALTER TABLE expenses ADD CONSTRAINT fk_expenses_rejected_by FOREIGN KEY (rejected_by) REFERENCES users(id) ON DELETE SET NULL;
ALTER TABLE expenses ADD CONSTRAINT fk_expenses_returned_by FOREIGN KEY (returned_by) REFERENCES users(id) ON DELETE SET NULL;

-- Create workflow_history table as alternative to JSON column
CREATE TABLE IF NOT EXISTS expense_workflow_history (
    id INT PRIMARY KEY AUTO_INCREMENT,
    expense_id INT NOT NULL,
    action_type ENUM('submit', 'verify', 'review', 'reject', 'return', 'resubmit') NOT NULL,
    from_status VARCHAR(20) NOT NULL,
    to_status VARCHAR(20) NOT NULL,
    user_id INT NOT NULL,
    comment TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    INDEX idx_expense_id (expense_id),
    INDEX idx_action_type (action_type),
    INDEX idx_created_at (created_at),
    
    FOREIGN KEY (expense_id) REFERENCES expenses(id) ON DELETE CASCADE,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
) COMMENT='Expense workflow history tracking';

-- Create stored procedure for adding workflow history (MariaDB 5.5.68 compatible)
DELIMITER //

DROP PROCEDURE IF EXISTS AddWorkflowHistory //

CREATE PROCEDURE AddWorkflowHistory(
    IN expense_id INT,
    IN action_type VARCHAR(20),
    IN from_status VARCHAR(20),
    IN to_status VARCHAR(20),
    IN user_id INT,
    IN comment TEXT
)
BEGIN
    DECLARE history_text TEXT;
    DECLARE new_entry TEXT;
    DECLARE current_timestamp VARCHAR(20);
    
    -- Get current timestamp
    SET current_timestamp = DATE_FORMAT(NOW(), '%Y-%m-%d %H:%i:%s');
    
    -- Get current workflow history
    SELECT workflow_history INTO history_text 
    FROM expenses 
    WHERE id = expense_id;
    
    -- Create new history entry as JSON string
    SET new_entry = CONCAT(
        '{"timestamp":"', current_timestamp, '",',
        '"action":"', action_type, '",',
        '"from_status":"', from_status, '",',
        '"to_status":"', to_status, '",',
        '"user_id":', user_id, ',',
        '"comment":"', IFNULL(REPLACE(comment, '"', '\\"'), ''), '"}'
    );
    
    -- If history is null or empty, initialize as array
    IF history_text IS NULL OR history_text = '' THEN
        SET history_text = CONCAT('[', new_entry, ']');
    ELSE
        -- Remove closing bracket and append new entry
        SET history_text = CONCAT(
            SUBSTRING(history_text, 1, LENGTH(history_text) - 1),
            ',', new_entry, ']'
        );
    END IF;
    
    -- Update the expense record
    UPDATE expenses 
    SET workflow_history = history_text 
    WHERE id = expense_id;
    
    -- Also insert into workflow_history table for easier querying
    INSERT INTO expense_workflow_history (
        expense_id, action_type, from_status, to_status, user_id, comment
    ) VALUES (
        expense_id, action_type, from_status, to_status, user_id, comment
    );
    
END //

DELIMITER ;

-- Create helper function to get workflow history count (MariaDB 5.5.68 compatible)
DELIMITER //

DROP FUNCTION IF EXISTS GetWorkflowHistoryCount //

CREATE FUNCTION GetWorkflowHistoryCount(expense_id INT) 
RETURNS INT
READS SQL DATA
DETERMINISTIC
BEGIN
    DECLARE history_count INT DEFAULT 0;
    DECLARE history_text TEXT;
    
    -- Get workflow history
    SELECT workflow_history INTO history_text 
    FROM expenses 
    WHERE id = expense_id;
    
    -- Count entries by counting commas + 1 (if not empty)
    IF history_text IS NOT NULL AND history_text != '' AND history_text != '[]' THEN
        -- Count commas between entries and add 1
        SET history_count = (
            LENGTH(history_text) - LENGTH(REPLACE(history_text, '},{', '')) 
        ) / 2 + 1;
        
        -- If it's just empty array, set to 0
        IF history_text = '[]' THEN
            SET history_count = 0;
        END IF;
    END IF;
    
    RETURN history_count;
END //

DELIMITER ;

-- Create view for easy workflow history access
CREATE OR REPLACE VIEW expense_workflow_summary AS
SELECT 
    e.id,
    e.exno,
    e.status,
    e.workflow_history,
    GetWorkflowHistoryCount(e.id) as history_count,
    e.rejection_reason,
    e.return_reason,
    e.rejected_at,
    e.returned_at,
    ru.full_name as rejected_by_name,
    retu.full_name as returned_by_name
FROM expenses e
LEFT JOIN users ru ON e.rejected_by = ru.id
LEFT JOIN users retu ON e.returned_by = retu.id;

-- Insert sample workflow data for testing
INSERT INTO expense_workflow_history (expense_id, action_type, from_status, to_status, user_id, comment)
SELECT 
    e.id,
    'submit',
    'draft',
    'open',
    e.created_by,
    'Initial submission'
FROM expenses e
WHERE e.id <= 5 AND NOT EXISTS (
    SELECT 1 FROM expense_workflow_history ewh WHERE ewh.expense_id = e.id
);

-- Update some expenses with workflow history JSON
UPDATE expenses 
SET workflow_history = CONCAT(
    '[{"timestamp":"', DATE_FORMAT(created_at, '%Y-%m-%d %H:%i:%s'), '",',
    '"action":"submit","from_status":"draft","to_status":"open",',
    '"user_id":', created_by, ',"comment":"Initial submission"}]'
)
WHERE workflow_history IS NULL AND id <= 5;

-- Add indexes for performance
CREATE INDEX idx_expenses_workflow_status ON expenses(status, rejected_at, returned_at);
CREATE INDEX idx_expenses_workflow_users ON expenses(rejected_by, returned_by);
