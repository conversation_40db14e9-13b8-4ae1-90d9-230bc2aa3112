🗄️ SIMPLE IMPORT GUIDE FOR MARIADB 5.5.68

❌ PROBLEM: php<PERSON>y<PERSON><PERSON><PERSON> can't parse stored procedures in large SQL files

✅ SOLUTION: Import in 3 separate steps

📁 FILES TO IMPORT (in order):

1. schema_only_mariadb55.sql     - Tables and indexes only
2. procedures_mariadb55.sql      - Stored procedures only
3. triggers_mariadb55.sql        - Auto-update triggers
4. sample_data_mariadb55.sql     - Sample data (optional)

🚀 STEP-BY-STEP INSTRUCTIONS:

STEP 1: Import Schema
- Open phpMyAdmin
- Select your database: expenses_system
- Click "Import" tab
- Choose file: schema_only_mariadb55.sql
- Click "Go"
- Wait for "Import has been successfully finished"

STEP 2: Import Procedures
- Still in phpMyAdmin
- Click "SQL" tab
- Copy and paste FIRST procedure from procedures_mariadb55.sql:

DROP PROCEDURE IF EXISTS CreateBatchOperation;

DELIMITER $$
CREATE PROCEDURE CreateBatchOperation(
    IN p_operation_type VARCHAR(20), 
    IN p_user_id INT, 
    IN p_expense_ids_text TEXT, 
    IN p_total_amount DECIMAL(12,2), 
    OUT p_batch_id VARCHAR(50)
)
BEGIN
    DECLARE v_batch_id VARCHAR(50);
    DECLARE v_item_count INT;
    DECLARE v_expense_id INT;
    DECLARE v_amount DECIMAL(12,2);
    DECLARE v_pos INT DEFAULT 1;
    DECLARE v_next_pos INT;
    DECLARE v_item TEXT;
    DECLARE v_done INT DEFAULT 0;
    
    SET v_batch_id = CONCAT('BATCH_', UPPER(p_operation_type), '_', DATE_FORMAT(NOW(), '%Y%m%d_%H%i%s'));
    SET v_item_count = (CHAR_LENGTH(p_expense_ids_text) - CHAR_LENGTH(REPLACE(p_expense_ids_text, ',', '')) + 1);
    
    INSERT INTO batch_operations (batch_id, operation_type, user_id, total_amount, item_count)
    VALUES (v_batch_id, p_operation_type, p_user_id, p_total_amount, v_item_count);
    
    WHILE v_pos <= CHAR_LENGTH(p_expense_ids_text) AND v_done = 0 DO
        SET v_next_pos = LOCATE(',', p_expense_ids_text, v_pos);
        
        IF v_next_pos = 0 THEN
            SET v_next_pos = CHAR_LENGTH(p_expense_ids_text) + 1;
            SET v_done = 1;
        END IF;
        
        SET v_item = SUBSTRING(p_expense_ids_text, v_pos, v_next_pos - v_pos);
        SET v_expense_id = CAST(SUBSTRING_INDEX(v_item, ':', 1) AS UNSIGNED);
        SET v_amount = CAST(SUBSTRING_INDEX(v_item, ':', -1) AS DECIMAL(12,2));
        
        INSERT INTO batch_items (batch_id, expense_id, individual_amount)
        VALUES (v_batch_id, v_expense_id, v_amount);
        
        SET v_pos = v_next_pos + 1;
    END WHILE;
    
    SET p_batch_id = v_batch_id;
END$$
DELIMITER ;

- Click "Go"
- Should see "1 row affected"

STEP 3: Import Second Procedure
- Click "SQL" tab again
- Copy and paste SECOND procedure:

DROP PROCEDURE IF EXISTS AddWorkflowHistory;

DELIMITER $$
CREATE PROCEDURE AddWorkflowHistory(
    IN expense_id INT,
    IN action_type VARCHAR(20),
    IN from_status VARCHAR(20),
    IN to_status VARCHAR(20),
    IN user_id INT,
    IN comment TEXT
)
BEGIN
    DECLARE history_text TEXT;
    DECLARE new_entry TEXT;
    DECLARE current_timestamp VARCHAR(20);
    
    SET current_timestamp = DATE_FORMAT(NOW(), '%Y-%m-%d %H:%i:%s');
    
    SELECT workflow_history INTO history_text 
    FROM expenses 
    WHERE id = expense_id;
    
    SET comment = IFNULL(REPLACE(REPLACE(comment, '\\', '\\\\'), '"', '\\"'), '');
    
    SET new_entry = CONCAT(
        '{"timestamp":"', current_timestamp, '",',
        '"action":"', action_type, '",',
        '"from_status":"', from_status, '",',
        '"to_status":"', to_status, '",',
        '"user_id":', user_id, ',',
        '"comment":"', comment, '"}'
    );
    
    IF history_text IS NULL OR history_text = '' THEN
        SET history_text = CONCAT('[', new_entry, ']');
    ELSE
        SET history_text = CONCAT(
            SUBSTRING(history_text, 1, CHAR_LENGTH(history_text) - 1),
            ',', new_entry, ']'
        );
    END IF;
    
    UPDATE expenses 
    SET workflow_history = history_text 
    WHERE id = expense_id;
    
    INSERT INTO expense_workflow_history (
        expense_id, action_type, from_status, to_status, user_id, comment
    ) VALUES (
        expense_id, action_type, from_status, to_status, user_id, comment
    );
END$$
DELIMITER ;

- Click "Go"
- Should see "1 row affected"

STEP 4: Import Triggers
- Click "Import" tab
- Choose file: triggers_mariadb55.sql
- Click "Go"
- Should see "4 rows affected" (4 triggers created)

STEP 5: Import Sample Data (Optional)
- Click "Import" tab
- Choose file: sample_data_mariadb55.sql
- Click "Go"

🧪 VERIFY SUCCESS:

1. Check Tables:
   SELECT COUNT(*) FROM information_schema.tables WHERE table_schema = 'expenses_system';
   -- Should return 12 (including drivers table)

2. Check Customers Table Structure:
   DESCRIBE customers;
   -- Should show: id, name, contact_person, phone, email, address, is_active, created_at, updated_at, created_by

3. Check Drivers Table Structure:
   DESCRIBE drivers;
   -- Should show: id, name, license_number, phone, vehicle_plate, payment_account_no, is_active, created_at, updated_at, created_by

4. Check Items Table Structure:
   DESCRIBE items;
   -- Should show: id, name, description, is_active, created_at, updated_at, created_by

5. Check Expenses Table Structure:
   DESCRIBE expenses;
   -- Should show all fields including: driver_id, vehicle_plate, payment_account_no, additional_details, requester, receiver, payer, withdrawal_date, transfer_no, transfer_amount, total_amount, etc.

2. Check Procedures:
   SHOW PROCEDURE STATUS WHERE Db = 'expenses_system';
   -- Should show 2 procedures

3. Check Triggers:
   SHOW TRIGGERS WHERE `Table` IN ('customers', 'drivers', 'items', 'users', 'expenses');
   -- Should show 5 triggers

4. Test Procedure:
   CALL CreateBatchOperation('verification', 1, '1:1500.00,2:2500.00', 4000.00, @batch_id);
   SELECT @batch_id;
   -- Should return a batch ID

5. Test Auto-Update:
   UPDATE customers SET name = 'Test Update' WHERE id = 1;
   SELECT updated_at FROM customers WHERE id = 1;
   -- Should show current timestamp

6. Test Sample Data:
   SELECT COUNT(*) FROM customers;  -- Should return 5
   SELECT COUNT(*) FROM drivers;    -- Should return 5
   SELECT COUNT(*) FROM items;      -- Should return 10
   SELECT COUNT(*) FROM expenses;   -- Should return 5

✅ SUCCESS! Your database is ready for the application.

🔗 Next: Configure config/database.php with your credentials:
- Host: your_host
- Database: expenses_system  
- Username: nksl_expense
- Password: %Kpr@lgAbgp28Fx9
