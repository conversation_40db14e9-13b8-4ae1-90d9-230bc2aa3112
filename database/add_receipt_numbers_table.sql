-- Create receipt_numbers table to store individual receipt numbers for each expense
CREATE TABLE IF NOT EXISTS receipt_numbers (
    id INT AUTO_INCREMENT PRIMARY KEY,
    expense_id INT NOT NULL,
    receipt_number VARCHAR(100) NOT NULL,
    amount DECIMAL(15,2) DEFAULT 0.00,
    description TEXT,
    receipt_image VARCHAR(255),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    created_by INT,
    FOREIGN KEY (expense_id) REFERENCES expenses(id) ON DELETE CASCADE,
    FOREIGN KEY (created_by) REFERENCES users(id),
    INDEX idx_expense_id (expense_id),
    INDEX idx_receipt_number (receipt_number)
);

-- Add transfer_amount field to expenses table (check if not exists)
SET @sql = (SELECT IF(
    (SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS
     WHERE table_name = 'expenses'
     AND table_schema = DATABASE()
     AND column_name = 'transfer_amount') > 0,
    'SELECT "transfer_amount column already exists"',
    'ALTER TABLE expenses ADD COLUMN transfer_amount DECIMAL(15,2) DEFAULT 0.00 AFTER transfer_no'
));
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- Add total_amount field to expenses table (check if not exists)
SET @sql = (SELECT IF(
    (SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS
     WHERE table_name = 'expenses'
     AND table_schema = DATABASE()
     AND column_name = 'total_amount') > 0,
    'SELECT "total_amount column already exists"',
    'ALTER TABLE expenses ADD COLUMN total_amount DECIMAL(15,2) DEFAULT 0.00 AFTER transfer_amount'
));
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- Update existing expenses to have transfer_amount and total_amount = 0
UPDATE expenses SET transfer_amount = 0.00 WHERE transfer_amount IS NULL;
UPDATE expenses SET total_amount = 0.00 WHERE total_amount IS NULL;
