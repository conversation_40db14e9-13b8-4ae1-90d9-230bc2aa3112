-- ========================================
-- CreateBatchOperation Procedure
-- MariaDB 5.5.68 Compatible
-- FOR PRODUCTION SERVER
-- ========================================
-- ✅ ไม่มี DEFINER - ใช้ได้ที่ server จริง
-- ✅ ใช้ได้กับ user ทั่วไป (ไม่ต้อง SUPER privilege)
-- ========================================

-- ตรวจสอบว่า Procedure มีอยู่แล้วหรือไม่
DROP PROCEDURE IF EXISTS CreateBatchOperation;

DELIMITER $$

CREATE PROCEDURE `CreateBatchOperation` (
  IN `p_operation_type` VARCHAR(20),
  IN `p_user_id` INT,
  IN `p_expense_ids_csv` VARCHAR(1000),
  IN `p_total_amount` DECIMAL(12,2),
  OUT `p_batch_id` VARCHAR(50)
)
BEGIN
  DECLARE v_batch_id VARCHAR(50);
  DECLARE v_item_count INT;
  DECLARE v_expense_id INT;
  DECLARE v_amount DECIMAL(12,2);
  DECLARE v_pos INT;
  DECLARE v_comma_pos INT;
  DECLARE v_expense_str VARCHAR(100);
  DECLARE v_pipe_pos INT;
  DECLARE done INT DEFAULT FALSE;

  -- สร้าง Batch ID
  SET v_batch_id = CONCAT('BATCH_', UPPER(p_operation_type), '_', DATE_FORMAT(NOW(), '%Y%m%d_%H%i%s'));

  -- นับจำนวนรายการ (นับจำนวน comma + 1)
  SET v_item_count = (CHAR_LENGTH(p_expense_ids_csv) - CHAR_LENGTH(REPLACE(p_expense_ids_csv, ',', ''))) + 1;

  -- บันทึก Batch Operation
  INSERT INTO batch_operations (batch_id, operation_type, user_id, total_amount, item_count)
  VALUES (v_batch_id, p_operation_type, p_user_id, p_total_amount, v_item_count);

  -- ประมวลผลแต่ละรายการ
  SET v_pos = 1;
  WHILE v_pos <= CHAR_LENGTH(p_expense_ids_csv) DO
    -- หาตำแหน่ง comma ถัดไป
    SET v_comma_pos = LOCATE(',', p_expense_ids_csv, v_pos);

    -- ถ้าไม่มี comma ให้ใช้ความยาวของ string
    IF v_comma_pos = 0 THEN
      SET v_comma_pos = CHAR_LENGTH(p_expense_ids_csv) + 1;
    END IF;

    -- ดึงข้อมูลรายการ (format: expense_id|amount)
    SET v_expense_str = TRIM(SUBSTRING(p_expense_ids_csv, v_pos, v_comma_pos - v_pos));

    -- หาตำแหน่ง pipe (|) เพื่อแยก expense_id และ amount
    SET v_pipe_pos = LOCATE('|', v_expense_str);

    IF v_pipe_pos > 0 THEN
      SET v_expense_id = CAST(SUBSTRING(v_expense_str, 1, v_pipe_pos - 1) AS UNSIGNED);
      SET v_amount = CAST(SUBSTRING(v_expense_str, v_pipe_pos + 1) AS DECIMAL(12,2));

      -- บันทึก Batch Item
      INSERT INTO batch_items (batch_id, expense_id, individual_amount)
      VALUES (v_batch_id, v_expense_id, v_amount);
    END IF;

    -- ย้ายไปตำแหน่งถัดไป
    SET v_pos = v_comma_pos + 1;
  END WHILE;

  -- คืนค่า Batch ID
  SET p_batch_id = v_batch_id;
END$$

DELIMITER ;

-- ========================================
-- ตรวจสอบว่า Procedure ถูกสร้างแล้ว
-- ========================================
SHOW PROCEDURE STATUS WHERE db = 'expenses_system' AND name = 'CreateBatchOperation';

