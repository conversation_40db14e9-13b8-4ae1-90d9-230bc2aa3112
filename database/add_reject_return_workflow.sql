-- Add Reject/Return Workflow Support
-- Execute this SQL to add reject/return functionality

-- Add new columns for reject/return workflow
ALTER TABLE expenses 
ADD COLUMN rejection_reason TEXT NULL AFTER reviewer_by,
ADD COLUMN rejection_date DATETIME NULL AFTER rejection_reason,
ADD COLUMN rejected_by INT NULL AFTER rejection_date,
ADD COLUMN return_reason TEXT NULL AFTER rejected_by,
ADD COLUMN return_date DATETIME NULL AFTER return_reason,
ADD COLUMN returned_by INT NULL AFTER return_date,
ADD COLUMN workflow_history TEXT NULL AFTER returned_by;

-- Add foreign key constraints
ALTER TABLE expenses 
ADD CONSTRAINT fk_expenses_rejected_by 
<PERSON><PERSON><PERSON><PERSON><PERSON> KEY (rejected_by) REFERENCES users(id) ON DELETE SET NULL;

ALTER TABLE expenses 
ADD CONSTRAINT fk_expenses_returned_by 
FOREIGN KEY (returned_by) REFERENCES users(id) ON DELETE SET NULL;

-- Update status enum to include new statuses
ALTER TABLE expenses 
MODIFY COLUMN status ENUM('open', 'pending', 'success', 'rejected', 'returned') NOT NULL DEFAULT 'open';

-- Add indexes for performance
CREATE INDEX idx_expenses_status_rejection ON expenses(status, rejection_date);
CREATE INDEX idx_expenses_status_return ON expenses(status, return_date);
CREATE INDEX idx_expenses_rejected_by ON expenses(rejected_by);
CREATE INDEX idx_expenses_returned_by ON expenses(returned_by);

-- Create view for workflow tracking
CREATE OR REPLACE VIEW expense_workflow_view AS
SELECT 
    e.id,
    e.exno,
    e.status,
    e.total_amount,
    e.created_at,
    e.created_by,
    creator.full_name as created_by_name,
    
    -- Verification info
    e.verification_date,
    e.verification_by,
    verifier.full_name as verified_by_name,
    
    -- Review info
    e.reviewer_date,
    e.reviewer_by,
    reviewer.full_name as reviewed_by_name,
    
    -- Rejection info
    e.rejection_reason,
    e.rejection_date,
    e.rejected_by,
    rejector.full_name as rejected_by_name,
    
    -- Return info
    e.return_reason,
    e.return_date,
    e.returned_by,
    returner.full_name as returned_by_name,
    
    -- Workflow history
    e.workflow_history
    
FROM expenses e
LEFT JOIN users creator ON e.created_by = creator.id
LEFT JOIN users verifier ON e.verification_by = verifier.id
LEFT JOIN users reviewer ON e.reviewer_by = reviewer.id
LEFT JOIN users rejector ON e.rejected_by = rejector.id
LEFT JOIN users returner ON e.returned_by = returner.id;

-- Insert sample workflow history function (stored procedure)
DELIMITER //

DROP PROCEDURE IF EXISTS AddWorkflowHistory;

CREATE PROCEDURE AddWorkflowHistory(
    IN expense_id INT,
    IN action_type VARCHAR(50),
    IN from_status VARCHAR(20),
    IN to_status VARCHAR(20),
    IN user_id INT,
    IN comment TEXT
)
BEGIN
    DECLARE history_json JSON;
    DECLARE new_entry JSON;
    
    -- Get current workflow history
    SELECT workflow_history INTO history_json 
    FROM expenses 
    WHERE id = expense_id;
    
    -- If history is null, initialize as empty array
    IF history_json IS NULL THEN
        SET history_json = JSON_ARRAY();
    END IF;
    
    -- Create new history entry
    SET new_entry = JSON_OBJECT(
        'timestamp', NOW(),
        'action', action_type,
        'from_status', from_status,
        'to_status', to_status,
        'user_id', user_id,
        'comment', comment
    );
    
    -- Append to history
    SET history_json = JSON_ARRAY_APPEND(history_json, '$', new_entry);
    
    -- Update the expense record
    UPDATE expenses 
    SET workflow_history = history_json 
    WHERE id = expense_id;
END //

DELIMITER ;

-- Add some sample data for testing (optional)
-- UPDATE expenses SET status = 'rejected', rejection_reason = 'Missing receipt details', rejection_date = NOW(), rejected_by = 1 WHERE id = 1;
-- UPDATE expenses SET status = 'returned', return_reason = 'Please provide more details', return_date = NOW(), returned_by = 1 WHERE id = 2;

-- Show the updated table structure
DESCRIBE expenses;
