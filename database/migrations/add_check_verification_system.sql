-- Migration: Add Check Verification System
-- Purpose: Add 'checked' status and related fields for verification workflow
-- Date: 2024-10-24

-- Check if 'checked' status already exists, if not add it
SET @sql = (SELECT IF(
    (SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS
     WHERE TABLE_SCHEMA = 'expenses_system'
     AND TABLE_NAME = 'expenses'
     AND COLUMN_NAME = 'status'
     AND COLUMN_TYPE LIKE '%checked%') > 0,
    'SELECT "Status checked already exists" as message',
    'ALTER TABLE expenses MODIFY COLUMN status ENUM(''open'', ''pending'', ''success'', ''rejected'', ''returned'', ''checked'') DEFAULT ''open'''
));
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- Check and add check-related fields if they don't exist
SET @sql = (SELECT IF(
    (SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS
     WHERE TABLE_SCHEMA = 'expenses_system'
     AND TABLE_NAME = 'expenses'
     AND COLUMN_NAME = 'checked_by') > 0,
    'SELECT "checked_by already exists" as message',
    'ALTER TABLE expenses ADD COLUMN checked_by INT NULL'
));
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

SET @sql = (SELECT IF(
    (SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS
     WHERE TABLE_SCHEMA = 'expenses_system'
     AND TABLE_NAME = 'expenses'
     AND COLUMN_NAME = 'checked_at') > 0,
    'SELECT "checked_at already exists" as message',
    'ALTER TABLE expenses ADD COLUMN checked_at TIMESTAMP NULL'
));
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- Update activity_logs to include 'check' action
ALTER TABLE activity_logs
MODIFY COLUMN action_type ENUM(
    'create', 'update', 'delete', 'status_change', 'login', 'logout',
    'batch_verification', 'batch_review', 'batch_cancel', 'batch_retry',
    'admin_override', 'reject', 'return', 'test', 'test_debug', 'check'
) NOT NULL;

-- Create index for better performance
CREATE INDEX idx_expenses_checked_by ON expenses(checked_by);
CREATE INDEX idx_expenses_checked_at ON expenses(checked_at);
CREATE INDEX idx_expenses_batch_id ON expenses(batch_id);
CREATE INDEX idx_expenses_reviewer_batch_id ON expenses(reviewer_batch_id);
CREATE INDEX idx_expenses_status_checked ON expenses(status, checked_at);

-- Update workflow history procedure to handle 'checked' status
DELIMITER //

DROP PROCEDURE IF EXISTS AddWorkflowHistory//

CREATE PROCEDURE AddWorkflowHistory(
    IN p_expense_id INT,
    IN p_action VARCHAR(50),
    IN p_from_status VARCHAR(20),
    IN p_to_status VARCHAR(20),
    IN p_user_id INT,
    IN p_comment TEXT
)
BEGIN
    DECLARE EXIT HANDLER FOR SQLEXCEPTION
    BEGIN
        ROLLBACK;
        RESIGNAL;
    END;

    START TRANSACTION;

    -- Insert into activity_logs
    INSERT INTO activity_logs (
        user_id, action_type, table_name, record_id, 
        description, created_at
    ) VALUES (
        p_user_id, p_action, 'expenses', p_expense_id,
        CONCAT('Status changed from ', p_from_status, ' to ', p_to_status, 
               CASE WHEN p_comment IS NOT NULL AND p_comment != '' 
                    THEN CONCAT('. Comment: ', p_comment) 
                    ELSE '' END),
        NOW()
    );

    COMMIT;
END//

DELIMITER ;

-- Sample data for testing (optional)
-- INSERT INTO expenses (sequence, exno, job_open_date, withdrawal_date, status, created_by) 
-- VALUES ('001', '20241024-001', '2024-10-24', '2024-10-24', 'open', 1);

-- Verification: This migration adds the 'checked' status and related functionality
-- to support the new verification workflow where verification users can 
-- first 'check' expenses before proceeding to verify or reject them.
