-- Migration: Add batch_file_uploads table for multi-file support
-- Date: 2025-01-25
-- Description: Create table to store multiple file uploads for batch processing

-- Create batch_file_uploads table
CREATE TABLE IF NOT EXISTS batch_file_uploads (
    id INT PRIMARY KEY AUTO_INCREMENT,
    batch_id VARCHAR(50) NOT NULL COMMENT 'Batch ID reference',
    file_path VARCHAR(500) NOT NULL COMMENT 'Path to uploaded file',
    transfer_number VARCHAR(100) NOT NULL COMMENT 'Transfer number for this file',
    amount DECIMAL(15,2) NOT NULL COMMENT 'Amount for this file',
    original_filename VARCHAR(255) NOT NULL COMMENT 'Original filename',
    file_size INT NOT NULL COMMENT 'File size in bytes',
    mime_type VARCHAR(100) NOT NULL COMMENT 'File MIME type',
    uploaded_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT 'Upload timestamp',
    
    INDEX idx_batch_id (batch_id),
    INDEX idx_transfer_number (transfer_number),
    INDEX idx_uploaded_at (uploaded_at),
    
    FOREIGN KEY (batch_id) REFERENCES batch_operations(batch_id) ON DELETE CASCADE
) COMMENT='Multiple file uploads for batch processing';

-- Add columns to batch_documents table for enhanced tracking (check if exists first)
SET @sql = (SELECT IF(
    (SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS
     WHERE TABLE_SCHEMA = 'expenses_system'
     AND TABLE_NAME = 'batch_documents'
     AND COLUMN_NAME = 'transfer_number') = 0,
    'ALTER TABLE batch_documents ADD COLUMN transfer_number VARCHAR(100) NULL COMMENT ''Transfer number for this document''',
    'SELECT ''Column transfer_number already exists'' as message'
));
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

SET @sql = (SELECT IF(
    (SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS
     WHERE TABLE_SCHEMA = 'expenses_system'
     AND TABLE_NAME = 'batch_documents'
     AND COLUMN_NAME = 'amount') = 0,
    'ALTER TABLE batch_documents ADD COLUMN amount DECIMAL(15,2) NULL COMMENT ''Amount associated with this document''',
    'SELECT ''Column amount already exists'' as message'
));
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- Add index for transfer_number in batch_documents (check if exists first)
SET @sql = (SELECT IF(
    (SELECT COUNT(*) FROM INFORMATION_SCHEMA.STATISTICS
     WHERE TABLE_SCHEMA = 'expenses_system'
     AND TABLE_NAME = 'batch_documents'
     AND INDEX_NAME = 'idx_batch_documents_transfer_number') = 0,
    'CREATE INDEX idx_batch_documents_transfer_number ON batch_documents(transfer_number)',
    'SELECT ''Index idx_batch_documents_transfer_number already exists'' as message'
));
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- Update batch_documents document_type enum to include new types
ALTER TABLE batch_documents 
MODIFY COLUMN document_type ENUM('verification_slip', 'review_slip', 'verification_file', 'review_file') NOT NULL;

-- Create view for batch file summary
CREATE OR REPLACE VIEW vw_batch_file_summary AS
SELECT 
    bfu.batch_id,
    COUNT(*) as total_files,
    SUM(bfu.amount) as total_amount,
    MIN(bfu.uploaded_at) as first_upload,
    MAX(bfu.uploaded_at) as last_upload,
    GROUP_CONCAT(bfu.transfer_number ORDER BY bfu.uploaded_at) as transfer_numbers
FROM batch_file_uploads bfu
GROUP BY bfu.batch_id;

-- Create view for detailed batch file information
CREATE OR REPLACE VIEW vw_batch_files_detail AS
SELECT 
    bfu.id,
    bfu.batch_id,
    bo.operation_type,
    bo.status as batch_status,
    bfu.file_path,
    bfu.transfer_number,
    bfu.amount,
    bfu.original_filename,
    bfu.file_size,
    bfu.mime_type,
    bfu.uploaded_at,
    u.username as uploaded_by
FROM batch_file_uploads bfu
JOIN batch_operations bo ON bfu.batch_id = bo.batch_id
LEFT JOIN users u ON bo.user_id = u.id
ORDER BY bfu.batch_id, bfu.uploaded_at;

-- Add logging for file operations
INSERT INTO system_logs (log_level, category, message, created_at) 
VALUES ('INFO', 'database', 'Migration 006: Added batch_file_uploads table and enhanced batch_documents', NOW());
