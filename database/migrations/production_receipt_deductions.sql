-- =====================================================
-- Production Receipt Deductions System Migration
-- =====================================================
-- This migration adds support for receipt deductions
-- on the production server (nkslgroup.com)
-- Database: nksl_expense
-- =====================================================

USE nksl_expense;

-- Step 1: Add new columns to receipt_numbers table
-- =====================================================

-- Add gross_amount column (amount before deductions)
ALTER TABLE receipt_numbers 
ADD COLUMN gross_amount DECIMAL(15,2) DEFAULT 0.00 COMMENT 'ยอดก่อนหัก' AFTER amount;

-- Add has_deductions flag
ALTER TABLE receipt_numbers 
ADD COLUMN has_deductions BOOLEAN DEFAULT FALSE COMMENT 'มีรายการหักหรือไม่' AFTER gross_amount;

-- Add net_amount_calculated column for verification
ALTER TABLE receipt_numbers 
ADD COLUMN net_amount_calculated DECIMAL(15,2) DEFAULT 0.00 COMMENT 'ยอดสุทธิที่คำนวณได้' AFTER has_deductions;

-- Step 2: Initialize data for existing records
-- =====================================================

-- Copy existing amount to gross_amount for all existing records
UPDATE receipt_numbers 
SET gross_amount = amount, 
    net_amount_calculated = amount,
    has_deductions = FALSE 
WHERE gross_amount = 0.00;

-- Step 3: Create receipt_deductions table
-- =====================================================

CREATE TABLE receipt_deductions (
    id INT PRIMARY KEY AUTO_INCREMENT,
    receipt_number_id INT NOT NULL COMMENT 'Foreign key to receipt_numbers.id',
    
    -- Deduction details
    deduction_type ENUM(
        'tax_vat',           -- ภาษีมูลค่าเพิ่ม
        'tax_withholding',   -- ภาษีหัก ณ ที่จ่าย
        'service_fee',       -- ค่าธรรมเนียม
        'discount',          -- ส่วนลด
        'penalty',           -- ค่าปรับ
        'commission',        -- ค่าคอมมิชชั่น
        'other'              -- อื่นๆ
    ) NOT NULL COMMENT 'ประเภทการหัก',
    
    amount DECIMAL(15,2) NOT NULL COMMENT 'จำนวนเงินที่หัก',
    percentage DECIMAL(5,2) NULL COMMENT 'เปอร์เซ็นต์การหัก (ถ้ามี)',
    description TEXT COMMENT 'รายละเอียดการหัก',
    
    -- Calculation flags
    is_percentage_based BOOLEAN DEFAULT FALSE COMMENT 'คำนวณจากเปอร์เซ็นต์หรือไม่',
    is_auto_calculated BOOLEAN DEFAULT FALSE COMMENT 'คำนวณอัตโนมัติหรือไม่',
    
    -- Audit fields
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    created_by INT COMMENT 'User ID who created this deduction',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    updated_by INT COMMENT 'User ID who last updated this deduction',
    
    -- Foreign key constraints
    FOREIGN KEY (receipt_number_id) REFERENCES receipt_numbers(id) ON DELETE CASCADE,
    FOREIGN KEY (created_by) REFERENCES users(id) ON DELETE SET NULL,
    FOREIGN KEY (updated_by) REFERENCES users(id) ON DELETE SET NULL,
    
    -- Indexes for performance
    INDEX idx_receipt_deductions_receipt_id (receipt_number_id),
    INDEX idx_receipt_deductions_type (deduction_type),
    INDEX idx_receipt_deductions_created_at (created_at)
) COMMENT='ตารางเก็บรายการหักของใบเสร็จ';

-- Step 4: Create helper views for easy querying
-- =====================================================

-- View to show receipt summary with deductions
CREATE VIEW receipt_summary AS
SELECT 
    rn.id,
    rn.expense_id,
    rn.receipt_number,
    rn.gross_amount,
    rn.amount as original_amount,
    rn.net_amount_calculated,
    rn.has_deductions,
    rn.description,
    rn.receipt_image,
    rn.created_at,
    rn.created_by,
    
    -- Deduction summary
    COALESCE(SUM(rd.amount), 0) as total_deductions,
    COUNT(rd.id) as deduction_count,
    
    -- Calculated net amount
    (rn.gross_amount - COALESCE(SUM(rd.amount), 0)) as calculated_net_amount
    
FROM receipt_numbers rn
LEFT JOIN receipt_deductions rd ON rn.id = rd.receipt_number_id
GROUP BY rn.id, rn.expense_id, rn.receipt_number, rn.gross_amount, 
         rn.amount, rn.net_amount_calculated, rn.has_deductions, 
         rn.description, rn.receipt_image, rn.created_at, rn.created_by;

-- Step 5: Create stored procedures for common operations
-- =====================================================

DELIMITER //

-- Procedure to add a deduction to a receipt
CREATE PROCEDURE AddReceiptDeduction(
    IN p_receipt_id INT,
    IN p_deduction_type VARCHAR(50),
    IN p_amount DECIMAL(15,2),
    IN p_percentage DECIMAL(5,2),
    IN p_description TEXT,
    IN p_is_percentage_based BOOLEAN,
    IN p_created_by INT
)
BEGIN
    DECLARE v_gross_amount DECIMAL(15,2);
    DECLARE v_calculated_amount DECIMAL(15,2);
    
    -- Start transaction
    START TRANSACTION;
    
    -- Get gross amount
    SELECT gross_amount INTO v_gross_amount 
    FROM receipt_numbers 
    WHERE id = p_receipt_id;
    
    -- Calculate amount if percentage-based
    IF p_is_percentage_based THEN
        SET v_calculated_amount = (v_gross_amount * p_percentage / 100);
    ELSE
        SET v_calculated_amount = p_amount;
    END IF;
    
    -- Insert deduction
    INSERT INTO receipt_deductions (
        receipt_number_id, deduction_type, amount, percentage, 
        description, is_percentage_based, created_by
    ) VALUES (
        p_receipt_id, p_deduction_type, v_calculated_amount, p_percentage,
        p_description, p_is_percentage_based, p_created_by
    );
    
    -- Update receipt_numbers
    CALL UpdateReceiptCalculations(p_receipt_id);
    
    COMMIT;
END //

-- Procedure to update receipt calculations
CREATE PROCEDURE UpdateReceiptCalculations(IN p_receipt_id INT)
BEGIN
    DECLARE v_gross_amount DECIMAL(15,2);
    DECLARE v_total_deductions DECIMAL(15,2);
    DECLARE v_net_amount DECIMAL(15,2);
    DECLARE v_has_deductions BOOLEAN;
    
    -- Get gross amount
    SELECT gross_amount INTO v_gross_amount 
    FROM receipt_numbers 
    WHERE id = p_receipt_id;
    
    -- Calculate total deductions
    SELECT COALESCE(SUM(amount), 0) INTO v_total_deductions
    FROM receipt_deductions 
    WHERE receipt_number_id = p_receipt_id;
    
    -- Calculate net amount
    SET v_net_amount = v_gross_amount - v_total_deductions;
    
    -- Check if has deductions
    SET v_has_deductions = (v_total_deductions > 0);
    
    -- Update receipt_numbers table
    UPDATE receipt_numbers 
    SET 
        net_amount_calculated = v_net_amount,
        has_deductions = v_has_deductions
    WHERE id = p_receipt_id;
    
END //

DELIMITER ;

-- Step 6: Create triggers to maintain data consistency
-- =====================================================

-- Trigger to update calculations when deduction is inserted
DELIMITER //
CREATE TRIGGER tr_receipt_deductions_after_insert
AFTER INSERT ON receipt_deductions
FOR EACH ROW
BEGIN
    CALL UpdateReceiptCalculations(NEW.receipt_number_id);
END //
DELIMITER ;

-- Trigger to update calculations when deduction is updated
DELIMITER //
CREATE TRIGGER tr_receipt_deductions_after_update
AFTER UPDATE ON receipt_deductions
FOR EACH ROW
BEGIN
    CALL UpdateReceiptCalculations(NEW.receipt_number_id);
END //
DELIMITER ;

-- Trigger to update calculations when deduction is deleted
DELIMITER //
CREATE TRIGGER tr_receipt_deductions_after_delete
AFTER DELETE ON receipt_deductions
FOR EACH ROW
BEGIN
    CALL UpdateReceiptCalculations(OLD.receipt_number_id);
END //
DELIMITER ;

-- =====================================================
-- Production Migration Complete
-- =====================================================
-- 
-- Summary of changes:
-- 1. Added gross_amount, has_deductions, net_amount_calculated to receipt_numbers
-- 2. Created receipt_deductions table with full audit trail
-- 3. Created receipt_summary view for easy querying
-- 4. Created stored procedures for common operations
-- 5. Created triggers to maintain data consistency
--
-- Next steps:
-- 1. Update PHP code to use new structure
-- 2. Update UI to support deductions input
-- 3. Test with production data
-- =====================================================

-- Verification queries
-- =====================================================

-- Check table structure
DESCRIBE receipt_numbers;
DESCRIBE receipt_deductions;

-- Check views and procedures
SHOW TABLES LIKE '%receipt%';
SHOW PROCEDURE STATUS WHERE Db = 'nksl_expense' AND Name LIKE '%Receipt%';

-- Sample data verification
SELECT COUNT(*) as total_receipts FROM receipt_numbers;
SELECT COUNT(*) as total_deductions FROM receipt_deductions;

-- Show sample receipt summary
SELECT * FROM receipt_summary LIMIT 5;
