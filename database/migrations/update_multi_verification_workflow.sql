-- Migration: Update Multi-Verification Workflow
-- Purpose: Modify multi-verification to work with checked status
-- Date: 2024-10-24

-- Check and add requires_check column if it doesn't exist
SET @sql = (SELECT IF(
    (SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS
     WHERE TABLE_SCHEMA = 'expenses_system'
     AND TABLE_NAME = 'batch_operations'
     AND COLUMN_NAME = 'requires_check') > 0,
    'SELECT "requires_check already exists" as message',
    'ALTER TABLE batch_operations ADD COLUMN requires_check BOOLEAN DEFAULT FALSE AFTER operation_type'
));
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- Check and add min_check_status column if it doesn't exist
SET @sql = (SELECT IF(
    (SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS
     WHERE TABLE_SCHEMA = 'expenses_system'
     AND TAB<PERSON>_NAME = 'batch_operations'
     AND COLUMN_NAME = 'min_check_status') > 0,
    'SELECT "min_check_status already exists" as message',
    'ALTER TABLE batch_operations ADD COLUMN min_check_status ENUM(''open'', ''checked'') DEFAULT ''checked'''
));
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- Add index for better performance
CREATE INDEX idx_batch_operations_requires_check ON batch_operations(requires_check);
CREATE INDEX idx_batch_operations_min_check_status ON batch_operations(min_check_status);

-- Update batch_items table to track individual item status
ALTER TABLE batch_items
ADD COLUMN item_checked_at TIMESTAMP NULL AFTER processed_at,
ADD COLUMN item_checked_by INT NULL AFTER item_checked_at;

-- Add foreign key for checked_by
ALTER TABLE batch_items 
ADD CONSTRAINT fk_batch_items_checked_by 
FOREIGN KEY (item_checked_by) REFERENCES users(id) ON DELETE SET NULL;

-- Create index for better performance
CREATE INDEX idx_batch_items_checked_at ON batch_items(item_checked_at);
CREATE INDEX idx_batch_items_checked_by ON batch_items(item_checked_by);

-- Create view for multi-verification eligible expenses
CREATE OR REPLACE VIEW vw_multi_verification_eligible AS
SELECT 
    e.*,
    u.username as created_by_name,
    cb.username as checked_by_name,
    CASE 
        WHEN e.status = 'checked' THEN 'eligible'
        WHEN e.status = 'open' THEN 'needs_check'
        ELSE 'not_eligible'
    END as verification_eligibility
FROM expenses e
LEFT JOIN users u ON e.created_by = u.id
LEFT JOIN users cb ON e.checked_by = cb.id
WHERE e.status IN ('open', 'checked')
AND e.id NOT IN (
    SELECT DISTINCT bi.expense_id 
    FROM batch_items bi 
    INNER JOIN batch_operations bo ON bi.batch_id = bo.batch_id 
    WHERE bo.status IN ('pending', 'processing')
);

-- Create view for reviewer eligible batches
CREATE OR REPLACE VIEW vw_reviewer_eligible_batches AS
SELECT 
    bo.*,
    u.username as created_by_name,
    COUNT(bi.expense_id) as total_items,
    SUM(e.total_amount) as total_amount,
    MIN(e.created_at) as earliest_expense_date,
    MAX(e.created_at) as latest_expense_date
FROM batch_operations bo
LEFT JOIN users u ON bo.user_id = u.id
LEFT JOIN batch_items bi ON bo.batch_id = bi.batch_id
LEFT JOIN expenses e ON bi.expense_id = e.id
WHERE bo.operation_type = 'verification'
AND bo.status = 'completed'
AND bo.batch_id NOT IN (
    SELECT DISTINCT reviewer_batch_id 
    FROM expenses 
    WHERE reviewer_batch_id IS NOT NULL
)
GROUP BY bo.batch_id, bo.id, bo.user_id, bo.operation_type, bo.status,
         bo.created_at, bo.completed_at, u.username;

-- Update stored procedure for batch creation with check requirement
DELIMITER //

DROP PROCEDURE IF EXISTS CreateVerificationBatch//

CREATE PROCEDURE CreateVerificationBatch(
    IN p_user_id INT,
    IN p_expense_ids JSON,
    IN p_requires_check BOOLEAN,
    OUT p_batch_id VARCHAR(50)
)
BEGIN
    DECLARE v_batch_id VARCHAR(50);
    DECLARE v_expense_id INT;
    DECLARE v_expense_status VARCHAR(20);
    DECLARE v_counter INT DEFAULT 0;
    DECLARE v_total_expenses INT;
    DECLARE done INT DEFAULT FALSE;
    
    DECLARE expense_cursor CURSOR FOR 
        SELECT JSON_UNQUOTE(JSON_EXTRACT(p_expense_ids, CONCAT('$[', v_counter, ']')));
    
    DECLARE EXIT HANDLER FOR SQLEXCEPTION
    BEGIN
        ROLLBACK;
        RESIGNAL;
    END;

    START TRANSACTION;
    
    -- Generate batch ID
    SET v_batch_id = CONCAT('VB-', DATE_FORMAT(NOW(), '%Y%m%d'), '-', 
                           LPAD((SELECT COALESCE(MAX(SUBSTRING(batch_id, -3)), 0) + 1 
                                FROM batch_operations 
                                WHERE batch_id LIKE CONCAT('VB-', DATE_FORMAT(NOW(), '%Y%m%d'), '-%')), 3, '0'));
    
    -- Create batch operation record
    INSERT INTO batch_operations (
        batch_id, user_id, operation_type, status, requires_check, min_check_status, created_at
    ) VALUES (
        v_batch_id, p_user_id, 'verification', 'pending', p_requires_check,
        CASE WHEN p_requires_check THEN 'checked' ELSE 'open' END, NOW()
    );
    
    -- Get total number of expenses
    SET v_total_expenses = JSON_LENGTH(p_expense_ids);
    
    -- Add expenses to batch
    WHILE v_counter < v_total_expenses DO
        SET v_expense_id = JSON_UNQUOTE(JSON_EXTRACT(p_expense_ids, CONCAT('$[', v_counter, ']')));
        
        -- Check expense status
        SELECT status INTO v_expense_status FROM expenses WHERE id = v_expense_id;
        
        -- Validate expense eligibility
        IF p_requires_check AND v_expense_status != 'checked' THEN
            SIGNAL SQLSTATE '45000' SET MESSAGE_TEXT = 'All expenses must be checked before batch verification';
        END IF;
        
        IF NOT p_requires_check AND v_expense_status NOT IN ('open', 'checked') THEN
            SIGNAL SQLSTATE '45000' SET MESSAGE_TEXT = 'Expense not eligible for verification';
        END IF;
        
        -- Add to batch (using individual_amount from expenses table)
        INSERT INTO batch_items (batch_id, expense_id, individual_amount)
        SELECT v_batch_id, v_expense_id, total_amount FROM expenses WHERE id = v_expense_id;
        
        SET v_counter = v_counter + 1;
    END WHILE;
    
    SET p_batch_id = v_batch_id;
    COMMIT;
END//

DELIMITER ;

-- Sample data for testing
INSERT INTO system_logs (log_level, category, message, context) VALUES
('INFO', 'MIGRATION', 'Multi-verification workflow updated', 
 '{"features": ["check_requirement", "batch_tracking", "reviewer_workflow"], "views_created": 2, "procedures_updated": 1}');

-- Verification: This migration enhances multi-verification workflow to:
-- 1. Support check requirement before verification
-- 2. Track individual item check status in batches
-- 3. Create views for eligible expenses and batches
-- 4. Update batch creation procedure with validation
