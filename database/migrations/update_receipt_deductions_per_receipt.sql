-- Migration: Update Receipt Deductions for Per-Receipt Support
-- Date: 2025-10-23
-- Description: Add receipt_index and deduction_image columns to support per-receipt deductions

USE expenses_system;

-- Add new columns to receipt_deductions table
ALTER TABLE receipt_deductions 
ADD COLUMN receipt_index INT NOT NULL DEFAULT 0 COMMENT 'Index ของ receipt file (0,1,2...)' AFTER receipt_number_id,
ADD COLUMN deduction_image VARCHAR(255) NULL COMMENT 'รูปหลักฐานการหัก' AFTER description;

-- Add index for better performance
ALTER TABLE receipt_deductions 
ADD INDEX idx_receipt_index (receipt_index);

-- Update existing stored procedure to handle receipt_index
DROP PROCEDURE IF EXISTS AddReceiptDeduction;

DELIMITER //
CREATE PROCEDURE AddReceiptDeduction(
    IN p_receipt_number_id INT,
    IN p_receipt_index INT,
    IN p_deduction_type VARCHAR(50),
    IN p_amount DECIMAL(15,2),
    IN p_percentage DECIMAL(5,2),
    IN p_description TEXT,
    IN p_deduction_image VARCHAR(255),
    IN p_is_percentage_based BOOLEAN,
    IN p_created_by INT,
    OUT p_deduction_id INT,
    OUT p_success BOOLEAN,
    OUT p_message VARCHAR(255)
)
BEGIN
    DECLARE EXIT HANDLER FOR SQLEXCEPTION
    BEGIN
        ROLLBACK;
        SET p_success = FALSE;
        SET p_message = 'Error adding deduction';
        SET p_deduction_id = NULL;
    END;

    START TRANSACTION;

    -- Insert deduction
    INSERT INTO receipt_deductions (
        receipt_number_id, 
        receipt_index,
        deduction_type, 
        amount, 
        percentage, 
        description, 
        deduction_image,
        is_percentage_based, 
        created_by
    ) VALUES (
        p_receipt_number_id, 
        p_receipt_index,
        p_deduction_type, 
        p_amount, 
        p_percentage, 
        p_description, 
        p_deduction_image,
        p_is_percentage_based, 
        p_created_by
    );

    SET p_deduction_id = LAST_INSERT_ID();

    -- Update receipt calculations
    CALL UpdateReceiptCalculations(p_receipt_number_id);

    SET p_success = TRUE;
    SET p_message = 'Deduction added successfully';

    COMMIT;
END //
DELIMITER ;

-- Update the UpdateReceiptCalculations procedure to be more accurate
DROP PROCEDURE IF EXISTS UpdateReceiptCalculations;

DELIMITER //
CREATE PROCEDURE UpdateReceiptCalculations(
    IN p_receipt_number_id INT
)
BEGIN
    DECLARE v_gross_amount DECIMAL(15,2) DEFAULT 0;
    DECLARE v_total_deductions DECIMAL(15,2) DEFAULT 0;
    DECLARE v_net_amount DECIMAL(15,2) DEFAULT 0;
    DECLARE v_has_deductions BOOLEAN DEFAULT FALSE;

    -- Get current gross amount
    SELECT COALESCE(gross_amount, amount, 0) INTO v_gross_amount
    FROM receipt_numbers 
    WHERE id = p_receipt_number_id;

    -- Calculate total deductions for this receipt
    SELECT COALESCE(SUM(amount), 0) INTO v_total_deductions
    FROM receipt_deductions 
    WHERE receipt_number_id = p_receipt_number_id;

    -- Calculate net amount
    SET v_net_amount = v_gross_amount - v_total_deductions;

    -- Check if has deductions
    SET v_has_deductions = (v_total_deductions > 0);

    -- Update receipt_numbers table
    UPDATE receipt_numbers 
    SET 
        has_deductions = v_has_deductions,
        net_amount_calculated = v_net_amount,
        updated_at = CURRENT_TIMESTAMP
    WHERE id = p_receipt_number_id;

END //
DELIMITER ;

-- Update receipt_summary view to include receipt_index
DROP VIEW IF EXISTS receipt_summary;

CREATE VIEW receipt_summary AS
SELECT 
    rn.id as receipt_id,
    rn.expense_id,
    rn.receipt_number,
    rn.amount as original_amount,
    rn.gross_amount,
    rn.has_deductions,
    rn.net_amount_calculated,
    rn.description as receipt_description,
    rn.receipt_image,
    COALESCE(SUM(rd.amount), 0) as total_deductions,
    COUNT(rd.id) as deduction_count,
    GROUP_CONCAT(
        CONCAT(rd.deduction_type, ':', rd.amount, 
               CASE WHEN rd.deduction_image IS NOT NULL 
                    THEN CONCAT(':', rd.deduction_image) 
                    ELSE '' 
               END
        ) 
        ORDER BY rd.receipt_index, rd.created_at 
        SEPARATOR '|'
    ) as deductions_detail,
    rn.created_at,
    rn.created_by
FROM receipt_numbers rn
LEFT JOIN receipt_deductions rd ON rn.id = rd.receipt_number_id
GROUP BY rn.id, rn.expense_id, rn.receipt_number, rn.amount, rn.gross_amount, 
         rn.has_deductions, rn.net_amount_calculated, rn.description, 
         rn.receipt_image, rn.created_at, rn.created_by;

-- Create uploads/deductions directory structure (will be created by PHP)
-- This is just a comment for reference

SELECT 'Migration completed successfully' as status;
