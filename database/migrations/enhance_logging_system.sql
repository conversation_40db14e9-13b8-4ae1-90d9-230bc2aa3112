-- Migration: Enhanced Logging System
-- Purpose: Improve activity logs and add system logs for better tracking
-- Date: 2024-10-24

-- Create system_logs table for system-level events
CREATE TABLE IF NOT EXISTS system_logs (
    id INT AUTO_INCREMENT PRIMARY KEY,
    log_level ENUM('INFO', 'WARNING', 'ERROR', 'DEBUG') NOT NULL DEFAULT 'INFO',
    category VARCHAR(50) NOT NULL,
    message TEXT NOT NULL,
    context JSON NULL,
    user_id INT NULL,
    ip_address VARCHAR(45) NULL,
    user_agent TEXT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    INDEX idx_system_logs_level (log_level),
    INDEX idx_system_logs_category (category),
    INDEX idx_system_logs_user_id (user_id),
    INDEX idx_system_logs_created_at (created_at),
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE SET NULL
);

-- Enhance activity_logs table
ALTER TABLE activity_logs 
ADD COLUMN batch_id VARCHAR(50) NULL AFTER record_id,
ADD COLUMN session_id VARCHAR(100) NULL AFTER batch_id,
ADD COLUMN duration_ms INT NULL AFTER session_id,
ADD COLUMN status ENUM('SUCCESS', 'FAILED', 'PENDING') DEFAULT 'SUCCESS' AFTER duration_ms,
ADD COLUMN error_message TEXT NULL AFTER status;

-- Add indexes for better performance
CREATE INDEX idx_activity_logs_batch_id ON activity_logs(batch_id);
CREATE INDEX idx_activity_logs_session_id ON activity_logs(session_id);
CREATE INDEX idx_activity_logs_status ON activity_logs(status);
CREATE INDEX idx_activity_logs_action_type_created ON activity_logs(action_type, created_at);

-- Create batch_operations_logs table for detailed batch tracking
CREATE TABLE IF NOT EXISTS batch_operations_logs (
    id INT AUTO_INCREMENT PRIMARY KEY,
    batch_id VARCHAR(50) NOT NULL,
    operation_type ENUM('CREATE', 'PROCESS', 'COMPLETE', 'CANCEL', 'ERROR') NOT NULL,
    expense_id INT NULL,
    user_id INT NOT NULL,
    details JSON NULL,
    processing_time_ms INT NULL,
    memory_usage_mb DECIMAL(10,2) NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    INDEX idx_batch_ops_batch_id (batch_id),
    INDEX idx_batch_ops_operation_type (operation_type),
    INDEX idx_batch_ops_expense_id (expense_id),
    INDEX idx_batch_ops_user_id (user_id),
    INDEX idx_batch_ops_created_at (created_at),
    FOREIGN KEY (expense_id) REFERENCES expenses(id) ON DELETE SET NULL,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
);

-- Create file_operations_logs table for file upload/download tracking
CREATE TABLE IF NOT EXISTS file_operations_logs (
    id INT AUTO_INCREMENT PRIMARY KEY,
    operation_type ENUM('UPLOAD', 'DOWNLOAD', 'DELETE', 'VIEW') NOT NULL,
    file_path VARCHAR(500) NOT NULL,
    file_name VARCHAR(255) NOT NULL,
    file_size_bytes BIGINT NULL,
    mime_type VARCHAR(100) NULL,
    expense_id INT NULL,
    batch_id VARCHAR(50) NULL,
    user_id INT NOT NULL,
    ip_address VARCHAR(45) NULL,
    user_agent TEXT NULL,
    success BOOLEAN DEFAULT TRUE,
    error_message TEXT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    INDEX idx_file_ops_operation_type (operation_type),
    INDEX idx_file_ops_expense_id (expense_id),
    INDEX idx_file_ops_batch_id (batch_id),
    INDEX idx_file_ops_user_id (user_id),
    INDEX idx_file_ops_created_at (created_at),
    FOREIGN KEY (expense_id) REFERENCES expenses(id) ON DELETE SET NULL,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
);

-- Update existing procedures to support enhanced logging
DELIMITER //

DROP PROCEDURE IF EXISTS LogSystemEvent//

CREATE PROCEDURE LogSystemEvent(
    IN p_level VARCHAR(10),
    IN p_category VARCHAR(50),
    IN p_message TEXT,
    IN p_context JSON,
    IN p_user_id INT,
    IN p_ip_address VARCHAR(45),
    IN p_user_agent TEXT
)
BEGIN
    INSERT INTO system_logs (
        log_level, category, message, context, 
        user_id, ip_address, user_agent
    ) VALUES (
        p_level, p_category, p_message, p_context,
        p_user_id, p_ip_address, p_user_agent
    );
END//

DROP PROCEDURE IF EXISTS LogBatchOperation//

CREATE PROCEDURE LogBatchOperation(
    IN p_batch_id VARCHAR(50),
    IN p_operation_type VARCHAR(20),
    IN p_expense_id INT,
    IN p_user_id INT,
    IN p_details JSON,
    IN p_processing_time_ms INT,
    IN p_memory_usage_mb DECIMAL(10,2)
)
BEGIN
    INSERT INTO batch_operations_logs (
        batch_id, operation_type, expense_id, user_id,
        details, processing_time_ms, memory_usage_mb
    ) VALUES (
        p_batch_id, p_operation_type, p_expense_id, p_user_id,
        p_details, p_processing_time_ms, p_memory_usage_mb
    );
END//

DELIMITER ;

-- Sample system log entries for testing
INSERT INTO system_logs (log_level, category, message, context) VALUES
('INFO', 'SYSTEM', 'Enhanced logging system initialized', '{"version": "2.0", "features": ["batch_tracking", "file_operations", "performance_metrics"]}'),
('INFO', 'MIGRATION', 'Database schema updated for enhanced logging', '{"tables_added": 3, "indexes_added": 15, "procedures_added": 2}');

-- Verification: This migration enhances the logging system with:
-- 1. system_logs table for system-level events
-- 2. Enhanced activity_logs with batch tracking and performance metrics
-- 3. batch_operations_logs for detailed batch operation tracking
-- 4. file_operations_logs for file operation auditing
-- 5. New stored procedures for structured logging
