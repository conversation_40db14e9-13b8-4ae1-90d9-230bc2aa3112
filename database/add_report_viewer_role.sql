-- Add report_viewer role to the system
-- This role can only view reports and has read-only access

-- First, check if the role already exists in the ENUM
SET @sql = (SELECT IF(
    (SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS
     WHERE table_name = 'users'
     AND table_schema = DATABASE()
     AND column_name = 'role'
     AND column_type LIKE '%report_viewer%') > 0,
    'SELECT "report_viewer role already exists"',
    'ALTER TABLE users MODIFY COLUMN role ENUM(''data_entry'', ''verification'', ''reviewer'', ''report_viewer'', ''administrator'') NOT NULL DEFAULT ''data_entry'''
));
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- Create a sample report_viewer user (optional)
-- Password: report123 (hashed with bcrypt)
INSERT IGNORE INTO users (username, email, password_hash, full_name, role, is_active) VALUES 
('report_viewer', '<EMAIL>', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'Report Viewer', 'report_viewer', 1);

-- Add activity log entry for the role addition
INSERT INTO activity_logs (user_id, action_type, table_name, record_id, description, ip_address, user_agent) VALUES
(1, 'create', 'users', NULL, 'Added report_viewer role to system', '127.0.0.1', 'System Migration');
