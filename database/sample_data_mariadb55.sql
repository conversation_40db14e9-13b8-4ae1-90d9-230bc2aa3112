-- Sample Data for MariaDB 5.5.68 Compatible Expenses System
-- Insert this after importing the main schema

--
-- Sample data for table `users`
--

INSERT INTO `users` (`id`, `username`, `password`, `full_name`, `role`) VALUES
(1, 'admin', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'System Administrator', 'administrator'),
(2, 'data_entry1', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'Data Entry User', 'data_entry'),
(3, 'verifier1', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'Verification Officer', 'verification'),
(4, 'reviewer1', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'Review Officer', 'reviewer'),
(5, 'reporter1', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'Report Viewer', 'report_viewer');

--
-- Sample data for table `customers`
--

INSERT INTO `customers` (`id`, `name`, `contact_person`, `phone`, `email`, `address`, `is_active`, `created_at`, `updated_at`, `created_by`) VALUES
(1, 'ABC Trading Co., Ltd.', 'John Smith', '+66-2-123-4567', '<EMAIL>', '123 Business District, Bangkok', 1, NOW(), NOW(), 1),
(2, 'XYZ Logistics Ltd.', 'Jane Doe', '+66-2-234-5678', '<EMAIL>', '456 Industrial Zone, Bangkok', 1, NOW(), NOW(), 1),
(3, 'Global Shipping Inc.', 'Mike Johnson', '+66-2-345-6789', '<EMAIL>', '789 Port Area, Bangkok', 1, NOW(), NOW(), 1),
(4, 'Ocean Freight Services', 'Sarah Wilson', '+66-2-456-7890', '<EMAIL>', '321 Shipping District, Bangkok', 1, NOW(), NOW(), 1),
(5, 'International Cargo Co.', 'David Brown', '+66-2-567-8901', '<EMAIL>', '654 Logistics Hub, Bangkok', 1, NOW(), NOW(), 1);

--
-- Sample data for table `items`
--

INSERT INTO `items` (`id`, `name`, `description`, `is_active`, `created_at`, `updated_at`, `created_by`) VALUES
(1, 'Fuel', 'Vehicle fuel expenses', 1, NOW(), NOW(), 1),
(2, 'Maintenance', 'Vehicle maintenance and repairs', 1, NOW(), NOW(), 1),
(3, 'Port Charges', 'Port and terminal charges', 1, NOW(), NOW(), 1),
(4, 'Documentation', 'Document processing fees', 1, NOW(), NOW(), 1),
(5, 'Insurance', 'Cargo and vehicle insurance', 1, NOW(), NOW(), 1),
(6, 'Storage', 'Warehouse and storage fees', 1, NOW(), NOW(), 1),
(7, 'Transportation', 'Transportation and delivery costs', 1, NOW(), NOW(), 1),
(8, 'Handling', 'Cargo handling and loading fees', 1, NOW(), NOW(), 1),
(9, 'Customs Clearance', 'Customs and clearance fees', 1, NOW(), NOW(), 1),
(10, 'Administrative Fees', 'Administrative and processing fees', 1, NOW(), NOW(), 1);

--
-- Sample data for table `drivers`
--

INSERT INTO `drivers` (`id`, `name`, `license_number`, `phone`, `vehicle_plate`, `payment_account_no`, `is_active`, `created_at`, `updated_at`, `created_by`) VALUES
(1, 'Mike Johnson', 'DL123456', '+66-81-234-5678', 'ABC-1234', 'ACC-001', 1, NOW(), NOW(), 1),
(2, 'Sarah Wilson', 'DL789012', '+66-81-345-6789', 'XYZ-5678', 'ACC-002', 1, NOW(), NOW(), 1),
(3, 'David Brown', 'DL345678', '+66-81-456-7890', 'DEF-9012', 'ACC-003', 1, NOW(), NOW(), 1),
(4, 'Lisa Chen', 'DL901234', '+66-81-567-8901', 'GHI-3456', 'ACC-004', 1, NOW(), NOW(), 1),
(5, 'Robert Taylor', 'DL567890', '+66-81-678-9012', 'JKL-7890', 'ACC-005', 1, NOW(), NOW(), 1);

--
-- Sample data for table `expenses`
--

INSERT INTO `expenses` (`id`, `sequence`, `exno`, `bookingno`, `job_open_date`, `item_id`, `customer_id`, `containerno`, `driver_id`, `vehicle_plate`, `payment_account_no`, `additional_details`, `requester`, `receiver`, `payer`, `withdrawal_date`, `transfer_no`, `transfer_amount`, `total_amount`, `amount`, `receipt_file`, `status`, `created_at`, `updated_at`, `created_by`) VALUES
(1, '001', 'EX001', '*********', '2025-01-15', 1, 1, 'CONT001', 1, 'ABC-1234', 'ACC-001', 'Fuel for delivery', 'John Smith', 'ABC Trading', 'ABC Trading', '2025-01-15', 'TRF001', 1500.00, 1500.00, 1500.00, NULL, 'open', NOW(), NOW(), 2),
(2, '002', 'EX002', '*********', '2025-01-16', 2, 2, 'CONT002', 2, 'XYZ-5678', 'ACC-002', 'Vehicle maintenance', 'Jane Doe', 'XYZ Logistics', 'XYZ Logistics', '2025-01-16', 'TRF002', 2500.00, 2500.00, 2500.00, NULL, 'pending', NOW(), NOW(), 2),
(3, '003', 'EX003', '*********', '2025-01-17', 3, 3, 'CONT003', 3, 'DEF-9012', 'ACC-003', 'Port charges payment', 'Mike Johnson', 'Global Shipping', 'Global Shipping', '2025-01-17', 'TRF003', 3500.00, 3500.00, 3500.00, NULL, 'success', NOW(), NOW(), 2),
(4, '004', 'EX004', 'BK2025004', '2025-01-18', 4, 4, 'CONT004', 4, 'GHI-3456', 'ACC-004', 'Documentation fees', 'Sarah Wilson', 'Ocean Freight', 'Ocean Freight', '2025-01-18', 'TRF004', 1200.00, 1200.00, 1200.00, NULL, 'open', NOW(), NOW(), 2),
(5, '005', 'EX005', 'BK2025005', '2025-01-19', 5, 5, 'CONT005', 5, 'JKL-7890', 'ACC-005', 'Insurance payment', 'David Brown', 'International Cargo', 'International Cargo', '2025-01-19', 'TRF005', 800.00, 800.00, 800.00, NULL, 'pending', NOW(), NOW(), 2);

--
-- Sample data for table `batch_operations`
--

INSERT INTO `batch_operations` (`id`, `batch_id`, `operation_type`, `user_id`, `total_amount`, `item_count`, `status`, `created_at`) VALUES
(1, 'BATCH_VER_20250119_140000', 'verification', 3, 4000.00, 2, 'completed', '2025-01-19 14:00:00'),
(2, 'BATCH_REV_20250119_150000', 'review', 4, 4000.00, 2, 'completed', '2025-01-19 15:00:00');

--
-- Sample data for table `batch_items`
--

INSERT INTO `batch_items` (`id`, `batch_id`, `expense_id`, `individual_amount`) VALUES
(1, 'BATCH_VER_20250119_140000', 2, 2500.00),
(2, 'BATCH_VER_20250119_140000', 3, 1500.00),
(3, 'BATCH_REV_20250119_150000', 2, 2500.00),
(4, 'BATCH_REV_20250119_150000', 3, 1500.00);

--
-- Sample data for table `batch_documents`
--

INSERT INTO `batch_documents` (`id`, `batch_id`, `document_type`, `file_path`, `original_filename`, `file_size`, `mime_type`, `metadata`) VALUES
(1, 'BATCH_VER_20250119_140000', 'verification_slip', 'batch_documents/verification/BATCH_VER_20250119_140000_verification.pdf', 'verification_slip_001.pdf', 245760, 'application/pdf', '{"pages":1,"verified_by":"verifier1"}'),
(2, 'BATCH_REV_20250119_150000', 'review_slip', 'batch_documents/review/BATCH_REV_20250119_150000_review.pdf', 'review_slip_001.pdf', 189440, 'application/pdf', '{"pages":1,"reviewed_by":"reviewer1"}');

--
-- Sample data for table `batch_performance_logs`
--

INSERT INTO `batch_performance_logs` (`id`, `batch_id`, `operation_type`, `item_count`, `processing_time_ms`, `memory_usage_mb`, `user_id`) VALUES
(1, 'BATCH_VER_20250119_140000', 'verification', 2, 1250, 12.50, 3),
(2, 'BATCH_REV_20250119_150000', 'review', 2, 980, 11.20, 4);

--
-- Sample data for table `expense_workflow_history`
--

INSERT INTO `expense_workflow_history` (`id`, `expense_id`, `action_type`, `from_status`, `to_status`, `user_id`, `comment`) VALUES
(1, 2, 'submit', 'open', 'pending', 2, 'Initial submission'),
(2, 2, 'verify', 'pending', 'success', 3, 'Verified via batch operation'),
(3, 3, 'submit', 'open', 'pending', 2, 'Initial submission'),
(4, 3, 'verify', 'pending', 'success', 3, 'Verified via batch operation'),
(5, 3, 'review', 'success', 'success', 4, 'Reviewed and approved');

--
-- Sample data for table `receipt_numbers`
--

INSERT INTO `receipt_numbers` (`id`, `expense_id`, `receipt_number`) VALUES
(1, 1, 'RCP001-2025'),
(2, 2, 'RCP002-2025'),
(3, 3, 'RCP003-2025'),
(4, 4, 'RCP004-2025'),
(5, 5, 'RCP005-2025');

--
-- Sample data for table `activity_logs`
--

INSERT INTO `activity_logs` (`id`, `user_id`, `action`, `table_name`, `record_id`, `old_values`, `new_values`, `ip_address`) VALUES
(1, 2, 'CREATE', 'expenses', 1, NULL, '{"exno":"EX001","amount":"1500.00","status":"open"}', '127.0.0.1'),
(2, 2, 'CREATE', 'expenses', 2, NULL, '{"exno":"EX002","amount":"2500.00","status":"open"}', '127.0.0.1'),
(3, 3, 'UPDATE', 'expenses', 2, '{"status":"open"}', '{"status":"pending"}', '127.0.0.1'),
(4, 3, 'BATCH_VERIFY', 'batch_operations', 1, NULL, '{"batch_id":"BATCH_VER_20250119_140000","item_count":2}', '127.0.0.1'),
(5, 4, 'BATCH_REVIEW', 'batch_operations', 2, NULL, '{"batch_id":"BATCH_REV_20250119_150000","item_count":2}', '127.0.0.1');

-- Update expenses with batch IDs and workflow history
UPDATE `expenses` SET 
    `batch_verification_id` = 'BATCH_VER_20250119_140000',
    `verification_by` = 3,
    `verification_at` = '2025-01-19 14:05:00',
    `verification_amount` = `amount`,
    `verification_transfer_no` = 'TRF001',
    `workflow_history` = '[{"timestamp":"2025-01-19 14:05:00","action":"verify","from_status":"pending","to_status":"success","user_id":3,"comment":"Verified via batch operation"}]'
WHERE `id` IN (2, 3);

UPDATE `expenses` SET 
    `batch_review_id` = 'BATCH_REV_20250119_150000',
    `reviewer_by` = 4,
    `reviewer_at` = '2025-01-19 15:05:00',
    `reviewer_amount` = `amount`,
    `reviewer_transfer_no` = 'TRF002',
    `workflow_history` = '[{"timestamp":"2025-01-19 14:05:00","action":"verify","from_status":"pending","to_status":"success","user_id":3,"comment":"Verified via batch operation"},{"timestamp":"2025-01-19 15:05:00","action":"review","from_status":"success","to_status":"success","user_id":4,"comment":"Reviewed and approved"}]'
WHERE `id` = 3;

-- Note: Default password for all users is 'password'
-- Remember to change passwords in production!
