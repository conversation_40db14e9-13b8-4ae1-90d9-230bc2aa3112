-- Fix Production Issues for MariaDB 5.5.68 Compatibility
-- Run this script to fix identified issues

-- Fix 1: Extend exno column length
-- Current: VARCHAR(15) -> New: VARCHAR(50)
ALTER TABLE expenses MODIFY COLUMN exno VARCHAR(50) NOT NULL COMMENT 'Expense number - extended for longer test IDs';

-- Fix 2: Ensure workflow_history uses TEXT instead of JSON
-- Add workflow_history column if it doesn't exist
ALTER TABLE expenses ADD COLUMN IF NOT EXISTS workflow_history TEXT DEFAULT NULL COMMENT 'Workflow history as JSON string (MariaDB 5.5.68 compatible)';

-- If workflow_history exists as JSON type, convert to TEXT
-- Note: This will only work on MySQL/newer MariaDB, but ensures compatibility
-- ALTER TABLE expenses MODIFY COLUMN workflow_history TEXT COMMENT 'Workflow history as JSON string (MariaDB 5.5.68 compatible)';

-- Fix 3: Add missing workflow-related columns for MariaDB 5.5.68
ALTER TABLE expenses ADD COLUMN IF NOT EXISTS rejection_reason TEXT DEFAULT NULL COMMENT 'Reason for rejection';
ALTER TABLE expenses ADD COLUMN IF NOT EXISTS return_reason TEXT DEFAULT NULL COMMENT 'Reason for return';
ALTER TABLE expenses ADD COLUMN IF NOT EXISTS rejected_by INT DEFAULT NULL COMMENT 'User who rejected';
ALTER TABLE expenses ADD COLUMN IF NOT EXISTS returned_by INT DEFAULT NULL COMMENT 'User who returned';
ALTER TABLE expenses ADD COLUMN IF NOT EXISTS rejected_at TIMESTAMP NULL DEFAULT NULL COMMENT 'When rejected';
ALTER TABLE expenses ADD COLUMN IF NOT EXISTS returned_at TIMESTAMP NULL DEFAULT NULL COMMENT 'When returned';

-- Add foreign key constraints if they don't exist
-- Note: Use IF NOT EXISTS equivalent for MariaDB 5.5.68
SET @sql = (SELECT IF(
    (SELECT COUNT(*) FROM INFORMATION_SCHEMA.KEY_COLUMN_USAGE 
     WHERE TABLE_SCHEMA = DATABASE() 
     AND TABLE_NAME = 'expenses' 
     AND CONSTRAINT_NAME = 'fk_expenses_rejected_by') = 0,
    'ALTER TABLE expenses ADD CONSTRAINT fk_expenses_rejected_by FOREIGN KEY (rejected_by) REFERENCES users(id) ON DELETE SET NULL',
    'SELECT "Foreign key fk_expenses_rejected_by already exists"'
));
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

SET @sql = (SELECT IF(
    (SELECT COUNT(*) FROM INFORMATION_SCHEMA.KEY_COLUMN_USAGE 
     WHERE TABLE_SCHEMA = DATABASE() 
     AND TABLE_NAME = 'expenses' 
     AND CONSTRAINT_NAME = 'fk_expenses_returned_by') = 0,
    'ALTER TABLE expenses ADD CONSTRAINT fk_expenses_returned_by FOREIGN KEY (returned_by) REFERENCES users(id) ON DELETE SET NULL',
    'SELECT "Foreign key fk_expenses_returned_by already exists"'
));
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- Fix 4: Ensure batch_documents table uses TEXT instead of JSON for metadata
-- Check if batch_documents table exists and has metadata column
SET @sql = (SELECT IF(
    (SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS 
     WHERE TABLE_SCHEMA = DATABASE() 
     AND TABLE_NAME = 'batch_documents' 
     AND COLUMN_NAME = 'metadata') > 0,
    'ALTER TABLE batch_documents MODIFY COLUMN metadata TEXT COMMENT "Document metadata as JSON string"',
    'SELECT "batch_documents.metadata column does not exist"'
));
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- Fix 5: Create expense_workflow_history table for better workflow tracking
CREATE TABLE IF NOT EXISTS expense_workflow_history (
    id INT PRIMARY KEY AUTO_INCREMENT,
    expense_id INT NOT NULL,
    action_type ENUM('submit', 'verify', 'review', 'reject', 'return', 'resubmit') NOT NULL,
    from_status VARCHAR(20) NOT NULL,
    to_status VARCHAR(20) NOT NULL,
    user_id INT NOT NULL,
    comment TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    INDEX idx_expense_id (expense_id),
    INDEX idx_action_type (action_type),
    INDEX idx_created_at (created_at),
    
    FOREIGN KEY (expense_id) REFERENCES expenses(id) ON DELETE CASCADE,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
) COMMENT='Expense workflow history tracking (MariaDB 5.5.68 compatible)';

-- Fix 6: Create helper function for workflow history count (MariaDB 5.5.68 compatible)
DELIMITER //

DROP FUNCTION IF EXISTS GetWorkflowHistoryCount //

CREATE FUNCTION GetWorkflowHistoryCount(expense_id INT) 
RETURNS INT
READS SQL DATA
DETERMINISTIC
BEGIN
    DECLARE history_count INT DEFAULT 0;
    DECLARE history_text TEXT;
    
    -- Get workflow history
    SELECT workflow_history INTO history_text 
    FROM expenses 
    WHERE id = expense_id;
    
    -- Count entries by counting commas between objects
    IF history_text IS NOT NULL AND history_text != '' AND history_text != '[]' THEN
        -- Simple count: look for "},{"  patterns and add 1
        SET history_count = (
            CHAR_LENGTH(history_text) - CHAR_LENGTH(REPLACE(history_text, '},{', ''))
        ) / 2 + 1;
        
        -- If it's just empty array, set to 0
        IF history_text = '[]' THEN
            SET history_count = 0;
        END IF;
    END IF;
    
    RETURN history_count;
END //

DELIMITER ;

-- Fix 7: Create stored procedure for adding workflow history (MariaDB 5.5.68 compatible)
DELIMITER //

DROP PROCEDURE IF EXISTS AddWorkflowHistory //

CREATE PROCEDURE AddWorkflowHistory(
    IN expense_id INT,
    IN action_type VARCHAR(20),
    IN from_status VARCHAR(20),
    IN to_status VARCHAR(20),
    IN user_id INT,
    IN comment TEXT
)
BEGIN
    DECLARE history_text TEXT;
    DECLARE new_entry TEXT;
    DECLARE current_timestamp VARCHAR(20);
    
    -- Get current timestamp
    SET current_timestamp = DATE_FORMAT(NOW(), '%Y-%m-%d %H:%i:%s');
    
    -- Get current workflow history
    SELECT workflow_history INTO history_text 
    FROM expenses 
    WHERE id = expense_id;
    
    -- Escape quotes in comment
    SET comment = IFNULL(REPLACE(REPLACE(comment, '\\', '\\\\'), '"', '\\"'), '');
    
    -- Create new history entry as JSON string
    SET new_entry = CONCAT(
        '{"timestamp":"', current_timestamp, '",',
        '"action":"', action_type, '",',
        '"from_status":"', from_status, '",',
        '"to_status":"', to_status, '",',
        '"user_id":', user_id, ',',
        '"comment":"', comment, '"}'
    );
    
    -- If history is null or empty, initialize as array
    IF history_text IS NULL OR history_text = '' THEN
        SET history_text = CONCAT('[', new_entry, ']');
    ELSE
        -- Remove closing bracket and append new entry
        SET history_text = CONCAT(
            SUBSTRING(history_text, 1, CHAR_LENGTH(history_text) - 1),
            ',', new_entry, ']'
        );
    END IF;
    
    -- Update the expense record
    UPDATE expenses 
    SET workflow_history = history_text 
    WHERE id = expense_id;
    
    -- Also insert into workflow_history table for easier querying
    INSERT INTO expense_workflow_history (
        expense_id, action_type, from_status, to_status, user_id, comment
    ) VALUES (
        expense_id, action_type, from_status, to_status, user_id, comment
    );
    
END //

DELIMITER ;

-- Fix 8: Create view for easy workflow access
CREATE OR REPLACE VIEW expense_workflow_summary AS
SELECT 
    e.id,
    e.exno,
    e.status,
    e.workflow_history,
    GetWorkflowHistoryCount(e.id) as history_count,
    e.rejection_reason,
    e.return_reason,
    e.rejected_at,
    e.returned_at,
    ru.full_name as rejected_by_name,
    retu.full_name as returned_by_name
FROM expenses e
LEFT JOIN users ru ON e.rejected_by = ru.id
LEFT JOIN users retu ON e.returned_by = retu.id;

-- Fix 9: Add indexes for better performance on MariaDB 5.5.68
CREATE INDEX IF NOT EXISTS idx_expenses_exno_length ON expenses(exno(20));
CREATE INDEX IF NOT EXISTS idx_expenses_workflow_status ON expenses(status, rejected_at, returned_at);
CREATE INDEX IF NOT EXISTS idx_expenses_workflow_users ON expenses(rejected_by, returned_by);

-- Fix 10: Update any existing long exno values to shorter format
UPDATE expenses 
SET exno = CONCAT('EX', id) 
WHERE CHAR_LENGTH(exno) > 15 
AND exno LIKE 'PROD_TEST_%';

-- Success message
SELECT 'Production issues fixed successfully! MariaDB 5.5.68 compatibility ensured.' as message;
