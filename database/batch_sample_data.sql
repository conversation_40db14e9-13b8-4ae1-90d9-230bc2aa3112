-- ===================================================================
-- Sample Data สำหรับ Batch Operations Testing
-- ===================================================================

-- ตัวอย่าง Batch Verification Operation
INSERT INTO batch_operations (
    batch_id, operation_type, user_id, total_amount, item_count, 
    status, notes, created_at, started_at, completed_at
) VALUES 
(
    'BATCH_VER_20251017_001', 'verification', 3, 15000.00, 5, 
    'completed', 'Batch verification for morning expenses', 
    '2025-10-17 09:00:00', '2025-10-17 09:05:00', '2025-10-17 09:15:00'
),
(
    'BATCH_VER_20251017_002', 'verification', 3, 8500.00, 3, 
    'processing', 'Afternoon batch verification', 
    '2025-10-17 14:00:00', '2025-10-17 14:02:00', NULL
);

-- ตัวอย่าง Batch Review Operation
INSERT INTO batch_operations (
    batch_id, operation_type, user_id, total_amount, item_count, 
    status, notes, created_at, started_at, completed_at
) VALUES 
(
    'BATCH_REV_20251017_001', 'review', 4, 15000.00, 5, 
    'completed', 'Review batch from morning verification', 
    '2025-10-17 10:00:00', '2025-10-17 10:05:00', '2025-10-17 10:20:00'
);

-- ตัวอย่าง Batch Items
INSERT INTO batch_items (batch_id, expense_id, individual_amount, status, processed_at) VALUES 
-- Batch VER 001
('BATCH_VER_20251017_001', 1, 3000.00, 'completed', '2025-10-17 09:08:00'),
('BATCH_VER_20251017_001', 2, 2500.00, 'completed', '2025-10-17 09:10:00'),
('BATCH_VER_20251017_001', 3, 4000.00, 'completed', '2025-10-17 09:12:00'),
('BATCH_VER_20251017_001', 4, 3500.00, 'completed', '2025-10-17 09:13:00'),
('BATCH_VER_20251017_001', 6, 2000.00, 'completed', '2025-10-17 09:15:00'),

-- Batch VER 002 (กำลังประมวลผล)
('BATCH_VER_20251017_002', 8, 3000.00, 'completed', '2025-10-17 14:05:00'),
('BATCH_VER_20251017_002', 9, 2500.00, 'processing', NULL),
('BATCH_VER_20251017_002', 10, 3000.00, 'pending', NULL),

-- Batch REV 001
('BATCH_REV_20251017_001', 1, 3000.00, 'completed', '2025-10-17 10:08:00'),
('BATCH_REV_20251017_001', 2, 2500.00, 'completed', '2025-10-17 10:12:00'),
('BATCH_REV_20251017_001', 3, 4000.00, 'completed', '2025-10-17 10:15:00'),
('BATCH_REV_20251017_001', 4, 3500.00, 'completed', '2025-10-17 10:18:00'),
('BATCH_REV_20251017_001', 6, 2000.00, 'completed', '2025-10-17 10:20:00');

-- ตัวอย่าง Batch Documents
INSERT INTO batch_documents (
    batch_id, document_type, file_path, original_filename, 
    file_size, mime_type, uploaded_at
) VALUES 
(
    'BATCH_VER_20251017_001', 'verification_slip', 
    'batch_documents/verification/BATCH_VER_20251017_001_verification.pdf',
    'morning_verification_batch.pdf', 2048576, 'application/pdf',
    '2025-10-17 09:02:00'
),
(
    'BATCH_VER_20251017_002', 'verification_slip',
    'batch_documents/verification/BATCH_VER_20251017_002_verification.jpg',
    'afternoon_verification_batch.jpg', 1536000, 'image/jpeg',
    '2025-10-17 14:01:00'
),
(
    'BATCH_REV_20251017_001', 'review_slip',
    'batch_documents/review/BATCH_REV_20251017_001_review.pdf',
    'morning_review_batch.pdf', 1843200, 'application/pdf',
    '2025-10-17 10:02:00'
);

-- ตัวอย่าง Performance Logs
INSERT INTO batch_performance_logs (
    batch_id, operation_step, start_time, end_time, 
    duration_ms, memory_usage_mb, items_processed, success_count, error_count
) VALUES 
-- Batch VER 001 Performance
('BATCH_VER_20251017_001', 'validation', '2025-10-17 09:05:00', '2025-10-17 09:06:00', 60000, 12.5, 5, 5, 0),
('BATCH_VER_20251017_001', 'file_upload', '2025-10-17 09:06:00', '2025-10-17 09:07:00', 45000, 8.2, 1, 1, 0),
('BATCH_VER_20251017_001', 'database_update', '2025-10-17 09:07:00', '2025-10-17 09:15:00', 480000, 15.8, 5, 5, 0),

-- Batch REV 001 Performance
('BATCH_REV_20251017_001', 'validation', '2025-10-17 10:05:00', '2025-10-17 10:06:00', 45000, 10.2, 5, 5, 0),
('BATCH_REV_20251017_001', 'amount_verification', '2025-10-17 10:06:00', '2025-10-17 10:07:00', 30000, 8.5, 5, 5, 0),
('BATCH_REV_20251017_001', 'file_upload', '2025-10-17 10:07:00', '2025-10-17 10:08:00', 35000, 7.8, 1, 1, 0),
('BATCH_REV_20251017_001', 'database_update', '2025-10-17 10:08:00', '2025-10-17 10:20:00', 720000, 18.2, 5, 5, 0);

-- อัปเดต expenses table ให้เชื่อมโยงกับ batch
UPDATE expenses SET 
    batch_verification_id = 'BATCH_VER_20251017_001',
    is_batch_processed = TRUE,
    batch_notes = 'Processed in morning verification batch'
WHERE id IN (1, 2, 3, 4, 6);

UPDATE expenses SET 
    batch_review_id = 'BATCH_REV_20251017_001',
    batch_notes = 'Processed in morning review batch'
WHERE id IN (1, 2, 3, 4, 6);

UPDATE expenses SET 
    batch_verification_id = 'BATCH_VER_20251017_002',
    is_batch_processed = TRUE,
    batch_notes = 'Processing in afternoon verification batch'
WHERE id IN (8, 9, 10);
