-- MariaDB 5.5.68 Compatible Stored Procedures
-- Run this AFTER importing schema_only_mariadb55.sql
-- Execute each procedure separately in phpMyAdmin

-- Drop existing procedures if they exist
DROP PROCEDURE IF EXISTS CreateBatchOperation;
DROP PROCEDURE IF EXISTS AddWorkflowHistory;

-- Procedure 1: CreateBatchOperation (without JSON parameters)
DELIMITER $$

CREATE PROCEDURE CreateBatchOperation(
    IN p_operation_type VARCHAR(20), 
    IN p_user_id INT, 
    IN p_expense_ids_text TEXT, 
    IN p_total_amount DECIMAL(12,2), 
    OUT p_batch_id VARCHAR(50)
)
BEGIN
    DECLARE v_batch_id VARCHAR(50);
    DECLARE v_item_count INT;
    DECLARE v_expense_id INT;
    DECLARE v_amount DECIMAL(12,2);
    DECLARE v_pos INT DEFAULT 1;
    DECLARE v_next_pos INT;
    DECLARE v_item TEXT;
    DECLARE v_done INT DEFAULT 0;
    
    -- Generate batch ID
    SET v_batch_id = CONCAT('BATCH_', UPPER(p_operation_type), '_', DATE_FORMAT(NOW(), '%Y%m%d_%H%i%s'));
    
    -- Count items by counting commas + 1
    SET v_item_count = (CHAR_LENGTH(p_expense_ids_text) - CHAR_LENGTH(REPLACE(p_expense_ids_text, ',', '')) + 1);
    
    -- Insert batch operation
    INSERT INTO batch_operations (batch_id, operation_type, user_id, total_amount, item_count)
    VALUES (v_batch_id, p_operation_type, p_user_id, p_total_amount, v_item_count);
    
    -- Parse expense_ids_text (format: "id1:amount1,id2:amount2,...")
    WHILE v_pos <= CHAR_LENGTH(p_expense_ids_text) AND v_done = 0 DO
        SET v_next_pos = LOCATE(',', p_expense_ids_text, v_pos);
        
        IF v_next_pos = 0 THEN
            SET v_next_pos = CHAR_LENGTH(p_expense_ids_text) + 1;
            SET v_done = 1;
        END IF;
        
        SET v_item = SUBSTRING(p_expense_ids_text, v_pos, v_next_pos - v_pos);
        
        -- Extract expense_id and amount from "id:amount" format
        SET v_expense_id = CAST(SUBSTRING_INDEX(v_item, ':', 1) AS UNSIGNED);
        SET v_amount = CAST(SUBSTRING_INDEX(v_item, ':', -1) AS DECIMAL(12,2));
        
        -- Insert batch item
        INSERT INTO batch_items (batch_id, expense_id, individual_amount)
        VALUES (v_batch_id, v_expense_id, v_amount);
        
        SET v_pos = v_next_pos + 1;
    END WHILE;
    
    SET p_batch_id = v_batch_id;
END$$

DELIMITER ;

-- Procedure 2: AddWorkflowHistory (without JSON)
DELIMITER $$

CREATE PROCEDURE AddWorkflowHistory(
    IN expense_id INT,
    IN action_type VARCHAR(20),
    IN from_status VARCHAR(20),
    IN to_status VARCHAR(20),
    IN user_id INT,
    IN comment TEXT
)
BEGIN
    DECLARE history_text TEXT;
    DECLARE new_entry TEXT;
    DECLARE current_timestamp VARCHAR(20);
    
    SET current_timestamp = DATE_FORMAT(NOW(), '%Y-%m-%d %H:%i:%s');
    
    -- Get current workflow history
    SELECT workflow_history INTO history_text 
    FROM expenses 
    WHERE id = expense_id;
    
    -- Escape quotes in comment
    SET comment = IFNULL(REPLACE(REPLACE(comment, '\\', '\\\\'), '"', '\\"'), '');
    
    -- Create new history entry as JSON string
    SET new_entry = CONCAT(
        '{"timestamp":"', current_timestamp, '",',
        '"action":"', action_type, '",',
        '"from_status":"', from_status, '",',
        '"to_status":"', to_status, '",',
        '"user_id":', user_id, ',',
        '"comment":"', comment, '"}'
    );
    
    -- If history is null or empty, initialize as array
    IF history_text IS NULL OR history_text = '' THEN
        SET history_text = CONCAT('[', new_entry, ']');
    ELSE
        -- Remove closing bracket and append new entry
        SET history_text = CONCAT(
            SUBSTRING(history_text, 1, CHAR_LENGTH(history_text) - 1),
            ',', new_entry, ']'
        );
    END IF;
    
    -- Update the expense record
    UPDATE expenses 
    SET workflow_history = history_text 
    WHERE id = expense_id;
    
    -- Also insert into workflow_history table for easier querying
    INSERT INTO expense_workflow_history (
        expense_id, action_type, from_status, to_status, user_id, comment
    ) VALUES (
        expense_id, action_type, from_status, to_status, user_id, comment
    );
END$$

DELIMITER ;
