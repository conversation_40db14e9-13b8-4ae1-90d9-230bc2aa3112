-- Add verification and review fields to expenses table

-- Add verification fields
SET @sql = (SELECT IF(
    (SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS
     WHERE table_name = 'expenses'
     AND table_schema = DATABASE()
     AND column_name = 'verification_slip_image') > 0,
    'SELECT "verification_slip_image column already exists"',
    'ALTER TABLE expenses ADD COLUMN verification_slip_image VARCHAR(255) NULL AFTER receipt_images'
));
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

SET @sql = (SELECT IF(
    (SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS
     WHERE table_name = 'expenses'
     AND table_schema = DATABASE()
     AND column_name = 'verification_amount') > 0,
    'SELECT "verification_amount column already exists"',
    'ALTER TABLE expenses ADD COLUMN verification_amount DECIMAL(15,2) NULL AFTER verification_slip_image'
));
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

SET @sql = (SELECT IF(
    (SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS
     WHERE table_name = 'expenses'
     AND table_schema = DATABASE()
     AND column_name = 'verification_date') > 0,
    'SELECT "verification_date column already exists"',
    'ALTER TABLE expenses ADD COLUMN verification_date DATETIME NULL AFTER verification_amount'
));
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

SET @sql = (SELECT IF(
    (SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS
     WHERE table_name = 'expenses'
     AND table_schema = DATABASE()
     AND column_name = 'verification_by') > 0,
    'SELECT "verification_by column already exists"',
    'ALTER TABLE expenses ADD COLUMN verification_by INT NULL AFTER verification_date'
));
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- Add reviewer fields
SET @sql = (SELECT IF(
    (SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS
     WHERE table_name = 'expenses'
     AND table_schema = DATABASE()
     AND column_name = 'reviewer_slip_image') > 0,
    'SELECT "reviewer_slip_image column already exists"',
    'ALTER TABLE expenses ADD COLUMN reviewer_slip_image VARCHAR(255) NULL AFTER verification_by'
));
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

SET @sql = (SELECT IF(
    (SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS
     WHERE table_name = 'expenses'
     AND table_schema = DATABASE()
     AND column_name = 'reviewer_amount') > 0,
    'SELECT "reviewer_amount column already exists"',
    'ALTER TABLE expenses ADD COLUMN reviewer_amount DECIMAL(15,2) NULL AFTER reviewer_slip_image'
));
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

SET @sql = (SELECT IF(
    (SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS
     WHERE table_name = 'expenses'
     AND table_schema = DATABASE()
     AND column_name = 'reviewer_date') > 0,
    'SELECT "reviewer_date column already exists"',
    'ALTER TABLE expenses ADD COLUMN reviewer_date DATETIME NULL AFTER reviewer_amount'
));
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

SET @sql = (SELECT IF(
    (SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS
     WHERE table_name = 'expenses'
     AND table_schema = DATABASE()
     AND column_name = 'reviewer_by') > 0,
    'SELECT "reviewer_by column already exists"',
    'ALTER TABLE expenses ADD COLUMN reviewer_by INT NULL AFTER reviewer_date'
));
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- Add foreign key constraints (only if they don't exist)
SET @sql = (SELECT IF(
    (SELECT COUNT(*) FROM INFORMATION_SCHEMA.KEY_COLUMN_USAGE
     WHERE table_name = 'expenses'
     AND table_schema = DATABASE()
     AND constraint_name = 'fk_expenses_verification_by') > 0,
    'SELECT "verification_by foreign key already exists"',
    'ALTER TABLE expenses ADD CONSTRAINT fk_expenses_verification_by FOREIGN KEY (verification_by) REFERENCES users(id)'
));
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

SET @sql = (SELECT IF(
    (SELECT COUNT(*) FROM INFORMATION_SCHEMA.KEY_COLUMN_USAGE
     WHERE table_name = 'expenses'
     AND table_schema = DATABASE()
     AND constraint_name = 'fk_expenses_reviewer_by') > 0,
    'SELECT "reviewer_by foreign key already exists"',
    'ALTER TABLE expenses ADD CONSTRAINT fk_expenses_reviewer_by FOREIGN KEY (reviewer_by) REFERENCES users(id)'
));
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- Add indexes for better performance
SET @sql = (SELECT IF(
    (SELECT COUNT(*) FROM INFORMATION_SCHEMA.STATISTICS
     WHERE table_name = 'expenses'
     AND table_schema = DATABASE()
     AND index_name = 'idx_verification_by') > 0,
    'SELECT "verification_by index already exists"',
    'ALTER TABLE expenses ADD INDEX idx_verification_by (verification_by)'
));
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

SET @sql = (SELECT IF(
    (SELECT COUNT(*) FROM INFORMATION_SCHEMA.STATISTICS
     WHERE table_name = 'expenses'
     AND table_schema = DATABASE()
     AND index_name = 'idx_reviewer_by') > 0,
    'SELECT "reviewer_by index already exists"',
    'ALTER TABLE expenses ADD INDEX idx_reviewer_by (reviewer_by)'
));
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;
