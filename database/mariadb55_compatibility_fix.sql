-- MariaDB 5.5.68 Compatibility Fixes
-- สำหรับแก้ไขปัญหาความเข้ากันได้กับ MariaDB 5.5.68

USE expenses_system;

-- 1. ตรวจสอบและปรับปรุง stored procedure สำหรับ MariaDB 5.5
-- ลบ stored procedure เก่าถ้ามี
DROP PROCEDURE IF EXISTS AddReceiptDeduction;
DROP PROCEDURE IF EXISTS UpdateReceiptCalculations;

-- สร้าง stored procedure ใหม่ที่เข้ากันได้กับ MariaDB 5.5
DELIMITER //

CREATE PROCEDURE AddReceiptDeduction(
    IN p_receipt_number_id INT,
    IN p_receipt_index INT,
    IN p_deduction_type VARCHAR(50),
    IN p_amount DECIMAL(15,2),
    IN p_percentage DECIMAL(5,2),
    IN p_description TEXT,
    IN p_deduction_image VARCHAR(255),
    IN p_is_percentage_based TINYINT(1),
    IN p_created_by INT,
    OUT p_deduction_id INT,
    OUT p_success TINYINT(1),
    OUT p_message VARCHAR(255)
)
BEGIN
    DECLARE EXIT HANDLER FOR SQLEXCEPTION
    BEGIN
        ROLLBACK;
        SET p_success = 0;
        SET p_message = 'Error adding deduction';
        SET p_deduction_id = NULL;
    END;

    START TRANSACTION;

    -- Insert deduction
    INSERT INTO receipt_deductions (
        receipt_number_id, 
        receipt_index,
        deduction_type, 
        amount, 
        percentage, 
        description, 
        deduction_image,
        is_percentage_based, 
        created_by
    ) VALUES (
        p_receipt_number_id, 
        p_receipt_index,
        p_deduction_type, 
        p_amount, 
        p_percentage, 
        p_description, 
        p_deduction_image,
        p_is_percentage_based, 
        p_created_by
    );

    SET p_deduction_id = LAST_INSERT_ID();

    -- Update receipt calculations
    CALL UpdateReceiptCalculations(p_receipt_number_id);

    SET p_success = 1;
    SET p_message = 'Deduction added successfully';

    COMMIT;
END //

CREATE PROCEDURE UpdateReceiptCalculations(
    IN p_receipt_number_id INT
)
BEGIN
    DECLARE v_gross_amount DECIMAL(15,2) DEFAULT 0;
    DECLARE v_total_deductions DECIMAL(15,2) DEFAULT 0;
    DECLARE v_net_amount DECIMAL(15,2) DEFAULT 0;
    DECLARE v_has_deductions TINYINT(1) DEFAULT 0;

    -- Get gross amount
    SELECT COALESCE(gross_amount, amount, 0) INTO v_gross_amount
    FROM receipt_numbers 
    WHERE id = p_receipt_number_id;

    -- Calculate total deductions
    SELECT COALESCE(SUM(amount), 0) INTO v_total_deductions
    FROM receipt_deductions 
    WHERE receipt_number_id = p_receipt_number_id;

    -- Calculate net amount
    SET v_net_amount = v_gross_amount - v_total_deductions;

    -- Set has_deductions flag
    IF v_total_deductions > 0 THEN
        SET v_has_deductions = 1;
    ELSE
        SET v_has_deductions = 0;
    END IF;

    -- Update receipt_numbers table
    UPDATE receipt_numbers 
    SET 
        has_deductions = v_has_deductions,
        net_amount_calculated = v_net_amount
    WHERE id = p_receipt_number_id;

END //

DELIMITER ;

-- 2. ตรวจสอบและปรับปรุงโครงสร้างตาราง
-- เพิ่ม columns ที่อาจขาดหายไปใน receipt_numbers
ALTER TABLE receipt_numbers 
ADD COLUMN IF NOT EXISTS gross_amount DECIMAL(15,2) DEFAULT NULL COMMENT 'ยอดก่อนหัก',
ADD COLUMN IF NOT EXISTS has_deductions TINYINT(1) DEFAULT 0 COMMENT 'มีรายการหักหรือไม่',
ADD COLUMN IF NOT EXISTS net_amount_calculated DECIMAL(15,2) DEFAULT NULL COMMENT 'ยอดสุทธิหลังหัก';

-- อัพเดต gross_amount สำหรับ records ที่มีอยู่แล้ว
UPDATE receipt_numbers 
SET gross_amount = amount 
WHERE gross_amount IS NULL;

UPDATE receipt_numbers 
SET net_amount_calculated = amount 
WHERE net_amount_calculated IS NULL;

-- 3. สร้าง view สำหรับ MariaDB 5.5 (ถ้ายังไม่มี)
DROP VIEW IF EXISTS receipt_summary;

CREATE VIEW receipt_summary AS
SELECT 
    rn.id,
    rn.expense_id,
    rn.receipt_number,
    rn.gross_amount,
    rn.amount as original_amount,
    rn.net_amount_calculated,
    rn.has_deductions,
    rn.description,
    rn.receipt_image,
    rn.created_at,
    rn.created_by,
    
    -- Deduction summary
    COALESCE(SUM(rd.amount), 0) as total_deductions,
    COUNT(rd.id) as deduction_count,
    
    -- Calculated net amount
    (rn.gross_amount - COALESCE(SUM(rd.amount), 0)) as calculated_net_amount
    
FROM receipt_numbers rn
LEFT JOIN receipt_deductions rd ON rn.id = rd.receipt_number_id
GROUP BY rn.id, rn.expense_id, rn.receipt_number, rn.gross_amount, 
         rn.amount, rn.net_amount_calculated, rn.has_deductions, 
         rn.description, rn.receipt_image, rn.created_at, rn.created_by;

-- 4. อัพเดตการคำนวณสำหรับ receipts ที่มี deductions อยู่แล้ว
UPDATE receipt_numbers rn
SET 
    has_deductions = (
        SELECT COUNT(*) > 0 
        FROM receipt_deductions rd 
        WHERE rd.receipt_number_id = rn.id
    ),
    net_amount_calculated = (
        rn.gross_amount - COALESCE((
            SELECT SUM(rd.amount) 
            FROM receipt_deductions rd 
            WHERE rd.receipt_number_id = rn.id
        ), 0)
    );

-- 5. ตรวจสอบผลลัพธ์
SELECT 'MariaDB 5.5.68 compatibility fixes applied successfully' as status;

-- แสดงสถิติ
SELECT 
    'Receipt Numbers' as table_name,
    COUNT(*) as total_records,
    SUM(CASE WHEN has_deductions = 1 THEN 1 ELSE 0 END) as with_deductions,
    SUM(CASE WHEN receipt_image IS NOT NULL THEN 1 ELSE 0 END) as with_images
FROM receipt_numbers

UNION ALL

SELECT 
    'Receipt Deductions' as table_name,
    COUNT(*) as total_records,
    COUNT(DISTINCT receipt_number_id) as unique_receipts,
    SUM(CASE WHEN deduction_image IS NOT NULL THEN 1 ELSE 0 END) as with_images
FROM receipt_deductions;
