-- phpMyAdmin SQL Dump
-- version 5.2.1 (Compatible with MariaDB 5.5.68)
-- https://www.phpmyadmin.net/
--
-- Host: localhost
-- Generation Time: Oct 19, 2025
-- Server version: MariaDB 5.5.68
-- PHP Version: 7.4+

SET SQL_MODE = "NO_AUTO_VALUE_ON_ZERO";
SET time_zone = "+00:00";

/*!40101 SET @OLD_CHARACTER_SET_CLIENT=@@CHARACTER_SET_CLIENT */;
/*!40101 SET @OLD_CHARACTER_SET_RESULTS=@@CHARACTER_SET_RESULTS */;
/*!40101 SET @OLD_COLLATION_CONNECTION=@@COLLATION_CONNECTION */;
/*!40101 SET NAMES utf8mb4 */;

--
-- Database: `expenses_system`
--

-- ========================================
-- NOTE: Procedure CreateBatchOperation
-- ========================================
-- Procedure ต้องสร้างแยกต่างหาก เพราะ phpMyAdmin ไม่รองรับ
-- ดูไฟล์ CREATE_PROCEDURE.sql สำหรับการสร้าง Procedure
-- ========================================

-- --------------------------------------------------------

--
-- Table structure for table `activity_logs`
--

CREATE TABLE `activity_logs` (
  `id` int NOT NULL,
  `user_id` int NOT NULL,
  `action_type` enum('create','update','delete','status_change','login','logout') COLLATE utf8mb4_general_ci NOT NULL,
  `table_name` varchar(50) COLLATE utf8mb4_general_ci DEFAULT NULL,
  `record_id` int DEFAULT NULL,
  `old_values` text COLLATE utf8mb4_general_ci,
  `new_values` text COLLATE utf8mb4_general_ci,
  `description` text COLLATE utf8mb4_general_ci,
  `ip_address` varchar(45) COLLATE utf8mb4_general_ci DEFAULT NULL,
  `user_agent` text COLLATE utf8mb4_general_ci,
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- --------------------------------------------------------

--
-- Table structure for table `batch_documents`
--

CREATE TABLE `batch_documents` (
  `id` int NOT NULL,
  `batch_id` varchar(50) COLLATE utf8mb4_general_ci NOT NULL,
  `document_type` enum('verification_slip','review_slip') COLLATE utf8mb4_general_ci NOT NULL,
  `file_path` varchar(500) COLLATE utf8mb4_general_ci NOT NULL COMMENT 'Path ของไฟล์',
  `original_filename` varchar(255) COLLATE utf8mb4_general_ci NOT NULL COMMENT 'ชื่อไฟล์ต้นฉบับ',
  `file_size` int NOT NULL COMMENT 'ขนาดไฟล์ (bytes)',
  `mime_type` varchar(100) COLLATE utf8mb4_general_ci NOT NULL COMMENT 'ประเภทไฟล์',
  `uploaded_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='เอกสารที่แนบกับ Batch';

-- --------------------------------------------------------

--
-- Table structure for table `batch_items`
--

CREATE TABLE `batch_items` (
  `id` int NOT NULL,
  `batch_id` varchar(50) COLLATE utf8mb4_general_ci NOT NULL,
  `expense_id` int NOT NULL,
  `individual_amount` decimal(12,2) NOT NULL COMMENT 'จำนวนเงินของรายการนี้',
  `status` enum('pending','processing','completed','failed') COLLATE utf8mb4_general_ci DEFAULT 'pending',
  `error_message` text COLLATE utf8mb4_general_ci COMMENT 'ข้อความ Error หากมี',
  `processed_at` timestamp NULL DEFAULT NULL COMMENT 'เวลาที่ประมวลผลรายการนี้'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='รายการ Expenses ใน Batch';

-- --------------------------------------------------------

--
-- Table structure for table `batch_operations`
--

CREATE TABLE `batch_operations` (
  `id` int NOT NULL,
  `batch_id` varchar(50) COLLATE utf8mb4_general_ci NOT NULL COMMENT 'รหัส Batch เช่น BATCH_VER_20251017_001',
  `operation_type` enum('verification','review') COLLATE utf8mb4_general_ci NOT NULL COMMENT 'ประเภทการดำเนินการ',
  `user_id` int NOT NULL COMMENT 'ผู้ดำเนินการ',
  `total_amount` decimal(12,2) NOT NULL COMMENT 'ยอดรวมทั้งหมด',
  `item_count` int NOT NULL COMMENT 'จำนวนรายการ',
  `status` enum('pending','processing','completed','failed','cancelled') COLLATE utf8mb4_general_ci DEFAULT 'pending',
  `notes` text COLLATE utf8mb4_general_ci COMMENT 'หมายเหตุเพิ่มเติม',
  `transfer_number` varchar(100) COLLATE utf8mb4_general_ci DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `started_at` timestamp NULL DEFAULT NULL COMMENT 'เวลาเริ่มประมวลผล',
  `completed_at` timestamp NULL DEFAULT NULL COMMENT 'เวลาเสร็จสิ้น'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='ตารางหลักสำหรับ Batch Operations';

-- --------------------------------------------------------

--
-- Table structure for table `batch_performance_logs`
--

CREATE TABLE `batch_performance_logs` (
  `id` int NOT NULL,
  `batch_id` varchar(50) COLLATE utf8mb4_general_ci NOT NULL,
  `operation_step` varchar(100) COLLATE utf8mb4_general_ci NOT NULL COMMENT 'ขั้นตอนการทำงาน',
  `start_time` timestamp NOT NULL,
  `end_time` timestamp NULL DEFAULT NULL,
  `duration_ms` int DEFAULT NULL COMMENT 'ระยะเวลา (milliseconds)',
  `memory_usage_mb` decimal(8,2) DEFAULT NULL COMMENT 'การใช้ Memory (MB)',
  `items_processed` int DEFAULT '0',
  `success_count` int DEFAULT '0',
  `error_count` int DEFAULT '0'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='Log การทำงานเพื่อ Performance Analysis';

-- --------------------------------------------------------

--
-- Stand-in structure for view `batch_summary_view`
-- (See below for the actual view)
--
CREATE TABLE `batch_summary_view` (
`batch_id` varchar(50)
,`completed_at` timestamp
,`completed_items` decimal(23,0)
,`created_at` timestamp
,`document_files` text
,`duration_minutes` bigint
,`failed_items` decimal(23,0)
,`full_name` varchar(100)
,`id` int
,`item_count` int
,`operation_type` enum('verification','review')
,`status` enum('pending','processing','completed','failed','cancelled')
,`total_amount` decimal(12,2)
,`total_items` bigint
,`username` varchar(50)
);

-- --------------------------------------------------------

--
-- Table structure for table `customers`
--

CREATE TABLE `customers` (
  `id` int NOT NULL,
  `name` varchar(100) COLLATE utf8mb4_general_ci NOT NULL,
  `contact_person` varchar(100) COLLATE utf8mb4_general_ci DEFAULT NULL,
  `phone` varchar(20) COLLATE utf8mb4_general_ci DEFAULT NULL,
  `email` varchar(100) COLLATE utf8mb4_general_ci DEFAULT NULL,
  `address` text COLLATE utf8mb4_general_ci,
  `is_active` tinyint(1) DEFAULT '1',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `created_by` int DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `customers`
--

INSERT INTO `customers` (`id`, `name`, `contact_person`, `phone`, `email`, `address`, `is_active`, `created_at`, `created_by`) VALUES
(1, 'ABC Logistics', 'John Smith', '+1234567890', '<EMAIL>', NULL, 1, '2025-10-14 13:19:38', 1),
(2, 'XYZ Shipping', 'Jane Doe', '+1234567891', '<EMAIL>', NULL, 1, '2025-10-14 13:19:38', 1),
(3, 'ลูกค้าใหม่', NULL, NULL, NULL, NULL, 1, '2025-10-15 12:51:31', 1),
(4, 'America', 'John Smith', '+66-2-123-4567', '<EMAIL>', 'ข้อมูลเก่าจาก Appsheet', 1, '2025-10-16 13:44:07', 1),
(5, 'Kubota เขาตะแบก', 'Jane Doe', '+66-2-234-5678', '<EMAIL>', 'ข้อมูลเก่าจาก Appsheet', 1, '2025-10-16 13:44:07', 1),
(6, 'BGC-AGI', 'Mike Johnson', '+66-2-345-6789', '<EMAIL>', 'ข้อมูลเก่าจาก Appsheet', 1, '2025-10-16 13:44:07', 1),
(7, 'BGF', '', '', '', 'ข้อมูลเก่าจาก Appsheet', 1, '2025-10-16 13:44:07', 1),
(8, 'Ford', '', '', '', 'ข้อมูลเก่าจาก Appsheet', 1, '2025-10-16 13:44:07', 1),
(9, 'Funai', '', '', '', 'ข้อมูลเก่าจาก Appsheet', 1, '2025-10-16 13:44:07', 1),
(10, 'P&G', '', '', '', 'ข้อมูลเก่าจาก Appsheet', 1, '2025-10-16 13:44:07', 1),
(11, 'Ocean', '', '', '', 'ข้อมูลเก่าจาก Appsheet', 1, '2025-10-16 13:44:07', 1),
(12, 'L&H', '', '', '', 'ข้อมูลเก่าจาก Appsheet', 1, '2025-10-16 13:44:07', 1),
(13, 'GLP', '', '', '', 'ข้อมูลเก่าจาก Appsheet', 1, '2025-10-16 13:44:07', 1),
(14, 'Lightup - Bangsaothong', '', '', '', 'ข้อมูลเก่าจาก Appsheet', 1, '2025-10-16 13:44:07', 1),
(15, 'Lightup - Rayong', '', '', '', 'ข้อมูลเก่าจาก Appsheet', 1, '2025-10-16 13:44:07', 1),
(16, 'Lightup - Both', '', '', '', 'ข้อมูลเก่าจาก Appsheet', 1, '2025-10-16 13:44:07', 1),
(17, 'Lovespeed', '', '', '', 'ข้อมูลเก่าจาก Appsheet', 1, '2025-10-16 13:44:07', 1),
(18, 'Galaxy', '', '', '', 'ข้อมูลเก่าจาก Appsheet', 1, '2025-10-16 13:44:07', 1),
(19, 'CRC', '', '', '', 'ข้อมูลเก่าจาก Appsheet', 1, '2025-10-16 13:44:07', 1),
(20, 'Poly Protech', '', '', '', 'ข้อมูลเก่าจาก Appsheet', 1, '2025-10-16 13:44:07', 1),
(21, 'Riken - 20\'', '', '', '', 'ข้อมูลเก่าจาก Appsheet', 1, '2025-10-16 13:44:07', 1),
(22, 'SWI - 20\'', '', '', '', 'ข้อมูลเก่าจาก Appsheet', 1, '2025-10-16 13:44:07', 1),
(23, 'SWI - 40\'', '', '', '', 'ข้อมูลเก่าจาก Appsheet', 1, '2025-10-16 13:44:07', 1),
(24, 'Toshiba 345 / Bangkadi', '', '', '', 'ข้อมูลเก่าจาก Appsheet', 1, '2025-10-16 13:44:07', 1),
(25, 'Unico', '', '', '', 'ข้อมูลเก่าจาก Appsheet', 1, '2025-10-16 13:44:07', 1),
(26, 'Midea', '', '', '', 'ข้อมูลเก่าจาก Appsheet', 1, '2025-10-16 13:44:07', 1),
(27, 'Haier - Bangsaothong', '', '', '', 'ข้อมูลเก่าจาก Appsheet', 1, '2025-10-16 13:44:07', 1),
(28, 'Haier - 304', '', '', '', 'ข้อมูลเก่าจาก Appsheet', 1, '2025-10-16 13:44:07', 1),
(29, 'Unic Pingthong 1 (P\'Amm)', '', '', '', 'ข้อมูลเก่าจาก Appsheet', 1, '2025-10-16 13:44:07', 1),
(30, 'TEP', '', '', '', 'ข้อมูลเก่าจาก Appsheet', 1, '2025-10-16 13:44:07', 1),
(31, 'Down', '', '', '', 'ข้อมูลเก่าจาก Appsheet', 1, '2025-10-16 13:44:07', 1),
(32, 'ATEC Production', '', '', '', 'ข้อมูลเก่าจาก Appsheet', 1, '2025-10-16 13:44:07', 1),
(33, 'Joon chee industrial', '', '', '', 'ข้อมูลเก่าจาก Appsheet', 1, '2025-10-16 13:44:07', 1),
(34, 'Giant', '', '', '', 'ข้อมูลเก่าจาก Appsheet', 1, '2025-10-16 13:44:07', 1),
(35, 'Siam Industry', '', '', '', 'ข้อมูลเก่าจาก Appsheet', 1, '2025-10-16 13:44:07', 1),
(36, 'Asia Golden Rice', '', '', '', 'ข้อมูลเก่าจาก Appsheet', 1, '2025-10-16 13:44:07', 1),
(37, 'RDC', '', '', '', 'ข้อมูลเก่าจาก Appsheet', 1, '2025-10-16 13:44:07', 1),
(38, 'S. Kijchai', '', '', '', 'ข้อมูลเก่าจาก Appsheet', 1, '2025-10-16 13:44:07', 1),
(39, 'LIXIL', '', '', '', 'ข้อมูลเก่าจาก Appsheet', 1, '2025-10-16 13:44:07', 1),
(40, 'Teijin Polyester อยุธยา', '', '', '', 'ข้อมูลเก่าจาก Appsheet', 1, '2025-10-16 13:44:07', 1),
(41, 'PNC - Teijin Polyyester ปทุม', '', '', '', 'ข้อมูลเก่าจาก Appsheet', 1, '2025-10-16 13:44:07', 1),
(42, 'Jing Gong - ปลวกแดง', '', '', '', 'ข้อมูลเก่าจาก Appsheet', 1, '2025-10-16 13:44:07', 1),
(43, 'Unico บ้านบึง', '', '', '', 'ข้อมูลเก่าจาก Appsheet', 1, '2025-10-16 13:44:07', 1),
(44, 'MLT บ่อวิน', '', '', '', 'ข้อมูลเก่าจาก Appsheet', 1, '2025-10-16 13:44:07', 1),
(45, 'MLT พนมสารคาม', '', '', '', 'ข้อมูลเก่าจาก Appsheet', 1, '2025-10-16 13:44:07', 1),
(46, 'MLT บ่อทองปราจีน', '', '', '', 'ข้อมูลเก่าจาก Appsheet', 1, '2025-10-16 13:44:07', 1),
(47, 'DBS Schenker', '', '', '', 'ข้อมูลเก่าจาก Appsheet', 1, '2025-10-16 13:44:07', 1),
(48, 'Korakit (Asia Shipping)', '', '', '', 'ข้อมูลเก่าจาก Appsheet', 1, '2025-10-16 13:44:07', 1),
(49, 'Mark and Friend (OPL)', '', '', '', 'ข้อมูลเก่าจาก Appsheet', 1, '2025-10-16 13:44:07', 1),
(50, 'Chinuts', '', '', '', 'ข้อมูลเก่าจาก Appsheet', 1, '2025-10-16 13:44:07', 1),
(51, 'KNT', '', '', '', 'ข้อมูลเก่าจาก Appsheet', 1, '2025-10-16 13:44:07', 1),
(52, 'OPL', '', '', '', 'ข้อมูลเก่าจาก Appsheet', 1, '2025-10-16 13:44:07', 1),
(53, 'ECOS', '', '', '', 'ข้อมูลเก่าจาก Appsheet', 1, '2025-10-16 13:44:07', 1),
(54, 'Asia Logistics', '', '', '', 'ข้อมูลเก่าจาก Appsheet', 1, '2025-10-16 13:44:07', 1),
(55, 'Yusen', '', '', '', 'ข้อมูลเก่าจาก Appsheet', 1, '2025-10-16 13:44:07', 1),
(56, 'ZHONGCE RUBBER', '', '', '', 'ข้อมูลเก่าจาก Appsheet', 1, '2025-10-16 13:44:07', 1),
(57, 'BYD', '', '', '', 'ข้อมูลเก่าจาก Appsheet', 1, '2025-10-16 13:44:07', 1),
(58, 'SUMSUNG', '', '', '', 'ข้อมูลเก่าจาก Appsheet', 1, '2025-10-16 13:44:07', 1),
(59, 'RENOWN', '', '', '', 'ข้อมูลเก่าจาก Appsheet', 1, '2025-10-16 13:44:07', 1),
(60, 'CP POLY', '', '', '', 'ข้อมูลเก่าจาก Appsheet', 1, '2025-10-16 13:44:07', 1),
(61, 'EPS', '', '', '', 'ข้อมูลเก่าจาก Appsheet', 1, '2025-10-16 13:44:07', 1),
(62, 'BUDGET', '', '', '', 'ข้อมูลเก่าจาก Appsheet', 1, '2025-10-16 13:44:07', 1),
(63, 'VALMET', '', '', '', 'ข้อมูลเก่าจาก Appsheet', 1, '2025-10-16 13:44:07', 1),
(64, 'UNION', '', '', '', 'ข้อมูลเก่าจาก Appsheet', 1, '2025-10-16 13:44:07', 1),
(65, 'QD STEEL', '', '', '', 'ข้อมูลเก่าจาก Appsheet', 1, '2025-10-16 13:44:07', 1),
(66, 'MIDIA', '', '', '', 'ข้อมูลเก่าจาก Appsheet', 1, '2025-10-16 13:44:07', 1),
(67, 'FTA', '', '', '', 'ข้อมูลเก่าจาก Appsheet', 1, '2025-10-16 13:44:07', 1),
(68, 'KJ WORLD', '', '', '', 'ข้อมูลเก่าจาก Appsheet', 1, '2025-10-16 13:44:07', 1),
(69, 'ทองดา', '', '', '', 'ข้อมูลเก่าจาก Appsheet', 1, '2025-10-16 13:44:07', 1),
(70, 'ECU', '', '', '', 'ข้อมูลเก่าจาก Appsheet', 1, '2025-10-16 13:44:07', 1),
(71, 'PFI', '', '', '', 'ข้อมูลเก่าจาก Appsheet', 1, '2025-10-16 13:44:07', 1),
(72, 'LFT', '', '', '', 'ข้อมูลเก่าจาก Appsheet', 1, '2025-10-16 13:44:07', 1),
(73, 'NORZIN', '', '', '', 'ข้อมูลเก่าจาก Appsheet', 1, '2025-10-16 13:44:07', 1),
(74, 'EL-ARABY', '', '', '', 'ข้อมูลเก่าจาก Appsheet', 1, '2025-10-16 13:44:07', 1),
(75, 'TN GROUP', '', '', '', 'ข้อมูลเก่าจาก Appsheet', 1, '2025-10-16 13:44:07', 1),
(76, 'HUAYI GROUP', '', '', '', 'ข้อมูลเก่าจาก Appsheet', 1, '2025-10-16 13:44:07', 1),
(77, 'ANJI', '', '', '', 'ข้อมูลเก่าจาก Appsheet', 1, '2025-10-16 13:44:07', 1),
(78, 'GENERAL RUBBER', '', '', '', 'ข้อมูลเก่าจาก Appsheet', 1, '2025-10-16 13:44:07', 1),
(79, 'GREAT WALL MOTOR', '', '', '', 'ข้อมูลเก่าจาก Appsheet', 1, '2025-10-16 13:44:07', 1),
(80, 'Suzuyo Thailand', '', '', '', 'ข้อมูลเก่าจาก Appsheet', 1, '2025-10-16 13:44:07', 1),
(81, 'METAL-ONE', '', '', '', 'ข้อมูลเก่าจาก Appsheet', 1, '2025-10-16 13:44:07', 1),
(82, 'NISSAN', '', '', '', 'ข้อมูลเก่าจาก Appsheet', 1, '2025-10-16 13:44:07', 1),
(83, 'CCSCM', '', '', '', 'ข้อมูลเก่าจาก Appsheet', 1, '2025-10-16 13:44:07', 1),
(84, 'APL LOGISTICS', '', '', '', 'ข้อมูลเก่าจาก Appsheet', 1, '2025-10-16 13:44:07', 1),
(85, 'Taika', '', '', '', 'ข้อมูลเก่าจาก Appsheet', 1, '2025-10-16 13:44:07', 1),
(86, 'PAC', '', '', '', 'ข้อมูลเก่าจาก Appsheet', 1, '2025-10-16 13:44:07', 1),
(87, 'Unithai', '', '', '', 'ข้อมูลเก่าจาก Appsheet', 1, '2025-10-16 13:44:07', 1),
(88, 'UNITHAI', '', '', '', 'ข้อมูลเก่าจาก Appsheet', 1, '2025-10-16 13:44:07', 1),
(89, 'เพชรชนะ', '', '', '', 'ข้อมูลเก่าจาก Appsheet', 1, '2025-10-16 13:44:07', 1),
(90, 'MGH', '', '', '', 'ข้อมูลเก่าจาก Appsheet', 1, '2025-10-16 13:44:07', 1),
(91, '3J', '', '', '', 'ข้อมูลเก่าจาก Appsheet', 1, '2025-10-16 13:44:07', 1),
(92, 'ASIA COMPOSITE', '', '', '', 'ข้อมูลเก่าจาก Appsheet', 1, '2025-10-16 13:44:07', 1);

-- --------------------------------------------------------

--
-- Table structure for table `drivers`
--

CREATE TABLE `drivers` (
  `id` int NOT NULL,
  `name` varchar(100) COLLATE utf8mb4_general_ci NOT NULL,
  `license_number` varchar(50) COLLATE utf8mb4_general_ci DEFAULT NULL,
  `phone` varchar(20) COLLATE utf8mb4_general_ci DEFAULT NULL,
  `vehicle_plate` varchar(20) COLLATE utf8mb4_general_ci DEFAULT NULL,
  `payment_account_no` varchar(50) COLLATE utf8mb4_general_ci DEFAULT NULL,
  `is_active` tinyint(1) DEFAULT '1',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `created_by` int DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `drivers`
--

INSERT INTO `drivers` (`id`, `name`, `license_number`, `phone`, `vehicle_plate`, `payment_account_no`, `is_active`, `created_at`, `created_by`) VALUES
(1, 'Mike Johnson', 'DL123456', '+**********', 'ABC-1234', 'ACC-001', 1, '2025-10-14 13:19:38', 1),
(2, 'Sarah Wilson', 'DL789012', '+**********', 'XYZ-5678', 'ACC-002', 1, '2025-10-14 13:19:38', 1),
(3, 'นิรันดร์', '', '+66-81-234-5678', '73-8966', '**********-Kbank', 1, '2025-10-16 13:33:34', 1),
(4, 'สุรพงษ์', '', '+66-82-345-6789', '67-2931', '**********-BAY', 1, '2025-10-16 13:33:34', 1),
(5, 'เสน่ห์', '', '+66-83-456-7890', '75-0792', '**********-TTB', 1, '2025-10-16 13:33:34', 1),
(6, 'บัญชา', '', '', '75-0866', '**********-Kbank', 1, '2025-10-16 13:33:34', 1),
(7, 'อำนาจ แก้วมุกดา', '', '', '73-7482', '**********-Kbank', 1, '2025-10-16 13:33:34', 1),
(8, 'วชิกร ตุ้มทอง', '', '', '68-9280', '**********-Kbank', 1, '2025-10-16 13:33:34', 1),
(9, 'อภิภัทร สุนทร', '', '', '72-6537', '**********-Kbank', 1, '2025-10-16 13:33:34', 1),
(10, 'จินดามนต์ ชูเลิศ', '', '', '70-2811', '**********-BAY', 1, '2025-10-16 13:33:34', 1),
(11, 'วันชัย ทุมนาหาด', '', '', '72-2144', '**********-Kbank', 1, '2025-10-16 13:33:34', 1),
(12, 'อนุสิทธิ์ บุตรกันยา', '', '', '69-5624', '**********-SCB', 1, '2025-10-16 13:33:34', 1),
(13, 'สิงห์ สีธงชัย', '', '', '70-3563', '**********-BAY', 1, '2025-10-16 13:33:34', 1),
(14, 'สมบูรณ์ สิมมา', '', '', '74-1052', 'ไม่มี', 1, '2025-10-16 13:33:34', 1),
(15, 'ขจรศักดิ์', '', '', '74-4682', '**********-Kbank', 1, '2025-10-16 13:33:34', 1),
(16, 'พงษ์ศักดิ์ ปกิระสา', '', '', '74-1425', '**********-SCB', 1, '2025-10-16 13:33:34', 1),
(17, 'ภาคภูมิ', '', '', '71-7080', 'ไม่มี', 1, '2025-10-16 13:33:34', 1),
(18, 'ธีรภัทร มหามาตร์', '', '', '67-5293', 'ไม่มี', 1, '2025-10-16 13:33:34', 1),
(19, 'วิศรุต กุลจารุสุธีพร', '', '', '73-4673', 'ไม่มี', 1, '2025-10-16 13:33:34', 1),
(20, 'ภาสกร สมภักดี', '', '', '67-7795', 'ไม่มี', 1, '2025-10-16 13:33:34', 1),
(21, 'สุวัฒน์ บัวนิล', '', '', '74-8119', 'ไม่มี', 1, '2025-10-16 13:33:34', 1),
(22, 'สวัสดิ์', '', '', '74-8123', 'ไม่มี', 1, '2025-10-16 13:33:34', 1),
(23, 'ประกอบ', '', '', '75-1053', 'ไม่มี', 1, '2025-10-16 13:33:34', 1),
(24, 'สรัล', '', '', '75-1045', 'ไม่มี', 1, '2025-10-16 13:33:34', 1),
(25, 'รำแคล', '', '', '67-9673', '6882815805-SCB', 1, '2025-10-16 13:33:34', 1),
(26, 'เฉลิมศักดิ์', '', '', '67-2931', '8292659176-SCB', 1, '2025-10-16 13:33:34', 1),
(27, 'วันชัย โกรัมย์', '', '', '67-3321', '4121749507', 1, '2025-10-16 13:33:34', 1),
(28, 'ศุภชัย', '', '', '74-1425', '', 1, '2025-10-16 13:33:34', 1),
(29, 'กรกฤต ชื่นกลิ่น', '', '', '74-1413', '', 1, '2025-10-16 13:33:34', 1),
(30, 'ดวงใจ', '', '', '70-9504', '', 1, '2025-10-16 13:33:34', 1),
(31, 'จำเนียน', '', '', '70-2811', '4-1221-7187-0', 1, '2025-10-16 13:33:34', 1),
(32, 'คำไพ', '', '', '71-1962', '', 1, '2025-10-16 13:33:34', 1),
(33, 'เอกชัย', '', '', '', '502-1-81630-5', 1, '2025-10-16 13:33:34', 1),
(34, 'ธีรพล', '', '', '', '', 1, '2025-10-16 13:33:34', 1),
(35, 'KKP TRANSPORT', '', '', '', '172-2-85246-6', 1, '2025-10-16 13:33:34', 1),
(36, 'สังวาลย์', '', '', '74-9405', '', 1, '2025-10-16 13:33:34', 1),
(37, 'COCO EXPRESS', '', '', '', '', 1, '2025-10-16 13:33:34', 1),
(38, 'สุทธิศักดิ์', '', '', '67-2931', '277-0-66215-5', 1, '2025-10-16 13:33:34', 1),
(39, 'พงษ์สิทธฺ์', '', '', '69-7838', '', 1, '2025-10-16 13:33:34', 1),
(40, 'TN Transport', '', '', '', '', 1, '2025-10-16 13:33:34', 1),
(41, 'อัฌชา', '', '', '75-3446', '', 1, '2025-10-16 13:33:34', 1),
(42, 'FANG', '', '', '70-9405', '', 1, '2025-10-16 13:33:34', 1),
(43, 'หจก.ศิริการ', '', '', '', '', 1, '2025-10-16 13:33:34', 1),
(44, 'สนั่น', '', '', '75-3446', '195-1-16379-8', 1, '2025-10-16 13:33:34', 1),
(45, 'พิทักษ์', '', '', '', '', 1, '2025-10-16 13:33:34', 1),
(46, 'โชติวิทย์', '', '', '', '', 1, '2025-10-16 13:33:34', 1),
(47, 'รถเงินสด มีน', '', '', '', '', 1, '2025-10-16 13:33:34', 1),
(48, 'รถพี่แดง', '', '', 'ซัพร่วม', '141464493', 1, '2025-10-16 13:33:34', 1),
(49, 'วศิธรณ์', '', '', '75-0792', '4221490202', 1, '2025-10-16 13:33:34', 1),
(50, 'สมศักดิ์', '', '', '', '703-0-67757-4', 1, '2025-10-16 13:33:34', 1),
(51, 'ชัยยงค์', '', '', '', '064-1-55448-0', 1, '2025-10-16 13:33:34', 1),
(52, 'กรกต', '', '', '', '167-411664-5', 1, '2025-10-16 13:33:34', 1),
(53, 'บุญเหลือ', '', '', '75-5104', '', 1, '2025-10-16 13:33:34', 1),
(54, 'อนุกูล', '', '', '75-0792', '', 1, '2025-10-16 13:33:34', 1),
(55, 'ศรี', '', '', '75-0792', '', 1, '2025-10-16 13:33:34', 1),
(56, 'ขวัญชัย', '', '', '', '', 1, '2025-10-16 13:33:34', 1),
(57, 'สุทิน', '', '', '75-0792', '', 1, '2025-10-16 13:33:34', 1),
(58, 'NKSL', '', '', '', '', 1, '2025-10-18 14:05:21', 1);

-- --------------------------------------------------------

--
-- Table structure for table `expenses`
--

CREATE TABLE `expenses` (
  `id` int NOT NULL,
  `sequence` varchar(3) COLLATE utf8mb4_general_ci NOT NULL,
  `exno` varchar(50) COLLATE utf8mb4_general_ci NOT NULL COMMENT 'Expense number - extended for production',
  `bookingno` varchar(100) COLLATE utf8mb4_general_ci DEFAULT NULL,
  `job_open_date` date NOT NULL,
  `item_id` int DEFAULT NULL,
  `customer_id` int DEFAULT NULL,
  `containerno` varchar(255) COLLATE utf8mb4_general_ci DEFAULT NULL,
  `driver_id` int DEFAULT NULL,
  `vehicle_plate` varchar(20) COLLATE utf8mb4_general_ci DEFAULT NULL,
  `payment_account_no` varchar(50) COLLATE utf8mb4_general_ci DEFAULT NULL,
  `additional_details` varchar(255) COLLATE utf8mb4_general_ci DEFAULT NULL,
  `requester` varchar(100) COLLATE utf8mb4_general_ci DEFAULT NULL,
  `receiver` varchar(100) COLLATE utf8mb4_general_ci DEFAULT NULL,
  `payer` varchar(100) COLLATE utf8mb4_general_ci DEFAULT NULL,
  `withdrawal_date` date NOT NULL,
  `transfer_no` varchar(100) COLLATE utf8mb4_general_ci DEFAULT NULL,
  `transfer_amount` decimal(15,2) DEFAULT '0.00',
  `total_amount` decimal(15,2) DEFAULT '0.00',
  `transfer_slip_image` varchar(255) COLLATE utf8mb4_general_ci DEFAULT NULL,
  `receipt_images` text COLLATE utf8mb4_general_ci,
  `verification_slip_image` varchar(255) COLLATE utf8mb4_general_ci DEFAULT NULL,
  `verification_amount` decimal(15,2) DEFAULT NULL,
  `verification_transfer_no` varchar(100) COLLATE utf8mb4_general_ci DEFAULT NULL,
  `verification_date` datetime DEFAULT NULL,
  `verification_by` int DEFAULT NULL,
  `batch_verification_id` varchar(50) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT 'รหัส Batch Verification',
  `reviewer_slip_image` varchar(255) COLLATE utf8mb4_general_ci DEFAULT NULL,
  `reviewer_amount` decimal(15,2) DEFAULT NULL,
  `reviewer_transfer_no` varchar(100) COLLATE utf8mb4_general_ci DEFAULT NULL,
  `reviewer_date` datetime DEFAULT NULL,
  `reviewer_by` int DEFAULT NULL,
  `batch_review_id` varchar(50) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT 'รหัส Batch Review',
  `rejection_reason` text COLLATE utf8mb4_general_ci,
  `rejection_date` datetime DEFAULT NULL,
  `rejected_by` int DEFAULT NULL,
  `return_reason` text COLLATE utf8mb4_general_ci,
  `return_date` datetime DEFAULT NULL,
  `returned_by` int DEFAULT NULL,
  `workflow_history` longtext COLLATE utf8mb4_general_ci,
  `status` enum('open','pending','success','rejected','returned') COLLATE utf8mb4_general_ci NOT NULL DEFAULT 'open',
  `is_batch_processed` tinyint(1) DEFAULT '0' COMMENT 'ประมวลผลแบบ Batch หรือไม่',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` datetime DEFAULT NULL,
  `created_by` int NOT NULL,
  `batch_notes` text COLLATE utf8mb4_general_ci COMMENT 'หมายเหตุจาก Batch Processing',
  `bulk_operation_batch_id` varchar(100) COLLATE utf8mb4_general_ci DEFAULT NULL,
  `rejected_at` timestamp NULL DEFAULT NULL COMMENT 'When rejected',
  `returned_at` timestamp NULL DEFAULT NULL COMMENT 'When returned'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- --------------------------------------------------------

--
-- Stand-in structure for view `expense_workflow_view`
-- (See below for the actual view)
--
CREATE TABLE `expense_workflow_view` (
`created_at` timestamp
,`created_by` int
,`created_by_name` varchar(100)
,`exno` varchar(50)
,`id` int
,`rejected_by` int
,`rejected_by_name` varchar(100)
,`rejection_date` datetime
,`rejection_reason` text
,`return_date` datetime
,`return_reason` text
,`returned_by` int
,`returned_by_name` varchar(100)
,`reviewed_by_name` varchar(100)
,`reviewer_by` int
,`reviewer_date` datetime
,`status` enum('open','pending','success','rejected','returned')
,`total_amount` decimal(15,2)
,`verification_by` int
,`verification_date` datetime
,`verified_by_name` varchar(100)
,`workflow_history` longtext
);

-- --------------------------------------------------------

--
-- Table structure for table `items`
--

CREATE TABLE `items` (
  `id` int NOT NULL,
  `name` varchar(100) COLLATE utf8mb4_general_ci NOT NULL,
  `description` text COLLATE utf8mb4_general_ci,
  `is_active` tinyint(1) DEFAULT '1',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `created_by` int DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `items`
--

INSERT INTO `items` (`id`, `name`, `description`, `is_active`, `created_at`, `created_by`) VALUES
(1, 'Fuel', 'Vehicle fuel expenses', 1, '2025-10-14 13:19:38', 1),
(2, 'Maintenance', 'Vehicle maintenance and repairs', 1, '2025-10-14 13:19:38', 1),
(3, 'Toll Fees', 'Highway and bridge toll fees', 1, '2025-10-14 13:19:38', 1),
(4, 'Parking', 'Parking fees', 1, '2025-10-14 13:19:38', 1),
(5, 'ลองเพิ่มใหม่', 'Added from expense form', 1, '2025-10-15 12:51:31', 1),
(6, 'ลองเพิ่มอีกครั้ง', 'ลองเพิ่มอีกครั้ง', 1, '2025-10-16 12:47:37', 1),
(7, 'ปะยาง', 'ขอมูลเก่าจาก appsheet', 1, '2025-10-16 13:41:02', 1),
(8, 'ค่าอะไหล่ ซ่อมรถ', 'ขอมูลเก่าจาก appsheet', 1, '2025-10-16 13:41:02', 1),
(9, 'ค่าใช้จ่ายเกี่ยวกับรถ', 'ขอมูลเก่าจาก appsheet', 1, '2025-10-16 13:41:02', 1),
(10, 'ค่าเปลี่ยนยาง', 'ขอมูลเก่าจาก appsheet', 1, '2025-10-16 13:41:02', 1),
(11, 'ค่าอุปกรณ์ลาน NK', 'ขอมูลเก่าจาก appsheet', 1, '2025-10-16 13:41:02', 1),
(12, 'ค่าแรง', 'ขอมูลเก่าจาก appsheet', 1, '2025-10-16 13:41:02', 1),
(13, 'ค่าซ่อมตู้', 'ขอมูลเก่าจาก appsheet', 1, '2025-10-16 13:41:02', 1),
(14, 'ค่าแก้ไขใบเสร็จ', 'ขอมูลเก่าจาก appsheet', 1, '2025-10-16 13:41:02', 1),
(15, 'ค่าคัดใบเสร็จ', 'ขอมูลเก่าจาก appsheet', 1, '2025-10-16 13:41:02', 1),
(16, 'ค่าแมสเซนเจอร์', 'ขอมูลเก่าจาก appsheet', 1, '2025-10-16 13:41:02', 1),
(17, 'ค่าธรรมเนียมรับฟอร์ม', 'ขอมูลเก่าจาก appsheet', 1, '2025-10-16 13:41:02', 1),
(18, 'ค่าวัสดุ-อุปกรณ์สำนักงาน', 'ขอมูลเก่าจาก appsheet', 1, '2025-10-16 13:41:02', 1),
(19, 'ค่าพรบ', 'ขอมูลเก่าจาก appsheet', 1, '2025-10-16 13:41:02', 1),
(20, 'ค่าภาษีรถยนต์', 'ขอมูลเก่าจาก appsheet', 1, '2025-10-16 13:41:02', 1),
(21, 'ค่าประกันภัย', 'ขอมูลเก่าจาก appsheet', 1, '2025-10-16 13:41:02', 1),
(22, 'ค่าดำเนินการต่อทะเบียน', 'ขอมูลเก่าจาก appsheet', 1, '2025-10-16 13:41:02', 1),
(23, 'ค่ารับรอง', 'ขอมูลเก่าจาก appsheet', 1, '2025-10-16 13:41:02', 1),
(24, 'ค่าน้ำมัน', 'ขอมูลเก่าจาก appsheet', 1, '2025-10-16 13:41:02', 1),
(25, 'ค่าเช่า', 'ขอมูลเก่าจาก appsheet', 1, '2025-10-16 13:41:02', 1),
(26, 'ค่าน้ำ ลาน NK', 'ขอมูลเก่าจาก appsheet', 1, '2025-10-16 13:41:02', 1),
(27, 'ค่าไฟ ลาน NK', 'ขอมูลเก่าจาก appsheet', 1, '2025-10-16 13:41:02', 1),
(28, 'เบิกล่วงหน้า', 'ขอมูลเก่าจาก appsheet', 1, '2025-10-16 13:41:02', 1),
(29, 'สำรองจ่ายค่าขนส่งล่วงหน้า', 'ขอมูลเก่าจาก appsheet', 1, '2025-10-16 13:41:02', 1),
(30, 'สำรองจ่ายค่าผ่าน', 'ขอมูลเก่าจาก appsheet', 1, '2025-10-16 13:41:02', 1),
(31, 'สำรองจ่ายค่ายกตู้', 'ขอมูลเก่าจาก appsheet', 1, '2025-10-16 13:41:02', 1),
(32, 'สำรองจ่ายค่าฝากตู้', 'ขอมูลเก่าจาก appsheet', 1, '2025-10-16 13:41:02', 1),
(33, 'สำรองจ่ายค่าชั่ง', 'ขอมูลเก่าจาก appsheet', 1, '2025-10-16 13:41:02', 1),
(34, 'สำรองจ่ายค่าน้ำมัน', 'ขอมูลเก่าจาก appsheet', 1, '2025-10-16 13:41:02', 1),
(35, 'ค่าปริ๊นเอกสาร', 'ขอมูลเก่าจาก appsheet', 1, '2025-10-16 13:41:02', 1),
(36, 'ค่า OT', 'ขอมูลเก่าจาก appsheet', 1, '2025-10-16 13:41:02', 1),
(37, 'ค่าปรับ', 'ขอมูลเก่าจาก appsheet', 1, '2025-10-16 13:41:02', 1),
(38, 'ค่าใช้จ่ายอื่นๆ', 'ขอมูลเก่าจาก appsheet', 1, '2025-10-16 13:41:02', 1),
(39, 'สำรองจ่าย ค่าฝากตู้ เรียกเก็บได้', 'ขอมูลเก่าจาก appsheet', 1, '2025-10-16 13:41:02', 1),
(40, 'จ่ายชอร์', 'ขอมูลเก่าจาก appsheet', 1, '2025-10-16 13:41:02', 1),
(41, 'ค่าโอที', 'ขอมูลเก่าจาก appsheet', 1, '2025-10-16 13:41:02', 1),
(42, 'เงินสำรอง พขร.', 'ขอมูลเก่าจาก appsheet', 1, '2025-10-16 13:41:02', 1);

-- --------------------------------------------------------

--
-- Table structure for table `receipt_numbers`
--

CREATE TABLE `receipt_numbers` (
  `id` int NOT NULL,
  `expense_id` int NOT NULL,
  `receipt_number` varchar(100) COLLATE utf8mb4_general_ci NOT NULL,
  `amount` decimal(15,2) DEFAULT '0.00',
  `description` text COLLATE utf8mb4_general_ci,
  `receipt_image` varchar(255) COLLATE utf8mb4_general_ci DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `created_by` int DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- --------------------------------------------------------

--
-- Table structure for table `users`
--

CREATE TABLE `users` (
  `id` int NOT NULL,
  `username` varchar(50) COLLATE utf8mb4_general_ci NOT NULL,
  `email` varchar(100) COLLATE utf8mb4_general_ci NOT NULL,
  `password_hash` varchar(255) COLLATE utf8mb4_general_ci NOT NULL,
  `full_name` varchar(100) COLLATE utf8mb4_general_ci NOT NULL,
  `role` enum('data_entry','verification','reviewer','report_viewer','administrator') COLLATE utf8mb4_general_ci NOT NULL DEFAULT 'data_entry',
  `is_active` tinyint(1) DEFAULT '1',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` datetime DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `users`
--

INSERT INTO `users` (`id`, `username`, `email`, `password_hash`, `full_name`, `role`, `is_active`, `created_at`, `updated_at`) VALUES
(1, 'admin', '<EMAIL>', '$2y$10$fxuG.L3vFWYV08GCucv2puoFw7.gbsK38KQxohsEDdV63xqS9CcMm', 'System Administrator', 'administrator', 1, '2025-10-14 13:19:38', '2025-10-19 11:17:04'),
(2, 'fai', '<EMAIL>', '$2y$10$wYsXmYmQsVC/QHzQ0g/zOOAWcw2KWpFID40Y73HI3KGA2lhTbBtNu', 'fai', 'report_viewer', 1, '2025-10-16 16:33:21', '2025-10-18 16:26:30'),
(3, 'pending', '<EMAIL>', '$2y$10$rLWaIr3.aTd6jWSxC/uTaOZE38In6jcaX6lWb0v7.XAy1h05I4xgm', 'pending', 'verification', 1, '2025-10-17 09:11:30', '2025-10-17 09:11:30'),
(4, 'success', '<EMAIL>', '$2y$10$.AoTb.8EF.SbtO37s1l2POac8J/AzDIlD.2M4yhdkhHA8DWUM2Wt2', 'success', 'reviewer', 1, '2025-10-17 09:12:26', '2025-10-17 09:12:26'),
(5, 'deaw', '<EMAIL>', '$2y$10$F.U9uiDPutsDSVuDwBFTDeC.FXPdCJrIg90577qyvkfXdmU4/atoO', 'deaw', 'data_entry', 1, '2025-10-18 13:57:55', '2025-10-18 13:57:55'),
(6, 'report_viewer', '<EMAIL>', '$2y$10$9aUwtOeYY.Bh2.LsFFkSIOqbbEc/gZ30e7hSQsqPNSEMWwROXGqim', 'Report Viewer', 'report_viewer', 1, '2025-10-18 15:47:39', '2025-10-18 16:29:20');

-- --------------------------------------------------------

--
-- Structure for view `batch_summary_view`
--
DROP TABLE IF EXISTS `batch_summary_view`;

CREATE ALGORITHM=MERGE SQL SECURITY INVOKER VIEW `batch_summary_view` AS SELECT `bo`.`id` AS `id`, `bo`.`batch_id` AS `batch_id`, `bo`.`operation_type` AS `operation_type`, `u`.`username` AS `username`, `u`.`full_name` AS `full_name`, `bo`.`total_amount` AS `total_amount`, `bo`.`item_count` AS `item_count`, `bo`.`status` AS `status`, `bo`.`created_at` AS `created_at`, `bo`.`completed_at` AS `completed_at`, TIMESTAMPDIFF(MINUTE,`bo`.`created_at`,`bo`.`completed_at`) AS `duration_minutes`, COUNT(`bi`.`id`) AS `total_items`, SUM(CASE WHEN (`bi`.`status` = 'completed') THEN 1 ELSE 0 END) AS `completed_items`, SUM(CASE WHEN (`bi`.`status` = 'failed') THEN 1 ELSE 0 END) AS `failed_items`, GROUP_CONCAT(`bd`.`original_filename` SEPARATOR ', ') AS `document_files` FROM (((`batch_operations` `bo` LEFT JOIN `users` `u` ON((`bo`.`user_id` = `u`.`id`))) LEFT JOIN `batch_items` `bi` ON((`bo`.`batch_id` = `bi`.`batch_id`))) LEFT JOIN `batch_documents` `bd` ON((`bo`.`batch_id` = `bd`.`batch_id`))) GROUP BY `bo`.`id`, `bo`.`batch_id`, `bo`.`operation_type`, `u`.`username`, `u`.`full_name`, `bo`.`total_amount`, `bo`.`item_count`, `bo`.`status`, `bo`.`created_at`, `bo`.`completed_at`;

-- --------------------------------------------------------

--
-- Structure for view `expense_workflow_view`
--
DROP TABLE IF EXISTS `expense_workflow_view`;

CREATE ALGORITHM=MERGE SQL SECURITY INVOKER VIEW `expense_workflow_view` AS SELECT `e`.`id` AS `id`, `e`.`exno` AS `exno`, `e`.`status` AS `status`, `e`.`total_amount` AS `total_amount`, `e`.`created_at` AS `created_at`, `e`.`created_by` AS `created_by`, `creator`.`full_name` AS `created_by_name`, `e`.`verification_date` AS `verification_date`, `e`.`verification_by` AS `verification_by`, `verifier`.`full_name` AS `verified_by_name`, `e`.`reviewer_date` AS `reviewer_date`, `e`.`reviewer_by` AS `reviewer_by`, `reviewer`.`full_name` AS `reviewed_by_name`, `e`.`rejection_reason` AS `rejection_reason`, `e`.`rejection_date` AS `rejection_date`, `e`.`rejected_by` AS `rejected_by`, `rejector`.`full_name` AS `rejected_by_name`, `e`.`return_reason` AS `return_reason`, `e`.`return_date` AS `return_date`, `e`.`returned_by` AS `returned_by`, `returner`.`full_name` AS `returned_by_name`, `e`.`workflow_history` AS `workflow_history` FROM (((((`expenses` `e` LEFT JOIN `users` `creator` ON((`e`.`created_by` = `creator`.`id`))) LEFT JOIN `users` `verifier` ON((`e`.`verification_by` = `verifier`.`id`))) LEFT JOIN `users` `reviewer` ON((`e`.`reviewer_by` = `reviewer`.`id`))) LEFT JOIN `users` `rejector` ON((`e`.`rejected_by` = `rejector`.`id`))) LEFT JOIN `users` `returner` ON((`e`.`returned_by` = `returner`.`id`)));

--
-- Indexes for dumped tables
--

--
-- Indexes for table `activity_logs`
--
ALTER TABLE `activity_logs`
  ADD PRIMARY KEY (`id`),
  ADD KEY `idx_user_id` (`user_id`),
  ADD KEY `idx_action_type` (`action_type`),
  ADD KEY `idx_created_at` (`created_at`);

--
-- Indexes for table `batch_documents`
--
ALTER TABLE `batch_documents`
  ADD PRIMARY KEY (`id`),
  ADD KEY `idx_batch_id` (`batch_id`),
  ADD KEY `idx_document_type` (`document_type`);

--
-- Indexes for table `batch_items`
--
ALTER TABLE `batch_items`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `unique_batch_expense` (`batch_id`,`expense_id`),
  ADD KEY `idx_batch_id` (`batch_id`),
  ADD KEY `idx_expense_id` (`expense_id`),
  ADD KEY `idx_status` (`status`);

--
-- Indexes for table `batch_operations`
--
ALTER TABLE `batch_operations`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `batch_id` (`batch_id`),
  ADD KEY `idx_batch_id` (`batch_id`),
  ADD KEY `idx_user_operation` (`user_id`,`operation_type`),
  ADD KEY `idx_status` (`status`),
  ADD KEY `idx_created_at` (`created_at`);

--
-- Indexes for table `batch_performance_logs`
--
ALTER TABLE `batch_performance_logs`
  ADD PRIMARY KEY (`id`),
  ADD KEY `idx_batch_id` (`batch_id`),
  ADD KEY `idx_operation_step` (`operation_step`);

--
-- Indexes for table `customers`
--
ALTER TABLE `customers`
  ADD PRIMARY KEY (`id`),
  ADD KEY `created_by` (`created_by`);

--
-- Indexes for table `drivers`
--
ALTER TABLE `drivers`
  ADD PRIMARY KEY (`id`),
  ADD KEY `created_by` (`created_by`);

--
-- Indexes for table `expenses`
--
ALTER TABLE `expenses`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `exno` (`exno`),
  ADD KEY `item_id` (`item_id`),
  ADD KEY `customer_id` (`customer_id`),
  ADD KEY `driver_id` (`driver_id`),
  ADD KEY `idx_exno` (`exno`),
  ADD KEY `idx_job_open_date` (`job_open_date`),
  ADD KEY `idx_withdrawal_date` (`withdrawal_date`),
  ADD KEY `idx_status` (`status`),
  ADD KEY `idx_created_by` (`created_by`),
  ADD KEY `idx_expenses_status_rejection` (`status`,`rejection_date`),
  ADD KEY `idx_expenses_status_return` (`status`,`return_date`),
  ADD KEY `idx_expenses_rejected_by` (`rejected_by`),
  ADD KEY `idx_expenses_returned_by` (`returned_by`),
  ADD KEY `idx_batch_verification` (`batch_verification_id`),
  ADD KEY `idx_batch_review` (`batch_review_id`),
  ADD KEY `idx_batch_processed` (`is_batch_processed`),
  ADD KEY `idx_status_batch` (`status`,`is_batch_processed`);

--
-- Indexes for table `items`
--
ALTER TABLE `items`
  ADD PRIMARY KEY (`id`),
  ADD KEY `created_by` (`created_by`);

--
-- Indexes for table `receipt_numbers`
--
ALTER TABLE `receipt_numbers`
  ADD PRIMARY KEY (`id`),
  ADD KEY `created_by` (`created_by`),
  ADD KEY `idx_expense_id` (`expense_id`),
  ADD KEY `idx_receipt_number` (`receipt_number`);

--
-- Indexes for table `users`
--
ALTER TABLE `users`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `username` (`username`),
  ADD UNIQUE KEY `email` (`email`);

--
-- AUTO_INCREMENT for dumped tables
--

--
-- AUTO_INCREMENT for table `activity_logs`
--
ALTER TABLE `activity_logs`
  MODIFY `id` int NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `batch_documents`
--
ALTER TABLE `batch_documents`
  MODIFY `id` int NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `batch_items`
--
ALTER TABLE `batch_items`
  MODIFY `id` int NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `batch_operations`
--
ALTER TABLE `batch_operations`
  MODIFY `id` int NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `batch_performance_logs`
--
ALTER TABLE `batch_performance_logs`
  MODIFY `id` int NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `customers`
--
ALTER TABLE `customers`
  MODIFY `id` int NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=93;

--
-- AUTO_INCREMENT for table `drivers`
--
ALTER TABLE `drivers`
  MODIFY `id` int NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=59;

--
-- AUTO_INCREMENT for table `expenses`
--
ALTER TABLE `expenses`
  MODIFY `id` int NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `items`
--
ALTER TABLE `items`
  MODIFY `id` int NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=43;

--
-- AUTO_INCREMENT for table `receipt_numbers`
--
ALTER TABLE `receipt_numbers`
  MODIFY `id` int NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `users`
--
ALTER TABLE `users`
  MODIFY `id` int NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=8;

--
-- Constraints for dumped tables
--

--
-- Constraints for table `activity_logs`
--
ALTER TABLE `activity_logs`
  ADD CONSTRAINT `activity_logs_ibfk_1` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`);

--
-- Constraints for table `batch_documents`
--
ALTER TABLE `batch_documents`
  ADD CONSTRAINT `batch_documents_ibfk_1` FOREIGN KEY (`batch_id`) REFERENCES `batch_operations` (`batch_id`) ON DELETE CASCADE;

--
-- Constraints for table `batch_items`
--
ALTER TABLE `batch_items`
  ADD CONSTRAINT `batch_items_ibfk_1` FOREIGN KEY (`batch_id`) REFERENCES `batch_operations` (`batch_id`) ON DELETE CASCADE,
  ADD CONSTRAINT `batch_items_ibfk_2` FOREIGN KEY (`expense_id`) REFERENCES `expenses` (`id`) ON DELETE CASCADE;

--
-- Constraints for table `batch_operations`
--
ALTER TABLE `batch_operations`
  ADD CONSTRAINT `batch_operations_ibfk_1` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE;

--
-- Constraints for table `batch_performance_logs`
--
ALTER TABLE `batch_performance_logs`
  ADD CONSTRAINT `batch_performance_logs_ibfk_1` FOREIGN KEY (`batch_id`) REFERENCES `batch_operations` (`batch_id`) ON DELETE CASCADE;

--
-- Constraints for table `customers`
--
ALTER TABLE `customers`
  ADD CONSTRAINT `customers_ibfk_1` FOREIGN KEY (`created_by`) REFERENCES `users` (`id`);

--
-- Constraints for table `drivers`
--
ALTER TABLE `drivers`
  ADD CONSTRAINT `drivers_ibfk_1` FOREIGN KEY (`created_by`) REFERENCES `users` (`id`);

--
-- Constraints for table `expenses`
--
ALTER TABLE `expenses`
  ADD CONSTRAINT `expenses_ibfk_1` FOREIGN KEY (`item_id`) REFERENCES `items` (`id`),
  ADD CONSTRAINT `expenses_ibfk_2` FOREIGN KEY (`customer_id`) REFERENCES `customers` (`id`),
  ADD CONSTRAINT `expenses_ibfk_3` FOREIGN KEY (`driver_id`) REFERENCES `drivers` (`id`),
  ADD CONSTRAINT `expenses_ibfk_4` FOREIGN KEY (`created_by`) REFERENCES `users` (`id`),
  ADD CONSTRAINT `fk_batch_review` FOREIGN KEY (`batch_review_id`) REFERENCES `batch_operations` (`batch_id`) ON DELETE SET NULL,
  ADD CONSTRAINT `fk_batch_verification` FOREIGN KEY (`batch_verification_id`) REFERENCES `batch_operations` (`batch_id`) ON DELETE SET NULL,
  ADD CONSTRAINT `fk_expenses_rejected_by` FOREIGN KEY (`rejected_by`) REFERENCES `users` (`id`) ON DELETE SET NULL,
  ADD CONSTRAINT `fk_expenses_returned_by` FOREIGN KEY (`returned_by`) REFERENCES `users` (`id`) ON DELETE SET NULL;

--
-- Constraints for table `items`
--
ALTER TABLE `items`
  ADD CONSTRAINT `items_ibfk_1` FOREIGN KEY (`created_by`) REFERENCES `users` (`id`);

--
-- Constraints for table `receipt_numbers`
--
ALTER TABLE `receipt_numbers`
  ADD CONSTRAINT `receipt_numbers_ibfk_1` FOREIGN KEY (`expense_id`) REFERENCES `expenses` (`id`) ON DELETE CASCADE,
  ADD CONSTRAINT `receipt_numbers_ibfk_2` FOREIGN KEY (`created_by`) REFERENCES `users` (`id`);

/*!40101 SET CHARACTER_SET_CLIENT=@OLD_CHARACTER_SET_CLIENT */;
/*!40101 SET CHARACTER_SET_RESULTS=@OLD_CHARACTER_SET_RESULTS */;
/*!40101 SET COLLATION_CONNECTION=@OLD_COLLATION_CONNECTION */;
