-- Update activity_logs table to include new action types
-- Execute this SQL to add new action types for batch operations and workflow actions

-- Update the action_type enum to include new values
ALTER TABLE activity_logs 
MODIFY COLUMN action_type ENUM(
    'create', 
    'update', 
    'delete', 
    'status_change', 
    'login', 
    'logout', 
    'batch_verification', 
    'batch_review', 
    'batch_cancel', 
    'batch_retry', 
    'admin_override', 
    'reject', 
    'return'
) NOT NULL;

-- Verify the change
DESCRIBE activity_logs;

-- Show current action types in use
SELECT DISTINCT action_type, COUNT(*) as count 
FROM activity_logs 
GROUP BY action_type 
ORDER BY action_type;
