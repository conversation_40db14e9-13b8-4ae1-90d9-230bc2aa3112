-- MariaDB 5.5.68 Compatible Triggers for Auto-Update DateTime
-- Run this AFTER importing schema and procedures

-- Drop existing triggers if they exist
DROP TRIGGER IF EXISTS customers_updated_at;
DROP TRIGGER IF EXISTS drivers_updated_at;
DROP TRIGGER IF EXISTS items_updated_at;
DROP TRIGGER IF EXISTS users_updated_at;
DROP TRIGGER IF EXISTS expenses_updated_at;

-- Trigger for customers table
DELIMITER $$
CREATE TRIGGER customers_updated_at
    BEFORE UPDATE ON customers
    FOR EACH ROW
BEGIN
    SET NEW.updated_at = NOW();
END$$
DELIMITER ;

-- Trigger for drivers table
DELIMITER $$
CREATE TRIGGER drivers_updated_at
    BEFORE UPDATE ON drivers
    FOR EACH ROW
BEGIN
    SET NEW.updated_at = NOW();
END$$
DELIMITER ;

-- Trigger for items table
DELIMITER $$
CREATE TRIGGER items_updated_at
    BEFORE UPDATE ON items
    FOR EACH ROW
BEGIN
    SET NEW.updated_at = NOW();
END$$
DELIMITER ;

-- Trigger for users table
DELIMITER $$
CREATE TRIGGER users_updated_at
    BEFORE UPDATE ON users
    FOR EACH ROW
BEGIN
    SET NEW.updated_at = NOW();
END$$
DELIMITER ;

-- Trigger for expenses table
DELIMITER $$
CREATE TRIGGER expenses_updated_at
    BEFORE UPDATE ON expenses
    FOR EACH ROW
BEGIN
    SET NEW.updated_at = NOW();
END$$
DELIMITER ;
