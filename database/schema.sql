-- Expenses System Database Schema
-- Create database
CREATE DATABASE IF NOT EXISTS expenses_system CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
USE expenses_system;

-- Users table for authentication and role management
CREATE TABLE users (
    id INT AUTO_INCREMENT PRIMARY KEY,
    username VARCHAR(50) UNIQUE NOT NULL,
    email VARCHAR(100) UNIQUE NOT NULL,
    password_hash VARCHAR(255) NOT NULL,
    full_name VARCHAR(100) NOT NULL,
    role ENUM('data_entry', 'verification', 'reviewer', 'administrator') NOT NULL DEFAULT 'data_entry',
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- Items table for expense items
CREATE TABLE items (
    id INT AUTO_INCREMENT PRIMARY <PERSON>EY,
    name VARCHAR(100) NOT NULL,
    description TEXT,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    created_by INT,
    FOREIGN KEY (created_by) REFERENCES users(id)
);

-- Customers table
CREATE TABLE customers (
    id INT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(100) NOT NULL,
    contact_person VARCHAR(100),
    phone VARCHAR(20),
    email VARCHAR(100),
    address TEXT,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    created_by INT,
    FOREIGN KEY (created_by) REFERENCES users(id)
);

-- Drivers table
CREATE TABLE drivers (
    id INT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(100) NOT NULL,
    license_number VARCHAR(50),
    phone VARCHAR(20),
    vehicle_plate VARCHAR(20),
    payment_account_no VARCHAR(50),
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    created_by INT,
    FOREIGN KEY (created_by) REFERENCES users(id)
);

-- Main expenses table
CREATE TABLE expenses (
    id INT AUTO_INCREMENT PRIMARY KEY,
    sequence VARCHAR(3) NOT NULL,
    exno VARCHAR(15) NOT NULL UNIQUE,
    bookingno VARCHAR(100),
    job_open_date DATE NOT NULL,
    item_id INT,
    customer_id INT,
    containerno VARCHAR(255),
    driver_id INT,
    vehicle_plate VARCHAR(20),
    payment_account_no VARCHAR(50),
    additional_details VARCHAR(255),
    requester VARCHAR(100),
    receiver VARCHAR(100),
    payer VARCHAR(100),
    withdrawal_date DATE NOT NULL,
    transfer_no VARCHAR(100),
    transfer_slip_image VARCHAR(255),
    receipt_images TEXT, -- JSON array of image paths
    status ENUM('open', 'pending', 'success') DEFAULT 'open',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    created_by INT NOT NULL,
    
    FOREIGN KEY (item_id) REFERENCES items(id),
    FOREIGN KEY (customer_id) REFERENCES customers(id),
    FOREIGN KEY (driver_id) REFERENCES drivers(id),
    FOREIGN KEY (created_by) REFERENCES users(id),
    
    INDEX idx_exno (exno),
    INDEX idx_job_open_date (job_open_date),
    INDEX idx_withdrawal_date (withdrawal_date),
    INDEX idx_status (status),
    INDEX idx_created_by (created_by)
);

-- Activity logs table for audit trail
CREATE TABLE activity_logs (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    action_type ENUM('create', 'update', 'delete', 'status_change', 'login', 'logout', 'batch_verification', 'batch_review', 'batch_cancel', 'batch_retry', 'admin_override', 'reject', 'return') NOT NULL,
    table_name VARCHAR(50),
    record_id INT,
    old_values TEXT, -- JSON of old values
    new_values TEXT, -- JSON of new values
    description TEXT,
    ip_address VARCHAR(45),
    user_agent TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,

    FOREIGN KEY (user_id) REFERENCES users(id),
    INDEX idx_user_id (user_id),
    INDEX idx_action_type (action_type),
    INDEX idx_created_at (created_at)
);

-- Insert default admin user (password: admin123)
INSERT INTO users (username, email, password_hash, full_name, role) VALUES 
('admin', '<EMAIL>', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'System Administrator', 'administrator');

-- Insert some sample data
INSERT INTO items (name, description, created_by) VALUES 
('Fuel', 'Vehicle fuel expenses', 1),
('Maintenance', 'Vehicle maintenance and repairs', 1),
('Toll Fees', 'Highway and bridge toll fees', 1),
('Parking', 'Parking fees', 1);

INSERT INTO customers (name, contact_person, phone, email, created_by) VALUES 
('ABC Logistics', 'John Smith', '+**********', '<EMAIL>', 1),
('XYZ Shipping', 'Jane Doe', '+**********', '<EMAIL>', 1);

INSERT INTO drivers (name, license_number, phone, vehicle_plate, payment_account_no, created_by) VALUES 
('Mike Johnson', 'DL123456', '+**********', 'ABC-1234', 'ACC-001', 1),
('Sarah Wilson', 'DL789012', '+**********', 'XYZ-5678', 'ACC-002', 1);
