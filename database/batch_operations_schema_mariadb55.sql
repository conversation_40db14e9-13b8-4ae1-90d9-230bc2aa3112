-- Batch Operations Schema for MariaDB 5.5.68 Compatibility
-- This version removes JSON functions and stored procedures that are not supported

USE expenses_system;

-- 1. ตารางหลักสำหรับ Batch Operations
CREATE TABLE IF NOT EXISTS batch_operations (
    id INT PRIMARY KEY AUTO_INCREMENT,
    batch_id VARCHAR(50) UNIQUE NOT NULL COMMENT 'รหัส Batch เช่น BATCH_VER_20251017_001',
    operation_type ENUM('verification', 'review') NOT NULL COMMENT 'ประเภทการดำเนินการ',
    user_id INT NOT NULL COMMENT 'ผู้ดำเนินการ',
    total_amount DECIMAL(12,2) NOT NULL COMMENT 'ยอดรวมทั้งหมด',
    item_count INT NOT NULL COMMENT 'จำนวนรายการ',
    status ENUM('pending', 'processing', 'completed', 'failed', 'cancelled') DEFAULT 'pending',
    notes TEXT NULL COMMENT 'หมายเหตุเพิ่มเติม',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    started_at TIMESTAMP NULL COMMENT 'เวลาเริ่มประมวลผล',
    completed_at TIMESTAMP NULL COMMENT 'เวลาเสร็จสิ้น',
    
    INDEX idx_batch_id (batch_id),
    INDEX idx_operation_type (operation_type),
    INDEX idx_user_id (user_id),
    INDEX idx_status (status),
    INDEX idx_created_at (created_at),
    
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
) COMMENT='ตารางหลักสำหรับ Batch Operations';

-- 2. รายการใน Batch
CREATE TABLE IF NOT EXISTS batch_items (
    id INT PRIMARY KEY AUTO_INCREMENT,
    batch_id VARCHAR(50) NOT NULL,
    expense_id INT NOT NULL,
    individual_amount DECIMAL(12,2) NOT NULL COMMENT 'จำนวนเงินของรายการนี้',
    status ENUM('pending', 'processing', 'completed', 'failed') DEFAULT 'pending',
    error_message TEXT NULL COMMENT 'ข้อความ Error หากมี',
    processed_at TIMESTAMP NULL COMMENT 'เวลาที่ประมวลผลรายการนี้',
    
    INDEX idx_batch_id (batch_id),
    INDEX idx_expense_id (expense_id),
    INDEX idx_status (status),
    
    FOREIGN KEY (batch_id) REFERENCES batch_operations(batch_id) ON DELETE CASCADE,
    FOREIGN KEY (expense_id) REFERENCES expenses(id) ON DELETE CASCADE,
    
    UNIQUE KEY unique_batch_expense (batch_id, expense_id)
) COMMENT='รายการ Expenses ใน Batch';

-- 3. เอกสารของ Batch
CREATE TABLE IF NOT EXISTS batch_documents (
    id INT PRIMARY KEY AUTO_INCREMENT,
    batch_id VARCHAR(50) NOT NULL,
    document_type ENUM('verification_slip', 'review_slip') NOT NULL,
    file_path VARCHAR(500) NOT NULL COMMENT 'Path ของไฟล์',
    original_filename VARCHAR(255) NOT NULL COMMENT 'ชื่อไฟล์ต้นฉบับ',
    file_size INT NOT NULL COMMENT 'ขนาดไฟล์ (bytes)',
    mime_type VARCHAR(100) NOT NULL COMMENT 'ประเภทไฟล์',
    uploaded_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    INDEX idx_batch_id (batch_id),
    INDEX idx_document_type (document_type),
    
    FOREIGN KEY (batch_id) REFERENCES batch_operations(batch_id) ON DELETE CASCADE
) COMMENT='เอกสารที่แนบกับ Batch';

-- 4. Performance Tracking
CREATE TABLE IF NOT EXISTS batch_performance_logs (
    id INT PRIMARY KEY AUTO_INCREMENT,
    batch_id VARCHAR(50) NOT NULL,
    operation_step VARCHAR(100) NOT NULL COMMENT 'ขั้นตอนการทำงาน',
    start_time TIMESTAMP NOT NULL,
    end_time TIMESTAMP NULL,
    duration_ms INT NULL COMMENT 'ระยะเวลา (milliseconds)',
    memory_usage_mb DECIMAL(8,2) NULL COMMENT 'การใช้ Memory (MB)',
    items_processed INT DEFAULT 0,
    success_count INT DEFAULT 0,
    error_count INT DEFAULT 0,
    
    INDEX idx_batch_id (batch_id),
    INDEX idx_operation_step (operation_step),
    
    FOREIGN KEY (batch_id) REFERENCES batch_operations(batch_id) ON DELETE CASCADE
) COMMENT='Log การทำงานเพื่อ Performance Analysis';

-- 5. ปรับปรุงตาราง expenses (ตรวจสอบว่ามีคอลัมน์แล้วหรือไม่)
-- Add batch_verification_id column if not exists
SET @sql = (SELECT IF(
    (SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS
     WHERE table_name = 'expenses'
     AND table_schema = DATABASE()
     AND column_name = 'batch_verification_id') > 0,
    'SELECT "batch_verification_id column already exists"',
    'ALTER TABLE expenses ADD COLUMN batch_verification_id VARCHAR(50) NULL COMMENT "รหัส Batch Verification" AFTER verification_by'
));
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- Add batch_review_id column if not exists
SET @sql = (SELECT IF(
    (SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS
     WHERE table_name = 'expenses'
     AND table_schema = DATABASE()
     AND column_name = 'batch_review_id') > 0,
    'SELECT "batch_review_id column already exists"',
    'ALTER TABLE expenses ADD COLUMN batch_review_id VARCHAR(50) NULL COMMENT "รหัส Batch Review" AFTER reviewer_by'
));
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- Add is_batch_processed column if not exists
SET @sql = (SELECT IF(
    (SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS
     WHERE table_name = 'expenses'
     AND table_schema = DATABASE()
     AND column_name = 'is_batch_processed') > 0,
    'SELECT "is_batch_processed column already exists"',
    'ALTER TABLE expenses ADD COLUMN is_batch_processed BOOLEAN DEFAULT FALSE COMMENT "ประมวลผลแบบ Batch หรือไม่" AFTER status'
));
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- Add batch_notes column if not exists
SET @sql = (SELECT IF(
    (SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS
     WHERE table_name = 'expenses'
     AND table_schema = DATABASE()
     AND column_name = 'batch_notes') > 0,
    'SELECT "batch_notes column already exists"',
    'ALTER TABLE expenses ADD COLUMN batch_notes TEXT NULL COMMENT "หมายเหตุจาก Batch Processing"'
));
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- เพิ่ม Index สำหรับ performance (ตรวจสอบว่ามีแล้วหรือไม่)
-- Add indexes if they don't exist
SET @sql = (SELECT IF(
    (SELECT COUNT(*) FROM INFORMATION_SCHEMA.STATISTICS
     WHERE table_name = 'expenses'
     AND table_schema = DATABASE()
     AND index_name = 'idx_batch_verification') > 0,
    'SELECT "idx_batch_verification index already exists"',
    'ALTER TABLE expenses ADD INDEX idx_batch_verification (batch_verification_id)'
));
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

SET @sql = (SELECT IF(
    (SELECT COUNT(*) FROM INFORMATION_SCHEMA.STATISTICS
     WHERE table_name = 'expenses'
     AND table_schema = DATABASE()
     AND index_name = 'idx_batch_review') > 0,
    'SELECT "idx_batch_review index already exists"',
    'ALTER TABLE expenses ADD INDEX idx_batch_review (batch_review_id)'
));
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

SET @sql = (SELECT IF(
    (SELECT COUNT(*) FROM INFORMATION_SCHEMA.STATISTICS
     WHERE table_name = 'expenses'
     AND table_schema = DATABASE()
     AND index_name = 'idx_batch_processed') > 0,
    'SELECT "idx_batch_processed index already exists"',
    'ALTER TABLE expenses ADD INDEX idx_batch_processed (is_batch_processed)'
));
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

SET @sql = (SELECT IF(
    (SELECT COUNT(*) FROM INFORMATION_SCHEMA.STATISTICS
     WHERE table_name = 'expenses'
     AND table_schema = DATABASE()
     AND index_name = 'idx_status_batch') > 0,
    'SELECT "idx_status_batch index already exists"',
    'ALTER TABLE expenses ADD INDEX idx_status_batch (status, is_batch_processed)'
));
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- เพิ่ม Foreign Key constraints (ตรวจสอบว่ามีแล้วหรือไม่)
SET @sql = (SELECT IF(
    (SELECT COUNT(*) FROM INFORMATION_SCHEMA.KEY_COLUMN_USAGE
     WHERE table_name = 'expenses'
     AND table_schema = DATABASE()
     AND constraint_name = 'fk_batch_verification') > 0,
    'SELECT "fk_batch_verification constraint already exists"',
    'ALTER TABLE expenses ADD CONSTRAINT fk_batch_verification FOREIGN KEY (batch_verification_id) REFERENCES batch_operations(batch_id) ON DELETE SET NULL'
));
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

SET @sql = (SELECT IF(
    (SELECT COUNT(*) FROM INFORMATION_SCHEMA.KEY_COLUMN_USAGE
     WHERE table_name = 'expenses'
     AND table_schema = DATABASE()
     AND constraint_name = 'fk_batch_review') > 0,
    'SELECT "fk_batch_review constraint already exists"',
    'ALTER TABLE expenses ADD CONSTRAINT fk_batch_review FOREIGN KEY (batch_review_id) REFERENCES batch_operations(batch_id) ON DELETE SET NULL'
));
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 6. สร้าง View สำหรับ Batch Summary (ไม่ใช้ JSON functions)
CREATE OR REPLACE VIEW batch_summary AS
SELECT 
    bo.id,
    bo.batch_id,
    bo.operation_type,
    u.username,
    u.full_name as user_name,
    bo.total_amount,
    bo.item_count,
    bo.status,
    bo.created_at,
    bo.completed_at,
    COUNT(bi.id) as actual_item_count,
    COUNT(bd.id) as document_count,
    CASE 
        WHEN bo.completed_at IS NOT NULL THEN 
            TIMESTAMPDIFF(SECOND, bo.created_at, bo.completed_at)
        ELSE NULL 
    END as processing_duration_seconds

FROM batch_operations bo
LEFT JOIN users u ON bo.user_id = u.id
LEFT JOIN batch_items bi ON bo.batch_id = bi.batch_id
LEFT JOIN batch_documents bd ON bo.batch_id = bd.batch_id
GROUP BY bo.id, bo.batch_id, bo.operation_type, u.username, u.full_name,
         bo.total_amount, bo.item_count, bo.status, bo.created_at, bo.completed_at;

-- สร้างตัวอย่างข้อมูล (ถ้าต้องการ)
-- INSERT INTO batch_operations (batch_id, operation_type, user_id, total_amount, item_count, status) 
-- VALUES ('BATCH_VER_20251019_001', 'verification', 1, 15000.00, 5, 'completed');
