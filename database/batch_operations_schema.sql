-- ===================================================================
-- Multi-select Batch Operations Database Schema
-- สำหรับ Verification และ Review Batch Processing
-- ===================================================================

-- 1. ตารางหลักสำหรับ Batch Operations
CREATE TABLE batch_operations (
    id INT PRIMARY KEY AUTO_INCREMENT,
    batch_id VARCHAR(50) UNIQUE NOT NULL COMMENT 'รหัส Batch เช่น BATCH_VER_20251017_001',
    operation_type ENUM('verification', 'review') NOT NULL COMMENT 'ประเภทการดำเนินการ',
    user_id INT NOT NULL COMMENT 'ผู้ดำเนินการ',
    total_amount DECIMAL(12,2) NOT NULL COMMENT 'ยอดรวมทั้งหมด',
    item_count INT NOT NULL COMMENT 'จำนวนรายการ',
    status ENUM('pending', 'processing', 'completed', 'failed', 'cancelled') DEFAULT 'pending',
    notes TEXT NULL COMMENT 'หมายเหตุเพิ่มเติม',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    started_at TIMESTAMP NULL COMMENT 'เวลาเริ่มประมวลผล',
    completed_at TIMESTAMP NULL COMMENT 'เวลาเสร็จสิ้น',
    
    INDEX idx_batch_id (batch_id),
    INDEX idx_user_operation (user_id, operation_type),
    INDEX idx_status (status),
    INDEX idx_created_at (created_at),
    
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
) COMMENT='ตารางหลักสำหรับ Batch Operations';

-- 2. รายการใน Batch
CREATE TABLE batch_items (
    id INT PRIMARY KEY AUTO_INCREMENT,
    batch_id VARCHAR(50) NOT NULL,
    expense_id INT NOT NULL,
    individual_amount DECIMAL(12,2) NOT NULL COMMENT 'จำนวนเงินของรายการนี้',
    status ENUM('pending', 'processing', 'completed', 'failed') DEFAULT 'pending',
    error_message TEXT NULL COMMENT 'ข้อความ Error หากมี',
    processed_at TIMESTAMP NULL COMMENT 'เวลาที่ประมวลผลรายการนี้',
    
    INDEX idx_batch_id (batch_id),
    INDEX idx_expense_id (expense_id),
    INDEX idx_status (status),
    
    FOREIGN KEY (batch_id) REFERENCES batch_operations(batch_id) ON DELETE CASCADE,
    FOREIGN KEY (expense_id) REFERENCES expenses(id) ON DELETE CASCADE,
    
    UNIQUE KEY unique_batch_expense (batch_id, expense_id)
) COMMENT='รายการ Expenses ใน Batch';

-- 3. เอกสารของ Batch
CREATE TABLE batch_documents (
    id INT PRIMARY KEY AUTO_INCREMENT,
    batch_id VARCHAR(50) NOT NULL,
    document_type ENUM('verification_slip', 'review_slip') NOT NULL,
    file_path VARCHAR(500) NOT NULL COMMENT 'Path ของไฟล์',
    original_filename VARCHAR(255) NOT NULL COMMENT 'ชื่อไฟล์ต้นฉบับ',
    file_size INT NOT NULL COMMENT 'ขนาดไฟล์ (bytes)',
    mime_type VARCHAR(100) NOT NULL COMMENT 'ประเภทไฟล์',
    uploaded_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    INDEX idx_batch_id (batch_id),
    INDEX idx_document_type (document_type),
    
    FOREIGN KEY (batch_id) REFERENCES batch_operations(batch_id) ON DELETE CASCADE
) COMMENT='เอกสารที่แนบกับ Batch';

-- 4. Performance Tracking (Optional)
CREATE TABLE batch_performance_logs (
    id INT PRIMARY KEY AUTO_INCREMENT,
    batch_id VARCHAR(50) NOT NULL,
    operation_step VARCHAR(100) NOT NULL COMMENT 'ขั้นตอนการทำงาน',
    start_time TIMESTAMP NOT NULL,
    end_time TIMESTAMP NULL,
    duration_ms INT NULL COMMENT 'ระยะเวลา (milliseconds)',
    memory_usage_mb DECIMAL(8,2) NULL COMMENT 'การใช้ Memory (MB)',
    items_processed INT DEFAULT 0,
    success_count INT DEFAULT 0,
    error_count INT DEFAULT 0,
    
    INDEX idx_batch_id (batch_id),
    INDEX idx_operation_step (operation_step),
    
    FOREIGN KEY (batch_id) REFERENCES batch_operations(batch_id) ON DELETE CASCADE
) COMMENT='Log การทำงานเพื่อ Performance Analysis';

-- 5. ปรับปรุงตาราง expenses
ALTER TABLE expenses 
ADD COLUMN batch_verification_id VARCHAR(50) NULL COMMENT 'รหัส Batch Verification' AFTER verification_by,
ADD COLUMN batch_review_id VARCHAR(50) NULL COMMENT 'รหัส Batch Review' AFTER reviewer_by,
ADD COLUMN is_batch_processed BOOLEAN DEFAULT FALSE COMMENT 'ประมวลผลแบบ Batch หรือไม่' AFTER status,
ADD COLUMN batch_notes TEXT NULL COMMENT 'หมายเหตุจาก Batch Processing';

-- เพิ่ม Index สำหรับ performance
ALTER TABLE expenses 
ADD INDEX idx_batch_verification (batch_verification_id),
ADD INDEX idx_batch_review (batch_review_id),
ADD INDEX idx_batch_processed (is_batch_processed),
ADD INDEX idx_status_batch (status, is_batch_processed);

-- เพิ่ม Foreign Key constraints
ALTER TABLE expenses 
ADD CONSTRAINT fk_batch_verification 
    FOREIGN KEY (batch_verification_id) REFERENCES batch_operations(batch_id) ON DELETE SET NULL,
ADD CONSTRAINT fk_batch_review
    FOREIGN KEY (batch_review_id) REFERENCES batch_operations(batch_id) ON DELETE SET NULL;

-- 6. สร้าง View สำหรับ Reporting
CREATE VIEW batch_summary_view AS
SELECT
    bo.id,
    bo.batch_id,
    bo.operation_type,
    u.username,
    u.full_name,
    bo.total_amount,
    bo.item_count,
    bo.status,
    bo.created_at,
    bo.completed_at,
    TIMESTAMPDIFF(MINUTE, bo.created_at, bo.completed_at) as duration_minutes,

    -- Item statistics
    COUNT(bi.id) as total_items,
    SUM(CASE WHEN bi.status = 'completed' THEN 1 ELSE 0 END) as completed_items,
    SUM(CASE WHEN bi.status = 'failed' THEN 1 ELSE 0 END) as failed_items,

    -- Document info
    GROUP_CONCAT(bd.original_filename SEPARATOR ', ') as document_files

FROM batch_operations bo
LEFT JOIN users u ON bo.user_id = u.id
LEFT JOIN batch_items bi ON bo.batch_id = bi.batch_id
LEFT JOIN batch_documents bd ON bo.batch_id = bd.batch_id
GROUP BY bo.id, bo.batch_id, bo.operation_type, u.username, u.full_name,
         bo.total_amount, bo.item_count, bo.status, bo.created_at, bo.completed_at;

-- 7. สร้าง Stored Procedures สำหรับ Batch Operations
DELIMITER //

CREATE PROCEDURE CreateBatchOperation(
    IN p_operation_type VARCHAR(20),
    IN p_user_id INT,
    IN p_expense_ids JSON,
    IN p_total_amount DECIMAL(12,2),
    OUT p_batch_id VARCHAR(50)
)
BEGIN
    DECLARE v_batch_id VARCHAR(50);
    DECLARE v_item_count INT;
    DECLARE v_expense_id INT;
    DECLARE v_amount DECIMAL(12,2);
    DECLARE i INT DEFAULT 0;
    DECLARE done INT DEFAULT FALSE;

    -- สร้าง Batch ID
    SET v_batch_id = CONCAT('BATCH_', UPPER(p_operation_type), '_', DATE_FORMAT(NOW(), '%Y%m%d_%H%i%s'));

    -- นับจำนวน items
    SET v_item_count = JSON_LENGTH(p_expense_ids);

    -- สร้าง Batch Operation
    INSERT INTO batch_operations (batch_id, operation_type, user_id, total_amount, item_count)
    VALUES (v_batch_id, p_operation_type, p_user_id, p_total_amount, v_item_count);

    -- เพิ่ม Batch Items
    WHILE i < v_item_count DO
        SET v_expense_id = JSON_UNQUOTE(JSON_EXTRACT(p_expense_ids, CONCAT('$[', i, '].expense_id')));
        SET v_amount = JSON_UNQUOTE(JSON_EXTRACT(p_expense_ids, CONCAT('$[', i, '].amount')));

        INSERT INTO batch_items (batch_id, expense_id, individual_amount)
        VALUES (v_batch_id, v_expense_id, v_amount);

        SET i = i + 1;
    END WHILE;

    SET p_batch_id = v_batch_id;
END //

DELIMITER ;
