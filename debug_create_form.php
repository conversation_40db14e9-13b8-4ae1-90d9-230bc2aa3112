<?php
session_start();
require_once 'config/database.php';
require_once 'includes/functions.php';

// Check if user is logged in
if (!isset($_SESSION['user_id'])) {
    header('Location: login.php');
    exit();
}

$database = new Database();
$db = $database->getConnection();

// Get user info
$stmt = $db->prepare("SELECT * FROM users WHERE id = ?");
$stmt->execute([$_SESSION['user_id']]);
$user = $stmt->fetch();
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Debug Create Form - Receipt Validation</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        /* Receipt number validation styling */
        .receipt-number-input.is-invalid {
            border-color: #dc3545;
            background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 12 12' width='12' height='12' fill='none' stroke='%23dc3545'%3e%3ccircle cx='6' cy='6' r='4.5'/%3e%3cpath d='m5.8 3.6h.4L6 6.5z'/%3e%3ccircle cx='6' cy='8.2' r='.6' fill='%23dc3545' stroke='none'/%3e%3c/svg%3e");
            background-repeat: no-repeat;
            background-position: right calc(0.375em + 0.1875rem) center;
            background-size: calc(0.75em + 0.375rem) calc(0.75em + 0.375rem);
        }
        
        .receipt-number-input.is-valid {
            border-color: #198754;
            background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 8 8'%3e%3cpath fill='%23198754' d='m2.3 6.73.94-.94 1.93 1.93 3.53-3.53.94.94L4.16 9.66z'/%3e%3c/svg%3e");
            background-repeat: no-repeat;
            background-position: right calc(0.375em + 0.1875rem) center;
            background-size: calc(0.75em + 0.375rem) calc(0.75em + 0.375rem);
        }
        
        .invalid-feedback {
            display: block;
            width: 100%;
            margin-top: 0.25rem;
            font-size: 0.875em;
            color: #dc3545;
        }
        
        .valid-feedback {
            display: block;
            width: 100%;
            margin-top: 0.25rem;
            font-size: 0.875em;
            color: #198754;
        }
        
        .receipt-number-checking {
            position: relative;
        }
        
        .receipt-number-checking::after {
            content: '';
            position: absolute;
            right: 10px;
            top: 50%;
            transform: translateY(-50%);
            width: 16px;
            height: 16px;
            border: 2px solid #f3f3f3;
            border-top: 2px solid #007bff;
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }
        
        @keyframes spin {
            0% { transform: translateY(-50%) rotate(0deg); }
            100% { transform: translateY(-50%) rotate(360deg); }
        }
    </style>
</head>
<body>
    <div class="container mt-4">
        <div class="row">
            <div class="col-md-8 mx-auto">
                <div class="card">
                    <div class="card-header">
                        <h4><i class="fas fa-bug me-2"></i>Debug Create Form - Receipt Validation</h4>
                        <small class="text-muted">User: <?php echo htmlspecialchars($user['username']); ?> (<?php echo htmlspecialchars($user['role']); ?>)</small>
                    </div>
                    <div class="card-body">
                        <div class="alert alert-info">
                            <h6><i class="fas fa-info-circle me-2"></i>Test Instructions:</h6>
                            <ul class="mb-0">
                                <li>Select receipt files to generate receipt number fields</li>
                                <li>Try entering "1111" or "2222" (existing numbers)</li>
                                <li>Try entering same number in multiple fields</li>
                                <li>Check browser console for debug messages</li>
                            </ul>
                        </div>
                        
                        <form>
                            <!-- Receipt Files -->
                            <div class="mb-3">
                                <label for="receipts" class="form-label">Receipt Files *</label>
                                <input type="file" class="form-control" id="receipts" name="receipts[]" 
                                       accept="image/*,.pdf" multiple required>
                                <div class="form-text">Select multiple receipt files to test validation</div>
                            </div>
                            
                            <!-- Receipt Numbers Container -->
                            <div id="receipt-numbers-container" class="d-none">
                                <h6>Receipt Details</h6>
                                <div class="alert alert-info">
                                    <small><i class="fas fa-info-circle"></i> กรอกรายละเอียดสำหรับแต่ละใบเสร็จ</small>
                                </div>
                                <div id="receipt-numbers"></div>
                                <div class="mt-3">
                                    <strong>Total Amount: <span id="total-amount">0.00</span> บาท</strong>
                                </div>
                            </div>
                            
                            <div class="mt-4">
                                <button type="submit" class="btn btn-primary" id="submit-btn">
                                    <i class="fas fa-save me-1"></i>Test Submit
                                </button>
                                <button type="button" class="btn btn-secondary" onclick="location.reload()">
                                    <i class="fas fa-refresh me-1"></i>Reload
                                </button>
                            </div>
                        </form>
                        
                        <div id="debug-info" class="mt-4">
                            <h6>Debug Information:</h6>
                            <div id="debug-log" class="bg-light p-3 rounded" style="font-family: monospace; font-size: 12px; max-height: 300px; overflow-y: auto;">
                                <!-- Debug messages will appear here -->
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script src="assets/js/expense-form.js"></script>
    
    <script>
        // Override console.log to also display in debug area
        const originalLog = console.log;
        console.log = function(...args) {
            originalLog.apply(console, args);
            const debugLog = document.getElementById('debug-log');
            if (debugLog) {
                const timestamp = new Date().toLocaleTimeString();
                const message = args.map(arg => 
                    typeof arg === 'object' ? JSON.stringify(arg) : String(arg)
                ).join(' ');
                debugLog.innerHTML += `[${timestamp}] ${message}<br>`;
                debugLog.scrollTop = debugLog.scrollHeight;
            }
        };
        
        $(document).ready(function() {
            console.log('Debug page loaded');
            console.log('jQuery version:', $.fn.jquery);
            console.log('Looking for receipt file input...');
            
            // Test if receipt file change handler works
            $('#receipts').on('change', function() {
                console.log('Receipt files selected:', this.files.length);
            });
        });
    </script>
</body>
</html>
